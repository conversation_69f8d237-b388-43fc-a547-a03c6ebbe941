# 楼宇经济管理系统

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.4.3-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://openjdk.java.net/)
[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)

## 📋 项目简介

楼宇经济管理系统是一个基于Spring Boot 3的企业级楼宇园区管理平台，专为政府部门和园区管理机构提供全方位的楼宇经济数据管理和分析服务。系统集成了企业管理、空间资源管理、税收监控、数据分析等核心功能，助力提升楼宇经济管理效率和决策水平。

## ✨ 核心功能

### 🏢 楼宇空间管理
- **楼宇档案管理**: 楼宇基本信息、产权信息、建筑参数等
- **楼层管理**: 楼层平面图配置、房间布局设计
- **房间管理**: 房间状态跟踪、租赁信息管理
- **空间分析**: 空置率统计、利用率分析

### 🏢 企业全生命周期管理
- **企业档案**: 企业基本信息、经营状况、人员规模
- **入驻管理**: 企业入驻流程、合同管理、费用结算
- **变更追踪**: 企业信息变更记录、搬迁管理
- **需求服务**: 企业服务需求收集与处理

### 📊 经济数据分析
- **经济驾驶舱**: 实时经济指标展示、趋势分析
- **税收监控**: 税收数据统计、异常预警
- **行业分析**: 产业结构分析、行业发展趋势
- **报表系统**: 自定义报表生成、数据导出

### ⚠️ 智能预警系统
- **预警规则配置**: 自定义预警指标和阈值
- **实时监控**: 关键指标异常自动预警
- **预警处理**: 预警事件跟踪和处理流程

### 📱 移动工作平台
- **移动端接口**: 支持移动设备访问
- **工作台功能**: 待办事项、消息通知
- **数据查询**: 随时随地查看关键数据

## 🛠️ 技术架构

### 后端技术栈
- **核心框架**: Spring Boot 3.4.3, Spring Security
- **数据访问**: MyBatis Plus, Druid连接池
- **数据库**: PostgreSQL (支持多数据源)
- **缓存**: Redis + JetCache (本地+远程双级缓存)
- **认证授权**: Sa-Token
- **API文档**: SpringDoc + Knife4j
- **工作流引擎**: Flowable
- **数据加密**: Jasypt

### 系统要求
- **JDK**: 21+
- **Maven**: 3.6+
- **PostgreSQL**: 12+
- **Redis**: 5.0+

## 🚀 快速开始

### 1. 环境准备

确保您的开发环境已安装以下软件：
- JDK 21
- Maven 3.6+
- PostgreSQL 12+
- Redis 5.0+
- IDE (推荐IDEA)

### 2. 克隆项目

```bash
# 从内部代码仓库获取项目代码
cd zjwn-building-economy
```

### 3. 数据库配置

#### 创建数据库
```sql
CREATE DATABASE building_economy_dev ENCODING 'UTF8';
```

#### 配置数据库连接
编辑 `zjwn-web-economy/src/main/resources/application-dev.yml`:

```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: *****************************************************
          username: your_username
          password: your_password
          driver-class-name: org.postgresql.Driver
```

### 4. Redis配置

配置Redis连接信息：
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: your_redis_password
      database: 0
```

### 5. 编译和运行

#### 编译项目
```bash
mvn clean compile
```

#### 运行应用
```bash
# 方式1: 使用Maven插件运行
cd zjwn-web-economy
mvn spring-boot:run

# 方式2: 打包后运行
mvn clean package
java -jar zjwn-web-economy/target/building_economy.jar
```

### 6. 访问应用

应用启动成功后，可通过以下地址访问：

- **应用首页**: http://localhost:7009/building/
- **API文档**: http://localhost:7009/building/doc.html
- **Swagger UI**: http://localhost:7009/building/swagger-ui.html

## 📁 项目结构

```
zjwn-building-economy/
├── zjwn-web-base/              # 基础模块
│   ├── src/main/java/
│   │   └── com/zjhh/
│   │       ├── db/             # 数据库配置
│   │       ├── system/         # 系统工具
│   │       ├── user/           # 用户认证
│   │       └── web/            # Web基础配置
│   └── src/main/resources/
├── zjwn-web-economy/           # 主业务模块
│   ├── src/main/java/
│   │   └── com/zjhh/economy/
│   │       ├── controller/     # REST API控制器
│   │       ├── service/        # 业务逻辑层
│   │       ├── dao/           # 数据访问层
│   │       │   ├── entity/    # 实体类
│   │       │   └── mapper/    # MyBatis映射
│   │       ├── request/       # 请求DTO
│   │       ├── vo/           # 响应VO
│   │       ├── enume/        # 枚举类
│   │       └── utils/        # 工具类
│   └── src/main/resources/
│       ├── mapper/           # MyBatis XML映射
│       └── application*.yml  # 配置文件
└── README.md
```

## 🔧 开发指南

### 代码规范
- 使用Lombok简化代码
- 遵循阿里巴巴Java开发手册
- API接口按业务模块分包管理
- 数据库表采用下划线命名，实体类采用驼峰命名

### 开发流程
1. 创建功能分支进行开发
2. 开发新功能并提交代码
3. 确保代码格式符合规范
4. 合并到主分支前进行代码评审

### API开发
- 使用SpringDoc注解编写API文档
- 统一返回结果格式
- 实现统一异常处理
- 添加请求参数校验

## 🚀 部署说明

### 环境配置
项目支持多环境配置：
- **开发环境**: application-dev.yml
- **测试环境**: application-test.yml  
- **生产环境**: application-prod.yml

### 打包部署
```bash
# 生产环境打包
mvn clean package

# 部署到服务器
java -jar zjwn-web-economy/target/building_economy.jar --spring.profiles.active=prod
```

## 📊 系统监控

系统集成了以下监控功能：
- **应用监控**: Spring Boot Actuator
- **数据库监控**: Druid监控页面
- **缓存监控**: Redis监控
- **API监控**: 接口调用统计

## 🔧 开发规范

1. 代码提交前必须进行本地测试
2. 遵循项目既定的代码规范和命名约定
3. 重要功能变更需要更新相关文档
4. 数据库变更需要提供迁移脚本
5. 新增API需要更新接口文档

## 📝 版本记录

### v1.0.0 (当前版本)
- ✨ 基础功能完成
- 🏢 楼宇管理模块
- 🏢 企业管理模块
- 📊 数据分析模块
- ⚠️ 预警监控模块

