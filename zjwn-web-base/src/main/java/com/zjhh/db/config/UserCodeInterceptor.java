package com.zjhh.db.config;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.zjhh.user.vo.LoginVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Connection;
import java.sql.PreparedStatement;

/**
 * 用户编码拦截器，用于设置数据库会话的用户编码
 *
 * <AUTHOR>
 * @since 2025/3/24 上午11:32
 */
@Slf4j
public class UserCodeInterceptor implements InnerInterceptor {

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) {
        // 获取SQL语句
        String sql = boundSql.getSql();

        // 检查SQL是否包含v_p_building（不区分大小写）
        if (!sql.toLowerCase().contains("v_p_building")) {
            return;
        }

        try {
            if (!StpUtil.isLogin()) {
                return;
            }
        } catch (Exception e) {
            return;
        }

        // 获取当前用户编码
        String userCode = ((LoginVo) StpUtil.getTokenSession().get("userSession")).getCode();
        if (StrUtil.isNotBlank(userCode)) {
            // 设置当前用户编码
            try {
                Configuration configuration = ms.getConfiguration();
                try (Connection connection = configuration.getEnvironment().getDataSource().getConnection()) {
                    // 使用SET 设置会话变量，值需要用单引号包裹
                    String setSql = String.format("SET app.current_user_code = '%s'", userCode);
                    try (PreparedStatement stmt = connection.prepareStatement(setSql)) {
                        stmt.execute();
                        log.debug("Set current user code to: {}", userCode);
                    }
                }
            } catch (Exception e) {
                log.error("Failed to set current user code: {}", userCode, e);
            }
        }
    }
}
