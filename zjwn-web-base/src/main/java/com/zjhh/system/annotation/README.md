# SaFieldPermission 字段级权限控制注解

## 简介

`SaFieldPermission` 是一个字段级权限控制注解，可以注解在实体类的字段上。如果用户没有对应权限，该字段在序列化为JSON时将返回为null。

## 特点

- 基于Sa-Token权限验证
- 支持单个权限校验
- 支持多权限"或"关系校验（满足任意一个即可）
- 支持多权限"与"关系校验（必须全部满足）
- 与Jackson序列化无缝集成

## 使用方法

### 1. 在实体类字段上添加注解

```java
@Data
public class UserInfo {
    // 普通字段，无权限限制
    private Long id;
    private String name;
    
    // 单权限控制字段，需要 phone:view 权限
    @SaFieldPermission("phone:view")
    private String phone;
    
    // 多权限"与"关系控制字段，需要同时拥有 salary:view 和 hr:view 权限
    @SaFieldPermission(andPermissions = {"salary:view", "hr:view"})
    private Double salary;
    
    // 多权限"或"关系控制字段，拥有 email:view 或 admin:view 任一权限即可
    @SaFieldPermission(orPermissions = {"email:view", "admin:view"})
    private String email;
}
```

### 2. 权限检测逻辑

- 如果用户未登录，则无权限访问任何标注了 `@SaFieldPermission` 的字段
- 如果设置了 `value`，则检查用户是否拥有该权限
- 如果设置了 `orPermissions`，则检查用户是否拥有其中任意一个权限
- 如果设置了 `andPermissions`，则检查用户是否拥有所有权限

## 实现原理

该注解通过自定义Jackson序列化器实现，在对象序列化为JSON时，会根据当前用户的权限决定字段是否序列化为null。
