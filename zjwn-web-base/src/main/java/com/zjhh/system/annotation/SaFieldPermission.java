package com.zjhh.system.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zjhh.system.serializer.SaFieldPermissionSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段级权限控制注解
 * 可注解在实体类的字段上，如果用户没有对应权限，该字段将返回空值
 *
 * <AUTHOR>
 * @since 2025/4/18
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@JacksonAnnotationsInside
@JsonSerialize(using = SaFieldPermissionSerializer.class)
public @interface SaFieldPermission {
    
    /**
     * 需要校验的权限码
     * @return 权限码
     */
    String value() default "";
    
    /**
     * 多个权限码，只要有一个通过即可
     * @return 权限码数组
     */
    String[] orPermissions() default {};
    
    /**
     * 多个权限码，必须全部通过
     * @return 权限码数组
     */
    String[] andPermissions() default {};
} 