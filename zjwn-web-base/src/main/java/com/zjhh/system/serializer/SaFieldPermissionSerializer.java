package com.zjhh.system.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.zjhh.system.annotation.SaFieldPermission;
import com.zjhh.system.util.SaFieldPermissionUtil;

import java.io.IOException;
import java.lang.reflect.Field;

/**
 * 字段权限序列化器
 * 用于处理对象序列化过程中的字段权限检查
 *
 * <AUTHOR>
 * @since 2025/4/18
 */
public class SaFieldPermissionSerializer extends JsonSerializer<Object> implements ContextualSerializer {

    private final Field field;

    public SaFieldPermissionSerializer() {
        this.field = null;
    }

    public SaFieldPermissionSerializer(Field field) {
        this.field = field;
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 如果字段为空或有权限，正常序列化
        if (field == null || SaFieldPermissionUtil.hasPermission(field)) {
            // 使用底层序列化方法避免再次调用此序列化器
            if (value == null) {
                gen.writeNull();
            } else {
                // 获取对象的默认序列化器，避免递归
                JsonSerializer<Object> defaultSerializer = serializers.findValueSerializer(value.getClass());
                // 只有当默认序列化器不是当前序列化器时才使用它
                if (defaultSerializer != this) {
                    defaultSerializer.serialize(value, gen, serializers);
                } else {
                    // 最后的保险措施，直接使用默认方式序列化对象
                    // JsonGenerator没有writePOJO方法，使用writeObject但要防止递归
                    serializers.defaultSerializeValue(value, gen);
                }
            }
        } else {
            // 没有权限则序列化为null
            gen.writeNull();
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        if (property == null) {
            return this;
        }

        try {
            // 获取字段
            Field field = property.getMember().getDeclaringClass().getDeclaredField(property.getName());
            
            // 检查字段是否有权限注解
            if (field.isAnnotationPresent(SaFieldPermission.class)) {
                return new SaFieldPermissionSerializer(field);
            }
        } catch (NoSuchFieldException e) {
            // 忽略异常，返回默认序列化器
        }

        return prov.findValueSerializer(property.getType(), property);
    }
} 