package com.zjhh.system.util;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.zjhh.system.annotation.SaFieldPermission;

import java.lang.reflect.Field;

/**
 * 字段权限工具类
 *
 * <AUTHOR>
 * @since 2025/4/18
 */
public class SaFieldPermissionUtil {

    /**
     * 检查用户是否拥有字段上标注的权限
     *
     * @param field 字段
     * @return true: 有权限; false: 无权限
     */
    public static boolean hasPermission(Field field) {
        // 检查字段是否有权限注解
        if (!field.isAnnotationPresent(SaFieldPermission.class)) {
            return true;
        }

        // 如果用户未登录，无权限
        if (!StpUtil.isLogin()) {
            return false;
        }

        SaFieldPermission annotation = field.getAnnotation(SaFieldPermission.class);
        
        // 单个权限检查
        String permissionCode = annotation.value();
        if (StrUtil.isNotBlank(permissionCode)) {
            return StpUtil.hasPermission(permissionCode);
        }
        
        // 或关系权限检查（有一个通过即可）
        String[] orPermissions = annotation.orPermissions();
        if (ArrayUtil.isNotEmpty(orPermissions)) {
            for (String permission : orPermissions) {
                if (StrUtil.isNotBlank(permission) && StpUtil.hasPermission(permission)) {
                    return true;
                }
            }
            // 所有权限都未通过
            return false;
        }
        
        // 与关系权限检查（必须全部通过）
        String[] andPermissions = annotation.andPermissions();
        if (ArrayUtil.isNotEmpty(andPermissions)) {
            for (String permission : andPermissions) {
                if (StrUtil.isBlank(permission) || !StpUtil.hasPermission(permission)) {
                    return false;
                }
            }
            // 全部权限都通过
            return true;
        }
        
        // 默认通过（未配置任何权限检查）
        return true;
    }
} 