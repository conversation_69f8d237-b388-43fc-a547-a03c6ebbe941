package com.zjhh.web.controller.login;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.zjhh.comm.response.ReData;
import com.zjhh.system.annotation.ApiLog;
import com.zjhh.system.enume.LogApiTypeEnum;
import com.zjhh.user.request.LoginReq;
import com.zjhh.user.request.QrCodeLoginReq;
import com.zjhh.user.service.LoginService;
import com.zjhh.user.vo.CaptchaVo;
import com.zjhh.user.vo.InitConfigVo;
import com.zjhh.user.vo.LoginVo;
import com.zjhh.user.vo.ZwddLoginVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2020/6/11 17:08
 */
@Tag(name = "登录")
@RestController
public class LoginController extends BaseController {

    @Resource
    private LoginService loginService;

    @Operation(summary = "登录")
    @ApiLog(value = "登录", type = LogApiTypeEnum.LOGIN)
    @PostMapping("login")
    public ReData<LoginVo> login(@RequestBody @Validated LoginReq req) {
        LoginVo loginVo = loginService.login(req);
        StpUtil.getTokenSession().set("LoginType", "LOGIN_TYPE:PC");
        return ReData.success(loginVo);
    }

    @Operation(summary = "扫码登录")
    @PostMapping("qr_code/login")
    public ReData<ZwddLoginVo> qrCodelogin(@RequestBody @Validated QrCodeLoginReq req) {
        return ReData.success(loginService.getEmployeeCodeByQrCode(req.getCode()));
    }

    @Operation(summary = "免登录")
    @PostMapping("auth_code/login")
    public ReData<ZwddLoginVo> authCodeLogin(@RequestBody @Validated QrCodeLoginReq req) {
        return ReData.success(loginService.getEmployeeCodeByAuthCode(req.getCode()));
    }

    @SaCheckLogin
    @Operation(summary = "退出登录")
    @ApiLog(value = "退出登录", type = LogApiTypeEnum.LOGOUT)
    @PostMapping("logout")
    public ReData<String> logout() {
        loginService.logout();
        return ReData.success("退出登录成功！");
    }

    @Operation(summary = "获取图形验证码")
    @PostMapping("get/captcha")
    public ReData<CaptchaVo> createCaptcha() {
        return ReData.success(loginService.createCaptcha());
    }

    @Operation(summary = "获取初始化配置")
    @RequestMapping("get/init/config")
    public ReData<InitConfigExtendVo> getInitConfig() {
        InitConfigVo initConfig = loginService.getInitConfig();
        InitConfigExtendVo vo = new InitConfigExtendVo();
        // 复制父类属性
        BeanUtil.copyProperties(initConfig, vo);
        // 设置扩展属性
        vo.setOnlyOfficeUrl("/onlyoffice");
        return ReData.success(vo);
    }

    @Operation(summary = "获取在线人数")
    @PostMapping("count/online/user")
    @SaCheckLogin
    public ReData<Integer> countOnlineUser() {
        return ReData.success(loginService.countOnlineUser());
    }

    @Operation(summary = "移动端登录")
    @ApiLog(value = "移动端登录", type = LogApiTypeEnum.LOGIN)
    @PostMapping("mobile/login")
    public ReData<LoginVo> mobileLogin(@RequestBody @Validated LoginReq req) {
        LoginVo loginVo = loginService.login(req);
        StpUtil.getTokenSession().set("LoginType", "LOGIN_TYPE:MOBILE");
        return ReData.success(loginVo);
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class InitConfigExtendVo extends InitConfigVo {

        @Serial
        private static final long serialVersionUID = 7765187934604917859L;

        private String onlyOfficeUrl;
    }
}
