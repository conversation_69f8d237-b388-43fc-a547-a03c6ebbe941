# 楼宇经济管理系统常用命令

## Maven构建命令

### 基础编译打包
```bash
# 清理并编译整个项目
mvn clean compile

# 清理并打包整个项目（跳过测试）
mvn clean package

# 安装到本地Maven仓库
mvn clean install

# 只编译不打包
mvn compile
```

### 模块化构建
```bash
# 只编译基础模块
mvn clean compile -pl zjwn-web-base

# 只编译业务模块
mvn clean compile -pl zjwn-web-economy

# 编译多个模块
mvn clean compile -pl zjwn-web-base,zjwn-web-economy

# 构建特定模块及其依赖
mvn clean package -pl zjwn-web-economy -am
```

### 依赖管理
```bash
# 查看依赖树
mvn dependency:tree

# 分析依赖冲突
mvn dependency:analyze

# 解析依赖
mvn dependency:resolve

# 下载源码
mvn dependency:sources
```

## 应用运行命令

### Spring Boot运行
```bash
# 进入业务模块目录
cd zjwn-web-economy

# 使用Maven插件运行
mvn spring-boot:run

# 指定配置文件运行
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 指定JVM参数运行
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xmx1024m"
```

### JAR包运行
```bash
# 运行打包后的JAR文件
java -jar zjwn-web-economy/target/building_economy.jar

# 指定环境运行
java -jar zjwn-web-economy/target/building_economy.jar --spring.profiles.active=prod

# 指定JVM参数运行
java -Xmx2g -Xms1g -jar zjwn-web-economy/target/building_economy.jar

# 后台运行
nohup java -jar zjwn-web-economy/target/building_economy.jar > app.log 2>&1 &
```

### 调试模式
```bash
# 开启远程调试
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar building_economy.jar

# Maven调试运行
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"
```

## Git操作命令

### 分支操作
```bash
# 查看所有分支
git branch -a

# 切换到开发分支
git checkout springboot3

# 切换到主分支
git checkout main

# 创建新分支
git checkout -b feature/new-feature

# 合并分支
git merge springboot3
```

### 代码管理
```bash
# 查看状态
git status

# 添加修改文件
git add .

# 提交修改
git commit -m "feat: 添加新功能"

# 推送到远程
git push origin springboot3

# 拉取最新代码
git pull origin springboot3
```

## 数据库相关命令

### MyBatis代码生成
```bash
# 使用MyBatis Plus代码生成器（需要配置）
mvn mybatis-plus:generator

# 生成特定表的代码
mvn mybatis-plus:generator -Dtables=building_info,enterprise_data
```

### 数据库连接检查
```bash
# 检查数据库连接（需要配置数据库连接信息）
mvn sql:execute -Dsql="SELECT 1"

# 执行SQL脚本
mvn sql:execute -Dsrcfiles=scripts/init.sql
```

## 测试相关命令

### 单元测试
```bash
# 运行所有测试（项目默认跳过测试）
mvn test -DskipTests=false

# 运行特定测试类
mvn test -Dtest=BuildingServiceTest

# 运行特定测试方法
mvn test -Dtest=BuildingServiceTest#testCreateBuilding
```

### 集成测试
```bash
# 运行集成测试
mvn verify -DskipITs=false

# 运行所有测试和集成测试
mvn clean verify -DskipTests=false -DskipITs=false
```

## 性能分析命令

### 应用性能监控
```bash
# 使用JConsole监控
jconsole

# 生成堆转储
jmap -dump:format=b,file=heap.hprof <pid>

# 查看GC情况
jstat -gc <pid> 5s

# 查看线程堆栈
jstack <pid>
```

### 应用健康检查
```bash
# 检查应用是否启动
curl -f http://localhost:8080/building/actuator/health

# 检查应用信息
curl http://localhost:8080/building/actuator/info

# 检查指标
curl http://localhost:8080/building/actuator/metrics
```

## Docker部署命令

### Docker构建
```bash
# 构建Docker镜像
docker build -t zjwn-building-economy:1.0.0 .

# 运行Docker容器
docker run -d -p 8080:8080 --name building-economy zjwn-building-economy:1.0.0

# 查看容器日志
docker logs -f building-economy

# 进入容器
docker exec -it building-economy /bin/bash
```

### Docker Compose
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f building-economy
```

## 日志查看命令

### 应用日志
```bash
# 查看应用日志
tail -f log/application.log

# 查看错误日志
tail -f log/error.log

# 查看特定时间段日志
grep "2024-01-01" log/application.log

# 查看特定级别日志
grep "ERROR" log/application.log
```

### 系统监控
```bash
# 查看系统资源使用
top

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看网络连接
netstat -tlnp
```

## 开发环境命令

### IDE相关
```bash
# 生成IntelliJ IDEA项目文件
mvn idea:idea

# 生成Eclipse项目文件
mvn eclipse:eclipse

# 清理IDE生成文件
mvn idea:clean
mvn eclipse:clean
```

### 代码质量检查
```bash
# 使用SpotBugs检查代码
mvn spotbugs:check

# 使用PMD检查代码
mvn pmd:check

# 使用Checkstyle检查代码风格
mvn checkstyle:check
```

## 常用组合命令

### 完整构建部署流程
```bash
# 清理 -> 编译 -> 打包 -> 运行
mvn clean compile package && java -jar zjwn-web-economy/target/building_economy.jar
```

### 快速重新部署
```bash
# 停止应用 -> 重新打包 -> 启动应用
pkill -f building_economy && mvn clean package -pl zjwn-web-economy && java -jar zjwn-web-economy/target/building_economy.jar &
```

### 开发模式快速启动
```bash
# 进入业务模块并启动
cd zjwn-web-economy && mvn spring-boot:run -Dspring-boot.run.profiles=dev
```