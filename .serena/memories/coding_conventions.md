# 楼宇经济管理系统编码规范

## 包命名规范

### 基础模块 (zjwn-web-base)
```
com.zjhh.web.*          # Web层相关
com.zjhh.user.*         # 用户相关
com.zjhh.system.*       # 系统工具相关  
com.zjhh.db.*           # 数据库相关
```

### 业务模块 (zjwn-web-economy)
```
com.zjhh.economy.controller.*    # REST API控制器
com.zjhh.economy.service.*       # 业务逻辑层
com.zjhh.economy.dao.entity.*    # 实体类
com.zjhh.economy.dao.mapper.*    # MyBatis映射接口
com.zjhh.economy.request.*       # 请求DTO
com.zjhh.economy.vo.*           # 响应VO
com.zjhh.economy.dto.*          # 数据传输对象
com.zjhh.economy.converter.*    # 对象转换器
com.zjhh.economy.constants.*    # 常量定义
com.zjhh.economy.enume.*        # 枚举类
com.zjhh.economy.utils.*        # 工具类
com.zjhh.economy.task.*         # 定时任务
```

## 类命名规范

### Controller层
- 命名格式: `{业务模块}Controller`
- 使用注解: `@RestController`, `@RequestMapping`
- 示例: `BuildingController`, `EnterpriseController`

### Service层
- 接口命名: `I{业务模块}Service`
- 实现类命名: `{业务模块}ServiceImpl`
- 使用注解: `@Service`
- 示例: `IBuildingService`, `BuildingServiceImpl`

### Dao层
- Mapper接口: `{实体名}Mapper`
- 实体类: `{业务名}Entity` 或 `{业务名}`
- 使用注解: `@Mapper`
- 示例: `BuildingMapper`, `BuildingEntity`

### DTO/VO命名
- 请求对象: `{业务}Request`, `{业务}Query`
- 响应对象: `{业务}VO`, `{业务}Response`
- 数据传输: `{业务}DTO`

## 注解使用规范

### Spring Boot注解
```java
@SpringBootApplication           # 启动类
@ComponentScan("com.zjhh")      # 组件扫描，固定包名
```

### Web层注解
```java
@RestController                 # REST控制器
@RequestMapping("/api/path")    # 路径映射
@GetMapping, @PostMapping       # HTTP方法映射
@Valid                         # 参数校验
```

### 业务层注解
```java
@Service                       # 业务服务
@Transactional                 # 事务管理
@Cacheable, @CacheEvict        # 缓存注解
```

### 数据层注解
```java
@Mapper                        # MyBatis映射接口
@TableName("table_name")       # 表名映射
@TableId                       # 主键标识
@TableField                    # 字段映射
```

### 权限注解
```java
@SaFieldPermission            # 字段权限控制
@SaCheckLogin                 # 登录检查
@SaCheckPermission            # 权限检查
```

## 数据库规范

### 表命名
- 全小写，下划线分隔
- 示例: `building_info`, `enterprise_data`

### 字段命名
- 全小写，下划线分隔
- 主键: `id`
- 创建时间: `create_time`
- 更新时间: `update_time`
- 创建人: `create_user`
- 更新人: `update_user`

### 实体类映射
```java
// 数据库表字段: building_name
// 实体类属性: buildingName
@TableField("building_name")
private String buildingName;
```

## MyBatis使用规范

### XML映射文件
- 位置: `src/main/resources/mapper/`
- 命名: `{实体名}Mapper.xml`
- namespace: 对应Mapper接口全路径

### SQL编写规范
```xml
<!-- 查询字段明确列出，避免使用 * -->
<select id="selectById" resultType="BuildingEntity">
    SELECT id, building_name, create_time, update_time
    FROM building_info 
    WHERE id = #{id}
</select>

<!-- 使用动态SQL -->
<select id="selectByCondition" resultType="BuildingEntity">
    SELECT * FROM building_info
    <where>
        <if test="name != null and name != ''">
            AND building_name LIKE CONCAT('%', #{name}, '%')
        </if>
    </where>
</select>
```

## 配置文件规范

### application.yml结构
```yaml
# 服务器配置
server:
  servlet:
    context-path: /building

# Spring配置  
spring:
  profiles:
    active: dev
  # 数据源配置
  datasource:
  # 缓存配置
  data:
    redis:

# 第三方组件配置
sa-token:
mybatis-plus:
springdoc:
knife4j:
jetcache:
flowable:
jasypt:
```

### 环境分离
- `application.yml`: 公共配置
- `application-dev.yml`: 开发环境
- `application-test.yml`: 测试环境  
- `application-prod.yml`: 生产环境

## 代码风格规范

### 导入规范
```java
// 标准库
import java.util.*;

// Spring框架
import org.springframework.stereotype.Service;

// 第三方库
import lombok.extern.slf4j.Slf4j;

// 项目内部
import com.zjhh.economy.dao.entity.BuildingEntity;
```

### 异常处理
```java
@RestController
public class BuildingController {
    
    @GetMapping("/building/{id}")
    public Result<BuildingVO> getBuilding(@PathVariable Long id) {
        try {
            BuildingVO building = buildingService.getById(id);
            return Result.success(building);
        } catch (Exception e) {
            log.error("查询楼宇信息失败", e);
            return Result.fail("查询失败");
        }
    }
}
```

### 日志使用
```java
@Slf4j
@Service
public class BuildingServiceImpl implements IBuildingService {
    
    public BuildingVO getById(Long id) {
        log.info("查询楼宇信息，id: {}", id);
        // 业务逻辑
        log.debug("查询结果: {}", result);
        return result;
    }
}
```

## Maven配置规范

### 版本管理
```xml
<properties>
    <java.version>21</java.version>
    <zjwn.version>1.0.0</zjwn.version>
    <zjwn.base.version>springboot3-1.2.11</zjwn.base.version>
</properties>
```

### 插件配置
- 编译插件: JDK 21
- 测试插件: 跳过测试
- 资源插件: 支持字体文件过滤

### 模块依赖
- 父项目管理公共依赖
- 子模块继承并添加特定依赖
- 使用dependencyManagement统一版本

## 其他规范说明

### Git提交规范
Git提交规范已配置在全局Claude Code配置中，所有项目统一遵循。

### 代码质量
- 使用Lombok减少样板代码
- 遵循阿里巴巴Java开发手册
- 定期进行代码审查
- 保持代码整洁和可读性