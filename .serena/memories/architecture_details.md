# 楼宇经济管理系统架构详细说明

## 系统架构概览

### Maven多模块架构
```
zjwn-building-economy (父项目)
├── zjwn-web-base (基础模块)      # 通用配置、认证、数据库配置等
└── zjwn-web-economy (主业务模块)  # 楼宇经济相关业务逻辑
```

## 技术栈详细信息

### 核心框架
- **Spring Boot**: 3.4.3
- **Java版本**: JDK 21
- **Maven**: 多模块项目管理
- **内部基础平台**: springboot3-1.2.11

### 数据层架构
- **ORM框架**: MyBatis Plus
  - XML映射文件位置: `classpath*:/mapper/*Mapper.xml`
  - 主键策略: ASSIGN_ID (雪花算法)
  - 表命名: 下划线命名规范
  - 实体类: 驼峰命名规范
- **连接池**: Druid
  - 初始连接数: 5
  - 最大连接数: 20
  - 支持监控统计
- **数据库**: 支持多数据源动态切换
- **类型处理**: 自定义TypeHandler (LocalDateTypeHandler)

### 缓存架构
- **分布式缓存**: Redis (数据库0)
- **本地缓存**: JetCache + Caffeine
  - 支持二级缓存架构
  - Key转换: fastjson
  - Value编解码: kryo

### 安全认证架构
- **认证框架**: Sa-Token
  - Token名称: token
  - Token风格: simple-uuid
  - 支持多地同时登录
  - 每次登录新建token
- **权限控制**: 基于注解的字段权限控制
  - `@SaFieldPermission` 注解
  - 字段级别权限序列化

### API文档架构
- **文档生成**: SpringDoc + OpenAPI 3
- **UI增强**: Knife4j (中文界面)
  - 访问路径: `/building/doc.html`
  - Swagger UI: `/building/swagger-ui.html`
  - 支持接口排序和分组

### 工作流架构
- **工作流引擎**: Flowable
  - 支持流程定义
  - 历史记录完整保存
  - 中文字体支持

### 文档处理架构
- **Office集成**: OnlyOffice SDK (1.6.0)
- **文档编辑**: 支持在线文档编辑
- **文档模板**: Word模板系统

### 配置加密
- **加密工具**: Jasypt
  - 算法: PBEWithMD5AndDES
  - 支持配置文件敏感信息加密

## 包结构详细说明

### zjwn-web-base模块
```
com.zjhh
├── web/                    # Web层基础配置
│   ├── controller/login/   # 登录控制器
│   └── db/typehandler/     # 数据类型处理器
├── user/config/            # 用户权限配置
├── system/                 # 系统工具
│   ├── annotation/         # 系统注解
│   ├── serializer/         # 序列化器
│   └── util/              # 工具类
└── db/config/             # 数据库配置
    ├── MyBatisPlusConfiguration # MyBatis Plus配置
    └── UserCodeInterceptor     # 用户代码拦截器
```

### zjwn-web-economy模块
```
com.zjhh.economy/
├── controller/             # REST API控制器
├── service/               # 业务逻辑层
├── dao/                   # 数据访问层
│   ├── entity/            # 实体类
│   └── mapper/            # MyBatis映射接口
├── request/               # 请求DTO
├── vo/                    # 响应VO
├── dto/                   # 数据传输对象
├── converter/             # 对象转换器
├── constants/             # 常量定义
├── enume/                 # 枚举类
├── utils/                 # 工具类
├── task/                  # 定时任务
├── onlyoffice/            # OnlyOffice集成
└── ueditor/               # UEditor集成
```

## 部署架构

### 应用配置
- **应用上下文**: `/building`
- **静态资源**: `classpath:/static/`, `classpath:/public/`
- **文件上传**: 最大100MB
- **字符编码**: UTF-8

### 环境配置
- **开发环境**: application-dev.yml
- **测试环境**: application-test.yml
- **生产环境**: application-prod.yml
- **默认激活**: dev环境

### 构建配置
- **编译器**: Maven Compiler Plugin (JDK 21)
- **测试**: 默认跳过测试
- **资源过滤**: 支持字体文件和Office文档
- **资源包含**: XML、JSON、FTL模板文件

## 监控与运维
- **Druid监控**: 支持SQL监控统计
- **JetCache统计**: 缓存性能监控
- **Flowable监控**: 工作流执行监控
- **Sa-Token日志**: 操作日志记录