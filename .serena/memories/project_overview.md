# 楼宇经济管理系统项目概览

## 项目基本信息

**项目名称**: 楼宇经济管理系统 (zjwn-building-economy)
**项目描述**: 基于Spring Boot 3的楼宇经济管理系统，用于楼宇园区的企业管理、空间资源管理、税收监控和数据分析
**技术栈**: Java 21 + Spring Boot 3.4.3 + MyBatis Plus + Redis + Sa-Token
**项目版本**: 1.0.0

## 业务功能模块

### 1. 楼宇管理
- **楼宇档案管理**: 楼宇基本信息维护
- **楼层管理**: 楼层信息及配置
- **房间管理**: 房间资源分配和状态管理
- **空间资源管理**: 楼宇空间利用率统计

### 2. 企业管理  
- **企业档案**: 入驻企业基本信息管理
- **企业入驻**: 企业入驻流程和审批
- **企业迁出**: 企业迁出管理和结算
- **企业状态跟踪**: 企业经营状态监控

### 3. 数据分析与监控
- **经济驾驶舱**: 楼宇经济数据可视化展示
- **分析报告**: 各类业务数据分析报告
- **税收监控**: 企业税收数据统计和预警
- **数据统计**: 多维度数据统计分析

### 4. 预警系统
- **预警规则配置**: 各类业务预警规则设置
- **预警监控**: 实时监控和预警通知
- **预警处理**: 预警事件处理流程

### 5. 政策管理
- **政策维护**: 相关政策法规管理
- **政策兑现**: 政策落实和执行跟踪
- **政策效果评估**: 政策实施效果分析

### 6. 工作平台
- **移动端接口**: 移动应用API支持
- **工作台功能**: 个性化工作台
- **消息通知**: 系统消息和通知管理

## 技术特性

### 核心框架
- **Spring Boot 3.4.3**: 现代化的Spring框架
- **Java 21**: 最新LTS版本的Java
- **Maven多模块**: 清晰的模块化架构

### 数据处理
- **MyBatis Plus**: 强大的ORM框架，支持代码生成
- **Druid**: 高性能数据库连接池，支持监控
- **多数据源**: 支持动态数据源切换

### 缓存系统
- **Redis**: 分布式缓存
- **JetCache**: 二级缓存，本地+远程
- **Caffeine**: 高性能本地缓存

### 安全认证
- **Sa-Token**: 轻量级认证授权框架
- **字段权限**: 细粒度的字段级权限控制
- **Jasypt**: 配置文件加密

### 文档与监控
- **SpringDoc**: OpenAPI 3文档生成
- **Knife4j**: 增强版Swagger UI
- **监控统计**: 多维度系统监控

### 工作流
- **Flowable**: 企业级工作流引擎
- **流程设计**: 可视化流程设计
- **流程监控**: 实时流程执行监控

### 文档处理
- **OnlyOffice**: 在线Office文档编辑
- **Word模板**: 支持Word文档模板处理
- **文档转换**: 多格式文档转换支持

## 系统特点

### 1. 模块化设计
- 清晰的模块边界
- 松耦合的系统架构
- 便于扩展和维护

### 2. 高性能
- 多级缓存架构
- 数据库连接池优化
- 异步处理机制

### 3. 高可用
- Redis集群支持
- 数据库主从配置
- 健康检查机制

### 4. 安全性
- 完整的认证授权体系
- 配置文件加密
- SQL注入防护

### 5. 可观测性
- 详细的日志记录
- 性能监控指标
- 业务数据统计

## 部署信息

### 应用配置
- **上下文路径**: `/building`
- **文档地址**: `/building/doc.html`
- **监控地址**: `/building/druid`

### 环境支持
- **开发环境**: application-dev.yml
- **测试环境**: application-test.yml  
- **生产环境**: application-prod.yml

### 构建信息
- **构建工具**: Maven 3.6+
- **JDK要求**: JDK 21
- **打包格式**: JAR包
- **启动脚本**: `java -jar building_economy.jar`

## 开发团队

### 技术要求
- 熟悉Spring Boot 3.x
- 了解MyBatis Plus
- 掌握Redis使用
- 熟悉前后端分离开发

### 开发工具
- **IDE**: IntelliJ IDEA (推荐)
- **构建**: Maven
- **版本控制**: Git
- **API测试**: Postman / Knife4j

## 项目状态

**当前版本**: 1.0.0
**开发状态**: 活跃开发中
**主要分支**: main (主分支), springboot3 (开发分支)
**最新更新**: Word模板系统深度重构与优化