<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zjwn.economy</groupId>
        <artifactId>zjwn-building-economy</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>zjwn-web-economy</artifactId>
    <name>zjwn-web-economy</name>
    <description>重大项目</description>
    <dependencies>

        <dependency>
            <groupId>com.zjwn.economy</groupId>
            <artifactId>zjwn-web-base</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-fileupload2-jakarta-servlet6</artifactId>
            <version>2.0.0-M2</version>
        </dependency>
        <dependency>
            <groupId>com.onlyoffice</groupId>
            <artifactId>docs-integration-sdk</artifactId>
        </dependency>
        
        <!-- Apache POI for Word document processing -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.4.1</version>
        </dependency>
        
        <!-- Commons IO - explicit version for POI 5.4.1 compatibility -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.18.0</version>
        </dependency>
        
        <!-- JUnit 5 test dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>building_economy</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.4.3</version>
                <configuration>
                    <mainClass>
                        com.zjhh.economy.BuildingEconomyApplication
                    </mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
