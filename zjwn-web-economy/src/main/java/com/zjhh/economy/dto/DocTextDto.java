package com.zjhh.economy.dto;

import com.aspose.words.ParagraphAlignment;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/5/18 11:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocTextDto extends DocDto {

    private static final long serialVersionUID = 8951844729783028357L;

    private String fontFamily;

    private Double fontSize;

    private String color;

    private String fontWeight;

    private String fontStyle;

    private String textDecoration;

    private String textAlign;

    private Double margin;

    private Double lineHeight;

    private String backgroundColor;

    public int getDocTextAlign() {
        switch (textAlign) {
            case "left":
                return ParagraphAlignment.LEFT;
            case "right":
                return ParagraphAlignment.RIGHT;
            case "center":
            default:
                return ParagraphAlignment.CENTER;
        }
    }

    public boolean isBold() {
        return "bold".equalsIgnoreCase(fontWeight);
    }

    public boolean isUnderline() {
        return "underline".equalsIgnoreCase(textDecoration);
    }

    public boolean isItalic() {
        return "oblique".equalsIgnoreCase(fontStyle);
    }
}
