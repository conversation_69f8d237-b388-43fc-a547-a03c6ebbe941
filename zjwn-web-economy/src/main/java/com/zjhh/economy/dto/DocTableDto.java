package com.zjhh.economy.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/18 11:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocTableDto extends DocDto {

    private static final long serialVersionUID = 4107800053368065172L;

    private Double lineHeight;

    private String overflow;

    private Double borderWidth;

    private String borderColor;

    private Boolean showTitle;

    /**
     * 线框类型
     */
    private String borderType;

    private List<DocTableColumnDto> columns;
}
