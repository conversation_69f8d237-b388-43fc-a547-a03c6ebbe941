package com.zjhh.economy.dto;

import com.aspose.words.DashStyle;
import com.aspose.words.HorizontalAlignment;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/5/18 11:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocLineDto extends DocDto {

    private static final long serialVersionUID = -7659122959445008671L;

    private Double width;

    private Double height;

    private String color;

    private String borderStyle;

    private String align;

    private Double margin;

    public int getDocLineAlign() {
        switch (align) {
            case "left":
                return HorizontalAlignment.LEFT;
            case "right":
                return HorizontalAlignment.RIGHT;
            case "center":
            default:
                return HorizontalAlignment.CENTER;
        }
    }

    public int getDocDashStyle() {
        switch (borderStyle) {
            case "dotted":
                return DashStyle.DOT;
            case "dashed":
                return DashStyle.DASH;
            case "solid":
            default:
                return DashStyle.SOLID;
        }
    }
}
