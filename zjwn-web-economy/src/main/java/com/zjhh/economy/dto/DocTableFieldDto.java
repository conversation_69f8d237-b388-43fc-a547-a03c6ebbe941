package com.zjhh.economy.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/5/23 11:19
 */
@Data
public class DocTableFieldDto implements Serializable {

    private static final long serialVersionUID = 2101392864931346111L;

    private String tableId;

    private String dataSetId;

    private List<String> fields;

    private List<DocTableColumnDto> columns;

    private List<Map<String, String>> values;
}
