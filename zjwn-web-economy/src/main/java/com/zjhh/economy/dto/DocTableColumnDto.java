package com.zjhh.economy.dto;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/18 11:43
 */
@Data
public class DocTableColumnDto implements Serializable {

    private static final long serialVersionUID = -877024818881771906L;

    private String field;

    private String title;

    private String key;

    private Boolean edit;

    private Boolean columnShow;

    private DocTableThDto th;

    private DocTableTdDto td;

    private Integer rowSpan;

    private Integer colSpan;

    private Integer level;

    /**
     * 1-行合并FiRST  2-行合并PREVIOUS 3-列合并FiRST 4-列合并PREVIOUS 5-NONE
     */
    private Integer cellMergeType;

    private List<DocTableColumnDto> children;

    private Double wordWidth;

    /**
     * 最大深度
     */
    private Integer maxDepth;

    /**
     * 最大广度
     */
    private Integer maxWeight;

    public Integer getMaxDepth() {
        if (hosNotChild()) {
            return 1;
        }
        int y = 1;
        for (DocTableColumnDto child : children) {
            if (child.getMaxDepth() > y) {
                y = child.getMaxDepth();
            }
        }
        return y + 1;
    }

    public Integer getMaxWeight() {
        if (hosNotChild()) {
            return 1;
        }
        int y = 0;
        for (DocTableColumnDto child : children) {
            y += child.getMaxWeight();
        }
        return y;
    }

    private boolean hosNotChild() {
        return CollUtil.isEmpty(children);
    }

    public DocTableColumnDto() {

    }

    public DocTableColumnDto(Integer rowSpan, Integer colSpan, String title) {
        this.title = title;
        this.rowSpan = rowSpan;
        this.colSpan = colSpan;
    }

    public DocTableColumnDto(Integer cellMergeType) {
        this.cellMergeType = cellMergeType;
    }

    public DocTableColumnDto(Integer cellMergeType, Double wordWidth) {
        this.cellMergeType = cellMergeType;
        this.wordWidth = wordWidth;
    }

}
