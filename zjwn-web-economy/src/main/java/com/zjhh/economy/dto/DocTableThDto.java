package com.zjhh.economy.dto;

import com.aspose.words.ParagraphAlignment;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/5/18 11:39
 */
@Data
public class DocTableThDto implements Serializable {

    private static final long serialVersionUID = 2917977332936886198L;

    private String fontFamily;

    private Double fontSize;

    private String color;

    private String fontWeight;

    private String fontStyle;

    private String textDecoration;

    private String textAlign;

    private Double width;

    private String backgroundColor;

    public boolean isBold() {
        return "bold".equalsIgnoreCase(fontWeight);
    }

    public boolean isUnderline() {
        return "underline".equalsIgnoreCase(textDecoration);
    }

    public boolean isItalic() {
        return "oblique".equalsIgnoreCase(fontStyle);
    }

    public int getDocTextAlign() {
        switch (textAlign) {
            case "left":
                return ParagraphAlignment.LEFT;
            case "right":
                return ParagraphAlignment.RIGHT;
            case "center":
            default:
                return ParagraphAlignment.CENTER;
        }
    }
}
