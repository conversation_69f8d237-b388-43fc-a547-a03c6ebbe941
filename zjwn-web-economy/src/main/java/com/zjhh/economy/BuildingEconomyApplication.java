package com.zjhh.economy;

import cn.dev33.satoken.SaManager;
import com.zjhh.comm.utils.AsposeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
@SpringBootApplication
@ComponentScan("com.zjhh")
public class BuildingEconomyApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(BuildingEconomyApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        AsposeUtil.init();
        log.info("\n----------------------------------------------------------\n\t" +
                "Application Zjhh-platform is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://localhost:" + port + path + "/\n\t" +
                "Swagger-ui: \thttp://localhost:" + port + path + "/doc.html\n\t" +
                "sa-token配置如下：" + SaManager.getConfig() +
                "----------------------------------------------------------");
    }

}
