package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryLabelsReq extends BaseReq {
    private static final long serialVersionUID = -3739850025885211496L;

    @Schema(description = "类型-楼宇标签-BuildingLabel，企业标签-EnterpriseLabel，房间标签-RoomLabel，迁出原因-MoveOut")
    private String type;

}
