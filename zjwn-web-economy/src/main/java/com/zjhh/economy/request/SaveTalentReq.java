package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 添加人才
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveTalentReq extends BaseReq {
    private static final long serialVersionUID = 7559336127581504081L;


    @Schema(description = "主键-更新的时候传")
    private String id;
    /**
     * 企业id
     */
    @Schema(description = "企业ID")
    private String enterpriseId;

    /**
     * 员工数
     */
    @Schema(description = "员工数")
    private Integer employeeNum;

    /**
     * 博士数量
     */
    @Schema(description = "博士数量")
    private Integer drNum;

    /**
     * 硕士数
     */
    @Schema(description = "硕士数量")
    private Integer masterNum;

    /**
     * 本科数
     */
    @Schema(description = "本科数")
    private Integer bachelorNum;

    /**
     * 专科数
     */
    @Schema(description = "专科数")
    private Integer juniorNumber;

    /**
     * 中专数
     */
    @Schema(description = "中专数")
    private Integer polytechnicNum;

    /**
     * 其他学历数
     */
    @Schema(description = "其他学历")
    private Integer otherNum;
}
