package com.zjhh.economy.request.analyzecockpit;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 分析驾驶舱通用Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeCockpitCommonReq extends BaseReq {
    private static final long serialVersionUID = 1854321687048270605L;

    @Schema(description = "1-全街道2-社区3-项目4-楼宇")
    private String type;

    @Schema(description = "code")
    private String code;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "移动端-查询时间")
    private String queryDate;

    @Schema(hidden = true)
    private String currentYear;

    @Schema(hidden = true)
    private String lastYear;

    @Schema(hidden = true)
    private String lastMonth;

    @Schema(hidden = true)
    private String currentDate;

    @Schema(hidden = true)
    private String currentYearMonth;

    @Schema(hidden = true)
    private String lastYearMonth;

    @Schema(hidden = true)
    private String taxCurrentYear;

    @Schema(hidden = true)
    private String taxLastYear;

    @Schema(hidden = true)
    private String taxLastMonth;

    @Schema(hidden = true)
    private String taxCurrentDate;


    @Schema(hidden = true)
    private String taxCurrentYearMonth;

    public String getTaxLastYearMonth() {
        Date currentDate = DateUtil.parse(taxCurrentDate,"yyyy-MM-dd");
        Date taxLastMonth = DateUtil.offset(currentDate, DateField.MONTH,-12);
        return DateUtil.format(taxLastMonth,"yyyyMM");
    }

    @Schema(hidden = true)
    private String taxLastYearMonth;


    public Boolean checkLastDayOfMonth() {
        DateTime dateTime = DateUtil.date();
        Boolean result = DateUtil.endOfMonth(dateTime).dayOfMonth() == dateTime.dayOfMonth();
        return result;
    }

    public String getCurrentYear() {
        return String.valueOf(DateUtil.year(new Date()));
    }

    public String getLastMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), -1);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return last;
    }

    public String getLastYear() {
        return String.valueOf(DateUtil.year(new Date()) - 1);
    }

    public String getCurrentDate() {
        String current = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN);
        return current;
    }

    public String getLastYearMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), -12);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.SIMPLE_MONTH_PATTERN);
        return last;
    }

    public String getCurrentYearMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), 0);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return last;
    }

    public String getTaxCurrentYear() {
        Date currentDate = DateUtil.parse(taxCurrentDate,"yyyy-MM-dd");
        return String.valueOf(DateUtil.year(currentDate));
    }

    public String getTaxLastYear() {
        Date currentDate = DateUtil.parse(taxCurrentDate,"yyyy-MM-dd");
        Date taxLastYear = DateUtil.offset(currentDate, DateField.YEAR,-1);
        return String.valueOf(DateUtil.year(taxLastYear));
    }

    public String getTaxLastMonth() {
        Date currentDate = DateUtil.parse(taxCurrentDate,"yyyy-MM-dd");
        Date taxLastMonth = DateUtil.offset(currentDate, DateField.MONTH,-1);
        return DateUtil.format(taxLastMonth,"yyyyMM");
    }

    public String getTaxCurrentYearMonth() {
        Date currentDate = DateUtil.parse(taxCurrentDate,"yyyy-MM-dd");
        Date taxLastMonth = DateUtil.offset(currentDate, DateField.MONTH,0);
        return DateUtil.format(taxLastMonth,"yyyyMM");
    }



    public void setTaxCurrentDate(String taxCurrentDate) {
        this.taxCurrentDate = taxCurrentDate.substring(0,4)  + "-" + taxCurrentDate.substring(4,6) + "-" + "01";

    }
}
