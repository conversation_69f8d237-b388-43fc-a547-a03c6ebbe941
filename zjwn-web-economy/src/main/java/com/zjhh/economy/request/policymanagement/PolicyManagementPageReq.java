package com.zjhh.economy.request.policymanagement;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyManagementPageReq extends PageReq {
    private static final long serialVersionUID = 3565351973319089822L;

    @Schema(description = "政策名称")
    private String policyName;

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "结束时间")
    private LocalDate endDate;



}
