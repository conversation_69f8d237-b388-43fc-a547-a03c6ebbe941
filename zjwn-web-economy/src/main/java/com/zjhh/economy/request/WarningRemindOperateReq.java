package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 预警提醒操作Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningRemindOperateReq extends BaseReq {
    private static final long serialVersionUID = 597267299127667102L;

    @Schema(description = "预警Ids")
    private List<String> ids;

    @Schema(description = "操作1-处理 -忽略")
    private Integer operateType;


}
