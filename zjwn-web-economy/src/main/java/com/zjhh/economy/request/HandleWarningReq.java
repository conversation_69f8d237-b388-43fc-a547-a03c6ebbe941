package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 处理预警
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HandleWarningReq extends BaseReq {
    private static final long serialVersionUID = -8554011436129111487L;


    @Schema(description = "处理方式1-已处理-2忽略")
    private Integer handleType;

    @Schema(description = "处理说明")
    private String handleDesc;

    @Schema(description = "预警IDs")
    private List<String> warningIds;


}
