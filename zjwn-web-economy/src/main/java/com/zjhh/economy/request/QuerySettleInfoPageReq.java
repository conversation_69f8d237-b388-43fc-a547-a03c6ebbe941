package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class QuerySettleInfoPageReq extends BaseReq {

    private static final long serialVersionUID = 8442152712805306774L;

    @Schema(description = "企业ID")
    private String enterpriseId;
}
