package com.zjhh.economy.request.analyzecockpit;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 空间税收趋势Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EconomicTaxTrendReq extends AnalyzeDateReq {
    private static final long serialVersionUID = -9027172341117984884L;

    @Schema(description = "1-税收收入 2-街道收入")
    private Integer incomeType;

    @Schema(hidden = true)
    private String queryYearMonth;

    @Schema(hidden = true)
    private String currentYear;

    @Schema(hidden = true)
    private String lastYear;

    @Schema(hidden = true)
    private String lastMonth;

    @Schema(hidden = true)
    private String currentDate;


    @Schema(hidden = true)
    private String currentYearMonth;

    @Schema(hidden = true)
    private String lastYearMonth;


    public Boolean checkLastDayOfMonth() {
        DateTime dateTime = DateUtil.date();
        Boolean result = DateUtil.endOfMonth(dateTime).dayOfMonth() == dateTime.dayOfMonth();
        return result;
    }

    public String getCurrentYear() {
        return String.valueOf(DateUtil.year(new Date()));
    }

    public String getLastMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), -1);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return last;
    }

    public String getLastYear() {
        return String.valueOf(DateUtil.year(new Date()) - 1);
    }

    public String getCurrentDate() {
        String current = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN);
        return current;
    }

    public String getLastYearMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), -12);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.SIMPLE_MONTH_PATTERN);
        return last;
    }

    public String getCurrentYearMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), 0);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return last;
    }

}
