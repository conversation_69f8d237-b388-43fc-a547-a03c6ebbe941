package com.zjhh.economy.request.analyzereport;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 企业分析对比导出
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntCompareExportReq extends BaseReq {
    private static final long serialVersionUID = 7821074934463262516L;

    @Schema(description = "企业ID")
    private List<String> ids;
}
