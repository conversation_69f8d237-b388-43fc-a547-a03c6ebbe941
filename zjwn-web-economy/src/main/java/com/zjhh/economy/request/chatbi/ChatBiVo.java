package com.zjhh.economy.request.chatbi;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/6/10 10:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatBiVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 3417847190902128099L;

    @Schema(description = "JWT权限token")
    private String bearerToken;

    @Schema(description = "url地址")
    private String url;
}
