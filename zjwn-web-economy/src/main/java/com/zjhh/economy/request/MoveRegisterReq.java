package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.economy.vo.SettleInfoListVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoveRegisterReq extends BaseReq {
    private static final long serialVersionUID = 3178817587742316704L;


    @Schema(description = "入驻信息")
    @Valid
    private List<SettleInfoListVo> settleInfos;

    @Schema(description = "实际搬离日期")
    @NotNull(message = "搬离日期不能为空")
    private LocalDate realMoveDate;

    @Schema(description = "搬离原因")
    private String moveReason;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "迁出原因")
    private List<String> labels;



}
