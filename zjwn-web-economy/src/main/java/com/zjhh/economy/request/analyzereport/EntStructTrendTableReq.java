package com.zjhh.economy.request.analyzereport;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EntStructTrendTableReq extends BaseReq {
    private static final long serialVersionUID = -6528586996064272596L;

    private String code;

    @Schema(description = "传入比较类型")
    private Integer compareType;
}
