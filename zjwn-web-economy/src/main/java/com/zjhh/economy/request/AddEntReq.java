package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import com.zjhh.economy.vo.EnterpriseLabelVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AddEntReq extends BaseReq {
    private static final long serialVersionUID = -5741380712895348631L;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    @NotBlank(message = "企业名称不能为空")
    private String enterpriseName;

    /**
     * 社会信用代码
     */
    @Schema(description = "社会信用代码")
    private String uscc;

    /**
     * 曾用名
     */
    @Schema(description = "曾用名")
    private String oldName;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    private LocalDate foundDate;

    /**
     * 法人
     */
    @Schema(description = "法人")
    private String legalPerson;

    /**
     * 企业类型
     */
    @Schema(description = "企业类型")
    private String enterpriseTypeCode;

    /**
     * 行业
     */

    @Schema(description = "行业")
    private String industryCode;

    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    @Schema(description = "货币类型 1-人民币 2-美元")
    private Integer currencyType;

    /**
     * 住所
     */

    @Schema(description = "住所")
    private String residence;

    /**
     * 电话
     */
    @Schema(description = "电话")
    private String phone;

    /**
     * 是否规上企业
     */
    private Boolean onScaled;

    /**
     * 是否属地企业
     */
    @Schema(description = "是否属地企业")
    private Boolean territorialized;

    /**
     * 营业期限开始时间
     */
    @Schema(description = "营业期限开始时间")
    private LocalDate businessTermStart;

    /**
     * 营业期限结束时间
     */
    @Schema(description = "营业期限结束时间")
    private LocalDate businessTermEnd;

    /**
     * 融资阶段
     */
    @Schema(description = "融资阶段")
    private String financingStageCode;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    private String registeredAddress;

    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    private String businessScope;

    /**
     * 企业logo
     */
    @Schema(description = "企业logo")
    private String logoImgId;

    @Schema(description = "企业联系人")
    @NotBlank(message = "企业联系人不能为空")
    private String entContactPerson;

    @Schema(description = "企业联系电话")
    @NotBlank(message = "企业联系电话不能为空")
    private String entPhone;

    @Schema(description = "企业标签")
    private List<EnterpriseLabelVo> enterpriseLabels;


    @Schema(description = "备注")
    private String remark;



}
