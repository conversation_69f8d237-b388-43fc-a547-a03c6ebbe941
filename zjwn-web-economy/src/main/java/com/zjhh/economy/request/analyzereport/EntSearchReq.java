package com.zjhh.economy.request.analyzereport;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EntSearchReq extends BaseReq {
    private static final long serialVersionUID = 4510538856033547266L;

    @Schema(description = "搜索关键词")
    private String keyword;
}
