package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageBuildingReq extends PageReq {

    private static final long serialVersionUID = 2998402684340701789L;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "所属社区")
    private String communityCode;


}
