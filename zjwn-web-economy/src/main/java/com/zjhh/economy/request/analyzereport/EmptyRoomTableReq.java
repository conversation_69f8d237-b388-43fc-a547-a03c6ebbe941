package com.zjhh.economy.request.analyzereport;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyRoomTableReq extends BaseReq {
    private static final long serialVersionUID = -1368397056618965947L;

    private String code;

    @Schema(description = "传入比较类型")
    private Integer compareType;

    @Schema(description = "当前日期", hidden = true)
    private String currentDate;

    public String getCurrentDate() {
        String current = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN);
        return current;
    }
}
