package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePlaneConfigReq extends BaseReq {

    private static final long serialVersionUID = -4840406836625674105L;

    @NotBlank(message = "id不能为空！")
    @Schema(description = "平面图配置id")
    private String planeConfigId;

    @Schema(description = "平面图配置")
    private String planeConfig;
}
