package com.zjhh.economy.request.earlywarning;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/8/14 16:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddAdsDmsReportReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -3594116730351494033L;

    @Schema(description = "报告编码")
    private String reportTypeCode;

    @Schema(description = "报告名称")
    private String reportTypeName;

    @Schema(description = "cron表达式")
    private String cronExpression;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "定时任务状态 1-正常 0-停止")
    private Integer cronState;

    @Schema(description = "成员范围 1-全部可见 2-部分成员可见")
    private Integer authType;

    @Schema(description = "范围编码")
    private List<String> orgCodes;

    @Schema(description = "报告模板Id")
    private String docId;
}
