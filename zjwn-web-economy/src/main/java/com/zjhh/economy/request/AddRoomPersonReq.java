package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/8 17:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddRoomPersonReq extends BaseReq {

    private static final long serialVersionUID = -6830278857909573222L;

    @Schema(description = "权属人")
    @NotBlank(message = "权属人不能为空！")
    private String person;

    @Schema(description = "联系方式")
    @NotBlank(message = "联系方式不能为空！")
    private String phone;
}
