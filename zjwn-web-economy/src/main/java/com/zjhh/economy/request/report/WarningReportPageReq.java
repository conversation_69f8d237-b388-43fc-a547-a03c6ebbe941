package com.zjhh.economy.request.report;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警分析报告列表Req
 *
 * <AUTHOR>
 * @date 2022-05-23 10:47 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportPageReq extends PageReq {

    private static final long serialVersionUID = 7227149900544593425L;

    @Schema(description = "报告名称")
    private String reportName;

    @Schema(description = "文件类型")
    private String fileType;

}
