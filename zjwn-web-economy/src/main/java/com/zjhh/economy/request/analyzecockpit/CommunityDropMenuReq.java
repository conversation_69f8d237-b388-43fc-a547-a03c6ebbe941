package com.zjhh.economy.request.analyzecockpit;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  街道社区下拉框
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommunityDropMenuReq extends BaseReq {
    private static final long serialVersionUID = -3883637637561264118L;

    @Schema(description = "项目类型")
    private String projectType;
}
