package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveFloorReq extends BaseReq {

    private static final long serialVersionUID = -6380666529958229287L;

    @Schema(description = "楼宇id")
    @NotBlank(message = "楼宇id不能为空")
    private String buildingId;

    @Schema(description = "楼层列表")
    private List<SaveFloorListReq> floorList;
}
