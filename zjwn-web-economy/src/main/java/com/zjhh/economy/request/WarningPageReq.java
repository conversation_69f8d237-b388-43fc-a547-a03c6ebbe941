package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class WarningPageReq extends PageReq {
    @Serial
    private static final long serialVersionUID = 2077194661365895241L;

    @Schema(description = "搜索关键词")
    private String searchWord;

    @Schema(description = "提醒类型")
    private List<Integer> remindType;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;
}
