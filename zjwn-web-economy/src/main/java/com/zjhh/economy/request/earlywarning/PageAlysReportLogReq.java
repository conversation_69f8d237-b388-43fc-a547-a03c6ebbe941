package com.zjhh.economy.request.earlywarning;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/5/11 11:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageAlysReportLogReq extends PageReq {

    private static final long serialVersionUID = -2879026201159100861L;

    @Schema(description = "报告编码")
    @NotBlank(message = "报告编码不能为空！")
    private String reportTypeCode;

    @Schema(description = "报告类型名称 0-失败；1-成功； 99-全部")
    private Integer reportState;

    @Schema(description = "开始时间 yyyyMMdd")
    private String startTime;

    @Schema(description = "结束时间 yyyyMMdd")
    private String endTime;
}
