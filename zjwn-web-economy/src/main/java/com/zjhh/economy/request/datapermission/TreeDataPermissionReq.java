package com.zjhh.economy.request.datapermission;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/8/3 11:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TreeDataPermissionReq extends BaseReq {

    private static final long serialVersionUID = 6726898915288445495L;

    @Schema(description = "用户编码")
    @NotBlank(message = "用户编码不能为空！")
    private String userCode;

    @Schema(description = "1-楼宇")
    @NotNull(message = "类型不能为空！")
    private Integer type;
}
