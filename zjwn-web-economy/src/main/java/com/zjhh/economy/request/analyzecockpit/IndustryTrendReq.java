package com.zjhh.economy.request.analyzecockpit;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 趋势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryTrendReq extends AnalyzeCockpitCommonReq {

    @Schema(description = "行业代码")
    private List<String> industryCodes;

    @Schema(description = "层级1-一级 2-二级 3-三级")
    private Integer level;

    @Schema(hidden = true, description = "根据是否月末查询日期")
    private String queryYearMonth;

    private String sqDate;

    private String byDate;

    public String getQueryYearMonth() {
        return checkLastDayOfMonth() ? getCurrentYearMonth() : getLastMonth();
    }
}
