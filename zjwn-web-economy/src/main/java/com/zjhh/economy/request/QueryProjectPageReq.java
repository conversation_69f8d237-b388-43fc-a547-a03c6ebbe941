package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryProjectPageReq extends PageReq {
    private static final long serialVersionUID = 8381801665240586860L;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "所属社区")
    private List<String> communityCodes;
}
