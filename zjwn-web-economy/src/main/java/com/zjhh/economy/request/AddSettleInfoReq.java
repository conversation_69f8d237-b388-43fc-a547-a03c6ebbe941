package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AddSettleInfoReq extends BaseReq {
    private static final long serialVersionUID = 8699237331521402482L;

    @Schema(description = "房间Id")
    @NotEmpty(message = "房间号不能为空")
    private List<String> roomIds;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "楼宇ID")
    private String buildingId;

    @Schema(description = "楼层ID")
    private String floorId;


    @Schema(description = "企业ID")
    private String enterpriseId;

    /**
     * 入住开始日期
     */
    @Schema(description = "入住开始日期")
    @NotNull(message = "入驻开始日期不能为空")
    private LocalDate checkInDate;

    /**
     * 预计搬离日期
     */
    @Schema(description = "预计搬离日期")
    private LocalDate expectMoveOutDate;

    /**
     * 入驻面积
     */
    @Schema(description = "入驻面积")
    private BigDecimal area;

    /**
     * 装修开始日期
     */
    @Schema(description = "装修开始日期")
    private LocalDate renovationStartDate;

    /**
     * 装修结束日期
     */
    @Schema(description = "装修结束日期")
    private LocalDate renovationEndDate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "附件ID")
    private String documentId;


}
