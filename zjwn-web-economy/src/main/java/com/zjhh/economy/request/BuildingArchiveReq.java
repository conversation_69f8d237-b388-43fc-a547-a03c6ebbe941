package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 一楼一档Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingArchiveReq extends PageReq {
    private static final long serialVersionUID = 3310871705577075351L;

    @Schema(description = "综合搜索")
    private String searchWord;

    @Schema(description = "法人")
    private String legalPerson;

    @Schema(description = "是否规上")
    private Boolean onScaled;

    @Schema(description = "所属社区/楼宇")
    private List<String> communityCodes;

    @Schema(description = "税收所属社区/楼宇")
    private List<String> taxCommunityCodes;

    @Schema(description = "是否属地")
    private Boolean territorialized;

    @Schema(description = "行业")
    private List<String> industryCodes;

    @Schema(description = "税收收入范围")
    private Integer taxRange;

    @Schema(description = "是否注经一致")
    private Boolean consistent;

    @Schema(description = "单位性质")
    private Integer unitProperty;

    @Schema(description = "企业经营状态")
    private Integer businessStatus;

    @Schema(description = "属地及纳税情况")
    private Integer localTaxStatus;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "结束日期")
    private String endDate;

    @Schema(hidden = true)
    private String lastStartDate;

    @Schema(hidden = true)
    private String lastEndDate;

    @Schema(hidden = true)
    private String lastYear;

    @Schema(hidden = true)
    private String lastYearMonth;

    @Schema(hidden = true,description = "上年累计数据库查询月份")
    private String accLastYear;

    @Schema(description = "数据库税收查询日期")
    private String queryDate;

    @Schema(description = "企业标签")
    private List<String> labels;
    public String getLastStartDate() {
        return getLastDate(startDate);
    }

    public String getLastEndDate() {
        return getLastDate(endDate);
    }

    public String getLastYear() {
        Integer year = Integer.valueOf(startDate.substring(0, 4));
        return year - 1 + "";
    }

    public String getAccLastYear() {
        Integer year = Integer.valueOf(queryDate.substring(0, 4));
        return year - 1 + "";
    }

    public String getLastYearMonth() {
        return getLastMonthDate(queryDate);
    }

    private String getLastDate(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 减去一年的时间
        LocalDate lastYearSamePeriod = date.minusYears(1);
        return lastYearSamePeriod.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }

    private String getLastMonthDate(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 上月
        LocalDate lastMonthSamePeriod = date.minusMonths(1);
        return lastMonthSamePeriod.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }
}
