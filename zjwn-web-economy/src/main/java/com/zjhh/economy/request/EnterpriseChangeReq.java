package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseChangeReq extends BaseReq {
    private static final long serialVersionUID = -7166750641938803026L;

    @Schema(description = "变更id,编辑的时候传")
    private String id;

    @Schema(description = "企业id")
    private String enterpriseId;

    @Schema(description = "变更日期")
    private LocalDate changeDate;

    @Schema(description = "变更类型")
    private String changeType;

    @Schema(description = "备注")
    private String remark;



}
