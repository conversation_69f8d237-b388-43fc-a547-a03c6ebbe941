package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryEnterprisePageReq extends PageReq {

    @Serial
    private static final long serialVersionUID = -7506234973064629571L;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "所属社区")
    private String communityCode;

    @Schema(description = "入驻项目")
    private String projectId;

    @Schema(description = "入驻楼宇")
    private String buildingId;

    @Schema(description = "入驻房号")
    private String roomNo;

    @Schema(description = "是否规上企业")
    private Boolean onScaled;

    @Schema(description = "移动端从行业跳转的时候加固定ly")
    private String projectType;

    /**
     * 是否属地企业
     */
    @Schema(description = "是否属地企业")
    private Boolean territorialized;

    @Schema(description = "行业分类")
    private List<String> industryCode;

    @Schema(description = "重点关注")
    private Boolean focused;

    @Schema(description = "入驻状态 0-全部 1-入驻中，2-已搬离，3-未分配")
    private String settleStatus;

    @Schema(description = "userCode", hidden = true)
    private String userCode;

    @Schema(description = "1-大于50 2-小于50")
    private Integer greaterThan;


    @Schema(description = "成立时间1-3个月内 2-半年内 3-1年内 4 1-3年 5 3-5年 6 5-10年 7 10年以上")
    private Integer foundDateType;

    @Schema(description = "企业标签")
    private List<String> labels;

    @Schema(hidden = true, description = "税收日期")
    private String datekey;

}
