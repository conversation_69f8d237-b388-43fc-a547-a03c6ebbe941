package com.zjhh.economy.request.analyzereport;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettledAreaTrendTableReq extends BaseReq {
    private static final long serialVersionUID = 2735081814696494183L;

    @Schema(description = "日期")
    private String datekey;

    private String code;

    @Schema(description = "传入比较类型")
    private Integer compareType;

}
