package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class EntTaxMonitorReq extends PageReq {
    private static final long serialVersionUID = 1247185123746550651L;

    @Schema(description = "楼宇ID")
    private String buildingId;

    @Schema(description = "行业Code")
    private String indCode;

    @Schema(description = "企业名称")
    private String entName;

    @Schema(description = "预警开始日期")
    private String startDate;

    @Schema(description = "预警结束日期")
    private String endDate;

    @Schema(description = "1-待处理-2处理-3忽略")
    private Integer handleType;

    @Schema(description = "项目")
    private String project;


}
