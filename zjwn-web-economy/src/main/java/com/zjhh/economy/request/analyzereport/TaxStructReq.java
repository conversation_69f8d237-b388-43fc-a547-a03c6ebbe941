package com.zjhh.economy.request.analyzereport;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxStructReq extends BaseReq {
    private static final long serialVersionUID = 6407176150718298703L;


    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

    @Schema(description = "月份",hidden = true)
    private List<String> months;

    @Schema(description = "同期月份",hidden = true)
    private List<String> tqMonths;


    public List<String> getMonths() {
        List<String> monthList = new ArrayList<>();
        LocalDate currentDate = DateUtil.parseLocalDateTime(startDate,"yyyyMM").toLocalDate();
        LocalDate end = DateUtil.parseLocalDateTime(endDate, "yyyyMM").toLocalDate();

        while (currentDate.isBefore(end) || currentDate.isEqual(end)) {
            String monthString = currentDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
            monthList.add(monthString);
            currentDate = currentDate.plusMonths(1);
        }
        return monthList;
    }

    public List<String> getTqMonths() {
        List<String> monthList = new ArrayList<>();
        String startStr = DateUtil.format(DateUtil.offset(DateUtil.parse(startDate,"yyyyMM"), DateField.YEAR,-1),"yyyyMM");

        String endStr = DateUtil.format(DateUtil.offset(DateUtil.parse(endDate,"yyyyMM"), DateField.YEAR,-1),"yyyyMM");

        LocalDate currentDate = DateUtil.parseLocalDateTime(startStr,"yyyyMM").toLocalDate();
        LocalDate end =DateUtil.parseLocalDateTime(endStr,"yyyyMM").toLocalDate();

        while (currentDate.isBefore(end) || currentDate.isEqual(end)) {
            String monthString = currentDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
            monthList.add(monthString);
            currentDate = currentDate.plusMonths(1);
        }
        return monthList;
    }
}
