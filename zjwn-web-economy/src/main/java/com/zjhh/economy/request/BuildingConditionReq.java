package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 楼宇项目情况Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingConditionReq extends PageReq {
    private static final long serialVersionUID = 4028674682688489442L;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "社区Code")
    private String communityCode;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;

    @Schema(description = "楼宇集合 NT-纳统楼宇 TJ-统计楼宇")
    private String buildingGroup;

}
