package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateCommentScoreReq extends BaseReq {
    private static final long serialVersionUID = 1977459595481958903L;

    @Schema(description = "有就传没有就不传")
    private String id;

    @Schema(description = "楼宇ID")
    private String buildingId;

    @Schema(description = "指标ID")
    private String commentIndicatorId;

    @Schema(description = "年度")
    private String datekey;

    @Schema(description = "分值")
    private BigDecimal commentScore;

}
