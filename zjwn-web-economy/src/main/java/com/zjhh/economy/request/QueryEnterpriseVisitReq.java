package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryEnterpriseVisitReq extends PageReq {
    private static final long serialVersionUID = 7088523071084744303L;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

}
