package com.zjhh.economy.request.policymanagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyManagementAddOrUpdateReq extends BaseReq {

    private static final long serialVersionUID = -8099761412265638295L;

    @Schema(description = "政策id。修改时传")
    private String id;

    @NotBlank(message = "政策名称不能为空！")
    @Schema(description = "政策名称")
    private String policyName;

    @Schema(description = "文号")
    private String policyNo;

    @NotBlank(message = "发布机构不能为空！")
    @Schema(description = "发布机构")
    private String publishAgency;

    @NotNull(message = "发布时间不能为空！")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "发布时间。格式：yyyy-MM-dd")
    private LocalDate publishDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "成文日期。格式：yyyy-MM-dd")
    private LocalDate writtenDate;

    @NotBlank(message = "政策详情不能为空！")
    @Schema(description = "政策详情")
    private String policyDetail;

    @Schema(description = "附件id")
    private List<String> fileIds;
}
