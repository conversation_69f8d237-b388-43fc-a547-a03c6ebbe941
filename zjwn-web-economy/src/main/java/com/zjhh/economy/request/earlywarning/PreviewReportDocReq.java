package com.zjhh.economy.request.earlywarning;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/5/20 16:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PreviewReportDocReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -3996482785954293769L;

    @Schema(description = "报告编码")
    @NotBlank(message = "报告编码不能为空！")
    private String reportTypeCode;

    @Schema(description = "月份")
    @NotBlank(message = "月份不能为空！")
    private String datekey;
}
