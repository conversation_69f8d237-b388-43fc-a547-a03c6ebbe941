package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveFloorListReq extends BaseReq {

    private static final long serialVersionUID = -369322742504431570L;

    @Schema(description = "楼层id，编辑的时候用到")
    private String floorId;

    @Schema(description = "建筑楼层")
    @NotNull(message = "建筑楼层不能为空！")
    private Integer floorNo;

    @Schema(description = "楼层名称")
    @NotBlank(message = "楼层名称不能为空！")
    private String floorName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "楼层平面图")
    private String planeImgId;

    @Schema(description = "楼层平面图配置id")
    private String planeConfigId;
}
