package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AddEnterpriseDemandReq extends BaseReq {
    private static final long serialVersionUID = 1198944637442512185L;

    @Schema(description = "id-编辑的时候传")
    private String id;

    @Schema(description = "诉求描述")
    private String demandDesc;

    @Schema(description = "提交日期")
    private LocalDate submitDate;

    @Schema(description = "诉求来源")
    private String submitSource;

    @Schema(description = "企业Id")
    private String enterpriseId;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "诉求类型")
    private String demandType;

    @Schema(description = "所属楼宇")
    private String buildingId;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "照片")
    private List<String> photos;


}
