package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateSettleInfoReq extends BaseReq {

    private static final long serialVersionUID = 3745932961861975513L;

    @Schema(description = "主键")
    private String id;

    /**
     * 入住开始日期
     */
    @Schema(description = "入住开始日期")
    @NotNull(message = "入驻开始日期不能为空")
    private LocalDate checkInDate;



    /**
     * 预计搬离日期
     */
    @Schema(description = "预计搬离日期")
    private LocalDate expectMoveOutDate;

    /**
     * 入驻面积
     */
    @Schema(description = "入驻面积")
    private BigDecimal area;

    /**
     * 装修开始日期
     */
    @Schema(description = "装修开始日期")
    private LocalDate renovationStartDate;

    /**
     * 装修结束日期
     */
    @Schema(description = "装修结束日期")
    private LocalDate renovationEndDate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "附件ID")
    private String documentId;


}
