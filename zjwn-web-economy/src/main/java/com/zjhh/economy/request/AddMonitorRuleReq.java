package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 添加监测规则
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddMonitorRuleReq extends BaseReq {
    private static final long serialVersionUID = -8500755132938416418L;

    @Schema(description = "新增不需要传,更新时候传")
    private String id;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "1-月度-2季度-3年度")
    private Integer monitorRulePeriod;

    @Schema(description = "1-同比-2环比")
    private Integer monitorRuleCompare;

    @Schema(description = "1-上升-2下降")
    private Integer monitorRuleChange;

    @Schema(description = "增幅")
    private BigDecimal monitorRuleZf;

    @Schema(description = "1-企业规则-2产业规则")
    private Integer monitorRuleType;

    @Schema(description = "监测范围")
    private List<String> monitorRanges;


}
