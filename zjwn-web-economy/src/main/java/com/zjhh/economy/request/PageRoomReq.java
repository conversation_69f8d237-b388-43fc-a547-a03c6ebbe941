package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/3/8 11:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageRoomReq extends PageReq {

    private static final long serialVersionUID = -2940665051730232727L;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "社区编码")
    private String communityCode;

    @Schema(description = "房号")
    private String roomNo;

    @Schema(description = "入驻企业")
    private String enterpriseName;

    @Schema(description = "权属编码")
    private String ownershipCode;

    @Schema(description = "房间状态：0-空置 1-已入驻 2-不可出租 3-新入驻装修中")
    private Integer roomStatus;

    @Schema(description = "最小空置面积")
    private BigDecimal minEmptyArea;

    @Schema(description = "最大空置面积")
    private BigDecimal maxEmptyArea;

    @Schema(description = "最小房源商务面积")
    private BigDecimal minBusinessArea;

    @Schema(description = "最大房源商务面积")
    private BigDecimal maxBusinessArea;

}
