package com.zjhh.economy.request.policymanagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyManagementCashReq extends BaseReq {
    private static final long serialVersionUID = -5370355494945871668L;

    @NotBlank(message = "企业不能为空！")
    @Schema(description = "企业id")
    private String entId;

    @NotNull(message = "兑现金额不能为空！")
    @Schema(description = "兑现金额")
    private BigDecimal cashAmount;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "兑现时间不能为空！")
    @Schema(description = "兑现时间。格式：yyyy-MM-dd")
    private LocalDate cashDate;

    @Schema(description = "备注")
    private String remark;
}
