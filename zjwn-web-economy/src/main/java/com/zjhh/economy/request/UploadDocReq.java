package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2024/3/11 10:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UploadDocReq extends BaseReq {
    private static final long serialVersionUID = 2372778754897969491L;

    @Schema(description = "文件")
    @NotNull(message = "上传文件不能为空！")
    private MultipartFile file;

    @Schema(description = "文档类型：projectImg-项目图片，buildingOutsideImg-楼宇外观图，buildingOtherImg-楼宇其它图片, floorPlaneImg-楼层平面图,roomImg-房间图片，enterpriseImg-企业logo，settleFile-入驻信息附件," +
            "policyManagementFile-政策管理附件 ")
    @NotBlank(message = "文档类型不能为空！")
    private String documentType;
}
