package com.zjhh.economy.request.report;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警报告上传Req
 *
 * <AUTHOR>
 * @date 2022-05-23 2:13 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportUploadReq extends BaseReq {
    private static final long serialVersionUID = 5617496961420468995L;

    @Schema(description = "报告名称")
    private String reportName;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "单位Code")
    private String unitCode;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件路径")
    private String filePath;


}
