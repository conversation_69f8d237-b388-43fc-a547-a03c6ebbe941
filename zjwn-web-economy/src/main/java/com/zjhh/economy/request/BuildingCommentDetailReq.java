package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingCommentDetailReq extends BaseReq {

    private static final long serialVersionUID = 5800366655560339867L;

    private String datekey;

    @Schema(description = "评价类型 1-建设评价2-服务评价3-贡献评价")
    private Integer commentType;

    private String buildingId;


}
