package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  启动规则Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnableRuleReq extends BaseReq {
    private static final long serialVersionUID = -2934938472025884199L;

    @Schema(description = "规则ID")
    private String id;

}
