package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxSummaryReq extends BaseReq {
    private static final long serialVersionUID = -647241508820774981L;

    @Schema(description = "1-企业-2产业")
    private Integer type;
}
