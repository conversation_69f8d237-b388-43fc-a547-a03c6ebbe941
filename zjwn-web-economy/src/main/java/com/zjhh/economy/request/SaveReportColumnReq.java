package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SaveReportColumnReq extends BaseReq {
    private static final long serialVersionUID = -7892928095207457347L;

    @Schema(description = "存储字段")
    private List<String> ids;

    @Schema(description = "1查询2-导出")
    private Integer queryType;
}
