package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AddLabelReq extends BaseReq {

    @Schema(description = "类型-楼宇标签-BuildingLabel，企业标签-EnterpriseLabel，房间标签-RoomLabel，迁出原因-MoveOut")
    private String type;

    @Schema(description = "标签")
    @Size(max = 50, message = "标签最多添加50个")
    @Valid
    private List<LabelReq> labelList;
}
