package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndTaxMonitorReq extends PageReq {
    private static final long serialVersionUID = -1653269256749682243L;

    @Schema(description = "行业Code")
    private String indCode;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

    @Schema(description = "不传-待处理-1处理-2忽略")
    private Integer handleType;

    @Schema(description = "项目")
    private String project;

    @Schema(description = "楼宇id")
    private String buildingId;
}
