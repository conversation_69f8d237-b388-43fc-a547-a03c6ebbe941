package com.zjhh.economy.request.earlywarning;

import com.zjhh.economy.dao.entity.AdsDmsReportPage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/5/20 16:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveAndPreviewAlysReportReq extends SaveAlysReportPageReq {

    @Serial
    private static final long serialVersionUID = 2737200146789074247L;

    @Schema(description = "月份")
    @NotBlank(message = "月份不能为空！")
    private String datekey;

}
