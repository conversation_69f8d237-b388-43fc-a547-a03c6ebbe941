package com.zjhh.economy.request.earlywarning;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/5/11 15:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddAlysReportDataColumnSetReq extends BaseReq {

    private static final long serialVersionUID = 8310463046258309696L;

    private String key;

    private String parentKey;

    private String title;

    @Schema(description = "字段编码")
    private String columnKey;

    @Schema(description = "字段名称")
    private String columnName;

    @Schema(description = "对应字段--列表返回用到，新增编辑不需要")
    private String field;

}
