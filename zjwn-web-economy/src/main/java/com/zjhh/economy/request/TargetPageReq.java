package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/11/5 09:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TargetPageReq extends BaseReq {

    private static final long serialVersionUID = -8503068818863404487L;

    @Schema(description = "targetPage")
    @NotBlank(message = "targetPage不能为空！")
    private String targetPage;
}
