package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DelWarningReq extends BaseReq {
    private static final long serialVersionUID = 6761217416668537746L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "1-企业税收预警-2产业税收预警")
    private Integer type;
}
