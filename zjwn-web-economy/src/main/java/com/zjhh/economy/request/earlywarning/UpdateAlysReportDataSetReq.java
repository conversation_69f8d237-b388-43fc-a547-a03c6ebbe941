package com.zjhh.economy.request.earlywarning;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/5/11 15:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateAlysReportDataSetReq extends AddAlysReportDataSetReq {

    private static final long serialVersionUID = -8406039899828847306L;

    @Schema(description = "数据集id")
    private String dataSetId;
}
