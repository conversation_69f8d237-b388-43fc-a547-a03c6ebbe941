package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoveSettleDropMenuReq extends BaseReq {

    @Schema(description = "企业id")
    private String id;

    @Schema(description = "入驻信息id")
    private List<String> ids;
}
