package com.zjhh.economy.request.analyzereport;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SpaceResourceDimensionReq extends BaseReq {
    private static final long serialVersionUID = 5721557546946912027L;

    @Schema(description = "1-社区对比 2-项目对比 3-楼宇对比")
    private Integer compareType;

    @Schema(description = "项目类型")
    private String projectType;


    @Schema(description = "选择的code")
    private List<String> codes;

    @Schema(description = "1-月 2-季度 3-年")
    private Integer dateType;

    @Schema(hidden = true)
    private List<String> months;

    @Schema(description = "当前日期", hidden = true)
    private String currentDate;

    @Schema(description = "去年", hidden = true)
    private String lastYear;

    @Schema(description = "去年月", hidden = true)
    private String lastYearMonth;

    @Schema(description = "当前年月", hidden = true)
    private String currentYearMonth;

    @Schema(description = "当前年", hidden = true)
    private String currentYear;



    public String getCurrentYear() {
        return String.valueOf(DateUtil.year(new Date()));
    }

    public String getLastMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), -1);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return last;
    }

    public String getLastYear() {
        return String.valueOf(DateUtil.year(new Date()) - 1);
    }

    public String getCurrentDate() {
        String current = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN);
        return current;
    }

    public String getLastYearMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), -12);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.SIMPLE_MONTH_PATTERN);
        return last;
    }

    public String getCurrentYearMonth() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), 0);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return last;
    }

}
