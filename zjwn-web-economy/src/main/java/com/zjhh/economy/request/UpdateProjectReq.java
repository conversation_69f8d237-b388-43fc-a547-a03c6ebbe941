package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateProjectReq extends BaseReq {
    private static final long serialVersionUID = 77757926461627313L;


    @Schema(description = "主键")
    private String id;


    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    @Size(max = 50,message = "项目名称不能超过50个字")
    private String projectName;

    @Schema(description = "项目类型")
    private String projectType;


    /**
     * 社区编码
     */
    @Schema(description = "社区编码")
    private String communityCode;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String longitude;

    /**
     * 维度
     */
    @Schema(description = "纬度")
    private String dimension;

    /**
     * 占地面积
     */
    @Schema(description = "战地面积")
    private BigDecimal projectArea;

    /**
     * 物业公司
     */
    @Schema(description = "物业公司")
    @Size(max = 50,message = "物业公司名称不能超过50个字")
    private String manageCompany;

    /**
     * 联系
     */
    @Schema(description = "联系人")
    @Size(max = 20,message = "联系人不能超过20个字")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 项目介绍
     */
    @Schema(description = "项目介绍")
    @Size(max = 500,message = "项目简介不能超过500个字")
    private String projectIntro;

    @Schema(description = "项目坐落")
    @NotBlank(message = "项目坐落不能为空")
    private String address;

    /**
     * 项目图片
     */
    private List<ProjectImageVo> projectImages;


}
