package com.zjhh.economy.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateBuildingReq extends AddBuildingReq {

    private static final long serialVersionUID = 1375193063554648783L;

    @Schema(description = "楼宇id")
    @NotBlank(message = "楼宇id不能为空！")
    private String buildingId;
}
