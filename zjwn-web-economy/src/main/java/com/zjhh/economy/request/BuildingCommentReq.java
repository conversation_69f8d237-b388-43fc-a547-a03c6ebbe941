package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingCommentReq extends PageReq {
    private static final long serialVersionUID = 4552464045368869175L;

    private String datekey;

    @Schema(description = "1-总分 2建设 3-服务 4-贡献")
    private Integer orderType;

}
