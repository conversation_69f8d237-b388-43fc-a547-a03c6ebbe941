package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SavePlaneConfigReq extends BaseReq {
    private static final long serialVersionUID = 1283946818468271086L;

    @Schema(description = "楼层id")
    private String floorId;

    @Schema(description = "房源平面图配置")
    private String planeConfig;
}
