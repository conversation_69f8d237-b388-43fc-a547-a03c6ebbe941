package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

@Data
@EqualsAndHashCode(callSuper = true)
public class ImportExternalDataReq extends BaseReq {
    private static final long serialVersionUID = -6798952259829514592L;

    @Schema(description = "上传文件")
    private MultipartFile file;

    @Schema(description = "上传类型")
    private String uploadType;


}
