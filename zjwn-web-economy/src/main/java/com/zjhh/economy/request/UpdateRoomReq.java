package com.zjhh.economy.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 09:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateRoomReq extends AddRoomReq {

    private static final long serialVersionUID = -5851083889530731379L;

    @Schema(description = "房间id")
    private String roomId;
}
