package com.zjhh.economy.request.datapermission;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/24 14:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AuthDataPermissionReq extends BaseReq {

    private static final long serialVersionUID = -140374720136062357L;

    @Schema(description = "用户code")
    @NotBlank(message = "用户code不能为空！")
    private String userCode;

    @Schema(description = "1-楼宇")
    @NotNull(message = "类型不能为空！")
    private Integer type;

    @Schema(description = "编码")
    @NotNull(message = "保存的编码不能为空！")
    private List<String> codes;
}
