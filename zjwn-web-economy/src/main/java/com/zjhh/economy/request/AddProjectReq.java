package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AddProjectReq extends BaseReq {
    private static final long serialVersionUID = 4555589412962240542L;


    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    @NotBlank(message = "项目名称不能为空，且不能超过50个字符")
    @Size(max = 50,message = "项目名称不能超过50个字")
    private String projectName;

    /**
     * 社区编码
     */
    @Schema(description = "社区编码")
    @NotBlank(message = "所属社区不能为空")
    private String communityCode;

    /**
     * 经度
     */
    @Schema(description = "经度")
    @NotBlank(message = "项目坐落不能为空")
    private String longitude;

    /**
     * 维度
     */
    @Schema(description = "纬度")
    @NotBlank(message = "项目坐落不能为空")
    private String dimension;

    /**
     * 占地面积
     */
    @Schema(description = "占地面积")
    private BigDecimal projectArea;

    /**
     * 物业公司
     */
    @Schema(description = "物业公司")
    @Size(max = 50,message = "物业公司名称不能超过50个字")
    private String manageCompany;

    /**
     * 联系
     */
    @Schema(description = "联系人")
    @Size(max = 20,message = "联系人不能超过20字")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 项目介绍
     */
    @Schema(description = "项目介绍")
    @Size(max = 500,message = "项目介绍不能超过500个字")
    private String projectIntro;

    @Schema(description = "坐落")
    @NotBlank(message = "项目坐落不能为空")
    private String address;

    @Schema(description = "项目类型")
    @NotBlank(message = "项目类型不能为空")
    private String projectType;

    /**
     * 项目图片
     */

    private List<ProjectImageVo> projectImages;






}
