package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ExportRecordReq extends BaseReq {
    private static final long serialVersionUID = 2907040733956153593L;

    @Schema(description = "导入id")
    private String importId;

    @Schema(description = "上传类型")
    private String uploadType;
}
