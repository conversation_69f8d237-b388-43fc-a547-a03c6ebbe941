package com.zjhh.economy.request.report;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingSettleEntBasicReq extends PageReq {
    private static final long serialVersionUID = -4885934265693223366L;

    /* 基础筛选条件 */
    @Schema(description = "月份(格式：yyyyMM)")
    private String endDate;

    @Schema(description = "楼宇名称(多选)")
    private List<String> project;

    @Schema(description = "机构类型(多选)")
    private List<String> institutional;

    @Schema(description = "主要业务活动")
    private String businessScope;

    /* 日期范围 */
    @Schema(description = "开业开始年月(yyyy-MM)")
    private String foundDateStart;

    @Schema(description = "开业结束年月(yyyy-MM)")
    private String foundDateEnd;

    @Schema(description = "入驻开始年月(yyyy-MM)")
    private String settledDateStart;

    @Schema(description = "入驻结束年月(yyyy-MM)")
    private String settledDateEnd;

    /* 数值范围 */
    @Schema(description = "注册资金最小值(万元)")
    private BigDecimal registeredCapitalMin;

    @Schema(description = "注册资金最大值(万元)")
    private BigDecimal registeredCapitalMax;

    @Schema(description = "企业经营状态(多选)")
    private List<String> businessStatus;

    /* 面积范围 */
    @Schema(description = "商业面积最小值(m²)")
    private BigDecimal businessAreaMin;

    @Schema(description = "商业面积最大值(m²)")
    private BigDecimal businessAreaMax;

    @Schema(description = "实用面积最小值(m²)")
    private BigDecimal settledAreaMin;

    @Schema(description = "实用面积最大值(m²)")
    private BigDecimal settledAreaMax;

    /* 税务范围 */
    @Schema(description = "地税最小值(万元)")
    private BigDecimal localTaxMin;

    @Schema(description = "地税最大值(万元)")
    private BigDecimal localTaxMax;

    @Schema(description = "变更情况(多选)")
    private List<String> changeSituation;

}
