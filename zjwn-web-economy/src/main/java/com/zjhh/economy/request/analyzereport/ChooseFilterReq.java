package com.zjhh.economy.request.analyzereport;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 选择筛选器Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChooseFilterReq extends BaseReq {
    private static final long serialVersionUID = 5744897237221014048L;

    @Schema(description = "筛选类型 1-社区 2-项目 3-楼宇")
    private Integer filterType;

    @Schema(description = "项目类型")
    private String projectType;


}
