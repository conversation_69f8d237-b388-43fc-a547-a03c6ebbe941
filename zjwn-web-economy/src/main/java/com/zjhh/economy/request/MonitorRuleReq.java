package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则监测查询Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorRuleReq extends PageReq {
    private static final long serialVersionUID = -1193497634783638579L;

    @Schema(description = "1-企业规则2-产业规则")
    private Integer type;

}
