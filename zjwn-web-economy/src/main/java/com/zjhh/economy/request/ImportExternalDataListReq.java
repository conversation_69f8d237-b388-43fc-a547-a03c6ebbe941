package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ImportExternalDataListReq extends PageReq {
    private static final long serialVersionUID = -4465398106352076300L;

    @Schema(description = "开始上传时间")
    private String uploadStartDate;

    @Schema(description = "结束上传时间")
    private String uploadEndDate;

    @Schema(description = "上传类型")
    private String uploadType;

    @Schema(description = "上传状态 0-失败 1-成功")
    private Integer uploadStatus;
}
