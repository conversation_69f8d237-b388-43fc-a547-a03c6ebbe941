package com.zjhh.economy.request.earlywarning;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2022/5/9 09:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageAnalysisReportReq extends PageReq {

    @Serial
    private static final long serialVersionUID = 8087120558952037404L;

    @Schema(description = "报告类型名称")
    private String reportTypeName;

    @Schema(description = "报告类型名称 0-失败；1-成功； 99-全部")
    private Integer reportState;
}
