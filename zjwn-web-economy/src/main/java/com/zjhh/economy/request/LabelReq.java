package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签添加Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LabelReq extends BaseReq {
    private static final long serialVersionUID = 1327886126721108852L;

    @Schema(description = "主键ID-更新时候传")
    private String id;

    @Schema(description = "标签名称")
    @Size(max = 10,message = "标签名字最多10个字")
    private String labelName;


}
