package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class HandleEnterpriseDemandReq extends BaseReq {
    private static final long serialVersionUID = -1738150140808874289L;


    @Schema(description = "诉求ID")
    private String demandId;

    @Schema(description = "处理结果")
    private Integer handleType;

    @Schema(description = "处理描述")
    private String handleDesc;

    @Schema(description = "附件ID")
    private String documentId;


}
