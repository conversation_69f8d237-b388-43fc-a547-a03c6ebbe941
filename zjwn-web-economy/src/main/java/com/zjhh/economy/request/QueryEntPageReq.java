package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;



@Data
@EqualsAndHashCode(callSuper = true)
public class QueryEntPageReq extends PageReq {
    private static final long serialVersionUID = 4905106652590662308L;

    @Schema(description = "企业名称")
    private String entName;

    @Schema(description = "所属社区")
    private String communityCode;

    @Schema(description = "入驻项目")
    private String projectId;

    @Schema(description = "入驻楼宇")
    private String buildingId;

    @Schema(description = "入驻房号")
    private String roomId;

    @Schema(description = "是否规上企业")
    private Boolean onScaled;

    @Schema(description = "是否属地企业")
    private Boolean localed;

    @Schema(description = "行业分类")
    private String industryCode;

    @Schema(description = "重点关注")
    private Boolean imported;

    @Schema(description = "入驻状态")
    private Integer settledStatus;


}
