package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BriefingReq extends BaseReq {
    private static final long serialVersionUID = -5376350936532832273L;

    @Schema(description = "1-7天 2-近30天 3-本月")
    private Integer period;
}
