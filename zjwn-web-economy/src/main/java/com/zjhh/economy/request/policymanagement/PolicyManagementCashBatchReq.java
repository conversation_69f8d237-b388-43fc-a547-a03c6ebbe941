package com.zjhh.economy.request.policymanagement;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyManagementCashBatchReq extends BaseReq {
    private static final long serialVersionUID = -5370355494945871668L;

    @Schema(description = "政策id")
    private String policyId;

    @Valid
    @Schema(description = "兑现列表")
    List<PolicyManagementCashReq> cashList;
}
