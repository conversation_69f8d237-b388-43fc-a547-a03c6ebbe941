package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ColumnSortReq extends BaseReq {
    private static final long serialVersionUID = 1343660813635336652L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "排序")
    private Integer sort;


}
