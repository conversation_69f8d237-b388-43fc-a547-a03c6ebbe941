package com.zjhh.economy.request.analyzecockpit;

import cn.hutool.core.date.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppEconomicReq extends AnalyzeCockpitCommonReq {
    private static final long serialVersionUID = -7338356773089344769L;

    @Schema(description = "1-社区对比 2-项目对比 3-楼宇对比")
    private Integer compareType;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "选择的code")
    private List<String> codes;

    @Schema(description = "1-税收收入与街道收入 2-税收收入与除房地产税收 3-单位产出与除房地产单位产出")
    private Integer queryType;


    public String getCurrentYear() {
        return String.valueOf(DateUtil.year(new Date()));
    }
}
