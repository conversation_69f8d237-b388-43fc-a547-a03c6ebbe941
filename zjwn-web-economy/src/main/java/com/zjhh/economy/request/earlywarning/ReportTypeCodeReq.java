package com.zjhh.economy.request.earlywarning;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/8/14 17:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportTypeCodeReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -2479927997835812031L;

    @Schema(description = "分析报告模板编码")
    @NotBlank(message = "分析报告模板编码不能为空！")
    private String reportTypeCode;
}
