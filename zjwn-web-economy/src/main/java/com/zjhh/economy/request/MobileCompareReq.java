package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
public class MobileCompareReq extends BaseReq {
    @Serial
    private static final long serialVersionUID = -5678026858885996129L;

    @Schema(description = "社区-COMMUNITY, 项目-PROJECT,楼宇-BUILDING")
    private String itemType;

    @Schema(description = "项目ID")
    private String projectId;
}
