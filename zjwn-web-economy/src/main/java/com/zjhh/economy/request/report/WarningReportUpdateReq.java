package com.zjhh.economy.request.report;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警报告上传Req
 *
 * <AUTHOR>
 * @date 2022-05-23 2:13 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportUpdateReq extends BaseReq {

    private static final long serialVersionUID = 666885651889382266L;

    @Schema(description = "报告Id")
    private String guid;

    @Schema(description = "报告名称")
    private String reportName;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "单位Code")
    private String unitCode;

    @Schema(description = "单位名称")
    private String unitName;

}
