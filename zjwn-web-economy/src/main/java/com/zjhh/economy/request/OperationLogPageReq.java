package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperationLogPageReq extends PageReq {
    private static final long serialVersionUID = 8435339169353837674L;

    @Schema(description = "开始时间。格式yyyy-MM-dd")
    private String startDate;

    @Schema(description = "结束时间。格式yyyy-MM-dd")
    private String endDate;

    @Schema(description = "用户姓名")
    private String username;

    @Schema(description = "操作模块")
    private List<String> modules;
}
