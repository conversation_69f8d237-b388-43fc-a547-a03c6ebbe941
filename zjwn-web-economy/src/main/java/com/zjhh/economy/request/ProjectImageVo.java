package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目图片
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectImageVo extends BaseReq {

    @Schema(description = "文件ID")
    private String documentId;

    @Schema(description = "文件名称")
    private String documentName;

    private Float size;


}
