package com.zjhh.economy.request.report;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 发送报告
 *
 * <AUTHOR>
 * @date 2022-05-23 4:11 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportSendReq extends BaseReq {
    private static final long serialVersionUID = 5029876621247519648L;

    @Schema(description = "报告guid")
    List<String> guids;
}
