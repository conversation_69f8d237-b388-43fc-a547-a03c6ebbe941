package com.zjhh.economy.request.analyzereport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业结构Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntStructReq extends SpaceResourceAnalyzeReq {
    private static final long serialVersionUID = 1139988016255820886L;

    @Schema(description = "1-迁入迁出 2-属地分析")
    private Integer type;

}
