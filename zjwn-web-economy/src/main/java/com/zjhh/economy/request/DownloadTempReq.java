package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DownloadTempReq extends BaseReq {
    private static final long serialVersionUID = 2568069213398485872L;

    @Schema(description = "模版类型 enterpriseSettled-入驻模版 enterpriseMove-搬离模版")
    private String tempType;
}
