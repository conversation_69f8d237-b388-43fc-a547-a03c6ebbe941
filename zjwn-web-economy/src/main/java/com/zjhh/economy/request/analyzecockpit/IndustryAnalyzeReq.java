package com.zjhh.economy.request.analyzecockpit;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryAnalyzeReq extends AnalyzeCockpitCommonReq {

    private static final long serialVersionUID = 1504603170984300859L;

    @Schema(description = "1-行业大类2-行业中类")
    private Integer industryType;

    @Schema(description = "行业代码长度", hidden = true)
    private Integer industryLength;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "1-纳税规模2-企业数量3-入驻面积")
    private Integer orderType;
}
