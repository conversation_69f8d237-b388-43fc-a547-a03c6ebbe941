package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WyWorkPlatformReq extends BaseReq {
    private static final long serialVersionUID = 6821434087961176336L;

    @Schema(description = "项目id或楼宇id")
    private String id;
}
