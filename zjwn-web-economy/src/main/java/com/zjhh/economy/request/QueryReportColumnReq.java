package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryReportColumnReq extends BaseReq {
    private static final long serialVersionUID = -8710394187947367435L;

    @Schema(description = "1-查询2-导出")
    private Integer queryType;
}
