package com.zjhh.economy.request.earlywarning;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/20 12:04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateReportDocReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 891640057882065501L;

    @Schema(description = "月份，格式yyyyMM")
    @NotBlank(message = "月份不能为空！")
    private String datekey;

    @Schema(description = "分析报告编码")
    @NotNull(message = "分析报告编码不能为空！")
    private List<String> reportTypeCodes;

    @Schema(hidden = true)
    private Integer type;
}
