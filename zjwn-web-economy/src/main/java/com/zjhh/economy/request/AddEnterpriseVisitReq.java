package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class AddEnterpriseVisitReq extends BaseReq {
    private static final long serialVersionUID = 2728249543366065560L;

    @Schema(description = "编辑的时候传")
    private String id;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "走访目的")
    private String visitPurpose;

    @Schema(description = "走访日期")
    private LocalDate visitDate;

    @Schema(description = "走访人员")
    private String visitor;

    @Schema(description = "对接人")
    private String receptionist;

    @Schema(description = "附件ID")
    private String documentId;

    @Schema(description = "备注")
    private String remark;

}
