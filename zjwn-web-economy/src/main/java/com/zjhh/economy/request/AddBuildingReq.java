package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddBuildingReq extends BaseReq {

    private static final long serialVersionUID = -1926825471358130676L;

    @Schema(description = "楼宇编号")
    private String serialNo;

    @Schema(description = "楼宇名称")
    @NotBlank(message = "楼宇名称不能为空！")
    private String buildingName;

    @Schema(description = "所属项目")
    @NotBlank(message = "所属项目不能为空！")
    private String projectId;

    @Schema(description = "建筑面积")
    @NotNull(message = "建筑面积不能为空！")
    private BigDecimal buildingArea;

    @Schema(description = "商务面积")
    @NotNull(message = "商务面积不能为空！")
    private BigDecimal businessArea;

    @Schema(description = "楼宇投入运行时间")
    @NotNull(message = "楼宇投入运行时间不能为空！")
    private LocalDate operationTime;

    @Schema(description = "楼宇状态")
    @NotBlank(message = "楼宇状态不能为空！")
    private String buildingStatusCode;

    @Schema(description = "楼宇类型")
    @NotBlank(message = "楼宇类型不能为空！")
    private String buildingTypeCode;

    @Schema(description = "负责人")
    @NotBlank(message = "负责人不能为空！")
    private String head;

    @Schema(description = "联系方式")
    @NotBlank(message = "联系方式不能为空！")
    private String phone;

    @Schema(description = "楼宇简介")
    @NotBlank(message = "楼宇简介不能为空！")
    private String introduce;

    @Schema(description = "楼宇外观图")
    @NotBlank(message = "楼宇外观图不能为空！")
    private String outsideImgId;

    @Schema(description = "其它图片")
    private String otherImgId;

    @Schema(description = "楼宇定位")
    @NotNull(message = "楼宇定位不能为空!")
    private List<String> positionCodes;

    @Schema(description = "楼宇标签")
    @NotNull(message = "楼宇标签不能为空!")
    private List<String> labelCodes;

    @Schema(description = "地上停车位")
    private Integer landParkSpace;

    @Schema(description = "地下停车位")
    private Integer undergroundParkSpace;

    @Schema(description = "楼宇配套")
    private List<String> configCodes;

    @Schema(description = "备注")
    private String remark;
}
