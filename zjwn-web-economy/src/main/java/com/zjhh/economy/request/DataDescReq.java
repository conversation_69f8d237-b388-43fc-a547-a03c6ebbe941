package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DataDescReq extends BaseReq {
    private static final long serialVersionUID = 470655898511182780L;

    @Schema(description = "描述code ZL-总览 KJFX-空间分析 JJFX-经济分析 QYFX-企业分析 CYFX-产业分析")
    private String code;
}
