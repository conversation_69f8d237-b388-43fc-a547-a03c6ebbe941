package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryTaxAnalyseReq extends BaseReq {
    private static final long serialVersionUID = -3480775029733606986L;

    private String enterpriseId;


    private String datekey;

    @Schema(description = "时间类型")
    private String updateType;

    @Schema(hidden = true)
    private String lastDatekey;

    public String getLastDatekey() {
        if (datekey.length() == 4) {
            return String.valueOf(Integer.valueOf(datekey) - 1);
        }else {
            return null;
        }
    }
}
