package com.zjhh.economy.request.analyzereport;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 楼宇空置楼层Reqw
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyBuildingFloorReq extends PageReq {
    private static final long serialVersionUID = 4601948553150738716L;

    @Schema(description = "社区")
    private List<String> communities;

    @Schema(description = "项目")
    private List<String> projects;

    @Schema(description = "楼宇")
    private List<String> buildings;

    @Schema(description = "项目类型")
    private List<String> projectTypes;
}
