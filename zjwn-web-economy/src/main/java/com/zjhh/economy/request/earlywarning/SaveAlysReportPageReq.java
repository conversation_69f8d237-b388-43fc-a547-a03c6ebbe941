package com.zjhh.economy.request.earlywarning;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/8/25 10:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveAlysReportPageReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = -1949669566577423583L;

    @Schema(description = "文件标识")
    private String documentKey;

    @Schema(description = "reportTypeCode")
    @NotBlank(message = "reportTypeCode不能为空！")
    private String reportTypeCode;
}
