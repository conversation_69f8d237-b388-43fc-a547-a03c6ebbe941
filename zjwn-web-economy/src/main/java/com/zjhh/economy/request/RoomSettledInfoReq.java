package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RoomSettledInfoReq extends BaseReq {
    private static final long serialVersionUID = -4866291142054362986L;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "房间ID")
    private String roomId;

}
