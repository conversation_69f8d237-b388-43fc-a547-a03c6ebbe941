package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作台企业诉求Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkPlatformEnterpriseDemandReq extends BaseReq {


    private static final long serialVersionUID = -2270029614657754355L;

    @Schema(description = "诉求状态 全部-不传 0-待处理 1-处理中 2-已处理 3-已忽略")
    private Integer demandStatus;

    @Schema(description = "项目id或楼宇id")
    private String id;


}
