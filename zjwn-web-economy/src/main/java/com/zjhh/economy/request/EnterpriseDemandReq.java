package com.zjhh.economy.request;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业诉求Req
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseDemandReq extends PageReq {
    private static final long serialVersionUID = 7129116014559169713L;


    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "提交途径")
    private String submitSource;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

    @Schema(description = "社区 项目 楼宇 code")
    private String code;

    @Schema(description = "诉求类型")
    private String demandType;

    @Schema(description = "诉求状态 全部-不传 0-待处理 1-处理中 2-已处理 3-已忽略")
    private Integer demandStatus;


}
