package com.zjhh.economy.request.report;

import com.zjhh.db.comm.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class EntBusinessInfoReq extends PageReq {
    private static final long serialVersionUID = -5235288351643377761L;

    /* 成立日期范围 */
    @Schema(description = "成立开始日期(yyyy-MM)")
    private String foundDateStart;

    @Schema(description = "成立结束日期(yyyy-MM)")
    private String foundDateEnd;

    /* 企业类型筛选 */
    @Schema(description = "企业类型(树状层级编码)")
    private List<String> enterpriseType;

    /* 注册资本范围 */
    @Schema(description = "注册资本最小值(万元)")
    private BigDecimal registeredCapitalMin;

    @Schema(description = "注册资本最大值(万元)")
    private BigDecimal registeredCapitalMax;

    /* 行业筛选 */
    @Schema(description = "行业分类(树状层级编码)")
    private List<String> industry;

    /* 文本筛选 */
    @Schema(description = "经营范围关键词")
    private String businessScope;

    @Schema(description = "住所关键词")
    private String residence;

}
