package com.zjhh.economy.constants;

import cn.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/22 11:03
 */
public class FileConstants {

    public static final List<String> CAN_PREVIEW_DOC_TYPE = CollUtil.newArrayList("pdf", "jpg", "jpeg", "png", "gif", "tiff", "bmp", "svg", "psd",
            "ai", "raw", "avi", "wmv", "mpg", "mpeg", "mov", "rm", "ram", "swf", "flv", "rmvb", "mp4");

    public static final List<String> CANNOT_PREVIEW_DOC_TYPE = CollUtil.newArrayList("zip", "7z", "rar", "tar", "gzip");

    public static final List<String> PREVIEW_PDF_DOC_TYPE = CollUtil.newArrayList("xls", "xlsx", "doc", "docx", "ppt", "pptx", "txt", "ofd");

    public static final String FILE_COMMON_TYPE = "CommonType";
}
