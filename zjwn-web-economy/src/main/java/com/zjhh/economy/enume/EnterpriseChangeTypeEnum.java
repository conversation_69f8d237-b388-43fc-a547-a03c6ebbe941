package com.zjhh.economy.enume;

/**
 * <AUTHOR>
 * @since 2024/3/11 16:06
 */
public enum EnterpriseChangeTypeEnum {

    /**
     * 新增
     */
    ADD("add","新增"),

    /**
     * 注销
     */

    CANCEL("cancel","注销"),

    /**
     * 迁入
     */
    MOVE_IN("moveIn","迁入"),

    /**
     * 迁出
     */
    MOVE_OUT("moveOut","迁出");




    private final String code;

    private final String name;

    EnterpriseChangeTypeEnum(String code,String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }

        for (EnterpriseChangeTypeEnum changeType : EnterpriseChangeTypeEnum.values()) {
            if (changeType.getCode().equals(code)) {
                return changeType.getName();
            }
        }

        return null;
    }
}
