package com.zjhh.economy.enume;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:43
 */
public enum BuildingExtendTypeEnum {

    /**
     * 定位
     */
    POSITION("position"),

    /**
     * 标签
     */
    LABEL("label"),

    /**
     * 楼宇配套
     */
    CONFIG("config");

    private final String code;

    BuildingExtendTypeEnum(String code) {
        this.code = code;
    }

    public String code() {
        return this.code;
    }
}
