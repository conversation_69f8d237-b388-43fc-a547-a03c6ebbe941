package com.zjhh.economy.enume;

import com.zjhh.comm.exception.BizException;

/**
 * <AUTHOR>
 * @since 2022/12/14 14:11
 */
public enum DataPermissionType {

    /**
     * 楼宇
     */
    BUILDING(1);

    private final int value;

    DataPermissionType(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }

    public static DataPermissionType intToEnum(int value) {
        switch (value) {
            case 1:
                return BUILDING;
            default:
                throw new BizException("数据权限类型错误！");
        }
    }
}
