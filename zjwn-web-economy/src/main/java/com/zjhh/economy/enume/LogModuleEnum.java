package com.zjhh.economy.enume;

import lombok.Getter;

import java.util.Objects;

/**
 * 日志操作模块
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Getter
public enum LogModuleEnum {

    PROJECT("PROJECT", "项目管理"),

    BUILDING("BUILDING", "楼宇管理"),

    ROOM("ROOM", "房源管理"),

    DEMAND("DEMAND", "企业诉求");

    private final String code;

    private final String name;

    LogModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getModuleName(String moduleCode) {
        for (LogModuleEnum value : LogModuleEnum.values()) {
            if (Objects.equals(value.code, moduleCode)) {
                return value.name;
            }
        }
        return null;
    }
}
