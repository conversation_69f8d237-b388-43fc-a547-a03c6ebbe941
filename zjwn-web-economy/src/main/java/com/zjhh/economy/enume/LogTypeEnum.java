package com.zjhh.economy.enume;

import lombok.Getter;

import java.util.Objects;

/**
 * 日志操作类型枚举
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Getter
public enum LogTypeEnum {

    PROJECT_ADD("PROJECT_ADD", "新建项目", LogModuleEnum.PROJECT),

    PROJECT_EDIT("PROJECT_EDIT", "编辑项目", LogModuleEnum.PROJECT),

    PROJECT_DELETE("PROJECT_DELETE", "删除项目", LogModuleEnum.PROJECT),


    BUILDING_ADD("BUILDING_ADD", "添加楼宇", LogModuleEnum.BUILDING),

    BUILDING_EDIT("BUILDING_EDIT", "编辑楼宇", LogModuleEnum.BUILDING),

    BUILDING_DELETE("BUILDING_DELETE", "删除楼宇", LogModuleEnum.BUILDING),

    BUILDING_FLOOR_CONFIG("BUILDING_FLOOR_CONFIG", "楼层配置", LogModuleEnum.BUILDING),

    BUILDING_ROOM_ADD("BUILDING_ROOM_ADD", "添加房源", LogModuleEnum.BUILDING),

    BUILDING_ROOM_EDIT("BUILDING_ROOM_EDIT", "修改房间信息", LogModuleEnum.BUILDING),


    ROOM_ADD("ROOM_ADD", "添加房源", LogModuleEnum.ROOM),

    ROOM_EDIT("ROOM_EDIT", "修改房间信息", LogModuleEnum.ROOM),

    ROOM_DELETE("ROOM_DELETE", "删除房源", LogModuleEnum.ROOM),

    ROOM_SETTLE_ADD("ROOM_SETTLE_ADD", "添加入驻信息", LogModuleEnum.ROOM),

    ROOM_SETTLE_EDIT("ROOM_SETTLE_EDIT", "编辑入驻信息", LogModuleEnum.ROOM),

    ROOM_SETTLE_REMOVE("ROOM_SETTLE_REMOVE", "搬离登记", LogModuleEnum.ROOM),


    DEMAND_ADD("DEMAND_ADD", "录入诉求", LogModuleEnum.DEMAND),

    DEMAND_EDIT("DEMAND_EDIT", "编辑诉求", LogModuleEnum.DEMAND),

    DEMAND_DELETE("DEMAND_DELETE", "删除诉求", LogModuleEnum.DEMAND),

    DEMAND_HANDLE("DEMAND_HANDLE", "处理诉求", LogModuleEnum.DEMAND),


    ;

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 所属操作模块
     */
    private final LogModuleEnum module;

    LogTypeEnum(String code, String name, LogModuleEnum module) {
        this.code = code;
        this.name = name;
        this.module = module;
    }

    public static String getTypeName(String typeCode) {
        for (LogTypeEnum value : LogTypeEnum.values()) {
            if (Objects.equals(value.code, typeCode)) {
                return value.name;
            }
        }
        return null;
    }
}
