package com.zjhh.economy.enume;

/**
 * <AUTHOR>
 * @since 2024/3/11 16:06
 */
public enum ImportTypeEnum {

    /**
     * 批量入驻
     */
    ENTERPRISE_SETTLE("enterpriseSettled","批量入驻"),

    /**
     * 搬离
     */

    ENTERPRISE_MOVE("enterpriseMove","批量搬离");



    private final String code;

    private final String name;

    ImportTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }

        for (ImportTypeEnum changeType : ImportTypeEnum.values()) {
            if (changeType.getCode().equals(code)) {
                return changeType.getName();
            }
        }

        return null;
    }
}
