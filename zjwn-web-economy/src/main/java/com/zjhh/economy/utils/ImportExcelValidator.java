package com.zjhh.economy.utils;

import cn.hutool.core.util.StrUtil;
import com.zjhh.economy.enume.ImportTypeEnum;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;
import java.util.concurrent.atomic.AtomicReference;

public class ImportExcelValidator {

    public static Boolean validateExcel(InputStream inputStream, String fileName, String importType) {
        AtomicReference<Boolean> result = new AtomicReference<>(true);

        try {
            Workbook workbook;
            if (fileName.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else if (fileName.endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                return false;
            }

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(1);
            if (sheet == null) {
                return false;
            }

            // 检查表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return false;
            }


            // 验证表头是否符合预期
            String[] expectedHeaders = null;
            if (importType.equals(ImportTypeEnum.ENTERPRISE_SETTLE.getCode())) {
                expectedHeaders = new String[] {"企业名称*", "统一社会信用代码", "法定代表人", "电话", "企业联系人*", "联系电话*", "项目", "楼宇*", "楼层*", "房号*"
                        , "入驻开始日期*", "租赁到期时间", "入驻面积（㎡）", "装修期开始日期", "装修期截止日期", "备注"};
            }else {
                expectedHeaders = new String[] {"企业名称*", "统一社会信用代码", "项目", "楼宇*", "楼层", "房号*"
                        , "实际搬离日期*", "去向登记", "迁出原因", "备注"};
            }

        for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell == null || !cell.getStringCellValue().trim().startsWith(expectedHeaders[i])) {
                    return false;
                }
            }


            workbook.close();
        } catch (Exception e) {
            return false;
        }

        return result.get();
    }

    public static boolean isValidDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return true;
        }

        try {

            cn.hutool.core.date.DateUtil.parseDate(dateStr);
            // 额外检查日期格式长度，确保格式为yyyy-MM-dd
            return dateStr.matches("\\d{4}-\\d{2}-\\d{2}");
        } catch (Exception e) {
            return false;
        }
    }
}
