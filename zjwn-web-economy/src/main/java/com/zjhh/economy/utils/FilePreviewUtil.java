package com.zjhh.economy.utils;

import cn.hutool.core.io.FileUtil;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.utils.AsposeUtil;
import com.zjhh.economy.constants.FileConstants;
import com.zjhh.economy.dto.ResourceDto;

import java.io.File;

/**
 * <AUTHOR>
 * @since 2023/6/2 10:14
 */
public class FilePreviewUtil {

    public static ResourceDto preview(String inPath, String filename) {
        ResourceDto dto = new ResourceDto();
        File file = new File(inPath);
        if (!file.exists()) {
            throw new BizException("该文件不存在或已删除！");
        }
        String originalSuffix = FileUtil.getSuffix(file);
        String suffix = originalSuffix.toLowerCase();
        if (FileConstants.CAN_PREVIEW_DOC_TYPE.contains(suffix)) {
            dto.setFileName(filename);
            dto.setFilePath(inPath);
            return dto;
        }
        if (FileConstants.CANNOT_PREVIEW_DOC_TYPE.contains(suffix)) {
            throw new BizException("该文件不支持预览！");
        }
        if (FileConstants.PREVIEW_PDF_DOC_TYPE.contains(suffix)) {
            String filePath = inPath.replace("." + originalSuffix, ".pdf");
            File pdf = new File(filePath);
            if (!pdf.exists()) {
                AsposeUtil.convertToPdf(suffix, inPath, pdf);
            }
            dto.setFilePath(filePath);
            dto.setFileName(filename);
        }
        return dto;
    }
}
