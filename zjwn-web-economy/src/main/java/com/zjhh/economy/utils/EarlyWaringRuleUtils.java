package com.zjhh.economy.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aspose.words.*;
import com.aspose.words.Font;
import com.aspose.words.Shape;
import com.aspose.words.Stroke;
import com.zjhh.comm.utils.AsposeUtil;
import com.zjhh.economy.dto.*;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/6/2 10:05
 */
public class EarlyWaringRuleUtils {

    public static void drawDoc(List<DocDto> list, File file, int saveFormat, Map<String, DocTableFieldDto> tableValueMap) throws Exception {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        FileOutputStream fos = new FileOutputStream(file);
        AsposeUtil.init();
        Document doc = new Document();
        DocumentBuilder builder = new DocumentBuilder(doc);
        builder.getPageSetup().setPaperSize(PaperSize.A4);
        builder.getPageSetup().setOrientation(Orientation.PORTRAIT);
        builder.getPageSetup().setVerticalAlignment(PageVerticalAlignment.TOP);
        builder.getPageSetup().setLeftMargin(100);
        builder.getPageSetup().setRightMargin(100);
        Font font;
        for (DocDto docDto : list) {
            switch (docDto.getType()) {
                case DocDto.TEXT:
                    DocTextDto textDto = (DocTextDto) docDto;
                    if (textDto.getMargin() > 0) {
                        builder.getParagraphFormat().clearFormatting();
                        font = builder.getFont();
                        font.clearFormatting();
                        font.setSize(textDto.getMargin());
                        builder.writeln();
                        docBuilderText(textDto, builder);
                        builder.getParagraphFormat().clearFormatting();
                        font = builder.getFont();
                        font.clearFormatting();
                        font.setSize(textDto.getMargin());
                        builder.writeln();
                    } else {
                        docBuilderText(textDto, builder);
                    }
                    break;
                case DocDto.LINE:
                    DocLineDto lineDto = (DocLineDto) docDto;
                    if (lineDto.getMargin() > 0) {
                        builder.getParagraphFormat().clearFormatting();
                        font = builder.getFont();
                        font.clearFormatting();
                        font.setSize(lineDto.getMargin());
                        builder.writeln();
                    }
                    docBuilderLine(lineDto, builder);
                    builder.getParagraphFormat().clearFormatting();
                    font = builder.getFont();
                    font.clearFormatting();
                    font.setSize(lineDto.getMargin() + lineDto.getHeight());
                    builder.writeln();
                    break;
                case DocDto.TABLE:
                    DocTableDto tableDto = (DocTableDto) docDto;
                    docBuilderTable(tableDto, builder, tableValueMap.get(docDto.getId()));
                    builder.getParagraphFormat().clearFormatting();
                    font = builder.getFont();
                    font.clearFormatting();
                    font.setSize(10);
                    builder.writeln();
                    break;
            }
        }
        doc.save(fos, saveFormat);
    }

    private static void docBuilderText(DocTextDto textDto, DocumentBuilder builder) {
        ParagraphFormat ph = builder.getParagraphFormat();
        ph.clearFormatting();
        ph.setAlignment(textDto.getDocTextAlign());
        Double lineHeight = textDto.getLineHeight();
        if (ObjectUtil.isNull(lineHeight) || lineHeight == 0) {
            lineHeight = 1d;
        }
        ph.setLineSpacing(lineHeight * 12);
        Font font = builder.getFont();
        font.clearFormatting();
        font.setSize(textDto.getFontSize());
        font.setColor(new Color(hex2Int(textDto.getColor())));
        font.setBold(textDto.isBold());
        if (textDto.isUnderline()) {
            font.setUnderline(Underline.SINGLE);
        } else {
            font.setUnderline(Underline.NONE);
        }
        font.setItalic(textDto.isItalic());
        font.setNameFarEast(textDto.getFontFamily());
        ph.getShading().setBackgroundPatternColor(new Color(hex2Int(textDto.getBackgroundColor())));
        builder.writeln(UnicodeUtil.toString(textDto.getText()));
        font.clearFormatting();
    }

    private static void docBuilderLine(DocLineDto lineDto, DocumentBuilder builder) throws Exception {
        builder.getParagraphFormat().clearFormatting();
        Shape lineShape = new Shape(builder.getDocument(), ShapeType.LINE);
        double lineWidth;
        if (lineDto.getWidth() >= 100) {
            lineWidth = builder.getPageSetup().getPageWidth() - 200;
        } else {
            lineWidth = (builder.getPageSetup().getPageWidth() - 200) * lineDto.getWidth() / 100;
        }
        lineShape.setWidth(lineWidth);
        lineShape.setHorizontalAlignment(lineDto.getDocLineAlign());
        Stroke stroke = lineShape.getStroke();
        stroke.setColor(new Color(hex2Int(lineDto.getColor())));
        stroke.setDashStyle(lineDto.getDocDashStyle());
        stroke.setWeight(lineDto.getHeight() == null ? 1 : lineDto.getHeight());
        builder.insertNode(lineShape);
    }

    private static void docBuilderTable(DocTableDto tableDto, DocumentBuilder builder, DocTableFieldDto fieldDto) throws Exception {
        Table table = builder.startTable();
        builder.getParagraphFormat().clearFormatting();
        RowFormat rowFormat = builder.getRowFormat();
        rowFormat.setHeight(tableDto.getLineHeight());
        List<DocTableColumnDto> children = tableDto.getColumns();
        if (CollUtil.isEmpty(children)) {
            return;
        }
        calWordWidth(tableDto.getColumns(), 395.3);
        DocTableColumnDto[][] arr = createColumnArr(tableDto);
        Map<Cell, Double> cellMap = new HashMap<>();
        Font font;
        DocTableColumnDto dto;
        for (DocTableColumnDto[] docTableColumnDtos : arr) {
            for (int j = 0; j < arr[0].length; j++) {
                dto = docTableColumnDtos[j];
                Cell cell = builder.insertCell();
                cellMap.put(cell, dto.getWordWidth());
                int cellMergeType = ObjectUtil.isNotNull(dto.getCellMergeType()) ? dto.getCellMergeType() : 5;
                switch (cellMergeType) {
                    case 1:
                        builder.getCellFormat().clearFormatting();
                        builder.getCellFormat().setHorizontalMerge(CellMerge.FIRST);
                        builder.getCellFormat().setVerticalMerge(CellMerge.NONE);
                        font = builder.getFont();
                        font.clearFormatting();
                        font.setSize(dto.getTh().getFontSize());
                        font.setNameFarEast(dto.getTh().getFontFamily());
                        font.setColor(new Color(hex2Int(dto.getTh().getColor())));
                        font.setBold(dto.getTh().isBold());
                        if (dto.getTh().isUnderline()) {
                            font.setUnderline(Underline.SINGLE);
                        } else {
                            font.setUnderline(Underline.NONE);
                        }
                        font.setItalic(dto.getTh().isItalic());
                        builder.getCellFormat().getShading().setBackgroundPatternColor(new Color(hex2Int(dto.getTh().getBackgroundColor())));
                        builder.getCellFormat().setVerticalAlignment(VerticalAlignment.TOP);
                        builder.getParagraphFormat().setAlignment(dto.getTh().getDocTextAlign());
                        builder.write(UnicodeUtil.toString(dto.getTitle()));
                        break;
                    case 2:
                        builder.getCellFormat().clearFormatting();
                        builder.getCellFormat().setHorizontalMerge(CellMerge.PREVIOUS);
                        builder.getCellFormat().setVerticalMerge(CellMerge.NONE);
                        break;
                    case 3:
                        builder.getCellFormat().clearFormatting();
                        builder.getCellFormat().setHorizontalMerge(CellMerge.NONE);
                        builder.getCellFormat().setVerticalMerge(CellMerge.FIRST);
                        font = builder.getFont();
                        font.clearFormatting();
                        font.setSize(dto.getTh().getFontSize());
                        font.setNameFarEast(dto.getTh().getFontFamily());
                        font.setColor(new Color(hex2Int(dto.getTh().getColor())));
                        font.setBold(dto.getTh().isBold());
                        if (dto.getTh().isUnderline()) {
                            font.setUnderline(Underline.SINGLE);
                        } else {
                            font.setUnderline(Underline.NONE);
                        }
                        font.setItalic(dto.getTh().isItalic());
                        builder.getCellFormat().getShading().setBackgroundPatternColor(new Color(hex2Int(dto.getTh().getBackgroundColor())));
                        builder.getCellFormat().setRightPadding(0);
                        builder.getCellFormat().setVerticalAlignment(VerticalAlignment.TOP);
                        builder.getParagraphFormat().setAlignment(dto.getTh().getDocTextAlign());
                        builder.write(UnicodeUtil.toString(dto.getTitle()));
                        break;
                    case 4:
                        builder.getCellFormat().clearFormatting();
                        builder.getCellFormat().setHorizontalMerge(CellMerge.NONE);
                        builder.getCellFormat().setVerticalMerge(CellMerge.PREVIOUS);
                        break;
                    case 5:
                        builder.getCellFormat().clearFormatting();
                        builder.getCellFormat().setHorizontalMerge(CellMerge.NONE);
                        builder.getCellFormat().setVerticalMerge(CellMerge.NONE);
                        font = builder.getFont();
                        font.clearFormatting();
                        font.setSize(dto.getTh().getFontSize());
                        font.setNameFarEast(dto.getTh().getFontFamily());
                        font.setColor(new Color(hex2Int(dto.getTh().getColor())));
                        font.setBold(dto.getTh().isBold());
                        if (dto.getTh().isUnderline()) {
                            font.setUnderline(Underline.SINGLE);
                        } else {
                            font.setUnderline(Underline.NONE);
                        }
                        font.setItalic(dto.getTh().isItalic());
                        builder.getCellFormat().setVerticalAlignment(VerticalAlignment.TOP);
                        builder.getCellFormat().getShading().setBackgroundPatternColor(new Color(hex2Int(dto.getTh().getBackgroundColor())));
                        builder.getParagraphFormat().setAlignment(dto.getTh().getDocTextAlign());
                        builder.write(UnicodeUtil.toString(dto.getTitle()));
                        break;
                }
            }
            builder.endRow();
        }
        cellMap.forEach((k, v) -> k.getCellFormat().setWidth(v));
        if (CollUtil.isNotEmpty(fieldDto.getValues())) {
            fieldDto.getValues().forEach(map -> {
                fieldDto.getColumns().forEach(column -> {
                    builder.insertCell();
                    builder.getCellFormat().clearFormatting();
                    builder.getCellFormat().setHorizontalMerge(CellMerge.NONE);
                    builder.getCellFormat().setVerticalMerge(CellMerge.NONE);
                    builder.getCellFormat().setVerticalAlignment(VerticalAlignment.TOP);
                    builder.getParagraphFormat().setAlignment(column.getTd().getDocTextAlign());
                    Font filedFont = builder.getFont();
                    filedFont.clearFormatting();
                    filedFont.setSize(column.getTd().getFontSize());
                    filedFont.setNameFarEast(column.getTd().getFontFamily());
                    filedFont.setColor(new Color(hex2Int(column.getTd().getColor())));
                    filedFont.setBold(column.getTd().isBold());
                    if (column.getTd().isUnderline()) {
                        filedFont.setUnderline(Underline.SINGLE);
                    } else {
                        filedFont.setUnderline(Underline.NONE);
                    }
                    filedFont.setItalic(column.getTd().isItalic());
                    String value;
                    if (StrUtil.isBlank(column.getField()) || !column.getField().contains(".")) {
                        value = "";
                    } else {
                        String field = column.getField().split("\\.")[1];
                        value = map.get(field);
                        if ("number".equals(column.getTd().getType())) {
                            if (StrUtil.isBlank(value)) {
                                value = "0";
                            } else {
                                String format;
                                if (column.getTd().getThousandth()) {
                                    format = ",###";
                                } else {
                                    format = "#";
                                }
                                if (column.getTd().getDecimal() > 0) {
                                    format = StrUtil.fillAfter(format + ".", '#', format.length() + 3);
                                }
                                value = NumberUtil.decimalFormat(format, new BigDecimal(value), RoundingMode.HALF_UP);
                            }
                        } else {
                            if (StrUtil.isBlank(value)) {
                                value = "";
                            }
                        }
                    }

                    builder.write(UnicodeUtil.toString(value));
                });
                builder.endRow();
            });
        }
        table.clearBorders();
        if (tableDto.getBorderWidth() > 0) {
            String borderType = StrUtil.isBlank(tableDto.getBorderType()) ? "A" : tableDto.getBorderType();
            switch (borderType) {
                case "B":
                    table.setBorder(BorderType.BOTTOM, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "P":
                    table.setBorder(BorderType.TOP, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "L":
                    table.setBorder(BorderType.LEFT, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "R":
                    table.setBorder(BorderType.RIGHT, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "A":
                    table.setBorders(LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())));
                    break;
                case "S":
                    table.setBorder(BorderType.BOTTOM, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    table.setBorder(BorderType.TOP, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    table.setBorder(BorderType.LEFT, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    table.setBorder(BorderType.RIGHT, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "I":
                    table.setBorder(BorderType.HORIZONTAL, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    table.setBorder(BorderType.VERTICAL, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "H":
                    table.setBorder(BorderType.HORIZONTAL, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "V":
                    table.setBorder(BorderType.VERTICAL, LineStyle.SINGLE, tableDto.getBorderWidth(), new Color(hex2Int(tableDto.getBorderColor())), true);
                    break;
                case "NONE":
                default:
                    break;
            }
        }
        if (ObjectUtil.isNotNull(tableDto.getShowTitle()) && !tableDto.getShowTitle()) {
            cellMap.forEach((k, v) -> k.remove());
        }
        builder.endTable();
    }

    private static int[] hex2RGB(String hexStr) {
        if (hexStr != null && !"".equals(hexStr) && hexStr.length() == 7) {
            int[] rgb = new int[3];
            rgb[0] = Integer.valueOf(hexStr.substring(1, 3), 16);
            rgb[1] = Integer.valueOf(hexStr.substring(3, 5), 16);
            rgb[2] = Integer.valueOf(hexStr.substring(5, 7), 16);
            return rgb;
        }
        return null;
    }

    private static int rgbToInt(int[] rgb) {
        int r = (rgb[0] << 16) & 0x00ff0000;
        int g = (rgb[1] << 8) & 0x0000FF00;
        int b = rgb[2] & 0x000000FF;
        return 0xFF000000 | r | g | b;
    }

    private static int hex2Int(String hexStr) {
        return rgbToInt(hex2RGB(hexStr));
    }

    private static void calWordWidth(List<DocTableColumnDto> list, double totalWordWidth) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        double widthAll = 0;
        for (DocTableColumnDto dto : list) {
            widthAll += dto.getTh().getWidth();
        }
        double sumWidth = 0;
        for (int i = 0; i < list.size(); i++) {
            DocTableColumnDto dto = list.get(i);
            double thWordWidth;
            if (i == (list.size() - 1)) {
                thWordWidth = Double.parseDouble(String.format("%.1f", totalWordWidth - sumWidth));
            } else {
                thWordWidth = calWidth(dto.getTh().getWidth(), widthAll, totalWordWidth);
                sumWidth += thWordWidth;
            }
            dto.setWordWidth(thWordWidth);
            calWordWidth(dto.getChildren(), thWordWidth);
        }
    }

    private static double calWidth(double width, double widthAll, double totalWordWidth) {
        return Double.parseDouble(String.format("%.1f", width / widthAll * totalWordWidth)); // 395.3
    }

    private static DocTableColumnDto[][] createColumnArr(DocTableDto tableDto) {
        DocTableColumnDto columnDto = new DocTableColumnDto();
        columnDto.setChildren(tableDto.getColumns());
        int row = columnDto.getMaxDepth() - 1;
        int col = columnDto.getMaxWeight();
        DocTableColumnDto[][] arr = new DocTableColumnDto[row][col];
        createRowArr(arr, columnDto.getChildren(), 0, 0);
        for (int i = 0; i < arr.length; i++) {
            for (int j = 0; j < arr[i].length; j++) {
                if (ObjectUtil.isNull(arr[i][j])) {
                    DocTableColumnDto temp = arr[i - 1][j];
                    if (ObjectUtil.isNull(temp.getCellMergeType())) {
                        temp.setCellMergeType(3);
                    }
                    arr[i][j] = new DocTableColumnDto(4, temp.getWordWidth());
                }
            }
        }
        return arr;
    }

    private static void createRowArr(DocTableColumnDto[][] arr, List<DocTableColumnDto> columnDtos, int startRow, int startCol) {
        for (DocTableColumnDto columnDto : columnDtos) {
            int weight = columnDto.getMaxWeight();
            createRowArr(arr, columnDto.getChildren(), startRow + 1, startCol);
            arr[startRow][startCol++] = columnDto;
            if (weight > 1) {
                columnDto.setCellMergeType(1);
                for (int i = 1; i < weight; i++) {
                    arr[startRow][startCol++] = new DocTableColumnDto(2, 0d);
                }
            }
        }
    }
}
