package com.zjhh.economy.utils;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zjhh.economy.dao.entity.AdsOperationConfig;
import com.zjhh.economy.dao.entity.AdsOperationLog;
import com.zjhh.economy.dao.mapper.AdsOperationLogMapper;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.vo.operationlog.CommonLogResultVo;
import com.zjhh.economy.vo.operationlog.CommonLogVo;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Component
public class OperationLogUtil {

    @Resource
    private AdsOperationLogMapper adsOperationLogMapper;

    @Resource
    private UserSession userSession;

    private static final int COLOR_NO = 0;

    private static final int COLOR_EDIT = 1;

    private static final int COLOR_ADD = 2;

    private static final int COLOR_DELETE = 3;

    /**
     * 添加操作日志
     *
     * @param module        操作模块
     * @param type          操作类型
     * @param objectId      操作对象id
     * @param objectName    操作对象名称
     * @param jsonStrBefore 操作前数据的json字符串
     * @param jsonStrAfter  操作后数据的json字符串
     */
    public void recordLog(LogModuleEnum module, LogTypeEnum type, String objectId, String objectName,
                          String jsonStrBefore, String jsonStrAfter) {
        AdsOperationLog adsOperationLog = new AdsOperationLog();
        adsOperationLog.setId(IdUtil.getSnowflakeNextIdStr());
        adsOperationLog.setCreateTime(LocalDateTime.now());
        if(StpUtil.isLogin()){
            adsOperationLog.setCreateUser(userSession.getUserCode());
        } else {
            // 游客提交企业诉求的情况
            adsOperationLog.setCreateUser("qiye");
        }
        adsOperationLog.setModule(module.getCode());
        adsOperationLog.setType(type.getCode());
        adsOperationLog.setObjectId(objectId);
        adsOperationLog.setObjectName(objectName);
        adsOperationLog.setDataBefore(jsonStrBefore);
        adsOperationLog.setDataAfter(jsonStrAfter);
        adsOperationLogMapper.insert(adsOperationLog);
    }

    public TreeMap<String, String> getFieldMap(List<AdsOperationConfig> operationConfigs) {
        List<AdsOperationConfig> validConfig = operationConfigs.stream().filter(config -> config != null && config.getShow())
                .sorted(Comparator.comparing(AdsOperationConfig::getSort)).collect(Collectors.toList());
        Map<String, Integer> sortMap = new HashMap<>();
        TreeMap<String, String> fieldMap = new TreeMap<>(Comparator.comparing(sortMap::get));
        if (CollUtil.isEmpty(validConfig)) {
            return fieldMap;
        }
        for (AdsOperationConfig config : validConfig) {
            sortMap.put(config.getColField(), config.getSort());
            fieldMap.put(config.getColField(), config.getColDesc());
        }
        return fieldMap;
    }

    /**
     * 比对数据并返回结果
     *
     * @param dataBefore       操作前数据
     * @param dataAfter        操作后数据
     * @param operationConfigs 操作配置（哪些列显示，列排序等）
     */
    public CommonLogResultVo contrast(JSONObject dataBefore, JSONObject dataAfter, List<AdsOperationConfig> operationConfigs) {
        List<AdsOperationConfig> validConfig = operationConfigs.stream().filter(config -> config != null && config.getShow())
                .sorted(Comparator.comparing(AdsOperationConfig::getSort)).collect(Collectors.toList());
        CommonLogResultVo resultVo = new CommonLogResultVo();
        resultVo.setFieldMap(getFieldMap(operationConfigs));
        resultVo.setOptBefore(new ArrayList<>());
        resultVo.setOptAfter(new ArrayList<>());
        if (CollUtil.isEmpty(validConfig)) {
            return resultVo;
        }
        if (dataBefore == null && dataAfter == null) {
            return resultVo;
        }

        Map<String, Integer> sortMap = new HashMap<>();
        TreeMap<String, Integer> contrastMap = new TreeMap<>(Comparator.comparing(sortMap::get));
        for (AdsOperationConfig config : validConfig) {
            sortMap.put(config.getColField(), config.getSort());
            if (dataBefore == null) {
                contrastMap.put(config.getColField(), COLOR_ADD);
            } else if (dataAfter == null) {
                contrastMap.put(config.getColField(), COLOR_DELETE);
            } else {
                // 编辑默认没有颜色，除非字段改的不一样了
                contrastMap.put(config.getColField(), COLOR_NO);
            }
        }
        if (dataBefore != null && dataAfter != null) {
            // 字段比对。不修改源json对象
            for (AdsOperationConfig config : validConfig) {
                String colField = config.getColField();
                // 字段不同，返回true，高亮显示（两数组顺序不同，也视为不同，调用前应先排序）
                if (!Objects.equals(dataBefore.get(colField), dataAfter.get(colField))) {
                    contrastMap.put(colField, COLOR_EDIT);
                }
            }
        }
        if (dataAfter != null) {
            resultVo.getOptAfter().add(new CommonLogVo(generateResultData(dataAfter, sortMap), contrastMap));
        }
        if (dataBefore != null) {
            resultVo.getOptBefore().add(new CommonLogVo(generateResultData(dataBefore, sortMap), contrastMap));
        }
        return resultVo;
    }

    private TreeMap<String, Object> generateResultData(JSONObject jsonObject, Map<String, Integer> sortMap) {
        TreeMap<String, Object> resultData = new TreeMap<>(Comparator.comparing(sortMap::get));
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (sortMap.containsKey(entry.getKey())) {
                resultData.put(entry.getKey(), entry.getValue());
            }
        }
        return resultData;
    }

}
