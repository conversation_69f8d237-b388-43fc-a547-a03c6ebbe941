package com.zjhh.economy.ueditor.upload;

import com.zjhh.economy.ueditor.define.State;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-03-29 5:40 下午
 */
public class Uploader {
    private HttpServletRequest request = null;
    private Map<String, Object> conf = null;

    public Uploader(HttpServletRequest request, Map<String, Object> conf) {
        this.request = request;
        this.conf = conf;
    }

    public final State doExec() {
        String filedName = (String) this.conf.get("fieldName");
        State state = null;

        if ("true".equals(this.conf.get("isBase64"))) {
            state = Base64Uploader.save(this.request.getParameter(filedName),
                    this.conf);
        } else {
            state = BinaryUploader.save(this.request, this.conf);
        }

        return state;
    }

}
