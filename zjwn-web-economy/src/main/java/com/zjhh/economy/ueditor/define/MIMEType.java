package com.zjhh.economy.ueditor.define;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-03-29 5:31 下午
 */
public class MIMEType {

    public static final Map<String, String> types = new HashMap<String, String>() {{
        put("image/gif", ".gif");
        put("image/jpeg", ".jpg");
        put("image/jpg", ".jpg");
        put("image/png", ".png");
        put("image/bmp", ".bmp");
    }};

    public static String getSuffix(String mime) {
        return MIMEType.types.get(mime);
    }

}
