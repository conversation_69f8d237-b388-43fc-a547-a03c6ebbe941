package com.zjhh.economy.service;


import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.report.WarningReportPageReq;
import com.zjhh.economy.request.report.WarningReportUploadReq;
import com.zjhh.economy.vo.report.WarningReportListVo;

public interface WarningReportService {

    /**
     * 预警列表
     *
     * @param req
     * @return
     */
    Page<WarningReportListVo> listWarningReport(WarningReportPageReq req);

    /**
     * 上传报告
     *
     * @param req
     */
    void uploadReport(WarningReportUploadReq req);

    /**
     * 删除报告
     *
     * @param guid
     */
    void deleteReport(String guid);


}
