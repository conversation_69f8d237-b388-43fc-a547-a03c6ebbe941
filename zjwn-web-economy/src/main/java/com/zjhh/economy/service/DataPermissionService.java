package com.zjhh.economy.service;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.comm.vo.TreeSelectedVo;
import com.zjhh.economy.dao.entity.AdsPmBuilding;
import com.zjhh.economy.request.datapermission.AuthDataPermissionReq;
import com.zjhh.economy.request.datapermission.TreeDataPermissionReq;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/24 上午10:47
 */
public interface DataPermissionService {

    /**
     * 数据权限-组织结构和用户树形结构
     *
     * @return
     */
    List<TreeSelectVo> treeOrganizeUser();

    /**
     * 获取用户数据权限树形
     *
     * @param req
     * @return
     */
    TreeSelectedVo treeDataPermission(TreeDataPermissionReq req);

    /**
     * 数据权限授权
     *
     * @param req
     */
    void authDataPermission(AuthDataPermissionReq req);

    /**
     * 测试代码
     *
     * @return
     */
    List<AdsPmBuilding> test();
}
