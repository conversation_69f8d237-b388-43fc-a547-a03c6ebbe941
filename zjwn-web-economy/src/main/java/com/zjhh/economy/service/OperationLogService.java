package com.zjhh.economy.service;

import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.OperationLogPageReq;
import com.zjhh.economy.vo.operationlog.CommonLogResultVo;
import com.zjhh.economy.vo.operationlog.OperationLogVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
public interface OperationLogService {

    /**
     * 模块列表
     *
     * @return
     */
    List<SingleSelectVo> listModule();

    /**
     * 获取操作日志详情
     *
     * @param idReq
     * @return
     */
    CommonLogResultVo getOperationLogDetail(IdReq idReq);

    /**
     * 操作日志列表
     *
     * @return
     */
    Page<OperationLogVo> pageOperationLogs(OperationLogPageReq req);

    /**
     * 导出操作日志
     *
     * @param req
     * @return
     */
    List<OperationLogVo> exportOperationLogs(OperationLogPageReq req);
}
