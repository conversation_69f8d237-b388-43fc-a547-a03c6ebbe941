package com.zjhh.economy.service.impl;

import com.zjhh.economy.dao.mapper.AdsBasScreenViewMapper;
import com.zjhh.economy.service.CockpitService;
import com.zjhh.economy.vo.cockpit.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class CockpitServiceImpl implements CockpitService {

    @Resource
    private AdsBasScreenViewMapper adsBasScreenViewMapper;


    @Override
    public List<EconomicIndicatorVo> listEconomicIndicator() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        String currentYearMonth = dateFormat.format(new Date());
        Calendar calendar = Calendar.getInstance();
        // 往前推12个月
        calendar.add(Calendar.MONTH, -12);
        // 将Calendar对象转换为Date对象
        Date twelveMonthsAgo = calendar.getTime();
        String preDatekey = dateFormat.format(twelveMonthsAgo);
        return adsBasScreenViewMapper.listEconomicIndicator(preDatekey, currentYearMonth);
    }

    @Override
    public List<IndustryStructVo> listIndustryStruct() {
        return adsBasScreenViewMapper.listIndustryStruct(currentYearDate());
    }

    @Override
    public List<IndustryEntStructVo> listIndustryEntStruct() {
        return adsBasScreenViewMapper.listIndustryEntStruct(currentYearDate());
    }

    @Override
    public List<EntTaxPaidVo> listEntTaxPaid() {
        return adsBasScreenViewMapper.listEntTaxPaid(currentYearDate());
    }

    @Override
    public List<EntTaxPaidRankVo> listEntTaxPaidRank() {
        return adsBasScreenViewMapper.listEntTaxPaidRank(currentYearDate());
    }

    @Override
    public List<BuildingWarningVo> listBuildingWarning() {
        return adsBasScreenViewMapper.listBuildingWarning(currentYearDate());
    }

    @Override
    public List<EntCountVo> listEntRegisterCount() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        String currentYearMonth = dateFormat.format(new Date());
        Calendar calendar = Calendar.getInstance();
        // 往前推12个月
        calendar.add(Calendar.MONTH, -12);
        // 将Calendar对象转换为Date对象
        Date twelveMonthsAgo = calendar.getTime();
        String preDatekey = dateFormat.format(twelveMonthsAgo);
        return adsBasScreenViewMapper.listEntRegisterCount(preDatekey, currentYearMonth);
    }

    @Override
    public List<BuildingRankVo> listBuildingRank() {
        return adsBasScreenViewMapper.listBuildingRank(currentYearDate());
    }

    @Override
    public BuildingSummaryVo getBuildingSummary() {
        return adsBasScreenViewMapper.getBuildingSummary(currentYearDate());
    }

    @Override
    public List<BuildingMapInfoUsedVo> listBuildingMapInfoUsed() {
        return adsBasScreenViewMapper.listBuildingMapInfoUsed(currentYearDate());
    }

    @Override
    public List<BuildingMapInfoUnusedVo> listBuildingMapInfoUnused() {
        return adsBasScreenViewMapper.listBuildingMapInfoUnused(currentYearDate());
    }

    @Override
    public List<BuildingMapInfo3DVo> listBuildingMapInfo3D() {
        return adsBasScreenViewMapper.listBuildingMapInfo3D(currentYearDate());
    }

    @Override
    public List<ShareCockpitVo> listShareCockpit() {
        return adsBasScreenViewMapper.listShareCockpit(currentYearDate());
    }

    private String currentMonthDate() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        String currentYearMonth = dateFormat.format(new Date());
        return currentYearMonth;
    }

    private String currentYearDate() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        String currentYear = dateFormat.format(new Date());
        return currentYear;
    }
}
