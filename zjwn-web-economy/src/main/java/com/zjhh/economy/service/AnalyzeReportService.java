package com.zjhh.economy.service;

import com.zjhh.comm.vo.ColumnResultVO;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.BuildingCommentDetailReq;
import com.zjhh.economy.request.BuildingCommentReq;
import com.zjhh.economy.request.UpdateCommentScoreReq;
import com.zjhh.economy.request.analyzereport.*;
import com.zjhh.economy.vo.BuildingCommentDetailVo;
import com.zjhh.economy.vo.BuildingCommentVo;
import com.zjhh.economy.vo.analyzereport.*;

import java.util.List;

public interface AnalyzeReportService {


    /**
     * 空间资源综合态势分析
     *
     * @param req
     * @return
     */
    List<SpaceResourceAnalyzeVo> listSpaceResourceAnalyze(SpaceResourceAnalyzeReq req);


    /**
     * 空间资源多维度走势对比
     *
     * @param req
     * @return
     */
    List<SpaceResourceDimensionVo> listSpaceResourceDimension(SpaceResourceDimensionReq req);


    /**
     * 面积走势情况
     *
     * @param req
     * @return
     */
    List<SettledAreaTrendTableVo> listSettledAreaTrendTable(SettledAreaTrendTableReq req);

    /**
     * 房源空置对比分析
     *
     * @param req
     * @return
     */
    List<EmptyRoomCompareVo> listEmptyRoomCompare(SpaceResourceAnalyzeReq req);


    /**
     * 房源空置明细
     *
     * @param
     * @return
     */
    List<EmptyRoomDetailTableVo> listEmptyDetailTable(EmptyRoomTableReq req);

    Page<EmptyBuildingFloorTableVo> listEmptyBuildingFloor(EmptyBuildingFloorReq req);

    List<TaxIncomeCompareVo> listTaxIncomeCompare(SpaceResourceAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendAnalyze(TaxIncomeTrendAnalyzeReq req);

    List<EntStructAssembleVo> listEntStructAssemble(EntStructReq req);

    List<EntStructTrendVo> listEntStructTrend(EntStructTrendReq req);

    List<EntStructTrendTableVo> listEntStructTrendTable(EntStructTrendTableReq req);

    ColumnResultVO listTaxIncomeDetailTable(TaxIncomeDetailReq req);

    ColumnResultVO listTaxStructAnalyze(TaxStructReq req);


    List<BuildingCommentDetailVo> listBuildingCommentDetail(BuildingCommentDetailReq req);

    Page<BuildingCommentVo> pageBuildingComment(BuildingCommentReq req);

    void updateCommentScore(UpdateCommentScoreReq req);








}
