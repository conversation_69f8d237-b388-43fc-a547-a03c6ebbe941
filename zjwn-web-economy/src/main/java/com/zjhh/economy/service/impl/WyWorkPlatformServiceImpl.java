package com.zjhh.economy.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.request.WorkPlatformEnterpriseDemandReq;
import com.zjhh.economy.request.WyWorkPlatformPageReq;
import com.zjhh.economy.request.WyWorkPlatformReq;
import com.zjhh.economy.service.WyWorkPlatformService;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class WyWorkPlatformServiceImpl implements WyWorkPlatformService {

    @Resource
    private AdsPmRoomEnterpriseMapper adsPmRoomEnterpriseMapper;

    @Resource
    private AdsPmEnterpriseDemandMapper adsPmEnterpriseDemandMapper;

    @Resource
    private WorkPlatformWarningMapper workPlatformWarningMapper;

    @Resource
    private AdsPmPolicyMtMapper adsPmPolicyMtMapper;


    @Resource
    private AdsPmBuildingMapper adsPmBuildingMapper;





    @Override
    public List<TreeSelectVo> listAuthBuildingMenu() {
        return TreeUtils.listToTree(adsPmBuildingMapper.listBuildingByAuth(), "root");
    }

    @Override
    public WorkPlatformRoomVo getWorkPlatformRoomByAuth(WyWorkPlatformReq req) {
        return adsPmRoomEnterpriseMapper.getWorkPlatformRoomByAuth(req);
    }

    @Override
    public WorkPlatformEntManageVo getWorkPlatformEntManageByAuth(WyWorkPlatformReq req) {
        DateTime current = DateUtil.offsetMonth(new Date(), 0);
        // 格式化为 yyyyMM
        String currentYearMonth = DateUtil.format(current, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return adsPmRoomEnterpriseMapper.getWorkPlatformEntManageByAuth(currentYearMonth,req);
    }

    @Override
    public WorkPlatformDemandVo getWorkPlatformDemandByAuth(WyWorkPlatformReq req) {
        return adsPmEnterpriseDemandMapper.getWorkPlatformDemandByAuth(req);
    }

    @Override
    public List<WorkPlatformWarningRemindVo> listWorkPlatformRemindByAuth(WyWorkPlatformReq req) {
        return workPlatformWarningMapper.listWorkPlatformWarningRemindByAuth(req);
    }

    @Override
    public Page<WarningRemindListVo> pageWorkPlatformRemindByAuth(WyWorkPlatformPageReq req ) {
        return workPlatformWarningMapper.pageWarningRemindListByAuth(req.getPage(WarningRemindListVo.class),req);
    }

    @Override
    public List<EnterpriseDemandVo> listEnterpriseDemandByAuth(WorkPlatformEnterpriseDemandReq req) {
        return adsPmEnterpriseDemandMapper.listEnterpriseDemandByAuth(req);
    }

    @Override
    public List<PolicyManagementPageVo> listPolicyManagement() {
        return adsPmPolicyMtMapper.listPolicyManagementByWorkPlatform();
    }
}
