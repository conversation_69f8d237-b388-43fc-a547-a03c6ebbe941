package com.zjhh.economy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.vo.ColumnResultVO;
import com.zjhh.comm.vo.ColumnVO;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsPmCommentScore;
import com.zjhh.economy.dao.mapper.AdsPmCommentScoreMapper;
import com.zjhh.economy.dao.mapper.AnalyzeReportMapper;
import com.zjhh.economy.dao.mapper.DmGyPageStyleMapper;
import com.zjhh.economy.enume.CompareEnum;
import com.zjhh.economy.enume.QueryDateEnum;
import com.zjhh.economy.request.BuildingCommentDetailReq;
import com.zjhh.economy.request.BuildingCommentReq;
import com.zjhh.economy.request.UpdateCommentScoreReq;
import com.zjhh.economy.request.analyzereport.*;
import com.zjhh.economy.service.AnalyzeReportService;
import com.zjhh.economy.vo.BuildingCommentDetailVo;
import com.zjhh.economy.vo.BuildingCommentVo;
import com.zjhh.economy.vo.analyzereport.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class AnalyzeReportServiceImpl implements AnalyzeReportService {

    @Resource
    private AnalyzeReportMapper analyzeReportMapper;

    @Resource
    private AdsPmCommentScoreMapper adsPmCommentScoreMapper;

    @Resource
    private DmGyPageStyleMapper dmGyPageStyleMapper;



    @Override
    public List<SpaceResourceAnalyzeVo> listSpaceResourceAnalyze(SpaceResourceAnalyzeReq req) {
        if (CollUtil.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }
        if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
            return analyzeReportMapper.listSpaceResourceAnalyzeByCommunity(req);
        } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
            return analyzeReportMapper.listSpaceResourceAnalyzeByProject(req);
        } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
            return analyzeReportMapper.listSpaceResourceAnalyzeByBuilding(req);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<SpaceResourceDimensionVo> listSpaceResourceDimension(SpaceResourceDimensionReq req) {
        if (CollUtil.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }
        req.setMonths(getMonths());
        if (req.getDateType().equals(QueryDateEnum.MONTH.value())) {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByCommunity(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByProject(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByBuilding(req);
            } else {
                return new ArrayList<>();
            }
        } else if (req.getDateType().equals(QueryDateEnum.QUARTER.value())) {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByCommunityByQuarter(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByProjectByQuarter(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByBuildingByQuarter(req);
            } else {
                return new ArrayList<>();
            }
        } else {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByCommunityByYear(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByProjectByYear(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listSpaceResourceDimensionByBuildingByYear(req);
            } else {
                return new ArrayList<>();
            }
        }
    }

    @Override
    public List<SettledAreaTrendTableVo> listSettledAreaTrendTable(SettledAreaTrendTableReq req) {

        if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
            return analyzeReportMapper.listSettledAreaTrendTableByCommunity(req);
        } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
            return analyzeReportMapper.listSettledAreaTrendTableByProject(req);
        } else {
            return analyzeReportMapper.listSettledAreaTrendTableByBuilding(req);
        }
    }

    @Override
    public List<EmptyRoomCompareVo> listEmptyRoomCompare(SpaceResourceAnalyzeReq req) {
        if (CollUtil.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }
        if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
            return analyzeReportMapper.listEmptyRoomCompareByCommunity(req);
        } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
            return analyzeReportMapper.listEmptyRoomCompareByProject(req);
        } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
            return analyzeReportMapper.listEmptyRoomCompareByBuilding(req);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<EmptyRoomDetailTableVo> listEmptyDetailTable(EmptyRoomTableReq req) {

        if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
            return analyzeReportMapper.listEmptyRoomDetailTableByCommunity(req);
        } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
            return analyzeReportMapper.listEmptyRoomDetailTableByProject(req);
        } else {
            return analyzeReportMapper.listEmptyRoomDetailTableByBuilding(req);
        }
    }

    @Override
    public Page<EmptyBuildingFloorTableVo> listEmptyBuildingFloor(EmptyBuildingFloorReq req) {

        return analyzeReportMapper.listEmptyBuildingFloorTable(req.getPage(EmptyBuildingFloorTableVo.class),req);
    }

    @Override
    public List<TaxIncomeCompareVo> listTaxIncomeCompare(SpaceResourceAnalyzeReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeReportMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }

        if (CollUtil.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }
        if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
            return analyzeReportMapper.listTaxIncomeCompareByCommunity(req);
        } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
            return analyzeReportMapper.listTaxIncomeCompareByProject(req);
        } else {
            return analyzeReportMapper.listTaxIncomeCompareByBuilding(req);
        }
    }

    @Override
    public List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendAnalyze(TaxIncomeTrendAnalyzeReq req) {

        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeReportMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }

        if (CollUtil.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }
        req.setMonths(getTaxMonths());
        if (req.getDateType().equals(QueryDateEnum.MONTH.value())) {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByCommunity(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByProject(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByBuilding(req);
            } else {
                return new ArrayList<>();
            }
        } else if (req.getDateType().equals(QueryDateEnum.QUARTER.value())) {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByCommunityByQuarter(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByProjectByQuarter(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByBuildingByQuarter(req);
            } else {
                return new ArrayList<>();
            }
        } else {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByCommunityByYear(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByProjectByYear(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listTaxIncomeTrendByBuildingByYear(req);
            } else {
                return new ArrayList<>();
            }
        }

    }

    @Override
    public List<EntStructAssembleVo> listEntStructAssemble(EntStructReq req) {
        if (req.getType().equals(1)) {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listEntStructMoveByCommunity(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listEntStructMoveByProject(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listEntStructMoveByBuilding(req);
            } else {
                return new ArrayList<>();
            }
        } else {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listEntStructLocaledByCommunity(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listEntStructLocaledByProject(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listEntStructLocaledByBuilding(req);
            } else {
                return new ArrayList<>();
            }
        }
    }

    @Override
    public List<EntStructTrendVo> listEntStructTrend(EntStructTrendReq req) {

        if (CollUtil.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }
        req.setMonths(getMonths());
        if (req.getDateType().equals(QueryDateEnum.MONTH.value())) {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listEntStructTrendByCommunity(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listEntStructTrendByProject(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listEntStructTrendByBuilding(req);
            } else {
                return new ArrayList<>();
            }
        } else if (req.getDateType().equals(QueryDateEnum.QUARTER.value())) {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listEntStructTrendByCommunityByQuarter(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listEntStructTrendByProjectByQuarter(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listEntStructTrendByBuildingByQuarter(req);
            } else {
                return new ArrayList<>();
            }
        } else {
            if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
                return analyzeReportMapper.listEntStructTrendByCommunityByYear(req);
            } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
                return analyzeReportMapper.listEntStructTrendByProjectByYear(req);
            } else if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
                return analyzeReportMapper.listEntStructTrendByBuildingByYear(req);
            } else {
                return new ArrayList<>();
            }
        }
    }

    @Override
    public List<EntStructTrendTableVo> listEntStructTrendTable(EntStructTrendTableReq req) {

        if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
            return analyzeReportMapper.listEntStructTrendTableByBuilding(req);
        } else {
            return analyzeReportMapper.listEntStructTrendTableByCommunity(req);
        }
    }

    @Override
    public ColumnResultVO listTaxIncomeDetailTable(TaxIncomeDetailReq req) {

        ColumnResultVO resultVO = null;
        if (req.getCompareType().equals(CompareEnum.BUILDING.value())) {
            List<TaxIncomeDetailTableVo> list = analyzeReportMapper.listTaxIncomeDetailTableByBuilding(req);
            resultVO = getTaxDetailColumnResult(list, req);
            return resultVO;
        } else {
            List<TaxIncomeDetailTableVo> list = analyzeReportMapper.listTaxIncomeDetailTableByCommunity(req);
            resultVO = getTaxDetailColumnResult(list, req);
            resultVO.getTables().forEach(map -> {
                req.setCode(map.get("code").toString());
                List<TaxIncomeDetailTableVo> subList = analyzeReportMapper.listTaxIncomeDetailTableByProject(req);
                ColumnResultVO subResult = getTaxDetailColumnResult(subList, req);
                if (CollUtil.isNotEmpty(subResult.getTables())) {
                    map.put("children", subResult.getTables());
                    map.put("hasChildren", true);
                }
            });
            return resultVO;
        }
    }

    @Override
    public ColumnResultVO listTaxStructAnalyze(TaxStructReq req) {
        List<TaxStructVo> list = analyzeReportMapper.listTaxStructVo(req);
        return getTaxStructResultVo(list, req);
    }

    @Override
    public List<BuildingCommentDetailVo> listBuildingCommentDetail(BuildingCommentDetailReq req) {
        return adsPmCommentScoreMapper.pageBuildingCommentDetail(req);
    }

    @Override
    public Page<BuildingCommentVo> pageBuildingComment(BuildingCommentReq req) {
        return adsPmCommentScoreMapper.pageBuildingCommentVo(req.getPage(BuildingCommentVo.class), req);
    }

    @Override
    public void updateCommentScore(UpdateCommentScoreReq req) {
        AdsPmCommentScore adsPmCommentScore = null;
        if (StrUtil.isNotBlank(req.getId())) {
            adsPmCommentScore = adsPmCommentScoreMapper.selectById(req.getId());
            adsPmCommentScore.setCommentScore(req.getCommentScore().floatValue());
            adsPmCommentScoreMapper.updateById(adsPmCommentScore);
        } else {
            adsPmCommentScore = new AdsPmCommentScore();
            adsPmCommentScore.setId(IdUtil.getSnowflakeNextIdStr());
            adsPmCommentScore.setDatekey(req.getDatekey());
            adsPmCommentScore.setEdited(true);
            adsPmCommentScore.setCommentIndicatorId(req.getCommentIndicatorId());
            adsPmCommentScore.setCommentScore(req.getCommentScore().floatValue());
            adsPmCommentScore.setBuildingId(req.getBuildingId());
            adsPmCommentScore.setCreateTime(LocalDateTime.now());
            adsPmCommentScore.setUpdateTime(LocalDateTime.now());
            adsPmCommentScoreMapper.insert(adsPmCommentScore);
        }
    }



    private List<String> getMonths() {

        LocalDate currentDate = LocalDate.now();
        List<String> monthList = new ArrayList<>();
        int currentQuarter = getCurrentQuarter(currentDate);
        int currentYear = currentDate.getYear();

        for (int i = 1; i <= 4; i++) {
            int quarter = currentQuarter - i;
            int year = currentYear;
            if (quarter <= 0) {
                quarter += 4;
                year--;
            }
            monthList.addAll(getMonthsInQuarter(year, quarter));
        }

        System.out.println(monthList);
        return monthList;
    }


    private List<String> getTaxMonths() {

        String taxMaxDate = analyzeReportMapper.getTaxMaxDate();
        String taxMaxDateFormat = taxMaxDate.substring(0,4) + "-" + taxMaxDate.substring(4,6) + "-" + "01";

        LocalDate localDate = LocalDate.parse(taxMaxDateFormat);
        List<String> monthList = new ArrayList<>();
        int currentQuarter = getCurrentQuarter(localDate);
        int currentYear = localDate.getYear();

        for (int i = 1; i <= 4; i++) {
            int quarter = currentQuarter - i;
            int year = currentYear;
            if (quarter <= 0) {
                quarter += 4;
                year--;
            }
            monthList.addAll(getMonthsInQuarter(year, quarter));
        }

        System.out.println(monthList);
        return monthList;
    }

    private List<String> getMonthsInQuarter(int year, int quarter) {
        List<String> monthList = new ArrayList<>();
        int startMonth = (quarter - 1) * 3 + 1;
        for (int i = 0; i < 3; i++) {
            int month = startMonth + i;
            YearMonth ym = YearMonth.of(year, month);
            monthList.add(ym.format(DateTimeFormatter.ofPattern("yyyyMM")));
        }
        return monthList;
    }

    private int getCurrentQuarter(LocalDate date) {
        int month = date.getMonthValue();
        return (month - 1) / 3 + 1;
    }

    private ColumnResultVO getTaxDetailColumnResult(List<TaxIncomeDetailTableVo> list, TaxIncomeDetailReq req) {
        ColumnResultVO resultVO = new ColumnResultVO();
        LinkedHashMap<String, List<TaxIncomeDetailTableVo>> groupMap = list.stream()
                .collect(Collectors.groupingBy(TaxIncomeDetailTableVo::getCode, LinkedHashMap::new, Collectors.toList()));
        List<Map<String, Object>> resultList = new ArrayList<>();

        for (String key : groupMap.keySet()) {
            List<TaxIncomeDetailTableVo> vos = groupMap.get(key);
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            vos.sort(Comparator.comparing(o -> o.getCode()));
            vos.forEach(table -> {
                map.putIfAbsent("code", table.getCode());
                map.putIfAbsent("name", table.getName());
                map.put("taxIncome" + table.getDatekey(), table.getTaxIncome());
                map.put("streetTaxIncome" + table.getDatekey(), table.getStreetTaxIncome());
                map.put("taxIncomeZf" + table.getDatekey(), table.getTaxIncomeZf());
                map.put("streetTaxIncomeZf" + table.getDatekey(), table.getStreetTaxIncomeZf());
                map.put("streetTaxIncomeZf" + table.getDatekey(), table.getStreetTaxIncomeZf());
                map.put("totalTaxIncome", table.getTotalTaxIncome());
                map.put("totalStreetTaxIncome", table.getTotalStreetTaxIncome());
                map.put("totalTaxIncomeZf", table.getTotalTaxIncomeZf());
                map.put("totalStreetTaxIncomeZf", table.getTotalStreetTaxIncomeZf());
                map.put("zb", table.getZb());
                map.put("hasChildren", table.getHasChildren());
                map.put("compareType", table.getCompareType());
            });
            resultList.add(map);

            List<ColumnVO> columnVOS = new ArrayList<>();
            req.getMonths().forEach(month -> {
                String title = month.substring(0, 4) + "-" + month.substring(4, 6);
                ColumnVO columnVO = new ColumnVO();
                columnVO.setTitle(title);
                columnVO.setDataIndex(month);
                columnVOS.add(columnVO);
            });

            resultVO.setTables(resultList);
            resultVO.setColumns(columnVOS);
        }
        return resultVO;
    }

    private ColumnResultVO getTaxStructResultVo(List<TaxStructVo> list, TaxStructReq req) {
        ColumnResultVO resultVO = new ColumnResultVO();
        LinkedHashMap<String, List<TaxStructVo>> groupMap = list.stream()
                .collect(Collectors.groupingBy(TaxStructVo::getTaxType, LinkedHashMap::new, Collectors.toList()));
        List<Map<String, Object>> resultList = new ArrayList<>();
        AtomicReference<BigDecimal> totalSs = new AtomicReference<>();
        AtomicReference<BigDecimal> totalJdsr = new AtomicReference<>();
        for (String key : groupMap.keySet()) {
            List<TaxStructVo> vos = groupMap.get(key);
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            vos.sort(Comparator.comparing(o -> o.getTaxType()));
            vos.forEach(table -> {
                if (table.getTaxType().equals("0")) {
                    totalSs.set(table.getTotalSs());
                    totalJdsr.set(table.getTotalJdsr());
                }
                map.putIfAbsent("taxType", table.getTaxType());
                map.putIfAbsent("taxName", table.getTaxName());
                map.put("ss" + table.getDatekey(), table.getSs());
                map.put("ssZf" + table.getDatekey(), table.getSsZf());
                map.put("jdsr" + table.getDatekey(), table.getJdsr());
                map.put("jdsrZf" + table.getDatekey(), table.getJdsrZf());
                map.put("totalSs", table.getTotalSs().floatValue() != 0 ? table.getTotalSs() : 0);
                map.put("totalJdsr", table.getTotalJdsr().floatValue() != 0 ? table.getTotalJdsr() : 0);
                map.put("totalSsZf", table.getTotalSsZf().floatValue() != 0 ? table.getTotalSsZf() : 0);
                map.put("totalJdsrZf", table.getTotalJdsrZf().floatValue() != 0 ? table.getTotalJdsrZf() : 0);
                map.put("ssZb", totalSs.get().floatValue() != 0 ? table.getTotalSs().divide(totalSs.get(), 2, RoundingMode.HALF_DOWN).multiply(new BigDecimal(100)) : 0);
                map.put("jdsrZb", totalJdsr.get().floatValue() != 0 ? table.getTotalJdsr().divide(totalJdsr.get(), 2, RoundingMode.HALF_DOWN).multiply(new BigDecimal(100)) : 0);
            });
            resultList.add(map);

            List<ColumnVO> columnVOS = new ArrayList<>();
            req.getMonths().forEach(month -> {
                String title = month.substring(0, 4) + "-" + month.substring(4, 6);
                ColumnVO columnVO = new ColumnVO();
                columnVO.setTitle(title);
                columnVO.setDataIndex(month);
                columnVOS.add(columnVO);
            });

            resultVO.setTables(resultList);
            resultVO.setColumns(columnVOS);
        }
        return resultVO;
    }

}
