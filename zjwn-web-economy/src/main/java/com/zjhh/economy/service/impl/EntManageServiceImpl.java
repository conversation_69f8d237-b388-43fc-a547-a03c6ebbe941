package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.*;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.request.*;
import com.zjhh.economy.request.analyzereport.EntCompareExportReq;
import com.zjhh.economy.request.analyzereport.EntSearchReq;
import com.zjhh.economy.service.EntManageService;
import com.zjhh.economy.utils.OperationLogUtil;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.analyzereport.EntCompareVo;
import com.zjhh.economy.vo.operationlog.MoveRegForLog;
import com.zjhh.economy.vo.operationlog.RoomSettleInfoForLog;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class EntManageServiceImpl extends BaseProjectServiceImpl implements EntManageService {


    @Resource
    private AdsPmEnterpriseChangeMapper adsPmEnterpriseChangeMapper;

    @Resource
    private AdsPmEnterpriseTalentMapper adsPmEnterpriseTalentMapper;

    @Resource
    private AdsPmEnterpriseMapper adsPmEnterpriseMapper;

    @Resource
    private AdsPmEnterpriseSettleMapper adsPmEnterpriseSettleMapper;

    @Resource
    private AdsPmRoomEnterpriseMapper adsPmRoomEnterpriseMapper;

    @Resource
    private AdsPmEnterpriseLabelMapper adsPmEnterpriseLabelMapper;

    @Resource
    private AdsPmEnterpriseTaxMapper adsPmEnterpriseTaxMapper;

    @Resource
    private AdsPmRoomMapper adsPmRoomMapper;

    @Resource
    private AdsPmEnterpriseFocusMapper adsPmEnterpriseFocusMapper;

    @Resource
    private AdsPmBuildingMapper adsPmBuildingMapper;

    @Resource
    private AnalyzeCockpitMapper analyzeCockpitMapper;

    @Resource
    private DmPmMapper dmPmMapper;


    @Resource
    private UserSession userSession;

    @Resource
    private AdsPmMoveRegisterLabelMapper adsPmMoveRegisterLabelMapper;

    @Resource
    private AdsPmEnterpriseMoveRegisterMapper adsPmEnterpriseMoveRegisterMapper;

    @Resource
    private ChangeInfoMapper changeInfoMapper;

    @Resource
    private OperationLogUtil operationLogUtil;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addEnterprise(AddEntReq req) {


        Boolean exists = adsPmEnterpriseMapper.checkEntExists(req.getEnterpriseName(), req.getOldName(), null);
        if (exists) {
            throw new BizException("企业名称或曾用名已存在!");
        }

        AdsPmEnterprise adsPmEnterprise = new AdsPmEnterprise();
        BeanUtil.copyProperties(req, adsPmEnterprise);
        adsPmEnterprise.setId(IdUtil.getSnowflakeNextIdStr());
        adsPmEnterprise.setCreateTime(LocalDateTime.now());
        adsPmEnterprise.setSerialNo(getEntSerialNo());
        adsPmEnterprise.setUpdateTime(LocalDateTime.now());
        adsPmEnterprise.setCreateUser(userSession.getUserCode());
        adsPmEnterprise.setRemark(req.getRemark());
        adsPmEnterpriseMapper.insert(adsPmEnterprise);
        if (CollUtil.isNotEmpty(req.getEnterpriseLabels())) {
            req.getEnterpriseLabels().forEach(label -> {
                AdsPmEnterpriseLabel adsPmEnterpriseLabel = new AdsPmEnterpriseLabel();
                adsPmEnterpriseLabel.setEnterpriseId(adsPmEnterprise.getId());
                adsPmEnterpriseLabel.setLabelCode(label.getLabelCode());
                adsPmEnterpriseLabel.setId(IdUtil.getSnowflakeNextIdStr());
                adsPmEnterpriseLabelMapper.insert(adsPmEnterpriseLabel);
            });
        }

    }

    @Override
    public void updateEnterprise(UpdateEntReq req) {
        AdsPmEnterprise adsPmEnterprise = adsPmEnterpriseMapper.selectById(req.getId());

        Boolean exists = adsPmEnterpriseMapper.checkEntExists(req.getEnterpriseName(), req.getOldName(), req.getId());
        if (exists) {
            throw new BizException("企业名称或曾用名已存在!");
        }
        BeanUtil.copyProperties(req, adsPmEnterprise);
        adsPmEnterprise.setUpdateTime(LocalDateTime.now());
        adsPmEnterpriseMapper.updateById(adsPmEnterprise);
        List<String> labelIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getEnterpriseLabels())) {
            req.getEnterpriseLabels().forEach(label -> {
                if (StrUtil.isBlank(label.getId())) {
                    AdsPmEnterpriseLabel adsPmEnterpriseLabel = new AdsPmEnterpriseLabel();
                    adsPmEnterpriseLabel.setEnterpriseId(adsPmEnterprise.getId());
                    adsPmEnterpriseLabel.setLabelCode(label.getLabelCode());
                    adsPmEnterpriseLabel.setId(IdUtil.getSnowflakeNextIdStr());
                    adsPmEnterpriseLabelMapper.insert(adsPmEnterpriseLabel);
                    labelIds.add(adsPmEnterpriseLabel.getId());
                } else {
                    labelIds.add(label.getId());
                }
            });
        }
        if (CollUtil.isNotEmpty(labelIds)) {
            UpdateWrapper<AdsPmEnterpriseLabel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(AdsPmEnterpriseLabel::getEnterpriseId, req.getId()).notIn(AdsPmEnterpriseLabel::getId, labelIds);
            adsPmEnterpriseLabelMapper.delete(updateWrapper);
        }else {
            UpdateWrapper<AdsPmEnterpriseLabel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(AdsPmEnterpriseLabel::getEnterpriseId,req.getId());
            adsPmEnterpriseLabelMapper.delete(updateWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addSettleInfo(AddSettleInfoReq req) {

        if (ObjectUtil.isNotNull(req.getExpectMoveOutDate()) && req.getExpectMoveOutDate().compareTo(req.getCheckInDate()) < 0) {
            throw new BizException("预计搬离日期必须大于入驻日期");
        }

        if ((ObjectUtil.isNotNull(req.getRenovationStartDate()) && ObjectUtil.isNotNull(req.getRenovationEndDate())) && req.getRenovationEndDate().compareTo(req.getRenovationStartDate()) < 0) {
            throw new BizException("装修结束日期必须大于装修开始日期");
        }
        req.getRoomIds().forEach(room -> {
            AdsPmRoom adsPmRoom = adsPmRoomMapper.selectById(room);
            AdsPmRoomEnterprise adsPmRoomEnterprise = new AdsPmRoomEnterprise();
            QueryWrapper<AdsPmRoomEnterprise> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(AdsPmRoomEnterprise::getRoomId, room).eq(AdsPmRoomEnterprise::getEnterpriseId, req.getEnterpriseId()).eq(AdsPmRoomEnterprise::getMoved, false);
            Boolean exists = adsPmRoomEnterpriseMapper.exists(queryWrapper);
            if (exists) {
                throw new BizException("入驻房间已入驻！");
            }
            BeanUtil.copyProperties(req, adsPmRoomEnterprise);
            adsPmRoomEnterprise.setId(IdUtil.getSnowflakeNextIdStr());
            adsPmRoomEnterprise.setArea(adsPmRoom.getBusinessArea());
            adsPmRoomEnterprise.setMoved(false);
            adsPmRoomEnterprise.setRoomId(room);
            adsPmRoomEnterprise.setCreateUser(userSession.getUserCode());
            adsPmRoomEnterprise.setCreateTime(LocalDateTime.now());
            adsPmRoomEnterprise.setUpdateTime(LocalDateTime.now());
            adsPmRoomEnterpriseMapper.insert(adsPmRoomEnterprise);
            addSettleInfoRecord(adsPmRoomEnterprise);

            RoomSettleInfoForLog dataAfter = changeInfoMapper.getRoomSettleInfoForLog(adsPmRoomEnterprise.getId());
            if (dataAfter != null) {
                operationLogUtil.recordLog(LogModuleEnum.ROOM, LogTypeEnum.ROOM_SETTLE_ADD, dataAfter.getId(),
                        dataAfter.getBuildingName() + "-" + dataAfter.getRoomNo(), null, JSON.toJSONString(dataAfter));
            }
        });

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateSettleInfo(UpdateSettleInfoReq req) {
        RoomSettleInfoForLog dataBefore = changeInfoMapper.getRoomSettleInfoForLog(req.getId());

        if (ObjectUtil.isNotNull(req.getExpectMoveOutDate()) && req.getExpectMoveOutDate().compareTo(req.getCheckInDate()) < 0) {
            throw new BizException("预计搬离日期必须大于入驻日期");
        }

        if ((ObjectUtil.isNotNull(req.getRenovationStartDate()) && ObjectUtil.isNotNull(req.getRenovationEndDate())) && req.getRenovationEndDate().compareTo(req.getRenovationStartDate()) < 0) {
            throw new BizException("装修结束日期必须大于装修开始日期");
        }
        AdsPmRoomEnterprise adsPmRoomEnterprise = adsPmRoomEnterpriseMapper.selectById(req.getId());
        AdsPmRoom room = adsPmRoomMapper.selectById(adsPmRoomEnterprise.getRoomId());
        if (ObjectUtil.isNotNull(req.getArea()) && req.getArea().compareTo(room.getBusinessArea()) > 0) {
            throw new BizException("入驻面积不可大于房间面积");
        }

        adsPmRoomEnterprise.setDocumentId(req.getDocumentId());
        adsPmRoomEnterprise.setRemark(req.getRemark());
        adsPmRoomEnterprise.setArea(req.getArea());
        adsPmRoomEnterprise.setCheckInDate(req.getCheckInDate());
        adsPmRoomEnterprise.setExpectMoveOutDate(req.getExpectMoveOutDate());


        adsPmRoomEnterprise.setRenovationStartDate(req.getRenovationStartDate());
        adsPmRoomEnterprise.setRenovationEndDate(req.getRenovationEndDate());
        adsPmRoomEnterpriseMapper.updateById(adsPmRoomEnterprise);

        updateSettleInfoRecord(adsPmRoomEnterprise);

        RoomSettleInfoForLog dataAfter = changeInfoMapper.getRoomSettleInfoForLog(adsPmRoomEnterprise.getId());
        if (dataBefore != null && dataAfter != null) {
            operationLogUtil.recordLog(LogModuleEnum.ROOM, LogTypeEnum.ROOM_SETTLE_EDIT, dataBefore.getId(), dataBefore.getBuildingName() + "-" + dataBefore.getRoomNo(),
                    JSON.toJSONString(dataBefore), JSON.toJSONString(dataAfter));
        }
    }

    @Override
    public void saveTalent(SaveTalentReq req) {
        if (StrUtil.isNotBlank(req.getId())) {
            AdsPmEnterpriseTalent talent = new AdsPmEnterpriseTalent();
            BeanUtil.copyProperties(req, talent);
            talent.setUpdateTime(LocalDateTime.now());
            adsPmEnterpriseTalentMapper.updateById(talent);
        } else {
            AdsPmEnterpriseTalent talent = new AdsPmEnterpriseTalent();
            BeanUtil.copyProperties(req, talent);
            talent.setId(IdUtil.getSnowflakeNextIdStr());
            talent.setCreateUser(userSession.getUserCode());
            talent.setCreateTime(LocalDateTime.now());
            talent.setUpdateTime(LocalDateTime.now());
            talent.setEnterpriseId(req.getEnterpriseId());
            adsPmEnterpriseTalentMapper.insert(talent);
        }
    }

    @Override
    public void moveRegister(MoveRegisterReq req) {
        if (CollUtil.isNotEmpty(req.getSettleInfos())) {
            AtomicReference<String> enterpriseId = new AtomicReference<>();
            req.getSettleInfos().forEach(settleInfo -> {
                AdsPmRoomEnterprise roomEnterprise = adsPmRoomEnterpriseMapper.selectById(settleInfo.getId());
                roomEnterprise.setRealityMoveOutDate(req.getRealMoveDate());
                roomEnterprise.setMoved(true);
                enterpriseId.set(roomEnterprise.getEnterpriseId());
                adsPmRoomEnterpriseMapper.updateById(roomEnterprise);
                addSettleInfoRecord(roomEnterprise);
                AdsPmEnterpriseMoveRegister moveRegister = new AdsPmEnterpriseMoveRegister();
                moveRegister.setId(IdUtil.getSnowflakeNextIdStr());
                moveRegister.setCreateTime(LocalDateTime.now());
                moveRegister.setUpdateTime(LocalDateTime.now());
                moveRegister.setProvince(req.getProvince());
                moveRegister.setCity(req.getCity());
                moveRegister.setMoveReason(req.getMoveReason());
                moveRegister.setSettledId(roomEnterprise.getId());
                adsPmEnterpriseMoveRegisterMapper.insert(moveRegister);
                if (CollUtil.isNotEmpty(req.getLabels())) {
                    req.getLabels().forEach(label -> {
                        AdsPmMoveRegisterLabel moveLabel = new AdsPmMoveRegisterLabel();
                        moveLabel.setId(IdUtil.getSnowflakeNextIdStr());
                        moveLabel.setLabelCode(label);
                        moveLabel.setMoveRegisterId(moveRegister.getId());
                        adsPmMoveRegisterLabelMapper.insert(moveLabel);
                    });
                }
                AdsPmBuilding adsPmBuilding = adsPmBuildingMapper.selectById(roomEnterprise.getBuildingId());
                AdsPmRoom adsPmRoom = adsPmRoomMapper.selectById(roomEnterprise.getRoomId());
                if (adsPmBuilding != null && adsPmRoom != null) {
                    String objectId = moveRegister.getId();
                    String objectName = adsPmBuilding.getBuildingName() + "-" + adsPmRoom.getRoomNo();
                    LocalDate realMoveDate = req.getRealMoveDate();
                    MoveRegForLog moveRegForLog = new MoveRegForLog();
                    moveRegForLog.setRealMoveDate(realMoveDate);
                    if (StrUtil.isNotBlank(req.getProvince()) && StrUtil.isNotBlank(req.getCity())) {
                        moveRegForLog.setMoveDirection(req.getProvince() + req.getCity());
                    }
                    moveRegForLog.setMoveReason(req.getMoveReason());
                    List<String> labels = dmPmMapper.listMoveRegisterLabel(moveRegister.getId())
                            .stream().map(MoveRegisterLabelVo::getLabelName).collect(Collectors.toList());
                    moveRegForLog.setLabels(labels);
                    operationLogUtil.recordLog(LogModuleEnum.ROOM, LogTypeEnum.ROOM_SETTLE_REMOVE, objectId, objectName,
                            null, JSON.toJSONString(moveRegForLog));
                }
            });
        } else {
            throw new BizException("入驻信息不能为空");
        }
    }

    @Override
    public void editMoveRegister(EditMoveRegisterReq req) {
        AdsPmRoomEnterprise roomEnterprise = adsPmRoomEnterpriseMapper.selectById(req.getSettledId());
        roomEnterprise.setRealityMoveOutDate(req.getRealMoveDate());
        QueryWrapper<AdsPmEnterpriseMoveRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AdsPmEnterpriseMoveRegister::getSettledId, req.getSettledId());
        AdsPmEnterpriseMoveRegister moveRegister = adsPmEnterpriseMoveRegisterMapper.selectOne(queryWrapper);
        moveRegister.setProvince(req.getProvince());
        moveRegister.setCity(req.getCity());
        moveRegister.setMoveReason(req.getMoveReason());
        moveRegister.setUpdateTime(LocalDateTime.now());
        adsPmEnterpriseMoveRegisterMapper.updateById(moveRegister);
        if (CollUtil.isNotEmpty(req.getLabels())) {
            QueryWrapper<AdsPmMoveRegisterLabel> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.lambda().eq(AdsPmMoveRegisterLabel::getMoveRegisterId, moveRegister.getId());
            adsPmMoveRegisterLabelMapper.delete(queryWrapper1);
            req.getLabels().forEach(label -> {
                AdsPmMoveRegisterLabel moveLabel = new AdsPmMoveRegisterLabel();
                moveLabel.setId(IdUtil.getSnowflakeNextIdStr());
                moveLabel.setLabelCode(label);
                moveLabel.setMoveRegisterId(moveRegister.getId());
                adsPmMoveRegisterLabelMapper.insert(moveLabel);
            });
        }
    }

    @Override
    public MoveRegisterDetailVo getMoveRegisterDetail(IdReq req) {
        QueryWrapper<AdsPmEnterpriseMoveRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AdsPmEnterpriseMoveRegister::getSettledId, req.getId());
        AdsPmEnterpriseMoveRegister moveRegister = adsPmEnterpriseMoveRegisterMapper.selectOne(queryWrapper);
        QueryWrapper<AdsPmRoomEnterprise> roomEnterpriseWrapper = new QueryWrapper<>();
        roomEnterpriseWrapper.lambda().eq(AdsPmRoomEnterprise::getId,req.getId());
        AdsPmRoomEnterprise roomEnterprise = adsPmRoomEnterpriseMapper.selectOne(roomEnterpriseWrapper);
        MoveRegisterDetailVo detailVo = new MoveRegisterDetailVo();
        detailVo.setRealMoveDate(roomEnterprise.getRealityMoveOutDate());
        if (ObjectUtil.isNotNull(moveRegister)) {
            BeanUtil.copyProperties(moveRegister, detailVo);
            detailVo.setLabels(dmPmMapper.listMoveRegisterLabel(moveRegister.getId()));
        }
        return detailVo;
    }

    @Override
    public List<SettleInfoListVo> listSettleInfoList(QuerySettleInfoPageReq req) {
        return adsPmRoomEnterpriseMapper.pageSettleInfo(req);
    }

    @Override
    public List<SettleInfoMenuVo> moveRegisterMenu(String enterpriseId, List<String> ids) {
        return adsPmRoomEnterpriseMapper.listSettleInfo(enterpriseId, ids);
    }

    @Override
    public void deleteSettleInfo(IdReq req) {
        AdsPmRoomEnterprise adsPmRoomEnterprise = adsPmRoomEnterpriseMapper.selectById(req.getId());
        if (adsPmRoomEnterprise.getMoved()) {
            throw new BizException("已搬离数据不可删除");
        } else {
            adsPmRoomEnterpriseMapper.deleteById(req.getId());
        }
    }

    @Override
    public EnterpriseDetailHeaderVo getDetailHeader(String enterpriseId) {
        EnterpriseDetailHeaderVo header = new EnterpriseDetailHeaderVo();
        AdsPmEnterprise enterprise = adsPmEnterpriseMapper.selectById(enterpriseId);
        List<String> enterpriseLabels = adsPmEnterpriseLabelMapper.listEnterpriseLabels(enterpriseId);
        List<SettleInfoShowVo> infoShows = adsPmRoomEnterpriseMapper.listSettleInfoShow(enterpriseId);
        String settleInfo = assembleSettleInfo(infoShows);
        List<SettleInfoShowVo> moveOutInfoShows = adsPmRoomEnterpriseMapper.listMoveOutInfoShow(enterpriseId);
        String moveOutInfo = assembleSettleInfo(moveOutInfoShows);
        LocalDate earlyCheckInDate = adsPmRoomEnterpriseMapper.getStartCheckInDate(enterpriseId);
        double totalArea = infoShows.stream().mapToDouble(SettleInfoShowVo::getArea).sum();
        Boolean focused = adsPmEnterpriseFocusMapper.checkFocus(enterpriseId, userSession.getUserCode());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        String datekey = adsPmEnterpriseTaxMapper.getTaxMaxDate();
        BigDecimal totalTax = adsPmEnterpriseTaxMapper.selectTotalTax(enterpriseId, datekey);
        BigDecimal output = totalArea == 0 ? new BigDecimal(0) : totalTax.multiply(new BigDecimal(10000)).divide(new BigDecimal(totalArea), 2, BigDecimal.ROUND_CEILING);
        header.setEnterpriseName(enterprise.getEnterpriseName());
        header.setPhone(enterprise.getPhone());
        header.setIndustryName(dmPmMapper.getTitleName(enterprise.getIndustryCode()));
        header.setFullIndustryName(dmPmMapper.getRecursiveTitleName(enterprise.getIndustryCode()));
        header.setOnScaled(enterprise.getOnScaled());
        header.setEntContactPerson(enterprise.getEntContactPerson());
        header.setEntPhone(enterprise.getEntPhone());
        header.setLogoImgId(enterprise.getLogoImgId());
        header.setSettleRoomCount(infoShows.size());
        header.setTerritorialized(enterprise.getTerritorialized());
        header.setEnterpriseLabels(enterpriseLabels);
        header.setMoveOutInfo(moveOutInfo);
        header.setUpdateDate(enterprise.getUpdateTime());
        header.setSettleArea(BigDecimal.valueOf(totalArea)
                .setScale(2, RoundingMode.HALF_UP));
        header.setTotalTax(totalTax);
        header.setOutput(output);
        if (adsPmRoomEnterpriseMapper.checkEnterpriseIsMoved(enterpriseId)) {
            LocalDate moveDate = adsPmRoomEnterpriseMapper.getMovedDate(enterpriseId);
            header.setSettledTime(assembleSettledTime(earlyCheckInDate, moveDate));
        } else {
            LocalDateTime currentDateTime = LocalDateTime.now();
            header.setSettledTime(assembleSettledTime(earlyCheckInDate, currentDateTime.toLocalDate()));
        }

        header.setFocused(focused);
        header.setSettleInfo(settleInfo);
        return header;
    }

    @Override
    public EnterpriseDetailVo getDetail(String enterpriseId) {
        EnterpriseDetailVo detail = adsPmEnterpriseMapper.getEnterpriseDetail(enterpriseId);
        detail.setEnterpriseLabels(adsPmEnterpriseLabelMapper.listEnterpriseLabelsCode(detail.getId()));
        return detail;
    }

    @Override
    public List<TaxAnalyseChartVo> listTaxAnalyseChart(QueryTaxAnalyseReq req) {

        if (req.getUpdateType().equals("Year")) {
            return adsPmEnterpriseTaxMapper.listTaxAnalyseChartByYear(req);
        } else {
            return adsPmEnterpriseTaxMapper.listTaxAnalyseChartByMonth(req);
        }
    }

    @Override
    public List<Map<String, Object>> listTaxAnalyseTable(QueryTaxAnalyseReq req) {

        List<TaxAnalyseTableVo> list = adsPmEnterpriseTaxMapper.listTaxAnalyseTable(req);
        LinkedHashMap<String, List<TaxAnalyseTableVo>> groupMap = list.stream()
                .collect(Collectors.groupingBy(TaxAnalyseTableVo::getMonthStr, LinkedHashMap::new, Collectors.toList()));
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (String key : groupMap.keySet()) {
            List<TaxAnalyseTableVo> vos = groupMap.get(key);
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            vos.sort(Comparator.comparing(o -> o.getDatekey()));
            vos.forEach(table -> {
                map.putIfAbsent("monthStr", table.getMonthStr());
                map.putIfAbsent(table.getYskmDm() + "hj", table.getHj());
                map.putIfAbsent(table.getYskmDm() + "zf", table.getZf());
            });
            resultList.add(map);
        }
        TaxAnalyseTableVo summaryTaxVo = adsPmEnterpriseTaxMapper.listTaxAnalyseSummaryTable(req);
        Map<String,Object> summaryTaxMap = new HashMap<>();
        summaryTaxMap.put("monthStr", "累计");
        summaryTaxMap.put("sshj",summaryTaxVo.getHj());
        summaryTaxMap.put("sszf",summaryTaxVo.getZf());
        resultList.add(summaryTaxMap);

        return resultList;
    }

    @Override
    public Page<EnterpriseListVo> pageEnterpriseList(QueryEnterprisePageReq req) {
        req.setUserCode(userSession.getUserCode());
        req.setDatekey(analyzeCockpitMapper.getTaxMaxDate());
        return adsPmEnterpriseMapper.pageEnterpriseList(req.getPage(EnterpriseListVo.class), req);
    }

    @Override
    public void addFocus(String enterpriseId) {
        AdsPmEnterpriseFocus focus = new AdsPmEnterpriseFocus();
        focus.setEnterpriseId(enterpriseId);
        focus.setUserCode(userSession.getUserCode());
        focus.setId(IdUtil.getSnowflakeNextIdStr());
        adsPmEnterpriseFocusMapper.insert(focus);
    }

    @Override
    public void cancelFocus(String enterpriseId) {
        QueryWrapper<AdsPmEnterpriseFocus> cancelWrapper = new QueryWrapper<>();
        cancelWrapper.lambda().eq(AdsPmEnterpriseFocus::getEnterpriseId, enterpriseId).eq(AdsPmEnterpriseFocus::getUserCode, userSession.getUserCode());
        adsPmEnterpriseFocusMapper.delete(cancelWrapper);
    }

    @Override
    public void deleteEnterprise(String enterpriseId) {
        QueryWrapper<AdsPmRoomEnterprise> settleWrapper = new QueryWrapper<>();
        settleWrapper.lambda().eq(AdsPmRoomEnterprise::getEnterpriseId, enterpriseId);
        Long count = adsPmRoomEnterpriseMapper.selectCount(settleWrapper);
        if (count > 0) {
            throw new BizException("该企业存在入驻历史不可删除！");
        } else {
            adsPmEnterpriseMapper.deleteById(enterpriseId);
        }
    }


    @Override
    public SettleInfoDetailVo getSettleInfoDetail(IdReq req) {
        return adsPmRoomEnterpriseMapper.getSettleDetailInfo(req.getId());
    }

    @Override
    public TalentDetailVo getTalentDetail(IdReq req) {
        return adsPmEnterpriseTalentMapper.getTalentDetail(req.getId());
    }

    @Override
    public void updateTalentDetail(SaveTalentReq req) {
        AdsPmEnterpriseTalent talent = new AdsPmEnterpriseTalent();
        BeanUtil.copyProperties(req, talent);
        talent.setUpdateTime(LocalDateTime.now());
        adsPmEnterpriseTalentMapper.updateById(talent);
    }

    @Override
    public List<TreeSelectVo> listEntSearch(EntKeywordSearchReq req) {
        return adsPmEnterpriseMapper.listSearchEnt(req);
    }

    @Override
    public RoomSettleInfoDetailVo getSettledInfoByRoomDetail(String enterpriseId, String roomId) {
        return adsPmRoomEnterpriseMapper.getSettleDetailInfoByRoomDetail(enterpriseId, roomId);

    }

    @Override
    public EntCompareVo getEntCompareInfo(String enterpriseId) {
        EntCompareVo compareVo = new EntCompareVo();
        EnterpriseDetailHeaderVo headerVo = getDetailHeader(enterpriseId);
        BeanUtil.copyProperties(headerVo, compareVo);
        EnterpriseDetailVo detailVo = getDetail(enterpriseId);
        BeanUtil.copyProperties(detailVo, compareVo, "enterpriseLabels");
        EntCompareSettledInfoVo compareSettledInfoVo = adsPmRoomEnterpriseMapper.getCompareSettledInfo(enterpriseId);
        BeanUtil.copyProperties(compareSettledInfoVo, compareVo);
        return compareVo;
    }

    @Override
    public void exportEntCompareInfo(EntCompareExportReq req, HttpServletResponse response) throws IOException {
        List<EntCompareVo> list = new ArrayList<>();
        req.getIds().forEach(id -> {
            EntCompareVo compareVo = getEntCompareInfo(id);
            list.add(compareVo);
        });

        String sheetName = "企业对比分析";
        String fileName = "企业对比分析";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo(null, null, null),
                        new ThreeTableVo.MidContentTwoVo(EntCompareVo.class, list)));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, fileName, sheetName);

    }

    @Override
    public List<TreeSelectVo> listMenuEnt(EntSearchReq req) {
        return adsPmEnterpriseMapper.listMenuEnt(req);
    }

    @Override
    public List<TreeSelectVo> listMenuMobileEnt(EntKeywordSearchReq req) {
        return adsPmEnterpriseMapper.listMenuMobileEnt(req);
    }

    @Override
    public List<TreeSelectVo> listBuildingSearch(BuildingKeywordSearchReq req) {
        return adsPmBuildingMapper.listBuildingSearch(req.getBuildingName());
    }

    @Override
    public List<EnterpriseChangeListVo> listEnterpriseChange(String enterpriseId) {
        return adsPmEnterpriseChangeMapper.listEnterpriseChange(enterpriseId);
    }

    @Override
    public void addOrUpdateEnterpriseChange(EnterpriseChangeReq req) {
        if (StrUtil.isNotBlank(req.getId())) {
            AdsPmEnterpriseChange changeRecord = adsPmEnterpriseChangeMapper.selectById(req.getId());
            changeRecord.setChangeDate(req.getChangeDate());
            changeRecord.setRemark(req.getRemark());
            changeRecord.setChangeType(req.getChangeType());
            changeRecord.setUpdateTime(LocalDateTime.now());
            adsPmEnterpriseChangeMapper.updateById(changeRecord);
        }else {
            AdsPmEnterpriseChange changeRecord = new AdsPmEnterpriseChange();
            BeanUtil.copyProperties(req,changeRecord);
            changeRecord.setId(IdUtil.getSnowflakeNextIdStr());
            changeRecord.setCreateTime(LocalDateTime.now());
            changeRecord.setUpdateTime(LocalDateTime.now());
            adsPmEnterpriseChangeMapper.insert(changeRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delEnterpriseChange(IdReq req) {
        adsPmEnterpriseChangeMapper.deleteById(req.getId());
    }

    private String getEntSerialNo() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMM");
        String currentYearMonth = dateFormat.format(new Date());
        String maxEntSerial = adsPmEnterpriseMapper.getMaxEntSerialNo(currentYearMonth);
        String serialNo = null;
        if (StrUtil.isBlank(maxEntSerial)) {
            serialNo = currentYearMonth + "00001";
        } else {
            Integer maxSerialNo = Integer.valueOf(maxEntSerial.substring(5));
            serialNo = currentYearMonth + String.format("%05d", maxSerialNo + 1);
        }
        return serialNo;
    }

    private String assembleSettleInfo(List<SettleInfoShowVo> infoShows) {
        Map<String, List<String>> map = new HashMap<>();

        infoShows.forEach(info -> {
            String key = info.getCommunityName() + "-" + info.getProjectName() + "-" + info.getBuildingName() + "-" + info.getFloorName() + "层";
            List<String> rooms = map.get(key);
            if (CollUtil.isNotEmpty(rooms)) {
                rooms.add(info.getRoomNo());
            } else {
                map.put(key, CollUtil.newArrayList(info.getRoomNo()));
            }
        });
        StringBuilder infoBuilder = new StringBuilder();
        map.keySet().forEach(key -> {
            List<String> rooms = map.get(key);
            String roomStr = String.join("、", rooms) + "室";
            infoBuilder.append(key + roomStr + ";");
        });
        return infoBuilder.toString();
    }

    private String assembleSettledTime(LocalDate checkInDate, LocalDate compareDate) {

        if (ObjectUtil.isNull(checkInDate)) {
            return null;
        }
        // 计算日期时间差值
        Period period = Period.between(checkInDate, compareDate);

        // 判断天数
        int days = period.getDays();
        int year = period.getYears();
        String yearStr = "";
        String monthStr = "";
        if (period.getYears() >= 1) {
            int years = period.getYears();
            yearStr = years + "年";
        } else {
            yearStr = "";
        }
        if (period.getMonths() >= 1) {
            int months = period.getMonths();
            monthStr = months + "月";
        } else {
            monthStr = "";
        }

        if (StrUtil.isBlank(yearStr) && StrUtil.isBlank(monthStr)) {
            return "新入驻";
        } else {
            return yearStr + monthStr;
        }
    }

    private void addSettleInfoRecord(AdsPmRoomEnterprise settledInfo) {
        AdsPmEnterpriseSettle settle = new AdsPmEnterpriseSettle();
        settle.setCreateTime(LocalDateTime.now());
        settle.setArea(settledInfo.getArea());
        settle.setCheckInDate(settledInfo.getCheckInDate());
        settle.setRealityMoveOutDate(settledInfo.getRealityMoveOutDate());
        settle.setId(IdUtil.getSnowflakeNextIdStr());
        settle.setRoomId(settledInfo.getRoomId());
        settle.setEnterpriseId(settledInfo.getEnterpriseId());
        adsPmEnterpriseSettleMapper.insert(settle);
    }

    private void updateSettleInfoRecord(AdsPmRoomEnterprise settledInfo) {
        QueryWrapper<AdsPmEnterpriseSettle> settleQueryWrapper = new QueryWrapper<>();
        settleQueryWrapper.lambda().eq(AdsPmEnterpriseSettle::getRoomId, settledInfo.getRoomId()).eq(AdsPmEnterpriseSettle::getEnterpriseId, settledInfo.getEnterpriseId());
        AdsPmEnterpriseSettle settle = adsPmEnterpriseSettleMapper.selectOne(settleQueryWrapper);
        settle.setCreateTime(LocalDateTime.now());
        settle.setArea(settledInfo.getArea());
        settle.setCheckInDate(settledInfo.getCheckInDate());
        settle.setRealityMoveOutDate(settledInfo.getRealityMoveOutDate());
        adsPmEnterpriseSettleMapper.updateById(settle);
    }

}
