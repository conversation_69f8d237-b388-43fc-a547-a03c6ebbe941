package com.zjhh.economy.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.exception.BizException;
import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.dao.mapper.AdsDocumentMapper;
import com.zjhh.economy.dto.ResourceDto;
import com.zjhh.economy.request.UploadDocReq;
import com.zjhh.economy.service.DocumentService;
import com.zjhh.economy.utils.FilePreviewUtil;
import com.zjhh.economy.vo.DocumentVo;
import com.zjhh.system.service.DictService;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 10:11
 */
@Service
public class DocumentServiceImpl implements DocumentService {

    @Resource
    private AdsDocumentMapper adsDocumentMapper;

    @Resource
    private DictService dictService;

    @Resource
    private UserSession userSession;

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public DocumentVo upload(UploadDocReq req) {
        AdsDocument adsDocument = new AdsDocument();
        String filename = req.getFile().getOriginalFilename();
        LocalDate now = LocalDate.now();
        String filepath = req.getDocumentType() + File.separator + now.getYear() + File.separator + now.getMonthValue() +
                File.separator + now.getDayOfMonth() + File.separator + IdUtil.getSnowflakeNextIdStr() + "." + FileUtil.getSuffix(filename);
        adsDocument.setId(IdUtil.getSnowflakeNextIdStr());
        adsDocument.setTitle(filename);
        adsDocument.setSize(req.getFile().getSize());
        adsDocument.setPath(filepath);
        if (ObjectUtil.isNotNull(userSession) && StrUtil.isNotBlank(userSession.getUserCode())) {
            adsDocument.setCreateUser(userSession.getUserCode());
        }
        adsDocument.setCreateTime(LocalDateTime.now());
        adsDocument.setUpdateTime(LocalDateTime.now());
        adsDocumentMapper.insert(adsDocument);

        try {
            File temp = FileUtil.touch(dictService.getFileUploadPath() + filepath);
            req.getFile().transferTo(temp);
        } catch (IOException e) {
            throw new BizException("上传文件失败！");
        }
        DocumentVo vo = new DocumentVo();
        vo.setFileId(adsDocument.getId());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public DocumentVo uploadNotToken(UploadDocReq req) {
        AdsDocument adsDocument = new AdsDocument();
        String filename = req.getFile().getOriginalFilename();
        LocalDate now = LocalDate.now();
        String filepath = req.getDocumentType() + File.separator + now.getYear() + File.separator + now.getMonthValue() +
                File.separator + now.getDayOfMonth() + File.separator + IdUtil.getSnowflakeNextIdStr() + "." + FileUtil.getSuffix(filename);
        adsDocument.setId(IdUtil.getSnowflakeNextIdStr());
        adsDocument.setTitle(filename);
        adsDocument.setSize(req.getFile().getSize());
        adsDocument.setPath(filepath);
        adsDocument.setCreateUser("admin");
        adsDocument.setCreateTime(LocalDateTime.now());
        adsDocument.setUpdateTime(LocalDateTime.now());
        adsDocumentMapper.insert(adsDocument);

        try {
            File temp = FileUtil.touch(dictService.getFileUploadPath() + filepath);
            req.getFile().transferTo(temp);
        } catch (IOException e) {
            throw new BizException("上传文件失败！");
        }
        DocumentVo vo = new DocumentVo();
        vo.setFileId(adsDocument.getId());
        return vo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void delete(List<String> ids) {
        List<AdsDocument> list = adsDocumentMapper.selectBatchIds(ids);
        String fileUploadPath = dictService.getFileUploadPath();
        list.forEach(doc -> FileUtil.del(fileUploadPath + doc.getPath()));
        adsDocumentMapper.deleteBatchIds(ids);
    }

    @Override
    public ResourceDto getPreview(String fileId) {
        AdsDocument adsDocument = adsDocumentMapper.selectById(fileId);
        if (ObjectUtil.isNull(adsDocument)) {
            throw new BizException("该文件不存在或已删除！");
        }
        String inPath = dictService.getFileUploadPath() + adsDocument.getPath();
        return FilePreviewUtil.preview(inPath, adsDocument.getTitle());
    }

    @Override
    public AdsDocument getDocument(String id) {
        return adsDocumentMapper.selectById(id);
    }
}
