package com.zjhh.economy.service;

import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.ExportRecordReq;
import com.zjhh.economy.request.ImportExternalDataListReq;
import com.zjhh.economy.request.ImportExternalDataReq;
import com.zjhh.economy.vo.DocumentVo;
import com.zjhh.economy.vo.ImportExternalDataVo;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

public interface ImportExternalDataService {


    Page<ImportExternalDataVo> pageImportExternalData(ImportExternalDataListReq req);



    void importExternalData(ImportExternalDataReq req) throws IOException;

    void exportRecords(HttpServletResponse response, ExportRecordReq req) throws IOException;


    /**
     * 获取模版
     * @param templateType
     * @return
     */
    DocumentVo getTemplate(String templateType);


}
