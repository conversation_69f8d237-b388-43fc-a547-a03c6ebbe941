package com.zjhh.economy.service.impl;

import com.zjhh.economy.dao.mapper.MobileGovEconomicMapper;
import com.zjhh.economy.service.MobileGovEconomicService;
import com.zjhh.economy.vo.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MobileGovEconomicServiceImpl implements MobileGovEconomicService {
    @Resource
    private MobileGovEconomicMapper mobileGovEconomicMapper;
    @Override
    public List<AppDynamicSummaryVo> listDynamicEco() {
        return mobileGovEconomicMapper.listDynamicEcoSummary();
    }

    @Override
    public TabOneVo getTabOne(String dynamicId) {
        return mobileGovEconomicMapper.getTabOne(dynamicId);
    }

    @Override
    public List<TabTwoVo> listTabTwo(String dynamicId) {
        return mobileGovEconomicMapper.listTabTwo(dynamicId);
    }

    @Override
    public List<TabTreeVo> listTabTree(String dynamicId) {
        return mobileGovEconomicMapper.listTabTree(dynamicId);
    }

    @Override
    public List<TabFourVo> listTabFour(String dynamicId) {
        return mobileGovEconomicMapper.listTabFour(dynamicId);
    }

    @Override
    public List<TabFiveVo> listTabFive(String dynamicId) {
        return mobileGovEconomicMapper.listTabFive(dynamicId);
    }

    @Override
    public List<TabSixVo> listTabSix(String dynamicId) {
        return mobileGovEconomicMapper.listTabSix(dynamicId);
    }

    @Override
    public List<TabSevenVo> listTabSeven(String dynamicId) {
        return mobileGovEconomicMapper.listTabSeven(dynamicId);
    }

    @Override
    public List<TabEightVo> listTabEight(String dynamicId) {
        return mobileGovEconomicMapper.listTabEight(dynamicId);
    }
}
