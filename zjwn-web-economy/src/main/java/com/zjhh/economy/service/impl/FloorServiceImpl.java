package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.request.IdReq;
import com.zjhh.economy.dao.entity.AdsPmBuilding;
import com.zjhh.economy.dao.entity.AdsPmFloor;
import com.zjhh.economy.dao.entity.AdsPmFloorConfig;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.request.SaveFloorListReq;
import com.zjhh.economy.request.SaveFloorReq;
import com.zjhh.economy.request.SavePlaneConfigReq;
import com.zjhh.economy.request.UpdatePlaneConfigReq;
import com.zjhh.economy.service.FloorService;
import com.zjhh.economy.utils.OperationLogUtil;
import com.zjhh.economy.vo.FloorConfigMsgVo;
import com.zjhh.economy.vo.FloorConfigSaveVo;
import com.zjhh.economy.vo.FloorRoomInfoVo;
import com.zjhh.economy.vo.FloorVo;
import com.zjhh.economy.vo.operationlog.FloorListForLog;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:25
 */
@Service
public class FloorServiceImpl implements FloorService {

    @Resource
    private AdsPmFloorMapper adsPmFloorMapper;

    @Resource
    private AdsPmRoomMapper adsPmRoomMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private AdsPmFloorConfigMapper adsPmFloorConfigMapper;

    @Resource
    private ChangeInfoMapper changeInfoMapper;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Resource
    private AdsPmBuildingMapper adsPmBuildingMapper;

    @Override
    public List<FloorVo> list(String buildingId) {
        return adsPmFloorMapper.list(buildingId);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void save(SaveFloorReq req) {
        List<FloorListForLog> beforeList = changeInfoMapper.listFloorListForLog(req.getBuildingId());

        List<String> floorIds = new ArrayList<>();
        req.getFloorList().forEach(floor -> {
            if (ObjectUtil.isNull(floor.getFloorNo())) {
                throw new BizException("建筑楼层不能为空！");
            }
            if (StrUtil.isBlank(floor.getFloorName())) {
                throw new BizException("楼层名称不能为空！");
            }
            if (StrUtil.isNotBlank(floor.getFloorId())) {
                floorIds.add(floor.getFloorId());
            }
        });
        if (adsPmFloorMapper.countRoomSize(req.getBuildingId(), floorIds) > 0) {
            throw new BizException("所选楼层存在房间，不可删除！");
        }
        QueryWrapper<AdsPmFloor> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsPmFloor::getBuildingId, req.getBuildingId())
                .notIn(CollUtil.isNotEmpty(floorIds), AdsPmFloor::getId, floorIds);
        adsPmFloorMapper.delete(wrapper);

        List<SaveFloorListReq> updateFloorList = new ArrayList<>();
        List<SaveFloorListReq> addFloorList = new ArrayList<>();
        req.getFloorList().forEach(floor -> {
            if (StrUtil.isNotBlank(floor.getFloorId())) {
                updateFloorList.add(floor);
            } else {
                addFloorList.add(floor);
            }
        });

        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();
        if (CollUtil.isNotEmpty(addFloorList)) {
            List<AdsPmFloor> adsPmFloorList = new ArrayList<>(addFloorList.size());
            addFloorList.forEach(addFloor -> {
                AdsPmFloor adsPmFloor = BeanUtil.copyProperties(addFloor, AdsPmFloor.class);
                adsPmFloor.setId(addFloor.getFloorId());
                adsPmFloor.setBuildingId(req.getBuildingId());
                adsPmFloor.setCreateUser(userCode);
                adsPmFloor.setCreateTime(now);
                adsPmFloor.setUpdateTime(now);
                adsPmFloorList.add(adsPmFloor);
            });
            adsPmFloorMapper.insertBatchSomeColumn(adsPmFloorList);
        }

        if (CollUtil.isNotEmpty(updateFloorList)) {
            for (SaveFloorListReq updateFloor : updateFloorList) {
                LambdaUpdateWrapper<AdsPmFloor> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(AdsPmFloor::getId, updateFloor.getFloorId())
                        .set(AdsPmFloor::getFloorName, updateFloor.getFloorName())
                        .set(AdsPmFloor::getRemark, updateFloor.getRemark())
                        .set(AdsPmFloor::getPlaneImgId, updateFloor.getPlaneImgId())
                        .set(AdsPmFloor::getUpdateTime, now)
                        .set(AdsPmFloor::getPlaneConfigId, updateFloor.getPlaneConfigId());
                adsPmFloorMapper.update(updateWrapper);
            }
        }
        AdsPmBuilding adsPmBuilding = adsPmBuildingMapper.selectById(req.getBuildingId());
        if (adsPmBuilding != null) {
            List<FloorListForLog> afterList = changeInfoMapper.listFloorListForLog(req.getBuildingId());
            operationLogUtil.recordLog(LogModuleEnum.BUILDING, LogTypeEnum.BUILDING_FLOOR_CONFIG, adsPmBuilding.getId(),
                    adsPmBuilding.getBuildingName(), JSON.toJSONString(beforeList), JSON.toJSONString(afterList));
        }
    }

    @Override
    public FloorConfigMsgVo getPlaneConfig(IdReq idReq) {
        AdsPmFloorConfig adsPmFloorConfig = adsPmFloorConfigMapper.selectById(idReq.getId());
        if (adsPmFloorConfig == null) {
            return null;
        }
        return new FloorConfigMsgVo(adsPmFloorConfig.getPlaneConfig(), adsPmFloorConfig.getId());
    }

    @Override
    public FloorConfigSaveVo savePlaneConfig(SavePlaneConfigReq req) {
        AdsPmFloor adsPmFloor = adsPmFloorMapper.selectById(req.getFloorId());
        if (adsPmFloor == null) {
            throw new BizException("楼层不存在！");
        }
        AdsPmFloorConfig adsPmFloorConfig = new AdsPmFloorConfig();
        adsPmFloorConfig.setPlaneConfig(req.getPlaneConfig());
        adsPmFloorConfig.setFloorId(req.getFloorId());
        adsPmFloorConfig.setId(IdUtil.getSnowflakeNextIdStr());
        adsPmFloorConfig.setCreateTime(LocalDateTime.now());
        adsPmFloorConfig.setCreateUser(userSession.getUserCode());
        adsPmFloorConfigMapper.insert(adsPmFloorConfig);
        return new FloorConfigSaveVo(adsPmFloorConfig.getId());
    }

    @Override
    public void updatePlaneConfig(UpdatePlaneConfigReq req) {
        LambdaUpdateWrapper<AdsPmFloorConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AdsPmFloorConfig::getId, req.getPlaneConfigId())
                .set(AdsPmFloorConfig::getPlaneConfig, req.getPlaneConfig());
        adsPmFloorConfigMapper.update(updateWrapper);
    }

    @Override
    public FloorRoomInfoVo getRoomInfo(IdReq idReq) {
        return adsPmRoomMapper.getRoomInfo(idReq.getId());
    }

    @Override
    public List<FloorRoomInfoVo> listRoomInfo(IdReq idReq) {
        return adsPmRoomMapper.listRoomInfo(idReq.getId());
    }
}
