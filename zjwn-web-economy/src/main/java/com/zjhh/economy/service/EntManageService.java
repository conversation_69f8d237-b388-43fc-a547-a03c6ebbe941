package com.zjhh.economy.service;

import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.*;
import com.zjhh.economy.request.analyzereport.EntCompareExportReq;
import com.zjhh.economy.request.analyzereport.EntSearchReq;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.analyzereport.EntCompareVo;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 企业管理
 */
public interface EntManageService {


    void addEnterprise(AddEntReq req);

    void updateEnterprise(UpdateEntReq req);

    void addSettleInfo(AddSettleInfoReq req);

    void updateSettleInfo(UpdateSettleInfoReq req);

    void saveTalent(SaveTalentReq req);

    void moveRegister(MoveRegisterReq req);

    void editMoveRegister(EditMoveRegisterReq req);

    MoveRegisterDetailVo getMoveRegisterDetail(IdReq req);

    List<SettleInfoListVo> listSettleInfoList(QuerySettleInfoPageReq req);

    List<SettleInfoMenuVo> moveRegisterMenu(String enterpriseId,List<String> ids);

    void deleteSettleInfo(IdReq req);

    /**
     * 企业详情比头
     *
     * @param enterpriseId
     * @return
     */
    EnterpriseDetailHeaderVo getDetailHeader(String enterpriseId);

    /**
     * 企业详情
     *
     * @param enterpriseId
     * @return
     */
    EnterpriseDetailVo getDetail(String enterpriseId);

    List<TaxAnalyseChartVo> listTaxAnalyseChart(QueryTaxAnalyseReq req);


    List<Map<String, Object>> listTaxAnalyseTable(QueryTaxAnalyseReq req);


    Page<EnterpriseListVo> pageEnterpriseList(QueryEnterprisePageReq req);

    void addFocus(String enterpriseId);

    void cancelFocus(String enterpriseId);

    void deleteEnterprise(String enterpriseId);

    SettleInfoDetailVo getSettleInfoDetail(IdReq req);

    TalentDetailVo getTalentDetail(IdReq req);

    void updateTalentDetail(SaveTalentReq req);


    List<TreeSelectVo> listEntSearch(EntKeywordSearchReq req);

    RoomSettleInfoDetailVo getSettledInfoByRoomDetail(String enterpriseId, String roomId);

    EntCompareVo getEntCompareInfo(String enterpriseId);

    void exportEntCompareInfo(EntCompareExportReq req, HttpServletResponse response) throws IOException;

    List<TreeSelectVo> listMenuEnt(EntSearchReq req);

    List<TreeSelectVo> listMenuMobileEnt(EntKeywordSearchReq req);


    List<TreeSelectVo> listBuildingSearch(BuildingKeywordSearchReq req);

    /**
     * 企业变更记录
     * @param enterpriseId
     * @return
     */
    List<EnterpriseChangeListVo> listEnterpriseChange(String enterpriseId);

    /**
     * 新增或更新企业变更记录
     * @param req
     */
    void addOrUpdateEnterpriseChange(EnterpriseChangeReq req);

    /**
     * 删除变更记录
     * @param req
     */
    void delEnterpriseChange(IdReq req);











}
