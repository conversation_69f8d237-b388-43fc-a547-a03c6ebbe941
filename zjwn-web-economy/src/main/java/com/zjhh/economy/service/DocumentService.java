package com.zjhh.economy.service;

import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.dto.ResourceDto;
import com.zjhh.economy.request.UploadDocReq;
import com.zjhh.economy.vo.DocumentVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 10:11
 */
public interface DocumentService {

    /**
     * 上传文件
     *
     * @param req
     * @return
     */
    DocumentVo upload(UploadDocReq req);

    DocumentVo uploadNotToken(UploadDocReq req);

    /**
     * 删除文件
     *
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 获取文件预览
     *
     * @param fileId
     * @return
     */
    ResourceDto getPreview(String fileId);

    /**
     * 获取文件
     *
     * @param id
     * @return
     */
    AdsDocument getDocument(String id);
}
