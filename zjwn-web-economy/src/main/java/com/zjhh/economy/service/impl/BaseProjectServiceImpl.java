package com.zjhh.economy.service.impl;

import com.zjhh.economy.dao.mapper.AdsPmBuildingMapper;
import com.zjhh.economy.dao.mapper.AdsPmProjectMapper;
import com.zjhh.economy.dto.SerialNoDto;
import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/3/8 09:46
 */
public class BaseProjectServiceImpl {

    @Resource
    private AdsPmProjectMapper adsPmProjectMapper;

    @Resource
    private AdsPmBuildingMapper adsPmBuildingMapper;

    /**
     * 根据社区编码获取编码
     *
     * @param communityCode 社区编码
     * @return
     */
    public SerialNoDto getProjectSerialNo() {
        int xh = adsPmProjectMapper.findMaxXh() + 1;
        SerialNoDto dto = new SerialNoDto();
        dto.setXh(xh);
        dto.setSerialNo(String.valueOf(1000 + xh));
        return dto;
    }

    /**
     * 根据项目编号获取楼宇编号
     *
     * @param projectSerialNo
     * @return
     */
    public SerialNoDto getBuildingSerialNo(String projectSerialNo) {
        Integer xh = adsPmBuildingMapper.findMaxXh(projectSerialNo) + 1;
        SerialNoDto dto = new SerialNoDto();
        dto.setXh(xh);
        dto.setSerialNo(projectSerialNo + "-" + xh);
        return dto;
    }

    public String getBuildingSerialNo(String projectSerialNo, Integer xh) {
        return projectSerialNo + "-" + xh;
    }

}
