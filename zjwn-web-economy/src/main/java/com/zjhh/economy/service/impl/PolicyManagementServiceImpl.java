package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.request.IdReq;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsBusinessDocument;
import com.zjhh.economy.dao.entity.AdsPmPolicyMt;
import com.zjhh.economy.dao.entity.AdsPmPolicyMtCash;
import com.zjhh.economy.dao.mapper.AdsBusinessDocumentMapper;
import com.zjhh.economy.dao.mapper.AdsPmEnterpriseMapper;
import com.zjhh.economy.dao.mapper.AdsPmPolicyMtCashMapper;
import com.zjhh.economy.dao.mapper.AdsPmPolicyMtMapper;
import com.zjhh.economy.enume.DocumentTypeEnum;
import com.zjhh.economy.request.policymanagement.PolicyManagementAddOrUpdateReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementCashBatchReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementCashReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementPageReq;
import com.zjhh.economy.service.PolicyManagementService;
import com.zjhh.economy.vo.policymanagement.PolicyManagementCashVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementVo;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Service
public class PolicyManagementServiceImpl implements PolicyManagementService {

    @Resource
    private AdsPmPolicyMtMapper policyMtMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private AdsBusinessDocumentMapper adsBusinessDocumentMapper;

    @Resource
    private AdsPmPolicyMtCashMapper policyMtCashMapper;

    @Resource
    private AdsPmEnterpriseMapper enterpriseMapper;

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addPolicyManagement(PolicyManagementAddOrUpdateReq req) {
        LocalDateTime now = LocalDateTime.now();
        AdsPmPolicyMt adsPmPolicyMt = BeanUtil.toBean(req, AdsPmPolicyMt.class);
        adsPmPolicyMt.setId(IdUtil.getSnowflakeNextIdStr());
        adsPmPolicyMt.setCreateTime(now);
        adsPmPolicyMt.setCreateUser(userSession.getUserCode());
        policyMtMapper.insert(adsPmPolicyMt);
        List<AdsBusinessDocument> list = getPolicyDocuments(req, adsPmPolicyMt.getId());
        if (CollUtil.isNotEmpty(list)) {
            adsBusinessDocumentMapper.insertBatchSomeColumn(list);
        }
    }

    private List<AdsBusinessDocument> getPolicyDocuments(PolicyManagementAddOrUpdateReq req, String policyManagementId) {
        List<String> fileIds = req.getFileIds();
        if (CollUtil.isEmpty(fileIds)) {
            return new ArrayList<>();
        }
        List<AdsBusinessDocument> list = new ArrayList<>();
        for (String fileId : fileIds) {
            AdsBusinessDocument adsBusinessDocument = new AdsBusinessDocument();
            adsBusinessDocument.setId(IdUtil.getSnowflakeNextIdStr());
            adsBusinessDocument.setBusinessId(policyManagementId);
            adsBusinessDocument.setDocumentId(fileId);
            adsBusinessDocument.setDocumentType(DocumentTypeEnum.POLICY_MANAGEMENT_FILE.value());
            list.add(adsBusinessDocument);
        }
        return list;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updatePolicyManagement(PolicyManagementAddOrUpdateReq req) {
        AdsPmPolicyMt adsPmPolicyMt = policyMtMapper.selectById(req.getId());
        if (adsPmPolicyMt == null) {
            throw new BizException("政策不存在！");
        }
        LocalDateTime now = LocalDateTime.now();
        LambdaUpdateWrapper<AdsPmPolicyMt> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AdsPmPolicyMt::getId, req.getId())
                .set(AdsPmPolicyMt::getPolicyName, req.getPolicyName())
                .set(AdsPmPolicyMt::getPolicyNo, req.getPolicyNo())
                .set(AdsPmPolicyMt::getPublishAgency, req.getPublishAgency())
                .set(AdsPmPolicyMt::getPublishDate, req.getPublishDate())
                .set(AdsPmPolicyMt::getWrittenDate, req.getWrittenDate())
                .set(AdsPmPolicyMt::getPolicyDetail, req.getPolicyDetail())
                .set(AdsPmPolicyMt::getUpdateTime, now)
                .set(AdsPmPolicyMt::getUpdateUser, userSession.getUserCode());
        policyMtMapper.update(updateWrapper);
        // 删除先前关联的附件
        LambdaQueryWrapper<AdsBusinessDocument> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsBusinessDocument::getBusinessId, req.getId())
                .eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.POLICY_MANAGEMENT_FILE.value());
        adsBusinessDocumentMapper.delete(deleteWrapper);
        // 重新添加附件
        List<AdsBusinessDocument> list = getPolicyDocuments(req, req.getId());
        if (CollUtil.isNotEmpty(list)) {
            adsBusinessDocumentMapper.insertBatchSomeColumn(list);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addPolicyManagementCash(PolicyManagementCashBatchReq req) {
        AdsPmPolicyMt adsPmPolicyMt = policyMtMapper.selectById(req.getPolicyId());
        if (adsPmPolicyMt == null) {
            throw new BizException("政策不存在！");
        }
        List<PolicyManagementCashReq> cashList = req.getCashList();
        // 添加新的兑现
        if (CollUtil.isEmpty(cashList)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        List<AdsPmPolicyMtCash> list = new ArrayList<>();
        for (PolicyManagementCashReq policyManagementCashReq : cashList) {
            AdsPmPolicyMtCash adsPmPolicyMtCash = BeanUtil.toBean(policyManagementCashReq, AdsPmPolicyMtCash.class);
            adsPmPolicyMtCash.setPolicyId(adsPmPolicyMt.getId());
            adsPmPolicyMtCash.setId(IdUtil.getSnowflakeNextIdStr());
            adsPmPolicyMtCash.setCreateTime(now);
            adsPmPolicyMtCash.setCreateUser(userSession.getUserCode());
            list.add(adsPmPolicyMtCash);
        }
        policyMtCashMapper.insertBatchSomeColumn(list);
    }

    @Override
    public PolicyManagementVo getPolicyManagement(IdReq req) {
        PolicyManagementVo policyManagementVo = policyMtMapper.getPolicyManagement(req.getId());
        if (policyManagementVo != null) {
            List<PolicyManagementCashVo> cashList = policyManagementVo.getCashList();
            if (CollUtil.isNotEmpty(cashList)) {
                policyManagementVo.setCashEntNum(cashList.size());
                BigDecimal totalAmount = BigDecimal.ZERO;
                int xh = 0;
                for (PolicyManagementCashVo cash : cashList) {
                    cash.setXh(++xh);
                    if (cash.getCashAmount() != null) {
                        totalAmount = totalAmount.add(cash.getCashAmount());
                    }
                }
                totalAmount = totalAmount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP);
                policyManagementVo.setCashTotalAmount(totalAmount);
            } else {
                policyManagementVo.setCashEntNum(0);
                policyManagementVo.setCashTotalAmount(BigDecimal.ZERO);

            }
        }
        return policyManagementVo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deletePolicyManagement(IdReq req) {
        // 删除附件
        LambdaQueryWrapper<AdsBusinessDocument> deleteFileWrapper = new LambdaQueryWrapper<>();
        deleteFileWrapper.eq(AdsBusinessDocument::getBusinessId, req.getId())
                .eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.POLICY_MANAGEMENT_FILE.value());
        adsBusinessDocumentMapper.delete(deleteFileWrapper);
        // 删除兑现
        LambdaQueryWrapper<AdsPmPolicyMtCash> deleteCashWrapper = new LambdaQueryWrapper<>();
        deleteCashWrapper.eq(AdsPmPolicyMtCash::getPolicyId, req.getId());
        policyMtCashMapper.delete(deleteCashWrapper);
        // 删除政策
        policyMtMapper.deleteById(req.getId());
    }

    @Override
    public Page<PolicyManagementPageVo> pagePolicyManagement(PolicyManagementPageReq req) {
        return policyMtMapper.pagePolicyManagement(req.getPage(PolicyManagementPageVo.class), req);
    }

    @Override
    public List<PolicyManagementCashVo> listPolicyManagementCash(IdReq req) {
        return policyMtMapper.listPolicyManagementCash(req.getId());
    }

    @Override
    public List<PolicyManagementPageVo> listPolicyByCockpit() {
        return policyMtMapper.listPolicyByCockpit();
    }
}
