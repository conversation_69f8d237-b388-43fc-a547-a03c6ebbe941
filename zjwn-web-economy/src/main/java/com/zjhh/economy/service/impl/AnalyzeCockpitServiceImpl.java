package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.economy.dao.entity.AdsPmEconomicScore;
import com.zjhh.economy.dao.mapper.AdsPmEconomicScoreMapper;
import com.zjhh.economy.dao.mapper.AnalyzeCockpitMapper;
import com.zjhh.economy.enume.CompareEnum;
import com.zjhh.economy.request.MobileCompareReq;
import com.zjhh.economy.request.analyzecockpit.*;
import com.zjhh.economy.service.AnalyzeCockpitService;
import com.zjhh.economy.vo.MobileCompareVo;
import com.zjhh.economy.vo.analyzecockpit.*;
import com.zjhh.economy.vo.cockpit.EconomicIndicatorScoreVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AnalyzeCockpitServiceImpl implements AnalyzeCockpitService {

    @Resource
    private AnalyzeCockpitMapper analyzeCockpitMapper;

    @Resource
    private AdsPmEconomicScoreMapper adsPmEconomicScoreMapper;





    @Override
    public AreaAnalyzeVo getAreaAnalyze(SpaceAnalyzeReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }

        if (req.getType().equals("1") && StrUtil.isEmpty(req.getProjectType())) {
            req.setProjectType("ly");
        }
        return analyzeCockpitMapper.getAreaAnalyze(req);
    }

    @Override
    public List<SettledTrendVo> listSettledTrend(AnalyzeDateReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getType().equals("1") && StrUtil.isEmpty(req.getProjectType())) {
            req.setProjectType("ly");
        }
        if (req.getDateType().equals(1)) {
            return analyzeCockpitMapper.listSettledTrendByMonth(req);
        } else if (req.getDateType().equals(2)) {
            req.setCurrentYear(String.valueOf(DateUtil.year(new Date())));
            return analyzeCockpitMapper.listSettledTrendByQuarter(req);
        } else {
            return analyzeCockpitMapper.listSettledTrendByYear(req);
        }
    }

    @Override
    public TaxAnalyzeVo getTaxAnalyze(SpaceAnalyzeReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.getTaxAnalyze(req);
    }

    @Override
    public List<TaxTrendVo> listTaxTrend(AnalyzeDateReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getDateType().equals(1)) {
            return analyzeCockpitMapper.listTaxTrendByMonth(req);
        } else if (req.getDateType().equals(2)) {
            req.setCurrentYear(String.valueOf(DateUtil.year(new Date())));
            return analyzeCockpitMapper.listTaxTrendByQuarter(req);
        } else {
            return analyzeCockpitMapper.listTaxTrendByYear(req);
        }
    }

    @Override
    public SettledEntAnalyzeVo getSettledEntAnalyze(SpaceAnalyzeReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.getSettledEntAnalyze(req);
    }

    @Override
    public List<SettledEntUnitTrendVo> listSettledEntUnitTrend(AnalyzeDateReq req) {
        if (req.getDateType().equals(1)) {
            return analyzeCockpitMapper.listSettledEntUnitTrendByMonth(req);
        } else if (req.getDateType().equals(2)) {
            req.setCurrentYear(String.valueOf(DateUtil.year(new Date())));
            return analyzeCockpitMapper.listSettledEntUnitTrendByQuarter(req);
        } else {
            return analyzeCockpitMapper.listSettledEntUnitTrendByYear(req);
        }
    }

    @Override
    public List<IndustryFocusAnalyzeVo> listTaxScaledRank(IndustryAnalyzeReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        req.setIndustryLength(req.getIndustryType().equals(1) ? 2 : 3);
        return analyzeCockpitMapper.listIndustryFocusAnalyzeTaxRank(req);
    }

    @Override
    public List<IndustryFocusAnalyzeVo> listEntCountRank(IndustryAnalyzeReq req) {
        req.setIndustryLength(req.getIndustryType().equals(1) ? 2 : 3);
        return analyzeCockpitMapper.listIndustryFocusAnalyzeEntCountRank(req);
    }

    @Override
    public List<IndustryFocusAnalyzeVo> listSettledAreaRank(IndustryAnalyzeReq req) {
        req.setIndustryLength(req.getIndustryType().equals(1) ? 2 : 3);
        return analyzeCockpitMapper.listIndustryFocusAnalyzeTaxSettledAreaRank(req);
    }

    @Override
    public CockpitProjectSummaryVo getCockpitProjectSummary(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.getCockpitProjectSummary(req);
    }

    @Override
    public CockpitBuildingSummaryVo getCockpitBuildingSummary(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.getCockpitBuildingSummary(req);
    }

    @Override
    public SpaceAnalyzeSummaryVo getSpaceAnalyzeSummary(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getType().equals("1") && StrUtil.isEmpty(req.getProjectType())) {
            req.setProjectType("ly");
        }
        return analyzeCockpitMapper.getSpaceAnalyzeSummary(req);
    }

    @Override
    public List<SettledTrendVo> listSettledTrendBySpace(AnalyzeDateReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getType().equals("1") && StrUtil.isEmpty(req.getProjectType())) {
            req.setProjectType("ly");
        }
        if (req.getDateType().equals(1)) {
            return analyzeCockpitMapper.listSettledTrendByMonth(req);
        } else if (req.getDateType().equals(2)) {
            req.setCurrentYear(String.valueOf(DateUtil.year(new Date())));
            return analyzeCockpitMapper.listSettledTrendByQuarter(req);
        } else {
            return analyzeCockpitMapper.listSettledTrendByYear(req);
        }
    }

    @Override
    public List<SpaceResourceVo> listSpaceResourceBySpace(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getType().equals("1") && StrUtil.isEmpty(req.getProjectType())) {
            req.setProjectType("ly");
        }
        if (req.getType().equals("1") || StrUtil.isBlank(req.getType())) {
            req.setType(null);
            return analyzeCockpitMapper.listSpaceResourceByCommunity(req);
        } else if (req.getType().equals("2")) {
            return analyzeCockpitMapper.listSpaceResourceByProject(req);
        } else {
            return analyzeCockpitMapper.listSpaceResourceByBuilding(req);
        }
    }

    @Override
    public List<RoomResourceVo> listRoomResourceBySpace(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listRoomResource(req);
    }

    @Override
    public EmptyPeriodSummaryVo getEmptyPeriodSummary(AnalyzeCockpitCommonReq req) {
        EmptyPeriodSummaryVo emptyPeriodSummaryVo = new EmptyPeriodSummaryVo();
        List<RoomPeriodVo> emptyRooms = analyzeCockpitMapper.listEmptyPeriod(req);
        Integer totalCount = analyzeCockpitMapper.getEmptyPeriodCount(req);
        emptyPeriodSummaryVo.setTotalCount(totalCount);
        emptyPeriodSummaryVo.setRooms(emptyRooms);

        return emptyPeriodSummaryVo;
    }

    @Override
    public List<RoomResourceVo> listEntSettledArea(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listEntRentRoomResource(req);
    }

    @Override
    public List<RoomResourceVo> listEmptyRoomArea(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listEmptyRoomResource(req);
    }

    @Override
    public List<FloorEmptyAreaRankVo> listFloorEmptyAreaRank(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listFloorEmptyAreaRank(req);
    }

    @Override
    public EconomicSummaryVo getEconomicSummary(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.getEconomicSummary(req);
    }

    @Override
    public TaxIncomeReasonVo getTaxIncomeReason(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        TaxIncomeReasonVo reasonVo = analyzeCockpitMapper.getTaxIncomeReasonByTax(req);
        BigDecimal taxChangeAmt = analyzeCockpitMapper.getTaxIncomeReasonByEntTax(req);
        BigDecimal moveChangeAmt = analyzeCockpitMapper.getTaxIncomeReasonByMove(req);
        reasonVo.setEntTaxReasonAmt(taxChangeAmt);
        reasonVo.setRegisterReasonAmt(moveChangeAmt);

        return reasonVo;
    }

    @Override
    public List<TaxTrendVo> listEconomicTaxTrend(EconomicTaxTrendReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getIncomeType().equals(1)) {
            if (req.getDateType().equals(1)) {
                return analyzeCockpitMapper.listTaxTrendByMonth(req);
            } else if (req.getDateType().equals(2)) {
                req.setCurrentYear(String.valueOf(DateUtil.year(new Date())));
                return analyzeCockpitMapper.listTaxTrendByQuarter(req);
            } else {
                return analyzeCockpitMapper.listTaxTrendByYear(req);
            }
        } else {
            if (req.getDateType().equals(1)) {
                return analyzeCockpitMapper.listStreetTaxTrendByMonth(req);
            } else if (req.getDateType().equals(2)) {
                req.setCurrentYear(String.valueOf(DateUtil.year(new Date())));
                return analyzeCockpitMapper.listStreetTaxTrendByQuarter(req);
            } else {
                return analyzeCockpitMapper.listStreetTaxTrendByYear(req);
            }
        }
    }

    @Override
    public List<TaxCategoryVo> listTaxCategory(EconomicTaxTrendReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getIncomeType().equals(1)) {
            return analyzeCockpitMapper.listTaxCategoryBySs(req);
        } else {
            return analyzeCockpitMapper.listTaxCategoryByStreet(req);
        }
    }

    @Override
    public List<UnitIncomeTrendVo> listUnitIncomeTrend(AnalyzeDateReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getDateType().equals(1)) {
            return analyzeCockpitMapper.listUnitIncomeTrendByMonth(req);
        } else if (req.getDateType().equals(2)) {
            req.setCurrentYear(String.valueOf(DateUtil.year(new Date())));
            return analyzeCockpitMapper.listUnitIncomeByQuarter(req);
        } else {
            return analyzeCockpitMapper.listUnitIncomeTrendByYear(req);
        }
    }

    @Override
    public List<IndustryFocusAnalyzeVo> listIndustryFocus(IndustryAnalyzeReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        req.setIndustryLength(req.getIndustryType().equals(1) ? 2 : 3);
        return analyzeCockpitMapper.listIndustryFocusAnalyze(req);
    }

    @Override
    public List<IndustryTaxTrendVo> listIndustryTaxTrend(IndustryTrendReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.listIndustryTaxTrendByMonth(req);
    }

    @Override
    public List<IndustryContributeVo> listIndustryContribute(IndustryTrendReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.listIndustryEntCountTrendByMonth(req);
    }

    @Override
    public List<IndustrySettledAreaVo> listIndustrySettledArea(IndustryTrendReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.listIndustrySettledAreaTrendByMonth(req);
    }

    @Override
    public EntSettledSummaryVo getEntSettledSummary(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.getEntSettledSummary(req);
    }

    @Override
    public List<EntTaxRankVo> listEntRank(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.listEntTaxRank(req);
    }

    @Override
    public List<EntSettledAreaVo> listEntSettled(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listEntSettledAreaRank(req);
    }

    @Override
    public List<EntStableRankVo> listEntStableRank(AnalyzeCockpitCommonReq req) {

        return analyzeCockpitMapper.listEntStableRank(req);
    }

    @Override
    public List<EntLocaledDisVo> listEntLocaledDis(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.listEntLocaledDis(req);
    }

    @Override
    public List<LocaledRegisterTrendVo> listLocaledRegisterTrend(AnalyzeDateReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getDateType().equals(1)) {
            return analyzeCockpitMapper.listLocaledRegisterTrendByMonth(req);
        } else if (req.getDateType().equals(2)) {
            return analyzeCockpitMapper.listLocaledRegisterTrendByQuarter(req);
        } else {
            return analyzeCockpitMapper.listLocaledRegisterTrendByYear(req);
        }
    }

    @Override
    public List<UnitPropertyDisVo> listUnitPropertyDis(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listUnitPropertyDis(req);
    }

    @Override
    public List<EntTaxIncomeVo> listEntTaxIncome(AnalyzeCockpitCommonReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.listEntTaxIncome(req);
    }

    @Override
    public List<RegisterBusinessSameVo> listRegisterBusinessSame(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listRegisterBusinessSame(req);
    }

    @Override
    public List<RegisterTypeVo> listRegisterType(AnalyzeCockpitCommonReq req) {
        return analyzeCockpitMapper.listRegisterType(req);
    }

    @Override
    public List<TreeSelectVo> listDefaultIndustries(IndustryTrendReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        return analyzeCockpitMapper.listIndustryTop5(req);
    }

    @Override
    public AppSummaryVo getAppSummary(AnalyzeCockpitCommonReq req) {
        String taxCurrentDate = null;
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
            taxCurrentDate = req.getQueryDate();
        }else {
           taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }

        AppSummaryVo appSummaryVo = new AppSummaryVo();
        SpaceAnalyzeReq taxReq = new SpaceAnalyzeReq();
        taxReq.setProjectType(req.getProjectType());
        taxReq.setCode(req.getCode());
        taxReq.setType(req.getType());
        taxReq.setTaxCurrentDate(taxCurrentDate);
        SpaceAnalyzeSummaryVo analyzeVo = analyzeCockpitMapper.getSpaceAnalyzeSummary(req);
        EconomicSummaryVo economicSummaryVo = analyzeCockpitMapper.getEconomicSummary(req);
        EntSettledSummaryVo entSettledSummaryVo = analyzeCockpitMapper.getEntSettledSummary(req);
        BeanUtil.copyProperties(analyzeVo, appSummaryVo);
        BeanUtil.copyProperties(economicSummaryVo, appSummaryVo);
        BeanUtil.copyProperties(entSettledSummaryVo, appSummaryVo);
        return appSummaryVo;
    }

    @Override
    public List<AppEconomicVo> listAppEconomic(AppEconomicReq req) {
        if (StrUtil.isNotBlank(req.getQueryDate())) {
            req.setTaxCurrentDate(req.getQueryDate());
        }else {
            String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
            req.setTaxCurrentDate(taxCurrentDate);
        }
        if (req.getCompareType().equals(CompareEnum.COMMUNITY.value())) {
            return analyzeCockpitMapper.listAppEconomicByCommunity(req);
        } else if (req.getCompareType().equals(CompareEnum.PROJECT.value())) {
            return analyzeCockpitMapper.listAppEconomicByProject(req);
        } else {
            return analyzeCockpitMapper.listAppEconomicByBuilding(req);
        }
    }

    @Override
    public EconomicIndicatorScoreVo getEconomicIndicatorScore(String buildingId) {

        String queryDateStr = analyzeCockpitMapper.getTaxMaxDate();
        EconomicIndicatorScoreVo vo = adsPmEconomicScoreMapper.getEconomicIndicatorScore(buildingId,queryDateStr);
        if (ObjectUtil.isEmpty(vo)) {
            vo = new EconomicIndicatorScoreVo();
            vo.setScore(new BigDecimal(0));
            vo.setTaxIncomeScore(new BigDecimal(0));
            vo.setTaxRateScore(new BigDecimal(0));
            vo.setSettledRateScore(new BigDecimal(0));
            vo.setEntCultivateScore(new BigDecimal(0));
            vo.setIndustryScaleScore(new BigDecimal(0));
            vo.setUnitOutputScore(new BigDecimal(0));
        }
        return vo;
    }

    @Override
    public void generationBuildingScore() {
        String taxCurrentDate = analyzeCockpitMapper.getTaxMaxDate();
        DateTime queryDate = DateUtil.parse(taxCurrentDate,"yyyyMM");
        DateTime lastQueryDate = DateUtil.offsetMonth(queryDate, -12);
        String queryDateStr = DateUtil.format(queryDate, DatePattern.SIMPLE_MONTH_FORMATTER);
        String lastQueryDateStr = DateUtil.format(lastQueryDate, DatePattern.SIMPLE_MONTH_FORMATTER);
        List<String> buildingIds = analyzeCockpitMapper.listBuildingIds();
        Map<String, BigDecimal> taxMaxMin = analyzeCockpitMapper.getTaxByMaxMin(queryDateStr);
        BigDecimal maxTax = taxMaxMin.get("maxtaxincome");
        BigDecimal minTax = taxMaxMin.get("mintaxincome");
        Map<String, BigDecimal> settledAreaMaxMin = analyzeCockpitMapper.getSettledRateByMaxMin(queryDateStr);
        BigDecimal maxSettledArea = settledAreaMaxMin.get("maxsettledrate");
        BigDecimal minSettledArea = settledAreaMaxMin.get("minsettledrate");
        Map<String, BigDecimal> localedRateMaxMin = analyzeCockpitMapper.getLocalRateByMaxMin(queryDateStr);
        BigDecimal maxLocaledRate = localedRateMaxMin.get("maxlocaledrate");
        BigDecimal minLocaledRate = localedRateMaxMin.get("minlocaledrate");
        Map<String, BigDecimal> TaxRateMaxMin = analyzeCockpitMapper.getTaxRateByMaxMin(queryDateStr, lastQueryDateStr);
        BigDecimal maxTaxRate = TaxRateMaxMin.get("maxtaxrate");
        BigDecimal minTaxRate = TaxRateMaxMin.get("mintaxrate");
        Map<String, BigDecimal> unitOutputMaxMin = analyzeCockpitMapper.getUnitOutputByMaxMin(queryDateStr);
        BigDecimal maxUnitOutput = unitOutputMaxMin.get("maxunitoutput");
        BigDecimal minUnitOutput = unitOutputMaxMin.get("minunitoutput");
        Map<String, BigDecimal> top3TaxMaxMin = analyzeCockpitMapper.getTop3IndustryTaxByMaxMin(queryDateStr);
        BigDecimal maxTop3Tax = top3TaxMaxMin.get("maxareatop3");
        BigDecimal minTop3Tax = top3TaxMaxMin.get("minareatop3");
        buildingIds.forEach(buildingId -> {
            BigDecimal currentTax = analyzeCockpitMapper.getTaxByBuilding(buildingId, queryDateStr);
            BigDecimal currentSettledRate = analyzeCockpitMapper.getSettledRateByBuilding(buildingId, queryDateStr);
            BigDecimal currentLocaledRate = analyzeCockpitMapper.getLocalRateByBuilding(buildingId, queryDateStr);
            BigDecimal currentTaxRate = analyzeCockpitMapper.getTaxRateByBuilding(buildingId, queryDateStr, lastQueryDateStr);
            BigDecimal currentUnitOutput = analyzeCockpitMapper.getUnitOutputByBuilding(buildingId, queryDateStr);
            BigDecimal currentTop3 = analyzeCockpitMapper.getTop3IndustryTaxByBuilding(buildingId, queryDateStr);
            BigDecimal taxPoint = finalValue(maxTax, minTax, currentTax);
            BigDecimal settledRatePoint = finalValue(maxSettledArea, minSettledArea, currentSettledRate);
            BigDecimal localedRatePoint = finalValue(maxLocaledRate, minLocaledRate, currentLocaledRate);
            BigDecimal taxRatePoint = finalValue(maxTaxRate, minTaxRate, currentTaxRate);
            BigDecimal unitOutputPoint = finalValue(maxUnitOutput, minUnitOutput, currentUnitOutput);
            BigDecimal top3Point = finalValue(maxTop3Tax, minTop3Tax, currentTop3);
            BigDecimal score = taxPoint.add(settledRatePoint).add(localedRatePoint).add(taxRatePoint).
                    add(unitOutputPoint).add(top3Point).divide(new BigDecimal(6),0,BigDecimal.ROUND_UP);
            QueryWrapper<AdsPmEconomicScore> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(AdsPmEconomicScore::getBuildingId,buildingId).eq(AdsPmEconomicScore::getDatekey,queryDateStr);

            AdsPmEconomicScore adsPmEconomicScore = adsPmEconomicScoreMapper.selectOne(queryWrapper);
            if (ObjectUtil.isEmpty(adsPmEconomicScore)) {
                adsPmEconomicScore = new AdsPmEconomicScore();
                adsPmEconomicScore.setBuildingId(buildingId);
                adsPmEconomicScore.setDatekey(queryDateStr);
                adsPmEconomicScore.setId(IdUtil.getSnowflakeNextIdStr());
                adsPmEconomicScore.setTaxIncomeScore(taxPoint);
                adsPmEconomicScore.setSettledRateScore(settledRatePoint);
                adsPmEconomicScore.setEntCultivateScore(localedRatePoint);
                adsPmEconomicScore.setTaxRateScore(taxRatePoint);
                adsPmEconomicScore.setCreateTime(LocalDateTime.now());
                adsPmEconomicScore.setScore(score);
                adsPmEconomicScore.setUpdateTime(LocalDateTime.now());
                adsPmEconomicScore.setUnitOutputScore(unitOutputPoint);
                adsPmEconomicScore.setIndustryScaleScore(top3Point);
                adsPmEconomicScoreMapper.insert(adsPmEconomicScore);
            }else {
                adsPmEconomicScore.setBuildingId(buildingId);
                adsPmEconomicScore.setDatekey(queryDateStr);
                adsPmEconomicScore.setTaxIncomeScore(taxPoint);
                adsPmEconomicScore.setSettledRateScore(settledRatePoint);
                adsPmEconomicScore.setEntCultivateScore(localedRatePoint);
                adsPmEconomicScore.setTaxRateScore(taxRatePoint);
                adsPmEconomicScore.setCreateTime(LocalDateTime.now());
                adsPmEconomicScore.setScore(score);
                adsPmEconomicScore.setUpdateTime(LocalDateTime.now());
                adsPmEconomicScore.setUnitOutputScore(unitOutputPoint);
                adsPmEconomicScore.setIndustryScaleScore(top3Point);
                adsPmEconomicScore.setUpdateTime(LocalDateTime.now());
                adsPmEconomicScoreMapper.updateById(adsPmEconomicScore);
            }
        });
    }

    @Override
    public MobileCompareVo getMobileCompareItem(MobileCompareReq req) {
        return analyzeCockpitMapper.getMobileCompare(req);
    }


    private BigDecimal finalValue(BigDecimal maxValue, BigDecimal minValue, BigDecimal currentValue) {
        log.info("maxValue:{},minValue:{},currentValue:{}",maxValue,minValue,currentValue);
        if (maxValue.subtract(minValue).floatValue() == 0) {
            return new BigDecimal(60);
        }
        if (ObjectUtil.isNull(currentValue)) {
            return new BigDecimal(60);
        }
        BigDecimal value = currentValue.subtract(minValue).divide(maxValue.subtract(minValue),0,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(40)).add(new BigDecimal(60));
        return value;
    }
}
