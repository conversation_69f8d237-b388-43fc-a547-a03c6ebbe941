package com.zjhh.economy.service;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.WorkPlatformEnterpriseDemandReq;
import com.zjhh.economy.request.WyWorkPlatformPageReq;
import com.zjhh.economy.request.WyWorkPlatformReq;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;

import java.util.List;

public interface WyWorkPlatformService {


    List<TreeSelectVo> listAuthBuildingMenu();

    WorkPlatformRoomVo getWorkPlatformRoomByAuth(WyWorkPlatformReq req);

    WorkPlatformEntManageVo getWorkPlatformEntManageByAuth(WyWorkPlatformReq req);

    WorkPlatformDemandVo getWorkPlatformDemandByAuth(WyWorkPlatformReq req);

    List<WorkPlatformWarningRemindVo> listWorkPlatformRemindByAuth(WyWorkPlatformReq req);

    Page<WarningRemindListVo> pageWorkPlatformRemindByAuth(WyWorkPlatformPageReq req);

    List<EnterpriseDemandVo> listEnterpriseDemandByAuth(WorkPlatformEnterpriseDemandReq req);

    List<PolicyManagementPageVo> listPolicyManagement();





}
