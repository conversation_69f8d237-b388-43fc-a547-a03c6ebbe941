package com.zjhh.economy.service.impl;

import com.zjhh.economy.dao.mapper.AdsPmDataDescMapper;
import com.zjhh.economy.service.DataDescService;
import com.zjhh.economy.vo.DataDescVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class DataDescServiceImpl implements DataDescService {

    @Resource
    private AdsPmDataDescMapper adsPmDataDescMapper;

    @Override
    public DataDescVo getDesc(String code) {
        String desc = adsPmDataDescMapper.getDesc(code);
        DataDescVo descVo = new DataDescVo();
        descVo.setDesc(desc);
        return descVo;
    }
}
