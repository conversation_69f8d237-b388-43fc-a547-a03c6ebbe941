package com.zjhh.economy.service;

import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.AddRoomReq;
import com.zjhh.economy.request.PageRoomReq;
import com.zjhh.economy.request.UpdateRoomReq;
import com.zjhh.economy.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/8 10:18
 */
public interface RoomService {

    /**
     * 楼宇编号
     *
     * @param buildingId
     * @return
     */
    List<RoomStateFloorVo> listFloor(String buildingId);

    /**
     * 房源列表分页
     *
     * @param req
     * @return
     */
    Page<RoomVo> page(PageRoomReq req);

    /**
     * 获取房间详情
     *
     * @param roomId
     * @return
     */
    RoomDetailVo getDetail(String roomId);

    /**
     * 根据楼层获取添加房间信息
     *
     * @param floorId
     * @return
     */
    AddRoomVo getAddRoom(String floorId);

    /**
     * 添加房间
     *
     * @param req
     */
    void add(AddRoomReq req);

    /**
     * 编辑房间
     *
     * @param req
     */
    void update(UpdateRoomReq req);

    /**
     * 删除房间
     *
     * @param roomId
     */
    void delete(String roomId);

    /**
     * 获取编辑详情
     *
     * @param roomId
     * @return
     */
    RoomUpdateDetailVo getUpdateDetail(String roomId);
}
