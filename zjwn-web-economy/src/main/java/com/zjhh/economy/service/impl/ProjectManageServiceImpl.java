package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.request.IdReq;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsBusinessDocument;
import com.zjhh.economy.dao.entity.AdsPmBuilding;
import com.zjhh.economy.dao.entity.AdsPmProject;
import com.zjhh.economy.dao.mapper.AdsBusinessDocumentMapper;
import com.zjhh.economy.dao.mapper.AdsPmBuildingMapper;
import com.zjhh.economy.dao.mapper.AdsPmProjectMapper;
import com.zjhh.economy.dao.mapper.ChangeInfoMapper;
import com.zjhh.economy.dto.SerialNoDto;
import com.zjhh.economy.enume.DocumentTypeEnum;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.request.AddProjectReq;
import com.zjhh.economy.request.QueryProjectPageReq;
import com.zjhh.economy.request.UpdateProjectReq;
import com.zjhh.economy.service.ProjectManageService;
import com.zjhh.economy.utils.OperationLogUtil;
import com.zjhh.economy.vo.ProjectDetailVo;
import com.zjhh.economy.vo.ProjectListVo;
import com.zjhh.economy.vo.operationlog.ProjectDetailForLog;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class ProjectManageServiceImpl extends BaseProjectServiceImpl implements ProjectManageService {

    @Resource
    private AdsPmProjectMapper adsPmProjectMapper;

    @Resource
    private AdsBusinessDocumentMapper adsBusinessDocumentMapper;


    @Resource
    private AdsPmBuildingMapper adsPmBuildingMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private ChangeInfoMapper changeInfoMapper;

    @Resource
    private OperationLogUtil operationLogUtil;


    @Override
    public Page<ProjectListVo> pageProjectList(QueryProjectPageReq req) {
        return adsPmProjectMapper.pageProject(req.getPage(ProjectListVo.class), req);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addProject(AddProjectReq req) {

        QueryWrapper<AdsPmProject> projectQueryWrapper = new QueryWrapper<>();
        projectQueryWrapper.lambda().eq(AdsPmProject::getProjectName, req.getProjectName());
        boolean exists = adsPmProjectMapper.exists(projectQueryWrapper);
        if (exists) {
            throw new BizException("该项目名称已存在!");
        }
        AdsPmProject project = new AdsPmProject();
        BeanUtil.copyProperties(req, project);
        project.setId(IdUtil.getSnowflakeNextIdStr());
        SerialNoDto serialNoDto = getProjectSerialNo();
        project.setSerialNo(serialNoDto.getSerialNo());
        project.setXh(serialNoDto.getXh());
        project.setUpdateTime(LocalDateTime.now());
        project.setCreateUser(userSession.getUserCode());
        project.setCreateTime(LocalDateTime.now());
        adsPmProjectMapper.insert(project);
        if (req.getProjectImages().size() <= 3) {
            req.getProjectImages().forEach(image -> {
                AdsBusinessDocument adsBusinessDocument = new AdsBusinessDocument();
                adsBusinessDocument.setBusinessId(project.getId());
                adsBusinessDocument.setDocumentId(image.getDocumentId());
                adsBusinessDocument.setDocumentType(DocumentTypeEnum.PROJECT_IMG.value());
                adsBusinessDocument.setId(IdUtil.getSnowflakeNextIdStr());
                adsBusinessDocumentMapper.insert(adsBusinessDocument);
            });
        } else {
            throw new BizException("上传图片最多3张");
        }
        ProjectDetailForLog dataAfter = changeInfoMapper.getProjectDetailForLog(project.getId());
        if (dataAfter != null) {
            operationLogUtil.recordLog(LogModuleEnum.PROJECT, LogTypeEnum.PROJECT_ADD, dataAfter.getId(), dataAfter.getProjectName(),
                    null, JSON.toJSONString(dataAfter));
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateProject(UpdateProjectReq req) {
        ProjectDetailForLog dataBefore = changeInfoMapper.getProjectDetailForLog(req.getId());

        AdsPmProject project = new AdsPmProject();
        BeanUtil.copyProperties(req, project);
        project.setUpdateTime(LocalDateTime.now());
        adsPmProjectMapper.updateById(project);
        List<String> imageIds = new ArrayList<>();
        if (req.getProjectImages().size() <= 3) {
            req.getProjectImages().forEach(image -> {
                QueryWrapper<AdsBusinessDocument> documentQueryWrapper = new QueryWrapper<>();
                documentQueryWrapper.lambda().eq(AdsBusinessDocument::getDocumentId, image.getDocumentId()).eq(AdsBusinessDocument::getBusinessId, project.getId());
                AdsBusinessDocument document = adsBusinessDocumentMapper.selectOne(documentQueryWrapper);
                if (ObjectUtil.isEmpty(document)) {
                    AdsBusinessDocument adsBusinessDocument = new AdsBusinessDocument();
                    adsBusinessDocument.setBusinessId(project.getId());
                    adsBusinessDocument.setDocumentId(image.getDocumentId());
                    adsBusinessDocument.setDocumentType(DocumentTypeEnum.PROJECT_IMG.value());
                    adsBusinessDocument.setId(IdUtil.getSnowflakeNextIdStr());
                    adsBusinessDocumentMapper.insert(adsBusinessDocument);
                }
                imageIds.add(image.getDocumentId());
            });
        } else {
            throw new BizException("上传图片最多3张");
        }

        if (CollUtil.isNotEmpty(imageIds)) {
            QueryWrapper<AdsBusinessDocument> delWrapper = new QueryWrapper<>();
            delWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, project.getId()).notIn(AdsBusinessDocument::getDocumentId, imageIds);
            adsBusinessDocumentMapper.delete(delWrapper);
        } else {
            if (CollUtil.isEmpty(req.getProjectImages())) {
                QueryWrapper<AdsBusinessDocument> delWrapper = new QueryWrapper<>();
                delWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, project.getId());
                adsBusinessDocumentMapper.delete(delWrapper);
            }
        }
        ProjectDetailForLog dataAfter = changeInfoMapper.getProjectDetailForLog(req.getId());
        if (dataBefore != null && dataAfter != null) {
            operationLogUtil.recordLog(LogModuleEnum.PROJECT, LogTypeEnum.PROJECT_EDIT, dataBefore.getId(), dataBefore.getProjectName(),
                    JSON.toJSONString(dataBefore), JSON.toJSONString(dataAfter));
        }
    }


    @Override
    public void delProject(IdReq req) {
        ProjectDetailForLog dataBefore = changeInfoMapper.getProjectDetailForLog(req.getId());

        QueryWrapper<AdsPmBuilding> buildingQueryWrapper = new QueryWrapper<>();
        buildingQueryWrapper.lambda().eq(AdsPmBuilding::getProjectId, req.getId());
        Long count = adsPmBuildingMapper.selectCount(buildingQueryWrapper);
        if (count > 0) {
            throw new BizException("该项目下已存在楼宇信息，不可删除");
        } else {
            adsPmProjectMapper.deleteById(req.getId());
            QueryWrapper<AdsBusinessDocument> adsBusinessDocumentQueryWrapper = new QueryWrapper<>();
            adsBusinessDocumentQueryWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, req.getId()).eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.PROJECT_IMG.value());
            adsBusinessDocumentMapper.delete(adsBusinessDocumentQueryWrapper);
        }
        if (dataBefore != null) {
            operationLogUtil.recordLog(LogModuleEnum.PROJECT, LogTypeEnum.PROJECT_DELETE, dataBefore.getId(), dataBefore.getProjectName(),
                    JSON.toJSONString(dataBefore), null);
        }
    }

    @Override
    public ProjectDetailVo getProjectDetail(IdReq req) {
        return adsPmProjectMapper.getProjectDetail(req.getId());
    }

}
