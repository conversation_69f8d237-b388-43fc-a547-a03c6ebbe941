package com.zjhh.economy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.*;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.enume.DocumentTypeEnum;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.enume.WarningRemindEnum;
import com.zjhh.economy.request.*;
import com.zjhh.economy.service.BusinessService;
import com.zjhh.economy.utils.OperationLogUtil;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.operationlog.DemandDetailForLog;
import com.zjhh.economy.vo.operationlog.DemandHandleForLog;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class BusinessServiceImpl implements BusinessService {

    @Resource
    private DmPmMapper dmPmMapper;

    @Resource
    private AdsPmBuildingExtendMapper adsPmBuildingExtendMapper;

    @Resource
    private WorkPlatformWarningMapper workPlatformWarningMapper;

    @Resource
    private AdsBusinessDocumentMapper adsBusinessDocumentMapper;

    @Resource
    private AdsPmRoomLabelMapper adsPmRoomLabelMapper;

    @Resource
    private AdsPmEnterpriseLabelMapper adsPmEnterpriseLabelMapper;

    @Resource
    private AdsPmEnterpriseDemandMapper adsPmEnterpriseDemandMapper;

    @Resource
    private AdsPmEnterpriseVisitMapper adsPmEnterpriseVisitMapper;

    @Resource
    private AdsPmEnterpriseDemandHandleMapper adsPmEnterpriseDemandHandleMapper;

    @Resource
    private AdsPmRoomEnterpriseMapper adsPmRoomEnterpriseMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private AdsDynamicDataBriefingMapper adsDynamicDataBriefingMapper;

    @Resource
    private AdsPmEnterpriseMapper adsPmEnterpriseMapper;

    @Resource
    private AdsCommonFunctionUserMapper adsCommonFunctionUserMapper;

    @Resource
    private ChangeInfoMapper changeInfoMapper;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Override
    public void saveLabel(AddLabelReq req) {

        List<String> ids = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getLabelList())) {
            req.getLabelList().forEach(label -> {
                if (StrUtil.isBlank(label.getId())) {
                    DmPm dmPm = new DmPm();
                    dmPm.setId(IdUtil.getSnowflakeNextIdStr());
                    String code = PinyinUtil.getFirstLetter(label.getLabelName(), "");
                    QueryWrapper<DmPm> existQuery = new QueryWrapper<>();
                    existQuery.lambda().likeLeft(DmPm::getCode, code).eq(DmPm::getType, req.getType());
                    boolean exist = dmPmMapper.exists(existQuery);
                    if (exist) {
                        dmPm.setCode(code + RandomUtil.randomNumbers(3));
                    } else {
                        dmPm.setCode(code);
                    }
                    dmPm.setName(label.getLabelName());
                    dmPm.setSort(dmPmMapper.getMaxSort(req.getType()));
                    dmPm.setType(req.getType());
                    dmPmMapper.insert(dmPm);
                    ids.add(dmPm.getId());
                } else {
                    ids.add(label.getId());
                }
            });

            if (CollUtil.isNotEmpty(ids)) {
                UpdateWrapper<DmPm> delWrapper = new UpdateWrapper<>();
                delWrapper.lambda().notIn(DmPm::getId, ids).eq(DmPm::getType, req.getType());
                dmPmMapper.delete(delWrapper);
            }
        } else {
            UpdateWrapper<DmPm> delWrapper = new UpdateWrapper<>();
            delWrapper.lambda().eq(DmPm::getType, req.getType());
            dmPmMapper.delete(delWrapper);
        }

        switch (req.getType()) {
            case "BuildingLabel":
                adsPmBuildingExtendMapper.delBuildingExtendByLabel(CollUtil.isNotEmpty(ids) ? ids : null);
                break;
            case "EnterpriseLabel":
                adsPmEnterpriseLabelMapper.delLabel(CollUtil.isNotEmpty(ids) ? ids : null);
                break;
            case "RoomLabel":
                adsPmRoomLabelMapper.delRoomLabel(CollUtil.isNotEmpty(ids) ? ids : null);
                break;
            default:

                break;
        }
    }

    @Override
    public List<LabelReq> listLabel(QueryLabelsReq req) {
        return dmPmMapper.listLabel(req.getType());
    }

    @Override
    public Page<EnterpriseDemandVo> pageEnterpriseDemand(EnterpriseDemandReq req) {

        return adsPmEnterpriseDemandMapper.pageEnterpriseDemand(req.getPage(EnterpriseDemandVo.class), req);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addEnterpriseDemand(AddEnterpriseDemandReq req) {
        DemandDetailForLog dataBefore = getDemandDetailForLog(req.getId());

        AdsPmEnterpriseDemand demand = adsPmEnterpriseDemandMapper.selectById(req.getId());
        if (ObjectUtil.isEmpty(demand)) {
            demand = new AdsPmEnterpriseDemand();
            demand.setId(IdUtil.getSnowflakeNextIdStr());
            demand.setBuildingId(req.getBuildingId());
            demand.setContactPerson(req.getContactPerson());
            demand.setEnterpriseId(req.getEnterpriseId());
            demand.setDemandType(req.getDemandType());
            demand.setDemandDesc(req.getDemandDesc());
            if (StpUtil.isLogin()) {
                demand.setCreateUser(userSession.getSessionLoginVo().getUsername());
            } else {
                demand.setCreateUser(req.getContactPerson());
            }
            demand.setPhone(req.getPhone());
            demand.setCreateTime(LocalDateTime.now());
            demand.setUpdateTime(LocalDateTime.now());
            if (ObjectUtil.isNotNull(req.getSubmitSource())) {
                demand.setSubmitSource(req.getSubmitSource());
            } else {
                demand.setSubmitSource("2");
            }
            if (ObjectUtil.isNotNull(req.getSubmitDate())) {
                demand.setSubmitDate(req.getSubmitDate());
            } else {
                demand.setSubmitDate(LocalDate.now());
            }

            adsPmEnterpriseDemandMapper.insert(demand);
            AdsPmEnterpriseDemand finalDemand = demand;
            if (CollUtil.isNotEmpty(req.getPhotos())) {
                QueryWrapper<AdsBusinessDocument> documentQueryWrapper = new QueryWrapper<>();
                documentQueryWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, finalDemand.getId()).eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.ENTERPRISE_DEMAND_IMG.value());
                adsBusinessDocumentMapper.delete(documentQueryWrapper);
            }
            req.getPhotos().forEach(photo -> {
                AdsBusinessDocument adsBusinessDocument = new AdsBusinessDocument();
                adsBusinessDocument.setId(IdUtil.getSnowflakeNextIdStr());
                adsBusinessDocument.setBusinessId(finalDemand.getId());
                adsBusinessDocument.setDocumentId(photo);
                adsBusinessDocument.setDocumentType(DocumentTypeEnum.ENTERPRISE_DEMAND_IMG.value());
                adsBusinessDocumentMapper.insert(adsBusinessDocument);
            });
        } else {
            demand.setEnterpriseId(req.getEnterpriseId());
            demand.setDemandType(req.getDemandType());
            demand.setBuildingId(req.getBuildingId());
            demand.setContactPerson(req.getContactPerson());
            demand.setDemandDesc(req.getDemandDesc());
            demand.setSubmitSource(req.getSubmitSource());
            demand.setPhone(req.getPhone());
            demand.setBuildingId(req.getBuildingId());
            demand.setContactPerson(req.getContactPerson());
            demand.setUpdateTime(LocalDateTime.now());
            demand.setSubmitDate(req.getSubmitDate());
            adsPmEnterpriseDemandMapper.updateById(demand);
            QueryWrapper<AdsBusinessDocument> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, demand.getId()).eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.ENTERPRISE_DEMAND_IMG.value());
            adsBusinessDocumentMapper.delete(queryWrapper);
            AdsPmEnterpriseDemand finalDemand1 = demand;

            req.getPhotos().forEach(photo -> {
                AdsBusinessDocument adsBusinessDocument = new AdsBusinessDocument();
                adsBusinessDocument.setId(IdUtil.getSnowflakeNextIdStr());
                adsBusinessDocument.setBusinessId(finalDemand1.getId());
                adsBusinessDocument.setDocumentId(photo);
                adsBusinessDocument.setDocumentType(DocumentTypeEnum.ENTERPRISE_DEMAND_IMG.value());
                adsBusinessDocumentMapper.insert(adsBusinessDocument);
            });

        }
        DemandDetailForLog dataAfter = getDemandDetailForLog(demand.getId());
        if (dataAfter != null) {
            if (dataBefore == null) {
                operationLogUtil.recordLog(LogModuleEnum.DEMAND, LogTypeEnum.DEMAND_ADD, dataAfter.getId(), dataAfter.getDemandDesc(),
                        null, JSON.toJSONString(dataAfter));
            } else {
                operationLogUtil.recordLog(LogModuleEnum.DEMAND, LogTypeEnum.DEMAND_EDIT, dataBefore.getId(), dataBefore.getDemandDesc(),
                        JSON.toJSONString(dataBefore), JSON.toJSONString(dataAfter));
            }
        }
    }

    @Override
    public EnterpriseDemandDetailVo getEnterpriseDemandDetail(IdReq req) {
        EnterpriseDemandDetailVo demandDetailVo = adsPmEnterpriseDemandMapper.getEnterpriseDemandDetail(req.getId());
        demandDetailVo.setPhotos(adsBusinessDocumentMapper.listDocument(demandDetailVo.getId(), DocumentTypeEnum.ENTERPRISE_DEMAND_IMG.value()));
        if (demandDetailVo.getShowHandle()) {
            List<DemandHandleVo> handles = adsPmEnterpriseDemandHandleMapper.listHandles(req.getId());
            demandDetailVo.setHandles(handles);
        }
        return demandDetailVo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void handleDemand(HandleEnterpriseDemandReq req) {

        AdsPmEnterpriseDemandHandle handle = new AdsPmEnterpriseDemandHandle();
        handle.setHandleType(req.getHandleType());
        handle.setDemandId(req.getDemandId());
        handle.setHandleDesc(req.getHandleDesc());
        handle.setDocumentId(req.getDocumentId());
        handle.setUserCode(userSession.getUserCode());
        handle.setCreateTime(LocalDateTime.now());
        handle.setUpdateTime(LocalDateTime.now());
        AdsPmEnterpriseDemand demand = adsPmEnterpriseDemandMapper.selectById(req.getDemandId());
        demand.setHandleType(req.getHandleType());
        adsPmEnterpriseDemandMapper.updateById(demand);
        adsPmEnterpriseDemandHandleMapper.insert(handle);

        DemandHandleForLog dataAfter = getDemandHandleForLog(handle.getId());
        if (dataAfter != null) {
            operationLogUtil.recordLog(LogModuleEnum.DEMAND, LogTypeEnum.DEMAND_HANDLE, demand.getId(), demand.getDemandDesc(),
                    null, JSON.toJSONString(dataAfter));
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteDemand(IdReq req) {
        DemandDetailForLog dataBefore = getDemandDetailForLog(req.getId());

        adsPmEnterpriseDemandMapper.deleteById(req.getId());
        QueryWrapper<AdsPmEnterpriseDemandHandle> deleteHandler = new QueryWrapper<>();
        deleteHandler.lambda().eq(AdsPmEnterpriseDemandHandle::getDemandId, req.getId());
        adsPmEnterpriseDemandHandleMapper.delete(deleteHandler);

        if (dataBefore != null) {
            operationLogUtil.recordLog(LogModuleEnum.DEMAND, LogTypeEnum.DEMAND_DELETE, dataBefore.getId(), dataBefore.getDemandDesc(),
                    JSON.toJSONString(dataBefore), null);
        }
    }

    @Override
    public void addEnterpriseVisit(AddEnterpriseVisitReq req) {
        if (StrUtil.isBlank(req.getId())) {
            AdsPmEnterpriseVisit adsPmEnterpriseVisit = new AdsPmEnterpriseVisit();
            adsPmEnterpriseVisit.setId(IdUtil.getSnowflakeNextIdStr());
            adsPmEnterpriseVisit.setVisitDate(req.getVisitDate());
            adsPmEnterpriseVisit.setEnterpriseId(req.getEnterpriseId());
            adsPmEnterpriseVisit.setVisitPurpose(req.getVisitPurpose());
            adsPmEnterpriseVisit.setRemark(req.getRemark());
            adsPmEnterpriseVisit.setReceptionist(req.getReceptionist());
            adsPmEnterpriseVisit.setVisitor(req.getVisitor());
            adsPmEnterpriseVisit.setDocumentId(req.getDocumentId());
            adsPmEnterpriseVisit.setCreateTime(LocalDateTime.now());
            adsPmEnterpriseVisit.setUpdateTime(LocalDateTime.now());
            adsPmEnterpriseVisitMapper.insert(adsPmEnterpriseVisit);
        } else {
            AdsPmEnterpriseVisit adsPmEnterpriseVisit = adsPmEnterpriseVisitMapper.selectById(req.getId());
            adsPmEnterpriseVisit.setVisitDate(req.getVisitDate());
            adsPmEnterpriseVisit.setEnterpriseId(req.getEnterpriseId());
            adsPmEnterpriseVisit.setVisitPurpose(req.getVisitPurpose());
            adsPmEnterpriseVisit.setRemark(req.getRemark());
            adsPmEnterpriseVisit.setReceptionist(req.getReceptionist());
            adsPmEnterpriseVisit.setVisitor(req.getVisitor());
            adsPmEnterpriseVisit.setDocumentId(req.getDocumentId());
            adsPmEnterpriseVisit.setUpdateTime(LocalDateTime.now());
            adsPmEnterpriseVisitMapper.updateById(adsPmEnterpriseVisit);
        }
    }

    @Override
    public void deleteEnterpriseVisit(IdReq req) {
        adsPmEnterpriseVisitMapper.deleteById(req.getId());
    }

    @Override
    public Page<EnterpriseVisitVo> pageEnterpriseVisit(QueryEnterpriseVisitReq req) {
        return adsPmEnterpriseVisitMapper.pageEnterpriseVisit(req.getPage(EnterpriseVisitVo.class), req);
    }

    @Override
    public EnterpriseVisitDetailVo getEnterpriseVisitDetail(String id) {
        return adsPmEnterpriseVisitMapper.getEnterpriseVisitDetail(id);
    }

    @Override
    public WorkPlatformRoomVo getWorkPlatformRoom() {
        return adsPmRoomEnterpriseMapper.getWorkPlatformRoom();
    }

    @Override
    public WorkPlatformEntManageVo getWorkPlatformEntManage() {
        DateTime current = DateUtil.offsetMonth(new Date(), 0);
        // 格式化为 yyyyMM
        String currentYearMonth = DateUtil.format(current, DatePattern.PURE_DATE_FORMAT).substring(0, 6);
        return adsPmRoomEnterpriseMapper.getWorkPlatformEntManage(currentYearMonth);
    }

    @Override
    public WorkPlatformDemandVo getWorkPlatformDemand(DatekeyReq req) {
        return adsPmEnterpriseDemandMapper.getWorkPlatformDemand(req);
    }

    @Override
    public List<String> listBriefing(Integer period) {
        return adsDynamicDataBriefingMapper.listBriefing(period, DateUtil.format(new Date(), "yyyy-MM-dd"));
    }

    @Override
    public void generationWarning() {
        generationMoveLessThanThirstyWarning();
        generationConfirmMoveWarning();
        generationEmptyRoomWarning();
        generationTaxWarning();
    }


    @Override
    public void generationDataBrief() {
        generationBriefDemand();
        generationBriefUpdateEntInfo();
        generationBriefEntSettledInfo();
        generationBriefMoveInfo();
    }

    @Override
    public List<WorkPlatformWarningRemindVo> listWorkPlatformRemind() {
        return workPlatformWarningMapper.listWorkPlatformWarningRemind();
    }

    @Override
    public Page<WarningRemindListVo> pageWarningRemindList(WarningPageReq req) {
        return workPlatformWarningMapper.pageWarningRemindList(req.getPage(WarningRemindListVo.class),req);
    }

    @Override
    public void operateRemind(WarningRemindOperateReq req) {
        UpdateWrapper<WorkPlatformWarning> updateWrapper = new UpdateWrapper<>();
        Integer operateType = req.getOperateType() == 1 ? 2 : 3;
        updateWrapper.lambda().in(WorkPlatformWarning::getId, req.getIds()).set(WorkPlatformWarning::getHandleType, operateType);
        workPlatformWarningMapper.update(updateWrapper);
    }

    @Override
    public List<CommonFunctionVo> listCommonFunctionByUser() {
        return adsCommonFunctionUserMapper.listCommonFunctionUser(userSession.getUserCode());
    }

    @Override
    public List<TreeSelectVo> treeCommonFunction() {
        return TreeUtils.listToTree(adsCommonFunctionUserMapper.listCommonFunction(), "root");
    }


    @Override
    public void commonFunctionEdit(CommonFunctionReq req) {
        QueryWrapper<AdsCommonFunctionUser> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.lambda().eq(AdsCommonFunctionUser::getUserCode, userSession.getUserCode());
        adsCommonFunctionUserMapper.delete(userQueryWrapper);
        req.getIds().forEach(id -> {
            AdsCommonFunctionUser adsCommonFunctionUser = new AdsCommonFunctionUser();
            adsCommonFunctionUser.setId(IdUtil.getSnowflakeNextIdStr());
            adsCommonFunctionUser.setFunctionId(id);
            adsCommonFunctionUser.setUserCode(userSession.getUserCode());
            adsCommonFunctionUserMapper.insert(adsCommonFunctionUser);
        });
    }

    @Override
    public DocumentVo getMobileContactPerson() {
        DocumentVo documentVo = adsBusinessDocumentMapper.getMobileContactPerson();
        return documentVo;
    }

    private void generationBriefDemand() {
        generationBriefDemandByPeriod(getBriefQueryDate(-7), getBriefQueryDate(0), 1);
        generationBriefDemandByPeriod(getBriefQueryDate(-30), getBriefQueryDate(0), 2);
        generationBriefDemandByPeriod(getBriefBeginMonth(), getBriefQueryDate(0), 3);
    }

    private void generationBriefDemandByPeriod(String startDate, String endDate, Integer period) {
        Map<String, Object> demandBriefData = adsPmEnterpriseDemandMapper.getDemandBrief(startDate, endDate);
        if (ObjectUtil.isNotNull(demandBriefData)) {
            String briefDesc = "本期企业诉求新增" + demandBriefData.get("totalcount").toString() +
                    "条，其中" + demandBriefData.get("pendingcount").toString() + "条未解决";
            insertBrief(briefDesc, period);
        }
    }

    private void generationBriefUpdateEntInfo() {
        generationBriefUpdateEntInfoByPeriod(getBriefQueryDate(-7), getBriefQueryDate(0), 1);
        generationBriefUpdateEntInfoByPeriod(getBriefQueryDate(-30), getBriefQueryDate(0), 2);
        generationBriefUpdateEntInfoByPeriod(getBriefBeginMonth(), getBriefQueryDate(0), 3);

    }

    private void generationBriefUpdateEntInfoByPeriod(String startDate, String endDate, Integer period) {
        Integer updateCount = adsPmEnterpriseMapper.getBriefEntUpdateCount(startDate, endDate);
        String briefDesc = "本期企业信息更新（不含新登记企业）数量" + updateCount + "家";
        insertBrief(briefDesc, period);
    }

    private void generationBriefEntSettledInfo() {
        generationBriefEntSettledInfoByPeriod(getBriefQueryDate(-7), getBriefQueryDate(0), 1);
        generationBriefEntSettledInfoByPeriod(getBriefQueryDate(-30), getBriefQueryDate(0), 2);
        generationBriefEntSettledInfoByPeriod(getBriefBeginMonth(), getBriefQueryDate(0), 3);

    }

    private void generationBriefEntSettledInfoByPeriod(String startDate, String endDate, Integer period) {
        Integer totalCount = adsPmRoomEnterpriseMapper.getBriefRegisterByTotalCount(startDate, endDate);
        Integer settledCount = adsPmRoomEnterpriseMapper.getBriefRegisterBySettledCount(startDate, endDate);
        Integer roomCount = adsPmRoomEnterpriseMapper.getBriefRegisterBySettleRoomCount(startDate, endDate);
        BigDecimal settledArea = adsPmRoomEnterpriseMapper.getBriefRegisterBySettleArea(startDate, endDate);
        String briefDesc = "本期新登记企业" + totalCount + "家，其中已入驻" + settledCount + "家，入驻房源" + roomCount + "间"
                + "入驻面积" + settledArea + "㎡";
        insertBrief(briefDesc, period);
    }

    private void generationBriefMoveInfo() {
        generationBriefMoveInfoByPeriod(getBriefQueryDate(-7), getBriefQueryDate(0), 1);
        generationBriefMoveInfoByPeriod(getBriefQueryDate(-30), getBriefQueryDate(0), 2);
        generationBriefMoveInfoByPeriod(getBriefBeginMonth(), getBriefQueryDate(0), 3);
    }

    private void generationBriefMoveInfoByPeriod(String startDate, String endDate, Integer period) {
        Integer totalCount = adsPmRoomEnterpriseMapper.getBriefRegisterByMoveTotalCount(startDate, endDate);
        Integer localCount = adsPmRoomEnterpriseMapper.getBriefRegisterByLocalCount(startDate, endDate);
        Integer notLocalCount = adsPmRoomEnterpriseMapper.getBriefRegisterByNotLocalCount(startDate, endDate);
        String briefDesc = "本期已搬离企业" + totalCount + "家，属地企业" + localCount + "家，非属地企业" + notLocalCount + "家";
        insertBrief(briefDesc, period);
    }


    private void insertBrief(String briefDesc, Integer period) {
        AdsDynamicDataBriefing briefing = new AdsDynamicDataBriefing();
        briefing.setId(IdUtil.getSnowflakeNextIdStr());
        briefing.setBriefingDesc(briefDesc);
        briefing.setPeriod(period);
        briefing.setGenerationDate(LocalDate.now());
        adsDynamicDataBriefingMapper.insert(briefing);
    }

    private void generationMoveLessThanThirstyWarning() {
        List<Map<String, Object>> ninetyMoves = adsPmRoomEnterpriseMapper.listEntMoveLessThanNinety(DateUtil.format(new Date(), "yyyy-MM-dd"));
        ninetyMoves.forEach(map -> {
            if (ObjectUtil.isNotEmpty(map.get("enterpriseid")) && ObjectUtil.isNotEmpty(map.get("buildingid")) && ObjectUtil.isNotEmpty(map.get("roomid"))) {
                String businessId = map.get("enterpriseid").toString() + map.get("buildingid") + map.get("roomid").toString() + "90";
                String desc = map.get("buildingname").toString() + map.get("roomno").toString() + "的" + map.get("enterprisename").toString() + "将在90天内租赁到期";
                WorkPlatformWarning warning = workPlatformWarningMapper.checkWorkPlatformWarning(businessId, 1);
                if (ObjectUtil.isEmpty(warning)) {
                    warning = new WorkPlatformWarning();
                    warning.setId(IdUtil.getSnowflakeNextIdStr());
                    warning.setBusinessId(businessId);
                    warning.setRemindDesc(desc);
                    warning.setRemindDate(LocalDate.now());
                    warning.setWarningType(WarningRemindEnum.ENT_MOVE.value());
                    warning.setCreateTime(LocalDateTime.now());
                    warning.setUpdateTime(LocalDateTime.now());
                    warning.setHandleType(1);
                    workPlatformWarningMapper.insert(warning);
                }
            }
        });
        List<Map<String, Object>> sixtyMoves = adsPmRoomEnterpriseMapper.listEntMoveLessThanSixty(DateUtil.format(new Date(), "yyyy-MM-dd"));
        sixtyMoves.forEach(map -> {
            if (ObjectUtil.isNotEmpty(map.get("enterpriseid")) && ObjectUtil.isNotEmpty(map.get("buildingid")) && ObjectUtil.isNotEmpty(map.get("roomid"))) {
                String businessId = map.get("enterpriseid").toString() + map.get("buildingid") + map.get("roomid").toString() + "60";
                String desc = map.get("buildingname").toString() + map.get("roomno").toString() + "的" + map.get("enterprisename").toString() + "将在60天内租赁到期";
                WorkPlatformWarning warning = workPlatformWarningMapper.checkWorkPlatformWarning(businessId, 1);
                if (ObjectUtil.isEmpty(warning)) {
                    warning = new WorkPlatformWarning();
                    warning.setId(IdUtil.getSnowflakeNextIdStr());
                    warning.setBusinessId(businessId);
                    warning.setRemindDesc(desc);
                    warning.setRemindDate(LocalDate.now());
                    warning.setWarningType(WarningRemindEnum.ENT_MOVE.value());
                    warning.setCreateTime(LocalDateTime.now());
                    warning.setUpdateTime(LocalDateTime.now());
                    warning.setHandleType(1);
                    workPlatformWarningMapper.insert(warning);
                }
            }
        });

        List<Map<String, Object>> thirtyMoves = adsPmRoomEnterpriseMapper.listEntMoveLessThanThirsty(DateUtil.format(new Date(), "yyyy-MM-dd"));
        thirtyMoves.forEach(map -> {
            if (ObjectUtil.isNotEmpty(map.get("enterpriseid")) && ObjectUtil.isNotEmpty(map.get("buildingid")) && ObjectUtil.isNotEmpty(map.get("roomid"))) {
                String businessId = map.get("enterpriseid").toString() + map.get("buildingid") + map.get("roomid").toString() + "30";
                String desc = map.get("buildingname").toString() + map.get("roomno").toString() + "的" + map.get("enterprisename").toString() + "将在30天内租赁到期";
                WorkPlatformWarning warning = workPlatformWarningMapper.checkWorkPlatformWarning(businessId, 1);
                if (ObjectUtil.isEmpty(warning)) {
                    warning = new WorkPlatformWarning();
                    warning.setId(IdUtil.getSnowflakeNextIdStr());
                    warning.setBusinessId(businessId);
                    warning.setRemindDesc(desc);
                    warning.setRemindDate(LocalDate.now());
                    warning.setWarningType(WarningRemindEnum.ENT_MOVE.value());
                    warning.setCreateTime(LocalDateTime.now());
                    warning.setUpdateTime(LocalDateTime.now());
                    warning.setHandleType(1);
                    workPlatformWarningMapper.insert(warning);
                }
            }

        });
    }

    private void generationConfirmMoveWarning() {
        List<Map<String, Object>> moves = adsPmRoomEnterpriseMapper.listEntConfirmMove(1, DateUtil.format(new Date(), "yyyy-MM-dd"));
        generationConfirmMoveWarningDetail(moves, 1);
    }

    private void generationConfirmMoveWarningDetail(List<Map<String, Object>> moves, Integer days) {
        moves.forEach(map -> {
            if (ObjectUtil.isNotEmpty(map.get("enterpriseid")) && ObjectUtil.isNotEmpty(map.get("buildingid")) && ObjectUtil.isNotEmpty(map.get("roomid"))) {
                String businessId = map.get("enterpriseid").toString() + map.get("buildingid") + map.get("roomid").toString() + "1";
                log.info("buildingname:{}", map.get("buildingname"));
                log.info("roomno:{}", map.get("roomno"));
                log.info("enterprisename:{}", map.get("enterprisename"));
                String desc = map.get("buildingname").toString() + map.get("roomno").toString() + "的" + map.get("enterprisename").toString() + "租赁期已满";
                WorkPlatformWarning warning = workPlatformWarningMapper.checkWorkPlatformWarning(businessId, 2);
                if (ObjectUtil.isEmpty(warning)) {
                    warning = new WorkPlatformWarning();
                    warning.setId(IdUtil.getSnowflakeNextIdStr());
                    warning.setBusinessId(businessId);
                    warning.setRemindDesc(desc);
                    warning.setRemindDate(LocalDate.now());
                    warning.setWarningType(WarningRemindEnum.ENT_MOVE_CONFIRM.value());
                    warning.setCreateTime(LocalDateTime.now());
                    warning.setUpdateTime(LocalDateTime.now());
                    warning.setHandleType(1);
                    workPlatformWarningMapper.insert(warning);
                }
            }

        });
    }

    private void generationEmptyRoomWarning() {
        List<Map<String, Object>> moves = adsPmRoomEnterpriseMapper.listEmptyRoomMoreThanSixty(DateUtil.format(new Date(), "yyyy-MM-dd"));
        moves.forEach(map -> {

            if (ObjectUtil.isNotEmpty(map.get("roomid")) && ObjectUtil.isNotEmpty(map.get("projectname")) &&
                    ObjectUtil.isNotEmpty(map.get("buildingname")) && ObjectUtil.isNotEmpty(map.get("roomno")) && ObjectUtil.isNotEmpty(map.get("floorname"))) {
                String businessId = map.get("roomid").toString();
                String desc = map.get("projectname").toString() + map.get("buildingname").toString()
                        + map.get("floorname").toString() +
                        map.get("roomno").toString() + "房源空置超60日";
                WorkPlatformWarning warning = workPlatformWarningMapper.checkWorkPlatformWarning(businessId, 3);
                if (ObjectUtil.isEmpty(warning)) {
                    warning = new WorkPlatformWarning();
                    warning.setId(IdUtil.getSnowflakeNextIdStr());
                    warning.setBusinessId(businessId);
                    warning.setRemindDesc(desc);
                    warning.setWarningType(WarningRemindEnum.ROOM_EMPTY.value());
                    warning.setRemindDate(LocalDate.now());
                    warning.setCreateTime(LocalDateTime.now());
                    warning.setUpdateTime(LocalDateTime.now());
                    warning.setHandleType(1);
                    workPlatformWarningMapper.insert(warning);
                }
            }

        });
    }

    private void generationTaxWarning() {
        DateTime lastYearSameMonth = DateUtil.offsetMonth(new Date(), -12);
        // 格式化为 yyyyMM
        String last = DateUtil.format(lastYearSameMonth, DatePattern.SIMPLE_MONTH_PATTERN);
        List<Map<String, Object>> moves = adsPmRoomEnterpriseMapper.listNoInputTax(last);
        moves.forEach(map -> {
            String businessId = map.get("enterpriseid").toString() + last;
            String desc = map.get("enterprisename").toString() + "未录入税收信息";
            WorkPlatformWarning warning = workPlatformWarningMapper.checkWorkPlatformWarning(businessId, 4);
            if (ObjectUtil.isEmpty(warning)) {
                warning = new WorkPlatformWarning();
                warning.setId(IdUtil.getSnowflakeNextIdStr());
                warning.setBusinessId(businessId);
                warning.setRemindDesc(desc);
                warning.setRemindDate(LocalDate.now());
                warning.setWarningType(WarningRemindEnum.ENT_INFO_UPDATE.value());
                warning.setCreateTime(LocalDateTime.now());
                warning.setUpdateTime(LocalDateTime.now());
                warning.setHandleType(1);
                workPlatformWarningMapper.insert(warning);
            }
        });
    }

    private String getBriefQueryDate(Integer offset) {
        DateTime lastYearSameMonth = DateUtil.offsetDay(new Date(), offset);
        return DateUtil.format(lastYearSameMonth, DatePattern.NORM_DATE_FORMAT);
    }

    private String getBriefBeginMonth() {
        Date firstDayOfMonth = DateUtil.beginOfMonth(new Date());
        return DateUtil.format(firstDayOfMonth, DatePattern.NORM_DATE_FORMAT);
    }

    private DemandDetailForLog getDemandDetailForLog(String demandId) {
        DemandDetailForLog vo = changeInfoMapper.getDemandDetailForLog(demandId);
        if (vo == null) {
            return null;
        }
        vo.setPhotos(changeInfoMapper.listDocTitles(demandId, DocumentTypeEnum.ENTERPRISE_DEMAND_IMG.value()));
        return vo;
    }

    private DemandHandleForLog getDemandHandleForLog(String handleId) {
        return changeInfoMapper.getDemandHandleForLog(handleId);
    }
}
