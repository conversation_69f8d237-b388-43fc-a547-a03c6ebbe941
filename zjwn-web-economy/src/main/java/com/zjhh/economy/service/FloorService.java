package com.zjhh.economy.service;

import com.zjhh.comm.request.IdReq;
import com.zjhh.economy.request.SaveFloorReq;
import com.zjhh.economy.request.SavePlaneConfigReq;
import com.zjhh.economy.request.UpdatePlaneConfigReq;
import com.zjhh.economy.vo.FloorConfigMsgVo;
import com.zjhh.economy.vo.FloorConfigSaveVo;
import com.zjhh.economy.vo.FloorRoomInfoVo;
import com.zjhh.economy.vo.FloorVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:25
 */
public interface FloorService {

    /**
     * 楼层列表
     *
     * @param buildingId
     * @return
     */
    List<FloorVo> list(String buildingId);

    /**
     * 保存楼层信息
     *
     * @param req
     */
    void save(SaveFloorReq req);

    /**
     * 获取楼层平面图配置
     *
     * @param idReq
     * @return
     */
    FloorConfigMsgVo getPlaneConfig(IdReq idReq);

    /**
     * 编辑楼层平面图配置
     *
     * @param req
     * @return
     */
    FloorConfigSaveVo savePlaneConfig(SavePlaneConfigReq req);

    /**
     * 立即更新平面图配置
     *
     * @param req
     */
    void updatePlaneConfig(UpdatePlaneConfigReq req);
    /**
     * 获取楼层某个房间信息
     *
     * @param idReq
     * @return
     */
    FloorRoomInfoVo getRoomInfo(IdReq idReq);

    /**
     * 获取楼层所有房间信息
     *
     * @param idReq
     * @return
     */
    List<FloorRoomInfoVo> listRoomInfo(IdReq idReq);

}
