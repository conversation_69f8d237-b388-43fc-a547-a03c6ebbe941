package com.zjhh.economy.service;

import com.zjhh.economy.vo.cockpit.*;

import java.util.List;

public interface CockpitService {


    /**
     *  经济指标趋势
     * @return
     */
    List<EconomicIndicatorVo> listEconomicIndicator();

    /**
     *  产业结构-税收
     * @return
     */
    List<IndustryStructVo> listIndustryStruct();

    /**
     * 产业结构-企业数量
     * @return
     */
    List<IndustryEntStructVo> listIndustryEntStruct();

    /**
     * 企业纳税情况 - 纳税情况
     * @retu
     */
    List<EntTaxPaidVo> listEntTaxPaid();

    /**
     * 企业纳税情况-top10
     * @return
     */
    List<EntTaxPaidRankVo> listEntTaxPaidRank();

    /**
     * 楼宇预警
     * @return
     */
    List<BuildingWarningVo> listBuildingWarning();

    /**
     * 企业注册数
     * @return
     */
    List<EntCountVo> listEntRegisterCount();

    /**
     * 楼宇TOp
     * @return
     */
    List<BuildingRankVo> listBuildingRank();

    /**
     * 楼宇概览
     * @return
     */
    BuildingSummaryVo getBuildingSummary();

    /**
     * 已投入使用地图
     * @return
     */
    List<BuildingMapInfoUsedVo> listBuildingMapInfoUsed();

    /**
     * 未投入使用地图
     * @return
     */
    List<BuildingMapInfoUnusedVo> listBuildingMapInfoUnused();

    /**
     * 3D
     * @return
     */
    List<BuildingMapInfo3DVo> listBuildingMapInfo3D();

    List<ShareCockpitVo> listShareCockpit();



}
