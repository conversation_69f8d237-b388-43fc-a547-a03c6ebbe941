package com.zjhh.economy.service;

import com.zjhh.economy.vo.*;

import java.util.List;

/**
 * 移动端政府经济
 */
public interface MobileGovEconomicService {


    List<AppDynamicSummaryVo> listDynamicEco();

    /**
     * 经济运行概括
     *
     * @param dynamicId
     * @return
     */

    TabOneVo getTabOne(String dynamicId);

    /**
     * 主要经济指标完成情况
     *
     * @param dynamicId
     * @return
     */
    List<TabTwoVo> listTabTwo(String dynamicId);

    /**
     * 房地产投资情况
     *
     * @param dynamicId
     * @return
     */
    List<TabTreeVo> listTabTree(String dynamicId);

    /**
     * 规上服务业主要经济指标完成情况
     *
     * @param dynamicId
     * @return
     */
    List<TabFourVo> listTabFour(String dynamicId);

    /**
     * 规上批零、住餐企业销售额（营业额）情况
     *
     * @param dynamicId
     * @return
     */
    List<TabFiveVo> listTabFive(String dynamicId);

    /**
     * 楼宇动态信息
     *
     * @param dynamicId
     * @return
     */
    List<TabSixVo> listTabSix(String dynamicId);

    /**
     * 纳税总额50万元以上企业名单
     *
     * @param dynamicId
     * @return
     */
    List<TabSevenVo> listTabSeven(String dynamicId);

    /**
     * 财政总收入分税种结构分析'
     *
     * @param dynamicId
     * @return
     */
    List<TabEightVo> listTabEight(String dynamicId);

}
