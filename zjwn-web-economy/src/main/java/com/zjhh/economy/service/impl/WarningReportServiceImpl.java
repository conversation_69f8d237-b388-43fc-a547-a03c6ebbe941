package com.zjhh.economy.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.utils.SnowflakeUtil;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.dao.entity.AdsMoaDiReport;
import com.zjhh.economy.dao.mapper.AdsDocumentMapper;
import com.zjhh.economy.dao.mapper.AdsMoaDiReportMapper;
import com.zjhh.economy.request.report.WarningReportPageReq;
import com.zjhh.economy.request.report.WarningReportUploadReq;
import com.zjhh.economy.service.WarningReportService;
import com.zjhh.economy.vo.report.WarningReportListVo;
import com.zjhh.system.service.DictService;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-05-23 2:30 下午
 */
@Service
@DS("czzc")
public class WarningReportServiceImpl implements WarningReportService {


    @Resource
    private AdsMoaDiReportMapper adsMoaDiReportMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private DictService dictService;

    @Resource
    private AdsDocumentMapper adsDocumentMapper;

    @Override
    public Page<WarningReportListVo> listWarningReport(WarningReportPageReq req) {
        return adsMoaDiReportMapper.pageWarningReportList(req.getPage(WarningReportListVo.class), req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadReport(WarningReportUploadReq req) {
        AdsDocument adsDocument = new AdsDocument();
        adsDocument.setId(IdUtil.getSnowflakeNextIdStr());
        adsDocument.setTitle(req.getReportName() + "." + FileUtil.getSuffix(req.getFilePath()));
        adsDocument.setPath(req.getFilePath());
        adsDocument.setSize(new File(dictService.getFileUploadPath() + req.getFilePath()).length());
        adsDocument.setCreateUser(userSession.getSessionLoginVo().getLoginName());
        adsDocument.setCreateTime(LocalDateTime.now());
        adsDocument.setUpdateTime(LocalDateTime.now());
        adsDocumentMapper.insert(adsDocument);

        AdsMoaDiReport adsMoaDiReport = new AdsMoaDiReport();
        adsMoaDiReport.setCreateUser(userSession.getUserCode());
        adsMoaDiReport.setReportName(req.getReportName());
        adsMoaDiReport.setDatekey(req.getDatekey());
        adsMoaDiReport.setFileGuid(adsDocument.getId());
        adsMoaDiReport.setMakeMode(2);
        adsMoaDiReport.setId(SnowflakeUtil.createId());
        adsMoaDiReport.setUnitCode(req.getUnitCode());
        adsMoaDiReport.setUnitName(req.getUnitName());
        adsMoaDiReportMapper.insert(adsMoaDiReport);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReport(String guid) {
        AdsMoaDiReport report = adsMoaDiReportMapper.selectById(guid);
        QueryWrapper<AdsDocument> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDocument::getId, report.getFileGuid())
                .select(AdsDocument::getPath);
        AdsDocument adsDocument = adsDocumentMapper.selectOne(wrapper);
        if (ObjectUtil.isNotNull(adsDocument) && StrUtil.isNotBlank(adsDocument.getPath())) {
            deleteFile(adsDocument.getPath());
        }
        adsMoaDiReportMapper.deleteById(guid);
    }


    private void deleteFile(String fileDir) {
        String inPath = dictService.getFileUploadPath() + fileDir;
        File file = new File(inPath);
        String suffix = FileUtil.getSuffix(file);
        List<String> types = CollUtil.newArrayList("xls", "xlsx", "doc", "docx", "ppt", "pptx", "txt", "ofd");
        if (types.contains(suffix)) {
            FileUtil.del(new File(dictService.getFileUploadPath() + fileDir.replace(suffix, ".pdf")));
        }
        FileUtil.del(file);
    }
}
