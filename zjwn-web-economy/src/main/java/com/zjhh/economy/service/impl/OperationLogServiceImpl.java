package com.zjhh.economy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.LambdaUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsOperationConfig;
import com.zjhh.economy.dao.entity.AdsOperationLog;
import com.zjhh.economy.dao.mapper.AdsOperationConfigMapper;
import com.zjhh.economy.dao.mapper.AdsOperationLogMapper;
import com.zjhh.economy.dao.mapper.ChangeInfoMapper;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.request.OperationLogPageReq;
import com.zjhh.economy.service.OperationLogService;
import com.zjhh.economy.utils.OperationLogUtil;
import com.zjhh.economy.vo.operationlog.CommonLogResultVo;
import com.zjhh.economy.vo.operationlog.CommonLogVo;
import com.zjhh.economy.vo.operationlog.FloorListForLog;
import com.zjhh.economy.vo.operationlog.OperationLogVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Resource
    private AdsOperationLogMapper adsOperationLogMapper;

    @Resource
    private AdsOperationConfigMapper adsOperationConfigMapper;

    @Resource
    private ChangeInfoMapper changeInfoMapper;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Override
    public List<SingleSelectVo> listModule() {
        List<SingleSelectVo> list = new ArrayList<>();
        for (LogModuleEnum value : LogModuleEnum.values()) {
            list.add(SingleSelectVo.builder()
                    .code(value.getCode())
                    .value(value.getCode())
                    .title(value.getName())
                    .build());
        }
        return list;
    }

    @Override
    public CommonLogResultVo getOperationLogDetail(IdReq idReq) {
        AdsOperationLog adsOperationLog = adsOperationLogMapper.selectById(idReq.getId());
        if (adsOperationLog == null) {
            return null;
        }
        String module = adsOperationLog.getModule();
        String type = adsOperationLog.getType();
        LambdaQueryWrapper<AdsOperationConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdsOperationConfig::getModule, module)
                .eq(AdsOperationConfig::getType, type)
                .eq(AdsOperationConfig::getShow, true)
                .orderByAsc(AdsOperationConfig::getSort);
        List<AdsOperationConfig> operationConfigs = adsOperationConfigMapper.selectList(queryWrapper);
        if (Objects.equals(adsOperationLog.getType(), LogTypeEnum.BUILDING_FLOOR_CONFIG.getCode())) {
            // 楼层配置单独处理。
            return handleFloorConfig(adsOperationLog.getDataBefore(), adsOperationLog.getDataAfter(), operationConfigs);
        }
        return operationLogUtil.contrast(JSON.parseObject(adsOperationLog.getDataBefore()),
                JSON.parseObject(adsOperationLog.getDataAfter()), operationConfigs);
    }

    @Override
    public Page<OperationLogVo> pageOperationLogs(OperationLogPageReq req) {
        return changeInfoMapper.pageOperationLog(req.getPage(OperationLogVo.class), req);
    }

    @Override
    public List<OperationLogVo> exportOperationLogs(OperationLogPageReq req) {
        LambdaQueryWrapper<AdsOperationConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(AdsOperationConfig::getShow, true)
                .orderByAsc(AdsOperationConfig::getSort);
        List<AdsOperationConfig> operationConfigs = adsOperationConfigMapper.selectList(queryWrapper);
        LinkedHashMap<String, List<AdsOperationConfig>> configMap =
                operationConfigs.stream().collect(Collectors.groupingBy(AdsOperationConfig::getType, LinkedHashMap::new, Collectors.toList()));
        Page<OperationLogVo> page = changeInfoMapper.pageOperationLog(req.getPage(OperationLogVo.class), req);
        List<OperationLogVo> records = page.getRecords();
        for (OperationLogVo record : records) {
            List<AdsOperationConfig> optConfigs = configMap.getOrDefault(record.getType(), Collections.emptyList());
            CommonLogResultVo resultVo;
            if (Objects.equals(record.getType(), LogTypeEnum.BUILDING_FLOOR_CONFIG.getCode())) {
                resultVo = handleFloorConfig(record.getDataBefore(), record.getDataAfter(), optConfigs);
            } else {
                resultVo = operationLogUtil.contrast(JSON.parseObject(record.getDataBefore()),
                        JSON.parseObject(record.getDataAfter()), optConfigs);
            }
            if (CollUtil.isNotEmpty(resultVo.getFieldMap())) {
                TreeMap<String, String> fieldMap = resultVo.getFieldMap();
                if (CollUtil.isNotEmpty(resultVo.getOptBefore())) {
                    StringBuilder beforeBuilder = new StringBuilder();
                    List<CommonLogVo> optBefore = resultVo.getOptBefore();
                    for (CommonLogVo commonLogVo : optBefore) {
                        beforeBuilder.append(convertStr(commonLogVo, fieldMap))
                                .append("\n");
                    }
                    record.setBeforeForExcel(beforeBuilder.toString());
                }
                if (CollUtil.isNotEmpty(resultVo.getOptAfter())) {
                    StringBuilder afterBuilder = new StringBuilder();
                    List<CommonLogVo> optAfter = resultVo.getOptAfter();
                    for (CommonLogVo commonLogVo : optAfter) {
                        afterBuilder.append(convertStr(commonLogVo, fieldMap))
                                .append("\n");
                    }
                    record.setAfterForExcel(afterBuilder.toString());
                }
            }
        }
        return records;
    }

    private String convertStr(CommonLogVo commonLogVo, TreeMap<String, String> fieldMap) {
        if (CollUtil.isEmpty(fieldMap) || commonLogVo == null) {
            return "";
        }
        TreeMap<String, Object> dataMap = commonLogVo.getDataMap();
        if (CollUtil.isEmpty(dataMap)) {
            return "";
        }
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            builder.append(value)
                    .append("：")
                    .append(dataMap.getOrDefault(key, ""))
                    .append("\n");
        }
        return builder.toString();
    }

    private CommonLogResultVo handleFloorConfig(String dataBefore, String dataAfter, List<AdsOperationConfig> operationConfigs) {
        CommonLogResultVo resultVo = new CommonLogResultVo();
        resultVo.setOptBefore(new ArrayList<>());
        resultVo.setOptAfter(new ArrayList<>());
        resultVo.setFieldMap(operationLogUtil.getFieldMap(operationConfigs));
        List<FloorListForLog> beforeList = JSON.parseArray(dataBefore, FloorListForLog.class);
        Map<String, FloorListForLog> beforeMap = new HashMap<>();
        for (FloorListForLog vo : beforeList) {
            beforeMap.put(vo.getId(), vo);
        }
        List<FloorListForLog> afterList = JSON.parseArray(dataAfter, FloorListForLog.class);
        Map<String, FloorListForLog> afterMap = new HashMap<>();
        for (FloorListForLog vo : afterList) {
            afterMap.put(vo.getId(), vo);
        }
        // 找before中有，但after中没有的楼层（删除的）
        for (FloorListForLog vo : beforeList) {
            if (!afterMap.containsKey(vo.getId())) {
                CommonLogResultVo contrast = operationLogUtil.contrast(JSONObject.parseObject(JSON.toJSONString(vo)), null, operationConfigs);
                if (contrast != null) {
                    resultVo.getOptBefore().addAll(contrast.getOptBefore());
                }
            }
        }
        // 找after中有，但before没有的数据（新增的）
        for (FloorListForLog vo : afterList) {
            if (!beforeMap.containsKey(vo.getId())) {
                CommonLogResultVo contrast = operationLogUtil.contrast(null, JSONObject.parseObject(JSON.toJSONString(vo)), operationConfigs);
                if (contrast != null) {
                    resultVo.getOptAfter().addAll(contrast.getOptAfter());
                }
            }
        }
        for (FloorListForLog vo : beforeList) {
            if (afterMap.containsKey(vo.getId())) {
                // 两者都有的情况
                CommonLogResultVo contrast = operationLogUtil.contrast(JSONObject.parseObject(JSON.toJSONString(vo)),
                        JSONObject.parseObject(JSON.toJSONString(afterMap.get(vo.getId()))), operationConfigs);
                if (contrast != null) {
                    resultVo.getOptBefore().addAll(contrast.getOptBefore());
                    resultVo.getOptAfter().addAll(contrast.getOptAfter());
                }
            }
        }
        // 前后部分依然按序号排序
        String xh = LambdaUtil.getFieldName(FloorListForLog::getXh);
        resultVo.getOptBefore().sort(Comparator.comparing(vo -> (Integer) vo.getDataMap().get(xh)));
        resultVo.getOptAfter().sort(Comparator.comparing(vo -> (Integer) vo.getDataMap().get(xh)));
        return resultVo;
    }
}
