package com.zjhh.economy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.ColumnVO;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsReportColumnRelation;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.request.BuildingArchiveReq;
import com.zjhh.economy.request.BuildingConditionReq;
import com.zjhh.economy.request.QueryReportColumnReq;
import com.zjhh.economy.request.SaveReportColumnReq;
import com.zjhh.economy.request.report.BuildingSettleEntBasicReq;
import com.zjhh.economy.request.report.EntBusinessInfoReq;
import com.zjhh.economy.request.report.ReportDateReq;
import com.zjhh.economy.service.ReportService;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.report.*;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ReportServiceImpl implements ReportService {

    @Resource
    private AdsPmProjectMapper adsPmProjectMapper;

    @Resource
    private AdsReportColumnRelationMapper adsReportColumnRelationMapper;


    @Resource
    private UserSession userSession;

    @Resource
    private AdsReportColumnMapper adsReportColumnMapper;

    @Resource
    private AdsPmEnterpriseTaxMapper adsPmEnterpriseTaxMapper;


    @Resource
    private AnalyzeReportMapper analyzeReportMapper;

    @Resource
    private DmGyPageStyleMapper dmGyPageStyleMapper;



    @Override
    public ReportResultVo pageBuildingCondition(BuildingConditionReq req) {
        ReportResultVo resultVo = new ReportResultVo();
        Page<BuildingConditionVo> page = adsPmProjectMapper.pageBuildingCondition(req.getPage(BuildingConditionVo.class), req);
        resultVo.setBuildingConditions(page);
        List<ReportColumnVo> columnVos = adsReportColumnMapper.listSelectedColumnByCondition(userSession.getUserCode(), 1);
        if (CollUtil.isEmpty(columnVos)) {
            columnVos = adsReportColumnMapper.listAllColumn(1);
        }
        resultVo.setColumns(columnVos);
        return resultVo;
    }

    @Override
    public ReportResultVo exportBuildingCondition(BuildingConditionReq req) {
        ReportResultVo resultVo = new ReportResultVo();
        Page<BuildingConditionVo> page = adsPmProjectMapper.pageBuildingCondition(req.getPage(BuildingConditionVo.class), req);
        BuildingConditionVo total = adsPmProjectMapper.getBuildingConditionHj(req);
        total.setProjectType("合计");
        if (CollUtil.isNotEmpty(page.getRecords())) {
            page.getRecords().add(0,total);
        }
        resultVo.setBuildingConditions(page);
        List<ReportColumnVo> columnVos = adsReportColumnMapper.listSelectedColumnByCondition(userSession.getUserCode(), 1);
        if (CollUtil.isEmpty(columnVos)) {
            columnVos = adsReportColumnMapper.listAllColumn(1);
        }
        resultVo.setColumns(columnVos);
        return resultVo;
    }

    @Override
    public List<ReportSummaryVo> listReportSummaryColumnByBuildingCondition(QueryReportColumnReq req) {
        return adsReportColumnMapper.listReportSummaryColumn(userSession.getUserCode(), "condition", req.getQueryType());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveReportColumn(SaveReportColumnReq req) {
        QueryWrapper<AdsReportColumnRelation> delWrapper = new QueryWrapper<>();
        delWrapper.lambda().eq(AdsReportColumnRelation::getType, req.getQueryType()).eq(AdsReportColumnRelation::getUserCode, userSession.getUserCode()).eq(AdsReportColumnRelation::getReportType, 1);
        adsReportColumnRelationMapper.delete(delWrapper);
        if (CollUtil.isNotEmpty(req.getIds())) {
            for (int i = 0; i < req.getIds().size(); i++) {
                AdsReportColumnRelation relation = new AdsReportColumnRelation();
                relation.setId(IdUtil.getSnowflakeNextIdStr());
                relation.setType(req.getQueryType());
                relation.setUserCode(userSession.getUserCode());
                relation.setSort(i + 1);
                relation.setReportType(1);
                relation.setFieldId(req.getIds().get(i));
                adsReportColumnRelationMapper.insert(relation);
            }
        }
    }

    @Override
    public void saveReportColumnByArchive(SaveReportColumnReq req) {
        QueryWrapper<AdsReportColumnRelation> delWrapper = new QueryWrapper<>();
        delWrapper.lambda().eq(AdsReportColumnRelation::getType, req.getQueryType()).eq(AdsReportColumnRelation::getUserCode, userSession.getUserCode()).eq(AdsReportColumnRelation::getReportType, 2);
        adsReportColumnRelationMapper.delete(delWrapper);
        if (CollUtil.isNotEmpty(req.getIds())) {
            for (int i = 0; i < req.getIds().size(); i++) {
                AdsReportColumnRelation relation = new AdsReportColumnRelation();
                relation.setId(IdUtil.getSnowflakeNextIdStr());
                relation.setType(req.getQueryType());
                relation.setUserCode(userSession.getUserCode());
                relation.setSort(i + 1);
                relation.setReportType(2);
                relation.setFieldId(req.getIds().get(i));
                adsReportColumnRelationMapper.insert(relation);
            }
        }
    }

    @Override
    public List<ReportSummaryVo> listReportSummaryColumnByBuildingArchive(QueryReportColumnReq req) {
        return adsReportColumnMapper.listReportSummaryColumn(userSession.getUserCode(), "archive", req.getQueryType());
    }

    @Override
    public ReportResultVo pageBuildingArchives(BuildingArchiveReq req) {
        ReportResultVo resultVo = new ReportResultVo();
        Page<BuildingArchiveVo> page = adsPmEnterpriseTaxMapper.pageBuildingArchive(req.getPage(BuildingArchiveVo.class), req);
        resultVo.setBuildingArchives(page);
        List<ReportColumnVo> columnVos = adsReportColumnMapper.listSelectedColumnByArchive(userSession.getUserCode(), 1);
        if (CollUtil.isEmpty(columnVos)) {
            columnVos = adsReportColumnMapper.listAllColumn(2);
        }
        resultVo.setColumns(columnVos);
        return resultVo;
    }



    @Override
    public List<ColumnVO> exportColumnVO(Integer reportType) {
        return adsReportColumnRelationMapper.listExportColumn(reportType,userSession.getUserCode());
    }


    @Override
    public Page<BuildingSettledEntBasicVo> listBuildingSettledEntBasic(BuildingSettleEntBasicReq req) {
        return analyzeReportMapper.listBuildingSettledEntBasicVo(req.getPage(BuildingSettledEntBasicVo.class),req);
    }

    @Override
    public Page<BuildingInvestigationVo> listBuildingInvestigationVo(ReportDateReq req) {
        return analyzeReportMapper.listBuildingInvestigationVo(req.getPage(BuildingInvestigationVo.class),req);
    }

    @Override
    public Page<BuildingDynamicInfoVo> listBuildingDynamicInfoVo(ReportDateReq req) {
        return analyzeReportMapper.listBuildingDynamicInfoVo(req.getPage(BuildingDynamicInfoVo.class),req);
    }

    @Override
    public Page<BuildingDynamicAnalyzeVo> listBuildingDynamicAnalyzeVo(ReportDateReq req) {
        return analyzeReportMapper.listBuildingDynamicAnalyzeVo(req.getPage(BuildingDynamicAnalyzeVo.class),req);
    }

    @Override
    public Page<EntBusinessInfoVo> listEntBusinessInfoVo(EntBusinessInfoReq req) {
        return analyzeReportMapper.listEntBusinessInfoVo(req.getPage(EntBusinessInfoVo.class),req);
    }

    @Override
    public List<TreeSelectVo> listBuildingMenu() {
        return dmGyPageStyleMapper.listBuildingMenu();
    }

    @Override
    public List<TreeSelectVo> listInstitutionMenu() {
        return dmGyPageStyleMapper.listInstitutionMenu();
    }

    @Override
    public List<TreeSelectVo> listEntStatusMenu() {
        return dmGyPageStyleMapper.listEntStatusMenu();
    }

    @Override
    public List<TreeSelectVo> listChangeSituationMenu() {
        return dmGyPageStyleMapper.listChangeSituationMenu();
    }

    @Override
    public List<TreeSelectVo> listTreeEntTypeMenu() {
        return TreeUtils.listToTree(dmGyPageStyleMapper.listTreeEntTypeMenu(),"root");
    }

    @Override
    public List<TreeSelectVo> listTreeIndustryMenu() {
        return TreeUtils.listToTree(dmGyPageStyleMapper.listTreeIndustryMenu(),"root");
    }

    @Override
    public BuildingConditionVo getBuildingConditionTotal(BuildingConditionReq req) {
        return adsPmProjectMapper.getBuildingConditionHj(req);
    }

}
