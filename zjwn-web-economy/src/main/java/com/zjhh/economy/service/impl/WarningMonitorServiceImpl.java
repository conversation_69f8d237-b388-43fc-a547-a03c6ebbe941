package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zjhh.comm.request.IdReq;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsRuleRange;
import com.zjhh.economy.dao.entity.HandleWarning;
import com.zjhh.economy.dao.entity.WarningRule;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.request.*;
import com.zjhh.economy.service.WarningMonitorService;
import com.zjhh.economy.vo.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Service
public class WarningMonitorServiceImpl implements WarningMonitorService {

    @Resource

    private EntTaxMonitorMapper entTaxMonitorMapper;

    @Resource
    private IndustryTaxMonitorMapper industryTaxMonitorMapper;

    @Resource
    private WarningRuleMapper warningRuleMapper;

    @Resource
    private HandleWarningMapper handleWarningMapper;

    @Resource
    private AdsRuleRangeMapper adsRuleRangeMapper;
    @Resource
    private AdsPmEnterpriseTaxMapper adsPmEnterpriseTaxMapper;


    @Override
    public Page<EntTaxMonitorVo> pageEntTaxMonitor(EntTaxMonitorReq req) {
        String buildingId = req.getBuildingId();
        String projectId = req.getProject();

        return entTaxMonitorMapper.pageEntTaxMonitor(req.getPage(EntTaxMonitorVo.class), req,buildingId,projectId);
    }

    @Override
    public Page<IndTaxMonitorVo> pageIndTaxMonitor(IndTaxMonitorReq req) {
        String projectId = req.getProject();
        String buildingId = req.getBuildingId();
        return industryTaxMonitorMapper.pageIndTaxMonitor(req.getPage(IndTaxMonitorVo.class), req,buildingId,projectId);
    }

    @Override
    public Page<WarningRuleVo> pageWarningRule(MonitorRuleReq req) {
        return warningRuleMapper.pageWarningRule(req.getPage(WarningRuleVo.class), req);
    }

    @Override
    public MonitorRuleDetailVo getRuleDetail(IdReq req) {
        return warningRuleMapper.getWarningRuleDetail(req.getId());
    }

    @Override
    public void handleWarning(HandleWarningReq req) {
        if (CollUtil.isNotEmpty(req.getWarningIds())) {
            req.getWarningIds().forEach(warningId -> {
                HandleWarning handleWarning = new HandleWarning();
                handleWarning.setHandleType(req.getHandleType());
                handleWarning.setHandleDesc(req.getHandleDesc());
                handleWarning.setCreateTime(LocalDateTime.now());
                handleWarning.setWarningId(warningId);
                handleWarning.setId(IdUtil.getSnowflakeNextIdStr());
                handleWarningMapper.insert(handleWarning);
            });
        }
    }

    @Override
    public void enableMonitor(IdReq req) {
        WarningRule warningRule = warningRuleMapper.selectById(req.getId());
        if (warningRule.getEnabled()) {
            warningRule.setEnabled(false);
            warningRule.setEnabledDate(null);
        } else {
            warningRule.setEnabled(true);
            warningRule.setEnabledDate(LocalDate.now());
        }
        warningRuleMapper.updateById(warningRule);
    }

    @Override
    public void delMonitorRule(IdReq req) {
        warningRuleMapper.deleteById(req.getId());
    }

    @Override
    public void delWarning(DelWarningReq req) {
        //1-企业 2-产业
        if (req.getType().equals(1)) {
            entTaxMonitorMapper.deleteById(req.getId());
        } else {
            industryTaxMonitorMapper.deleteById(req.getId());
        }
    }

    @Override
    public TaxSummaryVo getTaxSummary(TaxSummaryReq req) {

        WarningSummaryVo warningSummaryVo = null;
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 获取当前年份
        String currentYear = String.valueOf(currentDate.getYear());

        // 获取上年
        LocalDate lastYear = currentDate.minusYears(1);
        String lastYearValue = String.valueOf(lastYear.getYear());

        // 获取当前月份
        String currentMonth = String.format("%02d", currentDate.getMonthValue());
        // 获取上月
        LocalDate lastMonth = currentDate.minusMonths(1);
        String lastMonthValue = String.format("%02d", lastMonth.getMonthValue());
        String taxCurrentDate = adsPmEnterpriseTaxMapper.getTaxMaxDate();
        String taxCurrentYear = taxCurrentDate.substring(0,4);
        String taxCurrentMonth = taxCurrentDate.substring(5,6);
        Date taxCurrentDateTime = DateUtil.parse(taxCurrentDate,"yyyyMM");
        Date lastDateTime = DateUtil.offset(taxCurrentDateTime, DateField.MONTH,-12);
        String taxLastYear = String.valueOf(DateUtil.year(lastDateTime));
        Date lastMonthDateTime  = DateUtil.offset(taxCurrentDateTime, DateField.MONTH,-1);
        String lastMonthDate = DateUtil.format(lastMonthDateTime,"yyyyMM");
        String taxLastMonthValue = lastMonthDate.substring(5,6);



        TaxSummaryVo taxSummaryVo = adsPmEnterpriseTaxMapper.getTaxSummaryByEnt(taxCurrentYear,taxCurrentMonth,taxLastYear,taxLastMonthValue);
        if (req.getType().equals(1)) {
            warningSummaryVo = entTaxMonitorMapper.getWarningSummaryByEnt(currentYear,currentMonth,lastYearValue,lastMonthValue);
        } else {
            warningSummaryVo = industryTaxMonitorMapper.getWarningSummaryByInd(currentYear,currentMonth,lastYearValue,lastMonthValue);
        }
        taxSummaryVo.setBqWarningCount(warningSummaryVo.getBqWarningCount());
        taxSummaryVo.setByWarningCount(warningSummaryVo.getByWarningCount());
        taxSummaryVo.setWarningTbZf(warningSummaryVo.getTbZf());
        taxSummaryVo.setWarningHbZf(warningSummaryVo.getHbZf());
        return taxSummaryVo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveMonitorRule(AddMonitorRuleReq req) {
        if (StrUtil.isNotBlank(req.getId())) {
            WarningRule monitorRule = warningRuleMapper.selectById(req.getId());
            monitorRule.setMonitorRuleCompare(req.getMonitorRuleCompare());
            monitorRule.setMonitorRulePeriod(req.getMonitorRulePeriod());
            monitorRule.setMonitorRuleChange(req.getMonitorRuleChange());
            monitorRule.setMonitorRuleZf(req.getMonitorRuleZf());
            monitorRule.setRuleName(req.getRuleName());
            monitorRule.setCreateTime(LocalDateTime.now());
            warningRuleMapper.updateById(monitorRule);
            UpdateWrapper<AdsRuleRange> delRangeWrapper = new UpdateWrapper<>();
            delRangeWrapper.lambda().eq(AdsRuleRange::getRuleId, req.getId());
            adsRuleRangeMapper.delete(delRangeWrapper);
            if (CollUtil.isNotEmpty(req.getMonitorRanges())) {
                req.getMonitorRanges().forEach(range -> {
                    AdsRuleRange ruleRange = new AdsRuleRange();
                    ruleRange.setId(IdUtil.getSnowflakeNextIdStr());
                    ruleRange.setRuleId(monitorRule.getId());
                    ruleRange.setBuildingId(range);
                    adsRuleRangeMapper.insert(ruleRange);
                });
            }
        } else {
            WarningRule monitorRule = new WarningRule();
            BeanUtil.copyProperties(req, monitorRule);
            monitorRule.setId(IdUtil.getSnowflakeNextIdStr());
            warningRuleMapper.insert(monitorRule);
            if (CollUtil.isNotEmpty(req.getMonitorRanges())) {
                req.getMonitorRanges().forEach(range -> {
                    AdsRuleRange ruleRange = new AdsRuleRange();
                    ruleRange.setId(IdUtil.getSnowflakeNextIdStr());
                    ruleRange.setRuleId(monitorRule.getId());
                    ruleRange.setBuildingId(range);
                    adsRuleRangeMapper.insert(ruleRange);
                });
            }
        }
    }

    @Override
    public HandleDetailVo getHandleDetail(IdReq req) {
        return handleWarningMapper.getHandleDetail(req.getId());
    }
}
