package com.zjhh.economy.service;

import com.zjhh.comm.vo.ColumnVO;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.BuildingArchiveReq;
import com.zjhh.economy.request.BuildingConditionReq;
import com.zjhh.economy.request.QueryReportColumnReq;
import com.zjhh.economy.request.SaveReportColumnReq;
import com.zjhh.economy.request.report.BuildingSettleEntBasicReq;
import com.zjhh.economy.request.report.EntBusinessInfoReq;
import com.zjhh.economy.request.report.ReportDateReq;
import com.zjhh.economy.vo.BuildingConditionVo;
import com.zjhh.economy.vo.ReportResultVo;
import com.zjhh.economy.vo.ReportSummaryVo;
import com.zjhh.economy.vo.report.*;

import java.util.List;

public interface ReportService {


    ReportResultVo pageBuildingCondition(BuildingConditionReq req);

    ReportResultVo exportBuildingCondition(BuildingConditionReq req);

    List<ReportSummaryVo> listReportSummaryColumnByBuildingCondition(QueryReportColumnReq req);

    void saveReportColumn(SaveReportColumnReq req);


    void saveReportColumnByArchive(SaveReportColumnReq req);





    List<ReportSummaryVo> listReportSummaryColumnByBuildingArchive(QueryReportColumnReq req);

    ReportResultVo pageBuildingArchives(BuildingArchiveReq req);


    List<ColumnVO> exportColumnVO(Integer reportType);


    /**
     * 商务楼宇入驻单位基本情况表
     * @param req
     * @return
     */
    Page<BuildingSettledEntBasicVo> listBuildingSettledEntBasic(BuildingSettleEntBasicReq req);


    /**
     * 商务楼宇调查表
     * @param req
     * @return
     */
    Page<BuildingInvestigationVo> listBuildingInvestigationVo(ReportDateReq req);


    /**
     * 商务楼宇动态信息表
     * @param req
     * @return
     */
    Page<BuildingDynamicInfoVo> listBuildingDynamicInfoVo(ReportDateReq req);

    /**
     * 商务楼宇动态分析表
     * @param req
     * @return
     */
    Page<BuildingDynamicAnalyzeVo> listBuildingDynamicAnalyzeVo(ReportDateReq req);

    /**
     * 企业工商信息表
     * @param req
     * @return
     */
    Page<EntBusinessInfoVo> listEntBusinessInfoVo(EntBusinessInfoReq req);


    /**
     * 报表楼宇下拉
     * @return
     */
    List<TreeSelectVo> listBuildingMenu();

    /**
     * 报表机构下拉
     * @return
     */
    List<TreeSelectVo> listInstitutionMenu();

    /**
     * 报表企业状态下拉
     * @return
     */
    List<TreeSelectVo> listEntStatusMenu();

    /**
     * 报表企业变更情况下拉
     * @return
     */
    List<TreeSelectVo> listChangeSituationMenu();

    /**
     * 报表企业类型下拉
     * @return
     */
    List<TreeSelectVo> listTreeEntTypeMenu();

    /**
     * 报表行业
     * @return
     */
    List<TreeSelectVo> listTreeIndustryMenu();


    /**
     * 楼宇项目情况合计
     * @param req
     * @return
     */
    BuildingConditionVo getBuildingConditionTotal(BuildingConditionReq req);



}
