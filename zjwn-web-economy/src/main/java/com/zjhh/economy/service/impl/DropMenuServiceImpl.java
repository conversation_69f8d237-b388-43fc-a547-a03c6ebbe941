package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.economy.dao.entity.*;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.enume.CompareEnum;
import com.zjhh.economy.enume.DmPmTypeEnum;
import com.zjhh.economy.request.analyzecockpit.CommunityDropMenuReq;
import com.zjhh.economy.request.analyzereport.ChooseFilterReq;
import com.zjhh.economy.service.DropMenuService;
import com.zjhh.economy.vo.CockpitTreeSelectedVo;
import com.zjhh.economy.vo.DocDateVo;
import com.zjhh.economy.vo.SettleRoomMenuVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/11 15:58
 */
@Service
public class DropMenuServiceImpl implements DropMenuService {

    @Resource
    private AdsPmProjectMapper adsPmProjectMapper;

    @Resource
    private DmPmMapper dmPmMapper;

    @Resource
    private DmGyHyMapper dmGyHyMapper;

    @Resource
    private AdsPmBuildingMapper adsPmBuildingMapper;

    @Resource
    private AdsPmFloorMapper adsPmFloorMapper;

    @Resource
    private DmGyModuleDateMapper dmGyModuleDateMapper;

    @Resource
    private AdsPmRoomMapper adsPmRoomMapper;


    @Override
    public List<SingleSelectVo> listProject(String code) {
        QueryWrapper<AdsPmProject> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StrUtil.isNotBlank(code), AdsPmProject::getCommunityCode, code)
                .select(AdsPmProject::getId, AdsPmProject::getProjectName)
                .orderByAsc(AdsPmProject::getSerialNo);
        List<AdsPmProject> list = adsPmProjectMapper.selectList(wrapper);
        List<SingleSelectVo> res = new ArrayList<>(list.size());
        list.forEach(project -> {
            SingleSelectVo vo = new SingleSelectVo();
            vo.setCode(project.getId());
            vo.setTitle(project.getProjectName());
            vo.setValue(project.getId());
            res.add(vo);
        });
        return res;
    }

    @Override
    public List<SingleSelectVo> listCommunity() {
        return dmPmSelect(DmPmTypeEnum.COMMUNITY);
    }

    @Override
    public List<SingleSelectVo> listOwnership() {
        return dmPmSelect(DmPmTypeEnum.OWNERSHIP);
    }

    @Override
    public List<SingleSelectVo> listBuildingStatus() {
        return dmPmSelect(DmPmTypeEnum.BUILDING_STATUS);
    }

    @Override
    public List<SingleSelectVo> listBuildingType() {
        return dmPmSelect(DmPmTypeEnum.BUILDING_TYPE);
    }

    @Override
    public List<SingleSelectVo> listBuildingLabel() {
        return dmPmSelect(DmPmTypeEnum.BUILDING_LABEL);
    }

    @Override
    public List<SingleSelectVo> listBuildingConfig() {
        return dmPmSelect(DmPmTypeEnum.BUILDING_CONFIG);
    }

    @Override
    public List<TreeSelectVo> listBuildingPosition() {
        List<TreeSelectVo> list = dmGyHyMapper.listHy(2);
        return TreeUtils.listToTree(list, "0");
    }

    @Override
    public List<SingleSelectVo> listRenovation() {
        return dmPmSelect(DmPmTypeEnum.RENOVATION);
    }

    @Override
    public List<SingleSelectVo> listRoomType() {
        return dmPmSelect(DmPmTypeEnum.ROOM_TYPE);
    }

    @Override
    public List<SingleSelectVo> listPropertyCertificate() {
        return dmPmSelect(DmPmTypeEnum.PROPERTY_CERTIFICATE);
    }

    @Override
    public List<SingleSelectVo> listRoomLabel() {
        return dmPmSelect(DmPmTypeEnum.ROOM_LABEL);
    }

    @Override
    public List<SingleSelectVo> listBuilding(String projectId) {
        QueryWrapper<AdsPmBuilding> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StrUtil.isNotBlank(projectId), AdsPmBuilding::getProjectId, projectId)
                .select(AdsPmBuilding::getId, AdsPmBuilding::getBuildingName)
                .orderByAsc(AdsPmBuilding::getSerialNo);
        List<AdsPmBuilding> list = adsPmBuildingMapper.selectList(wrapper);
        List<SingleSelectVo> res = new ArrayList<>(list.size());
        list.forEach(building -> {
            SingleSelectVo vo = new SingleSelectVo();
            vo.setCode(building.getId());
            vo.setTitle(building.getBuildingName());
            vo.setValue(building.getId());
            res.add(vo);
        });
        return res;
    }

    @Override
    public List<SingleSelectVo> listFloor(String buildingId) {
        QueryWrapper<AdsPmFloor> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StrUtil.isNotBlank(buildingId), AdsPmFloor::getBuildingId, buildingId)
                .select(AdsPmFloor::getId, AdsPmFloor::getFloorName)
                .orderByAsc(AdsPmFloor::getFloorNo);
        List<AdsPmFloor> list = adsPmFloorMapper.selectList(wrapper);
        List<SingleSelectVo> res = new ArrayList<>(list.size());
        list.forEach(floor -> {
            SingleSelectVo vo = new SingleSelectVo();
            vo.setCode(floor.getId());
            vo.setTitle(floor.getFloorName());
            vo.setValue(floor.getId());
            res.add(vo);
        });
        return res;
    }

    @Override
    public List<TreeSelectVo> listIndustry() {
        List<TreeSelectVo> list = dmGyHyMapper.listHy(2);
        return TreeUtils.listToTree(list, "0");
    }

    @Override
    public List<TreeSelectVo> listIndustryByLevel4() {
        List<TreeSelectVo> list = dmGyHyMapper.listHy(4);
        return TreeUtils.listToTree(list, "0");
    }

    @Override
    public List<TreeSelectVo> listIndustryByLevel3(Integer level) {
        List<TreeSelectVo> list = dmGyHyMapper.listHy(level);
        List<TreeSelectVo> treeList = TreeUtils.listToTree(list, "0");
        List<TreeSelectVo> assembleList = new ArrayList<>();
        List<TreeSelectVo> reslutList = new ArrayList<>();
        if (CollUtil.isNotEmpty(treeList)) {
            treeList.forEach(tree -> {
                    assembleList.addAll(tree.getChildren());
            });
        }

        return assembleList;
    }

    @Override
    public List<SingleSelectVo> listEntLabel() {
        return dmPmSelect(DmPmTypeEnum.ENTERPRISE_LABEL);
    }

    @Override
    public List<TreeSelectVo> listEntType() {
        return TreeUtils.listToTree(dmPmTreeSelect(DmPmTypeEnum.ENTERPRISE_TYPE),"root");
    }

    @Override
    public List<SingleSelectVo> listFinancingStage() {
        return dmPmSelect(DmPmTypeEnum.FINANCING_STAGE);
    }

    @Override
    public DocDateVo getDate(String targetPage) {
        QueryWrapper<DmGyModuleDate> wrapper = new QueryWrapper<>();
        if (StrUtil.isBlank(targetPage)) {
            targetPage = "SYSTEM_YEAR";
        }
        wrapper.lambda().eq(DmGyModuleDate::getTargetPage, targetPage)
                .select(DmGyModuleDate::getMinDate, DmGyModuleDate::getMaxDate, DmGyModuleDate::getUpdateType);
        DmGyModuleDate dmGyModuleDate = dmGyModuleDateMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(dmGyModuleDate)) {
            wrapper.clear();
            wrapper.lambda().eq(DmGyModuleDate::getTargetPage, "SYSTEM_YEAR")
                    .select(DmGyModuleDate::getMinDate, DmGyModuleDate::getMaxDate, DmGyModuleDate::getUpdateType);
        }
        return BeanUtil.copyProperties(dmGyModuleDateMapper.selectOne(wrapper), DocDateVo.class);
    }

    @Override
    public List<SettleRoomMenuVo> listRoom(String floorId) {
        return adsPmRoomMapper.listRoom(floorId);
    }

    @Override
    public List<SingleSelectVo> listProjectType() {
        return dmPmSelect(DmPmTypeEnum.PROJECT_TYPE);
    }

    @Override
    public List<SingleSelectVo> listProjectTypeNoElse() {
        List<SingleSelectVo> list = dmPmSelect(DmPmTypeEnum.PROJECT_TYPE);
        List<SingleSelectVo> resultList = list.stream().filter(vo -> {
            return !vo.getCode().equals("qt");
        }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<TreeSelectVo> listCommunityBuilding() {
        return adsPmProjectMapper.listCommunityBuilding();
    }

    @Override
    public List<TreeSelectVo> listCommunityBuildingWithoutFloor() {
        return adsPmProjectMapper.listCommunityBuildingWithoutFloor();
    }

    @Override
    public List<CockpitTreeSelectedVo> listCommunityBuildingCockpit(CommunityDropMenuReq req) {
        return adsPmProjectMapper.listCommunityBuildingByCockpit(req);
    }

    @Override
    public List<TreeSelectVo> listChooseFilter(ChooseFilterReq req) {
        if (req.getFilterType().equals(CompareEnum.COMMUNITY.value())) {
            return dmPmMapper.listCommunity();
        } else if (req.getFilterType().equals(CompareEnum.PROJECT.value())) {
            return adsPmProjectMapper.listProject(req);
        } else {
            return adsPmBuildingMapper.listBuilding();
        }
    }

    @Override
    public List<SingleSelectVo> listMoreProject(List<String> codes) {
        QueryWrapper<AdsPmProject> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(CollUtil.isNotEmpty(codes), AdsPmProject::getCommunityCode, codes)
                .select(AdsPmProject::getId, AdsPmProject::getProjectName)
                .orderByAsc(AdsPmProject::getSerialNo);
        List<AdsPmProject> list = adsPmProjectMapper.selectList(wrapper);
        List<SingleSelectVo> res = new ArrayList<>(list.size());
        list.forEach(project -> {
            SingleSelectVo vo = new SingleSelectVo();
            vo.setCode(project.getId());
            vo.setTitle(project.getProjectName());
            vo.setValue(project.getId());
            res.add(vo);
        });
        return res;
    }

    @Override
    public List<SingleSelectVo> listMoreBuilding(List<String> codes) {
        QueryWrapper<AdsPmBuilding> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(CollUtil.isNotEmpty(codes), AdsPmBuilding::getProjectId, codes)
                .select(AdsPmBuilding::getId, AdsPmBuilding::getBuildingName)
                .orderByAsc(AdsPmBuilding::getSerialNo);
        List<AdsPmBuilding> list = adsPmBuildingMapper.selectList(wrapper);
        List<SingleSelectVo> res = new ArrayList<>(list.size());
        list.forEach(building -> {
            SingleSelectVo vo = new SingleSelectVo();
            vo.setCode(building.getId());
            vo.setTitle(building.getBuildingName());
            vo.setValue(building.getId());
            res.add(vo);
        });
        return res;
    }

    @Override
    public List<SingleSelectVo> listSubmitSource() {
        return dmPmSelect(DmPmTypeEnum.SUBMIT_SOURCE);

    }

    @Override
    public List<SingleSelectVo> listDemandType() {
        return dmPmSelect(DmPmTypeEnum.DEMAND_TYPE);
    }

    @Override
    public List<SingleSelectVo> listMoveLabel() {
        return dmPmSelect(DmPmTypeEnum.MOVE_LABEL);
    }

    @Override
    public List<SingleSelectVo> listCurrencyType() {
        return dmPmSelect(DmPmTypeEnum.CURRENCY_TYPE);
    }

    private List<SingleSelectVo> dmPmSelect(DmPmTypeEnum dmPmTypeEnum) {
        QueryWrapper<DmPm> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DmPm::getType, dmPmTypeEnum.code())
                .select(DmPm::getCode, DmPm::getName)

                .orderByAsc(DmPm::getSort);
        List<DmPm> list = dmPmMapper.selectList(wrapper);
        List<SingleSelectVo> res = new ArrayList<>(list.size());
        list.forEach(dmPm -> {
            SingleSelectVo vo = new SingleSelectVo();
            vo.setCode(dmPm.getCode());
            vo.setTitle(dmPm.getName());
            vo.setValue(dmPm.getCode());
            res.add(vo);
        });
        return res;
    }

    private List<TreeSelectVo> dmPmTreeSelect(DmPmTypeEnum dmPmTypeEnum) {
        QueryWrapper<DmPm> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DmPm::getType, dmPmTypeEnum.code())
                .select(DmPm::getCode, DmPm::getName,DmPm::getParentCode)

                .orderByAsc(DmPm::getSort);
        List<DmPm> list = dmPmMapper.selectList(wrapper);
        List<TreeSelectVo> res = new ArrayList<>(list.size());
        list.forEach(dmPm -> {
            TreeSelectVo vo = new TreeSelectVo();
            vo.setKey(dmPm.getCode());
            vo.setTitle(dmPm.getName());
            vo.setValue(dmPm.getCode());
            vo.setParentKey(dmPm.getParentCode());
            res.add(vo);
        });
        return res;
    }
}
