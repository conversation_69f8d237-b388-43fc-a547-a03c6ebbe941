package com.zjhh.economy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.comm.vo.TreeSelectedVo;
import com.zjhh.economy.dao.entity.AdsPmBuilding;
import com.zjhh.economy.dao.entity.DataPermission;
import com.zjhh.economy.dao.mapper.DataPermissionMapper;
import com.zjhh.economy.enume.DataPermissionType;
import com.zjhh.economy.request.datapermission.AuthDataPermissionReq;
import com.zjhh.economy.request.datapermission.TreeDataPermissionReq;
import com.zjhh.economy.service.DataPermissionService;
import com.zjhh.user.dao.mapper.VOrganizeMapper;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/24 上午10:47
 */
@Service
public class DataPermissionServiceImpl implements DataPermissionService {

    @Resource
    private VOrganizeMapper vOrganizeMapper;

    @Resource
    private DataPermissionMapper dataPermissionMapper;

    @Resource
    private UserSession userSession;

    @Override
    public List<TreeSelectVo> treeOrganizeUser() {
        List<TreeSelectVo> list = vOrganizeMapper.treeOrganizeUser();
        return TreeUtils.listToTree(list, "0");
    }

    @Override
    public TreeSelectedVo treeDataPermission(TreeDataPermissionReq req) {
        TreeSelectedVo vo = new TreeSelectedVo();
        DataPermissionType typeEnum = DataPermissionType.intToEnum(req.getType());
        switch (typeEnum) {
            case BUILDING:
                vo.setTreeSelects(TreeUtils.listToTree(dataPermissionMapper.listBuildingTree(), "root"));
                vo.setSelectedKeys(getCodes(typeEnum, req.getUserCode()));
                break;
        }
        return vo;
    }

    @Override
    public void authDataPermission(AuthDataPermissionReq req) {
        QueryWrapper<DataPermission> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DataPermission::getUserCode, req.getUserCode())
                .eq(DataPermission::getType, req.getType());
        dataPermissionMapper.delete(wrapper);

        String userCode = userSession.getUserCode();
        LocalDateTime now = LocalDateTime.now();

        if (CollUtil.isNotEmpty(req.getCodes())) {
            List<DataPermission> list = new ArrayList<>();
            req.getCodes().forEach(code -> {
                DataPermission dataPermission = new DataPermission();
                dataPermission.setId(IdUtil.getSnowflakeNextIdStr());
                dataPermission.setCode(code);
                dataPermission.setUserCode(req.getUserCode());
                dataPermission.setType(req.getType());
                dataPermission.setCreateUser(userCode);
                dataPermission.setCreateTime(now);
                list.add(dataPermission);
            });
            if (list.size() > 500) {
                List<DataPermission> temp;
                int times = list.size() / 500;
                for (int i = 0; i <= times; i++) {
                    if (i == times) {
                        temp = list.subList(i * 500, list.size());
                    } else {
                        temp = list.subList(i * 500, (i + 1) * 500);
                    }
                    dataPermissionMapper.insertBatchSomeColumn(temp);
                }
            } else {
                dataPermissionMapper.insertBatchSomeColumn(list);
            }
        }
    }

    @Override
    public  List<AdsPmBuilding> test() {
        return dataPermissionMapper.test();
    }

    /**
     * 获取权限
     *
     * @param type
     * @param userCode
     * @return
     */
    private List<String> getCodes(DataPermissionType type, String userCode) {
        QueryWrapper<DataPermission> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DataPermission::getType, type.value())
                .eq(DataPermission::getUserCode, userCode)
                .select(DataPermission::getCode)
                .orderByAsc(DataPermission::getId);
        return dataPermissionMapper.selectStrings(wrapper);
    }
}
