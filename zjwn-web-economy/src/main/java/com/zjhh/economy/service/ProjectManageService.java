package com.zjhh.economy.service;

import com.zjhh.comm.request.IdReq;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.AddProjectReq;
import com.zjhh.economy.request.QueryProjectPageReq;
import com.zjhh.economy.request.UpdateProjectReq;
import com.zjhh.economy.vo.ProjectDetailVo;
import com.zjhh.economy.vo.ProjectListVo;

public interface ProjectManageService {


    /**
     * 项目列表
     *
     * @param req
     * @return
     */
    Page<ProjectListVo> pageProjectList(QueryProjectPageReq req);

    /**
     * 添加项目
     *
     * @param req
     */
    void addProject(AddProjectReq req);

    /**
     * 编辑项目
     *
     * @param req
     */
    void updateProject(UpdateProjectReq req);

    /**
     * 删除项目
     *
     * @param req
     */
    void delProject(IdReq req);

    /**
     * 详情
     * @param req
     * @return
     */
    ProjectDetailVo getProjectDetail(IdReq req);


}
