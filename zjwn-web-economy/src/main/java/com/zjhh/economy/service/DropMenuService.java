package com.zjhh.economy.service;

import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.economy.request.analyzecockpit.CommunityDropMenuReq;
import com.zjhh.economy.request.analyzereport.ChooseFilterReq;
import com.zjhh.economy.vo.CockpitTreeSelectedVo;
import com.zjhh.economy.vo.DocDateVo;
import com.zjhh.economy.vo.SettleRoomMenuVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 15:58
 */
public interface DropMenuService {

    /**
     * 项目选择下拉
     *
     * @return
     */
    List<SingleSelectVo> listProject(String code);

    /**
     * 社区选择下拉
     *
     * @return
     */
    List<SingleSelectVo> listCommunity();

    /**
     * 权属性质下拉
     *
     * @return
     */
    List<SingleSelectVo> listOwnership();

    /**
     * 楼宇状态下拉
     *
     * @return
     */
    List<SingleSelectVo> listBuildingStatus();

    /**
     * 楼宇类型
     *
     * @return
     */
    List<SingleSelectVo> listBuildingType();

    /**
     * 楼宇标签
     *
     * @return
     */
    List<SingleSelectVo> listBuildingLabel();

    /**
     * 楼宇配套
     *
     * @return
     */
    List<SingleSelectVo> listBuildingConfig();

    /**
     * 楼宇定位
     *
     * @return
     */
    List<TreeSelectVo> listBuildingPosition();

    /**
     * 装修状态
     *
     * @return
     */
    List<SingleSelectVo> listRenovation();

    /**
     * 房间类型
     *
     * @return
     */
    List<SingleSelectVo> listRoomType();

    /**
     * 产证状态
     *
     * @return
     */
    List<SingleSelectVo> listPropertyCertificate();

    /**
     * 房间标签
     *
     * @return
     */
    List<SingleSelectVo> listRoomLabel();

    /**
     * 楼宇下拉
     *
     * @param projectId
     * @return
     */
    List<SingleSelectVo> listBuilding(String projectId);

    /**
     * 楼层下拉
     *
     * @param buildingId
     * @return
     */
    List<SingleSelectVo> listFloor(String buildingId);

    /**
     * 行业下拉
     *
     * @param
     * @return
     */
    List<TreeSelectVo> listIndustry();

    List<TreeSelectVo> listIndustryByLevel4();

    List<TreeSelectVo> listIndustryByLevel3(Integer level);

    List<SingleSelectVo> listEntLabel();

    List<TreeSelectVo> listEntType();

    List<SingleSelectVo> listFinancingStage();

    DocDateVo getDate(String targetPage);

    List<SettleRoomMenuVo> listRoom(String floorId );

    List<SingleSelectVo> listProjectType();

    List<SingleSelectVo> listProjectTypeNoElse();

    List<TreeSelectVo> listCommunityBuilding();


    List<TreeSelectVo> listCommunityBuildingWithoutFloor();


    List<CockpitTreeSelectedVo> listCommunityBuildingCockpit(CommunityDropMenuReq req);


    List<TreeSelectVo> listChooseFilter(ChooseFilterReq req);


    /**
     * 项目选择下拉-多选参数
     *
     * @return
     */
    List<SingleSelectVo> listMoreProject(List<String> codes);


    List<SingleSelectVo> listMoreBuilding(List<String> codes);


    List<SingleSelectVo> listSubmitSource();

    List<SingleSelectVo> listDemandType();

    List<SingleSelectVo> listMoveLabel();

    List<SingleSelectVo> listCurrencyType();


}
