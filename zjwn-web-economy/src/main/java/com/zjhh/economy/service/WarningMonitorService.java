package com.zjhh.economy.service;

import com.zjhh.comm.request.IdReq;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.*;
import com.zjhh.economy.vo.*;

/**
 * 预警Service
 */
public interface WarningMonitorService {


    Page<EntTaxMonitorVo> pageEntTaxMonitor(EntTaxMonitorReq req);


    Page<IndTaxMonitorVo> pageIndTaxMonitor(IndTaxMonitorReq req);


    Page<WarningRuleVo> pageWarningRule(MonitorRuleReq req);


    MonitorRuleDetailVo getRuleDetail(IdReq req);


    void handleWarning(HandleWarningReq req);

    void enableMonitor(IdReq req);

    void delMonitorRule(IdReq req);

    void delWarning(DelWarningReq req);

    TaxSummaryVo getTaxSummary(TaxSummaryReq req);

    void saveMonitorRule(AddMonitorRuleReq req);

    HandleDetailVo getHandleDetail(IdReq req);













}
