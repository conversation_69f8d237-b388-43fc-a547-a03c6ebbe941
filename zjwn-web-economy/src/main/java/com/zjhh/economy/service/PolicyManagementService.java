package com.zjhh.economy.service;

import com.zjhh.comm.request.IdReq;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.policymanagement.PolicyManagementAddOrUpdateReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementCashBatchReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementPageReq;
import com.zjhh.economy.vo.policymanagement.PolicyManagementCashVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
public interface PolicyManagementService {

    /**
     * 录入政策
     *
     * @param req
     */
    void addPolicyManagement(PolicyManagementAddOrUpdateReq req);

    /**
     * 修改政策
     *
     * @param req
     */
    void updatePolicyManagement(PolicyManagementAddOrUpdateReq req);

    /**
     * 兑现
     *
     * @param req
     */
    void addPolicyManagementCash(PolicyManagementCashBatchReq req);

    /**
     * 详情
     *
     * @param req
     * @return
     */
    PolicyManagementVo getPolicyManagement(IdReq req);

    /**
     * 删除
     *
     * @param req
     */
    void deletePolicyManagement(IdReq req);

    /**
     * 查询
     *
     * @param req
     * @return
     */
    Page<PolicyManagementPageVo> pagePolicyManagement(PolicyManagementPageReq req);

    /**
     * 兑现列表
     *
     * @param req
     * @return
     */
    List<PolicyManagementCashVo> listPolicyManagementCash(IdReq req);


    List<PolicyManagementPageVo> listPolicyByCockpit();
}
