package com.zjhh.economy.service;

import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.*;
import com.zjhh.economy.vo.*;

import java.util.List;

/**
 *  业务管理
 */
public interface BusinessService {


    /**
     *  添加
     * @param req
     */
    void saveLabel(AddLabelReq req);

    List<LabelReq> listLabel(QueryLabelsReq req);


    Page<EnterpriseDemandVo> pageEnterpriseDemand(EnterpriseDemandReq req);

    void addEnterpriseDemand(AddEnterpriseDemandReq req);

    EnterpriseDemandDetailVo getEnterpriseDemandDetail(IdReq req);

    void handleDemand(HandleEnterpriseDemandReq req);


    void deleteDemand(IdReq req);


    void addEnterpriseVisit(AddEnterpriseVisitReq req);

    void deleteEnterpriseVisit(IdReq req);

    Page<EnterpriseVisitVo> pageEnterpriseVisit(QueryEnterpriseVisitReq req);

    EnterpriseVisitDetailVo getEnterpriseVisitDetail(String id);


    WorkPlatformRoomVo getWorkPlatformRoom();

    WorkPlatformEntManageVo getWorkPlatformEntManage();


    WorkPlatformDemandVo getWorkPlatformDemand(DatekeyReq req);

    List<String> listBriefing(Integer period);

    void generationWarning();

    void generationDataBrief();

    List<WorkPlatformWarningRemindVo> listWorkPlatformRemind();

    Page<WarningRemindListVo> pageWarningRemindList(WarningPageReq req);


    void operateRemind(WarningRemindOperateReq req);

    List<CommonFunctionVo> listCommonFunctionByUser();

    List<TreeSelectVo> treeCommonFunction();

    void commonFunctionEdit(CommonFunctionReq req);


    DocumentVo getMobileContactPerson();








}
