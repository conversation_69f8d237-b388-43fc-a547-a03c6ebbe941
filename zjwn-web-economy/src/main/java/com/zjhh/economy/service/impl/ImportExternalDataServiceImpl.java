package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.exception.BizException;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.*;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.enume.DocumentTypeEnum;
import com.zjhh.economy.enume.ImportTypeEnum;
import com.zjhh.economy.request.ExportRecordReq;
import com.zjhh.economy.request.ImportExternalDataListReq;
import com.zjhh.economy.request.ImportExternalDataReq;
import com.zjhh.economy.service.ImportExternalDataService;
import com.zjhh.economy.utils.ImportExcelValidator;
import com.zjhh.economy.vo.*;
import com.zjhh.user.service.impl.BaseServiceImpl;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class ImportExternalDataServiceImpl extends BaseServiceImpl implements ImportExternalDataService {

    @Resource
    private AdsPmImportMapper adsPmImportMapper;

    @Resource
    private AdsPmImportRecordMapper adsPmImportRecordMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private AdsPmEnterpriseMapper adsPmEnterpriseMapper;

    @Resource
    private AdsPmRoomEnterpriseMapper adsPmRoomEnterpriseMapper;

    @Resource
    private AdsPmEnterpriseMoveRegisterMapper adsPmEnterpriseMoveRegisterMapper;

    @Resource
    private AdsPmMoveRegisterLabelMapper adsPmMoveRegisterLabelMapper;

    @Resource
    private AdsPmRoomMapper adsPmRoomMapper;

    @Resource
    private DmPmMapper dmPmMapper;

    @Resource
    private AdsBusinessDocumentMapper adsBusinessDocumentMapper;


    @Override
    public Page<ImportExternalDataVo> pageImportExternalData(ImportExternalDataListReq req) {
        return adsPmImportMapper.pageImportExternalData(req.getPage(ImportExternalDataVo.class), req);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void importExternalData(ImportExternalDataReq req) throws IOException {
        Boolean checkFile = ImportExcelValidator.validateExcel(req.getFile().getInputStream(),req.getFile().getOriginalFilename(),req.getUploadType());

        if (!checkFile) {
            throw  new BizException("请检查模版是否匹配");
        }

        AdsPmImport adsPmImport = new AdsPmImport();
        adsPmImport.setId(IdUtil.getSnowflakeNextIdStr());
        adsPmImport.setCreateUser(userSession.getUserCode());
        adsPmImport.setCreateTime(LocalDateTime.now());
        adsPmImport.setUpdateTime(LocalDateTime.now());
        adsPmImport.setUploadType(req.getUploadType());
        adsPmImport.setUploadDate(LocalDateTime.now());
        adsPmImport.setFileName(FileUtil.getPrefix(req.getFile().getOriginalFilename()));
        AtomicReference<Boolean> allMatch = new AtomicReference<>(true);
        List<AdsPmImportRecord> resultRecords = new ArrayList<>();
        if (req.getUploadType().equals(ImportTypeEnum.ENTERPRISE_SETTLE.getCode())) {
            List<ImportSettleVo> importRecords = EasyExcel.read(req.getFile().getInputStream()).head(ImportSettleVo.class).sheet(1).doReadSync();
            if (CollUtil.isEmpty(importRecords)) {
                throw  new BizException("请检查模版是否匹配");
            }
            adsPmImportMapper.insert(adsPmImport);
            importRecords.forEach(record -> {
                AdsPmImportRecord insertRecord = new AdsPmImportRecord();
                BeanUtil.copyProperties(record, insertRecord);
                insertRecord.setId(IdUtil.getSnowflakeNextIdStr());
                insertRecord.setImportId(adsPmImport.getId());
                if (checkImportSettle(insertRecord)) {
                    insertRecord.setMatchResult("匹配成功！");
                    resultRecords.add(insertRecord);
                } else {
                    insertRecord.setMatchResult("匹配失败！");
                    allMatch.set(false);
                }
                adsPmImportRecordMapper.insert(insertRecord);
            });
        } else {
            List<ImportMoveVo> importRecords = EasyExcel.read(req.getFile().getInputStream()).head(ImportMoveVo.class).sheet(1).doReadSync();
            if (CollUtil.isEmpty(importRecords)) {
                throw  new BizException("请检查模版是否匹配");
            }
            adsPmImportMapper.insert(adsPmImport);
            importRecords.forEach(record -> {
                AdsPmImportRecord insertRecord = new AdsPmImportRecord();
                BeanUtil.copyProperties(record, insertRecord);
                insertRecord.setId(IdUtil.getSnowflakeNextIdStr());
                insertRecord.setImportId(adsPmImport.getId());
                if (checkMove(insertRecord)) {
                    insertRecord.setMatchResult("匹配成功！");
                    resultRecords.add(insertRecord);
                } else {
                    insertRecord.setMatchResult("匹配失败！");
                    allMatch.set(false);
                }
                adsPmImportRecordMapper.insert(insertRecord);
            });
        }
        if (allMatch.get()) {
            saveImportSettleInfo(resultRecords, req.getUploadType());
            adsPmImport.setUploadStatus(1);
        } else {
            adsPmImport.setUploadStatus(0);
        }
        adsPmImportMapper.updateById(adsPmImport);

    }

    @Override
    public void exportRecords(HttpServletResponse response, ExportRecordReq req) throws IOException {
        AdsPmImport adsPmImport = adsPmImportMapper.selectById(req.getImportId());
        if (req.getUploadType().equals(ImportTypeEnum.ENTERPRISE_SETTLE.getCode())) {
            List<ImportSettleVo> list = adsPmImportRecordMapper.listImportSettle(req.getImportId());
            CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                    new ThreeTableVo(
                            new ThreeTableVo.MidContentTwoVo(ImportSettleVo.class, list)
                    ));
            EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, adsPmImport.getFileName() + (adsPmImport.getUploadStatus().equals(0) ? "失败信息" : ""), null);
        } else {
            List<ImportMoveVo> list = adsPmImportRecordMapper.listImportMove(req.getImportId());
            CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                    new ThreeTableVo(
                            new ThreeTableVo.MidContentTwoVo(ImportMoveVo.class, list)
                    ));
            EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, adsPmImport.getFileName() + (adsPmImport.getUploadStatus().equals(0) ? "失败信息" : ""), null);
        }
    }

    @Override
    public DocumentVo getTemplate(String templateType) {
        DocumentVo documentVo = new DocumentVo();
        QueryWrapper<AdsBusinessDocument> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().
                eq(AdsBusinessDocument::getBusinessId, templateType.equals(ImportTypeEnum.ENTERPRISE_SETTLE.getCode()) ? "settle_template" : "move_template").
                eq(AdsBusinessDocument::getDocumentType, templateType.equals(ImportTypeEnum.ENTERPRISE_SETTLE.getCode()) ? DocumentTypeEnum.SETTLE_TEMPLATE.value()
                        : DocumentTypeEnum.MOVE_TEMPLATE.value());
        AdsBusinessDocument document = adsBusinessDocumentMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(document)) {
            documentVo.setFileId(document.getDocumentId());
        }

        return documentVo;
    }

    private Boolean checkImportSettle(AdsPmImportRecord record) {
        ImportRoomVo roomVo = getRoom(record);
        AdsPmEnterprise enterprise = getEnterprise(record.getEnterpriseName());
        AdsPmRoomEnterprise roomEnterprise = getSettledInfo(enterprise, roomVo);
        BigDecimal settledArea = new BigDecimal(0);
        if (StrUtil.isNotBlank(record.getArea())) {
            settledArea = new BigDecimal(record.getArea());
        }
        Boolean checkRenovation = ObjectUtil.isNotNull(record.getRenovationStartDate()) && ObjectUtil.isNotNull(record.getRenovationEndDate())
                ? (record.getRenovationEndDate().isAfter(record.getRenovationStartDate())) : true;
        Boolean checkCheckInDate = ObjectUtil.isNotNull(record.getCheckInDate()) ? (record.getCheckInDate().isBefore(LocalDate.now())) : false;

        Boolean checkExpectMoveOutDate = true;
        if (checkCheckInDate) {
            checkExpectMoveOutDate = ObjectUtil.isNotNull(record.getExpectMoveOutDate()) ? record.getExpectMoveOutDate().isAfter(record.getCheckInDate()) : true;
        }
        return ObjectUtil.isNotNull(roomVo) &&
                ObjectUtil.isNull(roomEnterprise) &&
                settledArea.floatValue() < roomVo.getBusinessArea().floatValue() &&
                checkRenovation && checkCheckInDate && checkExpectMoveOutDate;
    }

    Boolean checkMove(AdsPmImportRecord record) {
        ImportRoomVo roomVo = getRoom(record);
        AdsPmEnterprise enterprise = getEnterprise(record.getEnterpriseName());
        AdsPmRoomEnterprise roomEnterprise = getSettledInfo(enterprise, roomVo);
        return ObjectUtil.isNotNull(roomVo) &&
                ObjectUtil.isNotNull(enterprise) &&
                ObjectUtil.isNotNull(roomEnterprise) &&
                (record.getRealityMoveOutDate().isBefore(LocalDate.now()) || record.getRealityMoveOutDate().equals(LocalDate.now()));
    }

    private ImportRoomVo getRoom(AdsPmImportRecord record) {
        ImportRoomVo roomVo = adsPmRoomMapper.getImportRoom(record.getProjectName(), record.getBuildingName(), record.getFloorNo(), record.getRoomNo());
        return roomVo;
    }

    private AdsPmEnterprise getEnterprise(String enterpriseName) {
        QueryWrapper<AdsPmEnterprise> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AdsPmEnterprise::getEnterpriseName, enterpriseName);
        AdsPmEnterprise enterprise = adsPmEnterpriseMapper.selectOne(queryWrapper);
        return enterprise;
    }

    private AdsPmRoomEnterprise getSettledInfo(AdsPmEnterprise enterprise, ImportRoomVo roomVo) {
        if (ObjectUtil.isNotNull(enterprise) && ObjectUtil.isNotNull(roomVo)) {
            QueryWrapper<AdsPmRoomEnterprise> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(AdsPmRoomEnterprise::getRoomId, roomVo.getRoomId())
                    .eq(AdsPmRoomEnterprise::getEnterpriseId, enterprise.getId()).eq(AdsPmRoomEnterprise::getMoved,false);
            AdsPmRoomEnterprise roomEnterprise = adsPmRoomEnterpriseMapper.selectOne(queryWrapper);
            return roomEnterprise;
        }

        return null;

    }


    private void saveImportSettleInfo(List<AdsPmImportRecord> records, String uploadType) {
        if (uploadType.equals(ImportTypeEnum.ENTERPRISE_SETTLE.getCode())) {
            records.forEach(record -> {
                AdsPmEnterprise enterprise = getEnterprise(record.getEnterpriseName());
                ImportRoomVo roomVo = getRoom(record);
                AdsPmRoomEnterprise checkRoomEnterprise = getSettledInfo(enterprise,roomVo);
                if (ObjectUtil.isNull(enterprise)) {
                    enterprise = new AdsPmEnterprise();
                    BeanUtil.copyProperties(record, enterprise);
                    enterprise.setId(IdUtil.getSnowflakeNextIdStr());
                    enterprise.setSerialNo(getEntSerialNo());
                    enterprise.setCreateUser(userSession.getUserCode());
                    enterprise.setCreateTime(LocalDateTime.now());
                    enterprise.setUpdateTime(LocalDateTime.now());
                    enterprise.setFoundDate(LocalDate.now());
                    adsPmEnterpriseMapper.insert(enterprise);
                }
                if (ObjectUtil.isNotNull(enterprise) && ObjectUtil.isNotNull(roomVo) && ObjectUtil.isNull(checkRoomEnterprise)) {
                    AdsPmRoomEnterprise roomEnterprise = new AdsPmRoomEnterprise();
                    roomEnterprise.setId(IdUtil.getSnowflakeNextIdStr());
                    roomEnterprise.setArea(StrUtil.isNotBlank(record.getArea()) ? new BigDecimal(record.getArea()) : null);
                    roomEnterprise.setMoved(false);
                    roomEnterprise.setEnterpriseId(enterprise.getId());
                    roomEnterprise.setProjectId(roomVo.getProjectId());
                    roomEnterprise.setBuildingId(roomVo.getBuildingId());
                    roomEnterprise.setFloorId(roomVo.getFloorId());
                    roomEnterprise.setRoomId(roomVo.getRoomId());
                    roomEnterprise.setCheckInDate(record.getCheckInDate());
                    roomEnterprise.setExpectMoveOutDate(record.getExpectMoveOutDate());
                    roomEnterprise.setRenovationStartDate(record.getRenovationStartDate());
                    roomEnterprise.setRenovationEndDate(record.getRenovationEndDate());
                    roomEnterprise.setRemark(record.getRemark());
                    roomEnterprise.setCreateTime(LocalDateTime.now());
                    roomEnterprise.setUpdateTime(LocalDateTime.now());
                    adsPmRoomEnterpriseMapper.insert(roomEnterprise);
                }
            });
        } else {
            records.forEach(record -> {
                AdsPmEnterprise enterprise = getEnterprise(record.getEnterpriseName());
                ImportRoomVo roomVo = getRoom(record);
                AdsPmRoomEnterprise roomEnterprise = getSettledInfo(enterprise, roomVo);
                roomEnterprise.setMoved(true);
                roomEnterprise.setRealityMoveOutDate(record.getRealityMoveOutDate());
                adsPmRoomEnterpriseMapper.updateById(roomEnterprise);
                AdsPmEnterpriseMoveRegister register = new AdsPmEnterpriseMoveRegister();
                register.setId(IdUtil.getSnowflakeNextIdStr());
                register.setSettledId(roomEnterprise.getId());
                register.setCreateTime(LocalDateTime.now());
                register.setUpdateTime(LocalDateTime.now());
                List<String> destinations = StrUtil.isNotBlank(record.getDestination()) ? Arrays.asList(record.getDestination().split("/")) : null;
                if (CollUtil.isNotEmpty(destinations)) {
                    register.setProvince(destinations.get(0));
                    register.setCity(destinations.get(1));
                }
                register.setMoveReason(record.getRemark());
                List<String> moveOutReasons = StrUtil.isNotBlank(record.getMoveOutReason()) ? Arrays.asList(record.getMoveOutReason().split("、")) : null;
                adsPmEnterpriseMoveRegisterMapper.insert(register);
                List<String> registerLabels = new ArrayList<>();
                if (CollUtil.isNotEmpty(moveOutReasons)) {
                    registerLabels = dmPmMapper.listMoveRegisterLabelByName(moveOutReasons);
                }
                registerLabels.forEach(label -> {
                    AdsPmMoveRegisterLabel moveRegisterLabel = new AdsPmMoveRegisterLabel();
                    moveRegisterLabel.setId(IdUtil.getSnowflakeNextIdStr());
                    moveRegisterLabel.setLabelCode(label);
                    moveRegisterLabel.setMoveRegisterId(register.getId());
                    adsPmMoveRegisterLabelMapper.insert(moveRegisterLabel);
                });

            });
        }
    }

    private String getEntSerialNo() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMM");
        String currentYearMonth = dateFormat.format(new Date());
        String maxEntSerial = adsPmEnterpriseMapper.getMaxEntSerialNo(currentYearMonth);
        String serialNo = null;
        if (StrUtil.isBlank(maxEntSerial)) {
            serialNo = currentYearMonth + "00001";
        } else {
            Integer maxSerialNo = Integer.valueOf(maxEntSerial.substring(5));
            serialNo = currentYearMonth + String.format("%05d", maxSerialNo + 1);
        }
        return serialNo;
    }


}
