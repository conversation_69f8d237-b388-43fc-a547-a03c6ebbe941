package com.zjhh.economy.service;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.economy.request.MobileCompareReq;
import com.zjhh.economy.request.analyzecockpit.*;
import com.zjhh.economy.vo.MobileCompareVo;
import com.zjhh.economy.vo.analyzecockpit.*;
import com.zjhh.economy.vo.cockpit.EconomicIndicatorScoreVo;

import java.util.List;

/**
 * 楼宇分析大屏Service
 */

public interface AnalyzeCockpitService {


    /**
     * 面积分析
     *
     * @param req
     * @return
     */
    AreaAnalyzeVo getAreaAnalyze(SpaceAnalyzeReq req);

    /**
     * 入驻趋势
     *
     * @param req
     * @return
     */
    List<SettledTrendVo> listSettledTrend(AnalyzeDateReq req);

    /**
     * 税收分析
     *
     * @param req
     * @return
     */
    TaxAnalyzeVo getTaxAnalyze(SpaceAnalyzeReq req);

    /**
     * 税收整体走势
     *
     * @param req
     * @return
     */
    List<TaxTrendVo> listTaxTrend(AnalyzeDateReq req);

    /**
     * 企业入驻分析
     *
     * @param req
     * @return
     */
    SettledEntAnalyzeVo getSettledEntAnalyze(SpaceAnalyzeReq req);

    /**
     * 入驻企业单位走势
     *
     * @param req
     * @return
     */
    List<SettledEntUnitTrendVo> listSettledEntUnitTrend(AnalyzeDateReq req);


    /**
     * 产业聚焦分析-纳税规模
     *
     * @param req
     * @return
     */
    List<IndustryFocusAnalyzeVo> listTaxScaledRank(IndustryAnalyzeReq req);


    /**
     * 产业聚焦分析-企业数量
     *
     * @param req
     * @return
     */
    List<IndustryFocusAnalyzeVo> listEntCountRank(IndustryAnalyzeReq req);


    /**
     * 产业聚焦分析-入驻面积
     *
     * @param req
     * @return
     */
    List<IndustryFocusAnalyzeVo> listSettledAreaRank(IndustryAnalyzeReq req);

    /**
     * 项目总览
     *
     * @param req
     * @return
     */
    CockpitProjectSummaryVo getCockpitProjectSummary(AnalyzeCockpitCommonReq req);


    /**
     * 楼宇总览
     *
     * @param req
     * @return
     */
    CockpitBuildingSummaryVo getCockpitBuildingSummary(AnalyzeCockpitCommonReq req);

    /**
     * 空间分析汇总
     *
     * @param req
     * @return
     */
    SpaceAnalyzeSummaryVo getSpaceAnalyzeSummary(AnalyzeCockpitCommonReq req);

    /**
     * 入驻走势
     *
     * @param req
     * @return
     */
    List<SettledTrendVo> listSettledTrendBySpace(AnalyzeDateReq req);

    /**
     * 空间资源分析
     *
     * @param req
     * @return
     */
    List<SpaceResourceVo> listSpaceResourceBySpace(AnalyzeCockpitCommonReq req);

    /**
     * 房源面积分布
     *
     * @param req
     * @return
     */
    List<RoomResourceVo> listRoomResourceBySpace(AnalyzeCockpitCommonReq req);

    /**
     * 空置周期分析
     *
     * @param req
     * @return
     */
    EmptyPeriodSummaryVo getEmptyPeriodSummary(AnalyzeCockpitCommonReq req);

    /**
     * 企业租用面积分布
     *
     * @param req
     * @return
     */
    List<RoomResourceVo> listEntSettledArea(AnalyzeCockpitCommonReq req);

    /**
     * 空置房源面积分布
     *
     * @param req
     * @return
     */
    List<RoomResourceVo> listEmptyRoomArea(AnalyzeCockpitCommonReq req);

    /**
     * 楼层空置面积排行
     *
     * @param req
     * @return
     */
    List<FloorEmptyAreaRankVo> listFloorEmptyAreaRank(AnalyzeCockpitCommonReq req);

    /**
     * 关键指标
     *
     * @param req
     * @return
     */
    EconomicSummaryVo getEconomicSummary(AnalyzeCockpitCommonReq req);

    /**
     * 税收增长可能性
     *
     * @param req
     * @return
     */
    TaxIncomeReasonVo getTaxIncomeReason(AnalyzeCockpitCommonReq req);


    /**
     * 经济分析-税收整体趋势
     *
     * @param req
     * @return
     */
    List<TaxTrendVo> listEconomicTaxTrend(EconomicTaxTrendReq req);

    /**
     * 税种结构分析
     *
     * @param req
     * @return
     */
    List<TaxCategoryVo> listTaxCategory(EconomicTaxTrendReq req);

    /**
     * 单位产出趋势对比
     *
     * @param req
     * @return
     */
    List<UnitIncomeTrendVo> listUnitIncomeTrend(AnalyzeDateReq req);


    /**
     * 产业聚焦
     *
     * @param req
     * @return
     */
    List<IndustryFocusAnalyzeVo> listIndustryFocus(IndustryAnalyzeReq req);

    /**
     * 产业税收趋势
     *
     * @param req
     * @return
     */
    List<IndustryTaxTrendVo> listIndustryTaxTrend(IndustryTrendReq req);

    /**
     * 产业企业数量
     *
     * @param req
     * @return
     */
    List<IndustryContributeVo> listIndustryContribute(IndustryTrendReq req);

    /**
     * 产业入驻面积
     *
     * @param req
     * @return
     */
    List<IndustrySettledAreaVo> listIndustrySettledArea(IndustryTrendReq req);


    /**
     * 企业入驻汇总
     *
     * @param req
     * @return
     */
    EntSettledSummaryVo getEntSettledSummary(AnalyzeCockpitCommonReq req);


    /**
     * 企业排行榜
     *
     * @param req
     * @return
     */
    List<EntTaxRankVo> listEntRank(AnalyzeCockpitCommonReq req);


    /**
     * 入驻面积排行榜
     *
     * @param req
     * @return
     */
    List<EntSettledAreaVo> listEntSettled(AnalyzeCockpitCommonReq req);


    /**
     * 企业稳定性排行榜
     *
     * @param req
     * @return
     */
    List<EntStableRankVo> listEntStableRank(AnalyzeCockpitCommonReq req);


    /**
     * 企业属地分布
     *
     * @param req
     * @return
     */
    List<EntLocaledDisVo> listEntLocaledDis(AnalyzeCockpitCommonReq req);


    /**
     * 属地注册趋势
     *
     * @param req
     * @return
     */
    List<LocaledRegisterTrendVo> listLocaledRegisterTrend(AnalyzeDateReq req);


    /**
     * 单位性质分布
     *
     * @param req
     * @return
     */
    List<UnitPropertyDisVo> listUnitPropertyDis(AnalyzeCockpitCommonReq req);


    /**
     * 企业税收情况
     *
     * @param req
     * @return
     */
    List<EntTaxIncomeVo> listEntTaxIncome(AnalyzeCockpitCommonReq req);


    /**
     * 注经一致
     *
     * @param req
     * @return
     */
    List<RegisterBusinessSameVo> listRegisterBusinessSame(AnalyzeCockpitCommonReq req);


    /**
     * 注册登记
     *
     * @param req
     * @return
     */
    List<RegisterTypeVo> listRegisterType(AnalyzeCockpitCommonReq req);


    List<TreeSelectVo> listDefaultIndustries(IndustryTrendReq req);

    /**
     *  App 总览情况
     * @param req
     * @return
     */
    AppSummaryVo getAppSummary(AnalyzeCockpitCommonReq req);

    List<AppEconomicVo> listAppEconomic(AppEconomicReq req);

    EconomicIndicatorScoreVo getEconomicIndicatorScore(String buildingId);

    void generationBuildingScore();


    MobileCompareVo getMobileCompareItem(MobileCompareReq req);








}
