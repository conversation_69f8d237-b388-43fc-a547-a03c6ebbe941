package com.zjhh.economy.service;

import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsDmsColumns;
import com.zjhh.economy.request.AddBuildingReq;
import com.zjhh.economy.request.PageBuildingReq;
import com.zjhh.economy.request.UpdateBuildingReq;
import com.zjhh.economy.vo.BuildingDetailVo;
import com.zjhh.economy.vo.BuildingProjectVo;
import com.zjhh.economy.vo.BuildingVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 16:58
 */
public interface BuildingService {

    /**
     * 楼宇分页
     *
     * @param req
     * @return
     */
    Page<BuildingVo> page(PageBuildingReq req);

    /**
     * 添加楼宇
     *
     * @param req
     */
    void add(AddBuildingReq req);

    /**
     * 修改楼宇
     *
     * @param req
     */
    void update(UpdateBuildingReq req);

    /**
     * 删除楼宇
     *
     * @param buildingId
     */
    void delete(String buildingId);

    /**
     * 显示列
     *
     * @return
     */
    List<AdsDmsColumns> listShowColumn();

    /**
     * 项目选择
     *
     * @return
     */
    List<BuildingProjectVo> listBuildingProject();

    /**
     * 楼宇详情
     *
     * @param buildingId
     * @return
     */
    BuildingDetailVo getDetail(String buildingId);
}
