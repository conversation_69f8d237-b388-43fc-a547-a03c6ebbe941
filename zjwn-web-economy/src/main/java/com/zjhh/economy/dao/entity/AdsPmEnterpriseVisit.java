package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_enterprise_visit")
public class AdsPmEnterpriseVisit implements Serializable {
    private static final long serialVersionUID = 4200014460065445626L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String enterpriseId;

    private String visitor;

    private LocalDate visitDate;

    private String receptionist;

    private String visitPurpose;

    private String remark;

    private String documentId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
