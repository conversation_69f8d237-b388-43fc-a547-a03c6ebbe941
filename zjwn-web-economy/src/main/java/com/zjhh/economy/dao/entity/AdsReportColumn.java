package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_report_column")
public class AdsReportColumn implements Serializable {


    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 字段
     */
    private String field;

    /**
     * 字段名称
     */
    private String fieldName;


    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否可以删除
     */
    private Boolean deleted;

    @Schema(description = "父字段")
    private String parentField;

    /**
     * 创建日期
     */
    private LocalDateTime createTime;

}
