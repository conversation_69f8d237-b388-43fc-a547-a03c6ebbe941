package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 人才表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_enterprise_talent")
public class AdsPmEnterpriseTalent implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 企业id
     */
    private String enterpriseId;

    /**
     * 员工数
     */
    private Integer employeeNum;

    /**
     * 博士数量
     */
    private Integer drNum;

    /**
     * 硕士数
     */
    private Integer masterNum;

    /**
     * 本科数
     */
    private Integer bachelorNum;

    /**
     * 专科数
     */
    private Integer juniorNumber;

    /**
     * 中专数
     */
    private Integer polytechnicNum;

    /**
     * 其他学历数
     */
    private Integer otherNum;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
