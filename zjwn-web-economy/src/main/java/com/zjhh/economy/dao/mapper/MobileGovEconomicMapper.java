package com.zjhh.economy.dao.mapper;

import com.zjhh.economy.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MobileGovEconomicMapper {



    List<AppDynamicSummaryVo> listDynamicEcoSummary();

    /**
     *  经济运行概括
     * @param dynamicId
     * @return
     */

    TabOneVo getTabOne(@Param("dynamicId") String dynamicId);

    /**
     * 主要经济指标完成情况
     * @param dynamicId
     * @return
     */
    List<TabTwoVo> listTabTwo(@Param("dynamicId") String dynamicId);

    /**
     *房地产投资情况
     * @param dynamicId
     * @return
     */
    List<TabTreeVo> listTabTree(@Param("dynamicId") String dynamicId);

    /**
     *规上服务业主要经济指标完成情况
     * @param dynamicId
     * @return
     */
    List<TabFourVo> listTabFour(@Param("dynamicId") String dynamicId);

    /**
     *规上批零、住餐企业销售额（营业额）情况
     * @param dynamicId
     * @return
     */
    List<TabFiveVo> listTabFive(@Param("dynamicId") String dynamicId);

    /**
     *楼宇动态信息
     * @param dynamicId
     * @return
     */
    List<TabSixVo> listTabSix(@Param("dynamicId") String dynamicId);

    /**
     *纳税总额50万元以上企业名单
     * @param dynamicId
     * @return
     */
    List<TabSevenVo> listTabSeven(@Param("dynamicId") String dynamicId);

    /**
     *财政总收入分税种结构分析'
     * @param dynamicId
     * @return
     */
    List<TabEightVo> listTabEight(@Param("dynamicId") String dynamicId);


}
