package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_business_document")
public class AdsBusinessDocument implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 文档id
     */
    private String documentId;

    /**
     * 类型：projectImg-项目图片，
buildingOutsideImg-楼宇外观图
buildingOtherImg-楼宇其它图片
floorPlaneImg-楼层平面图
roomImg-房间图片
     enterpriseDemandImg-企业诉求图片
     */
    private String documentType;


}
