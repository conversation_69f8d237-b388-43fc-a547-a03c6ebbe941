package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 大屏内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_bas_screen_view")
public class AdsBasScreenView implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private String guid;

    /**
     * 数据日期
     */
    private String datekey;

    /**
     * 大屏模块
     */
    private String screenModule;

    /**
     * 内容1
     */
    private String hold1;

    /**
     * 内容2
     */
    private String hold2;

    /**
     * 内容3
     */
    private String hold3;

    /**
     * 内容4
     */
    private String hold4;

    /**
     * 内容5
     */
    private String hold5;

    /**
     * 内容6
     */
    private String hold6;

    /**
     * 内容7
     */
    private String hold7;

    /**
     * 内容8
     */
    private String hold8;

    /**
     * 内容9
     */
    private String hold9;

    /**
     * 内容10
     */
    private String hold10;

    /**
     * 内容11
     */
    private String hold11;

    /**
     * 内容12
     */
    private String hold12;

    /**
     * 内容13
     */
    private String hold13;

    /**
     * 内容14
     */
    private String hold14;

    /**
     * 内容15
     */
    private String hold15;

    /**
     * 内容16
     */
    private String hold16;

    /**
     * 内容17
     */
    private String hold17;

    /**
     * 内容18
     */
    private String hold18;

    /**
     * 内容19
     */
    private String hold19;

    /**
     * 内容20
     */
    private String hold20;


}
