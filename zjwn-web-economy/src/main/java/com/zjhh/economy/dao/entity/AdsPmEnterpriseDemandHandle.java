package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_enterprise_demand_handle")
public class AdsPmEnterpriseDemandHandle implements Serializable {
    private static final long serialVersionUID = 1721543905596123366L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String demandId;

    private Integer handleType;

    private String handleDesc;

    private String documentId;

    private String userCode;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
