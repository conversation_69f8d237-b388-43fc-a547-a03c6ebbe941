package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 数据权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_data_permission")
public class DataPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    private String id;

    private String userCode;

    private String code;

    private Integer type;

    private String createUser;

    private LocalDateTime createTime;


}
