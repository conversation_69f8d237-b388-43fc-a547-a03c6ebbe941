package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_project")
public class AdsPmProject implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String serialNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 社区编码
     */
    private String communityCode;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String dimension;

    /**
     * 占地面积
     */
    private BigDecimal projectArea;

    /**
     * 物业公司
     */
    private String manageCompany;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 项目介绍
     */
    private String projectIntro;

    /**
     * 序号
     */
    private Integer xh;



    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 地址
     */
    private String address;

    private String projectType;

}
