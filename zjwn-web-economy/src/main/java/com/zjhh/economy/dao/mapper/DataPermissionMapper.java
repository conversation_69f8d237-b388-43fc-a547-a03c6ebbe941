package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmBuilding;
import com.zjhh.economy.dao.entity.DataPermission;

import java.util.List;

/**
 * <p>
 * 数据权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface DataPermissionMapper extends CustomerBaseMapper<DataPermission> {

    /**
     * 获取楼宇树形结构
     *
     * @return
     */
    List<TreeSelectVo> listBuildingTree();

    /**
     * 测试代码
     *
     * @return
     */
    List<AdsPmBuilding> test();
}
