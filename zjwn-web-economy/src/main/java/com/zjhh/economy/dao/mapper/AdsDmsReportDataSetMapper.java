package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsDmsReportDataSet;
import com.zjhh.economy.request.earlywarning.AddAlysReportDataSetReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 数据设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-19
 */
public interface AdsDmsReportDataSetMapper extends CustomerBaseMapper<AdsDmsReportDataSet> {

    List<AddAlysReportDataSetReq> getDataSet(@Param("reportTypeCode") String reportTypeCode);

    Map<String, String> findDataValue(@Param("dataSetId") String dataSetId, @Param("datekey") String datekey);

    List<Map<String, String>> listDataValue(@Param("dataSetId") String dataSetId, @Param("datekey") String datekey, @Param("selectDatas") List<String> selectDatas);

    /**
     * 根据数据源名称查找所有字段的名称
     *
     * @param reportTypeCode
     * @param dataSetName
     * @return
     */
    Set<String> listFieldNames(@Param("reportTypeCode") String reportTypeCode, @Param("dataSetName") String dataSetName);
}
