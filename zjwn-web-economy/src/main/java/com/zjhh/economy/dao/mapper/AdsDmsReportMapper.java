package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsDmsReport;
import com.zjhh.economy.request.earlywarning.PageAnalysisReportReq;
import com.zjhh.economy.vo.earlywarning.AdsDmsReportVo;
import com.zjhh.economy.vo.earlywarning.CreateReportDocVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 智能预警分析报告 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-19
 */
public interface AdsDmsReportMapper extends CustomerBaseMapper<AdsDmsReport> {

    List<CreateReportDocVo> listCreateReportDoc(@Param("reportTypeCodes") List<String> reportTypeCodes);

    /**
     * 分析报告模板列表
     *
     * @param req
     * @return
     */
    Page<AdsDmsReportVo> pageAnalysisReport(Page<AdsDmsReportVo> page, @Param("req") PageAnalysisReportReq req);

    /**
     * 获取可选择的行政区划
     *
     * @return
     */
    List<TreeSelectVo> listSelectOrgCodes();
}
