package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_enterprise_demand")
public class AdsPmEnterpriseDemand implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 诉求描述
     */
    private String demandDesc;

    /**
     * 提交途径
     */
    private String submitSource;

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 诉求类型
     */
    private String demandType;

    private Integer handleType;


    private LocalDate submitDate;


    private String buildingId;

    private String contactPerson;

    /**
     * 提交时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 录入用户
     */
    private String createUser;


}
