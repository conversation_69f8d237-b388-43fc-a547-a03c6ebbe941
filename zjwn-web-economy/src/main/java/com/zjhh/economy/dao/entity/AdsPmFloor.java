package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 楼层表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_floor")
public class AdsPmFloor implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String buildingId;

    /**
     * 建筑楼层
     */
    private Integer floorNo;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 备注
     */
    private String remark;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 楼层平面图
     */
    private String planeImgId;

    /**
     * 楼层平面图配置id
     */
    private String planeConfigId;
}
