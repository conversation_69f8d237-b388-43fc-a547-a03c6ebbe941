package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsCommonFunctionUser;
import com.zjhh.economy.vo.CommonFunctionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsCommonFunctionUserMapper extends CustomerBaseMapper<AdsCommonFunctionUser> {

    List<TreeSelectVo> listCommonFunction();
    List<CommonFunctionVo> listCommonFunctionUser(@Param("userCode") String userCode);



}
