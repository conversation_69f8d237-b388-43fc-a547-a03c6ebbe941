package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策管理-兑现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_policy_mt_cash")
public class AdsPmPolicyMtCash implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 兑现金额
     */
    private BigDecimal cashAmount;

    /**
     * 兑现时间
     */
    private LocalDate cashDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 政策id
     */
    private String policyId;

    /**
     * 企业id
     */
    private String entId;
}
