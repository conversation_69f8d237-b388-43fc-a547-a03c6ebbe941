package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业税收
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_enterprise_tax")
public class AdsPmEnterpriseTax implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String enterpriseId;

    /**
     * 时间
     */
    private String datekey;

    /**
     * 国内增值税
     */
    private BigDecimal zzs;

    /**
     * 个人所得税
     */
    private BigDecimal grsds;

    /**
     * 企业所得税
     */
    private BigDecimal qysds;

    /**
     * 城市维护建设税
     */
    private BigDecimal cswhjss;

    /**
     * 房产税
     */
    private BigDecimal fcs;

    /**
     * 城镇土地使用税
     */
    private BigDecimal cztdsys;

    /**
     * 土地增值税
     */
    private BigDecimal tdzzs;

    /**
     * 印花税
     */
    private BigDecimal yhs;

    /**
     * 耕地占用税
     */
    private BigDecimal gdzys;

    /**
     * 消费税
     */
    private BigDecimal xfs;

    /**
     * 资源税
     */
    private BigDecimal zys;

    /**
     * 环境保护税
     */
    private BigDecimal hjbhs;

    /**
     * 国有资源
     */
    private BigDecimal gyzy;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
