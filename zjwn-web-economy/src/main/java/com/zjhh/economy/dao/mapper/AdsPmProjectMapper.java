package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmProject;
import com.zjhh.economy.request.BuildingConditionReq;
import com.zjhh.economy.request.QueryProjectPageReq;
import com.zjhh.economy.request.analyzecockpit.CommunityDropMenuReq;
import com.zjhh.economy.request.analyzecockpit.SpaceAnalyzeReq;
import com.zjhh.economy.request.analyzereport.ChooseFilterReq;
import com.zjhh.economy.vo.BuildingConditionVo;
import com.zjhh.economy.vo.CockpitTreeSelectedVo;
import com.zjhh.economy.vo.ProjectDetailVo;
import com.zjhh.economy.vo.ProjectListVo;
import com.zjhh.economy.vo.analyzecockpit.AreaAnalyzeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsPmProjectMapper extends CustomerBaseMapper<AdsPmProject> {

    /**
     * 获取最大的编码
     *
     * @param communityCode
     * @return
     */
    Integer findMaxXh();


    /**
     * 获取项目列表
     *
     * @param page
     * @param req
     * @return
     */
    Page<ProjectListVo> pageProject(@Param("page") Page<ProjectListVo> page, @Param("req") QueryProjectPageReq req);

    ProjectDetailVo getProjectDetail(@Param("projectId") String projectId);

    Page<BuildingConditionVo> pageBuildingCondition(@Param("page") Page<BuildingConditionVo> page, @Param("req")BuildingConditionReq req);

    BuildingConditionVo getBuildingConditionHj(@Param("req") BuildingConditionReq req);


    List<TreeSelectVo> listCommunityBuilding();

    List<TreeSelectVo> listCommunityBuildingWithoutFloor();


    List<CockpitTreeSelectedVo> listCommunityBuildingByCockpit(@Param("req")CommunityDropMenuReq req);


    AreaAnalyzeVo getAreaAnalyze(SpaceAnalyzeReq req);

    List<TreeSelectVo> listProject(@Param("req") ChooseFilterReq req);






}
