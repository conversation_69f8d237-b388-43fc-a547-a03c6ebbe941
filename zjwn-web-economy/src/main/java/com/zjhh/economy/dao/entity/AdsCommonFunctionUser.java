package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_common_function_user")
public class AdsCommonFunctionUser implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String functionId;

    private String userCode;

}
