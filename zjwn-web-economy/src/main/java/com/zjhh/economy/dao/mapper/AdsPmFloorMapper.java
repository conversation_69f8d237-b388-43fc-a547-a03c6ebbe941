package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmFloor;
import com.zjhh.economy.vo.FloorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 楼层表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsPmFloorMapper extends CustomerBaseMapper<AdsPmFloor> {


    /**
     * 楼层列表
     *
     * @param buildingId
     * @return
     */
    List<FloorVo> list(@Param("buildingId") String buildingId);

    /**
     * 获取房间数
     *
     * @param buildingId
     * @param floorIds
     * @return
     */
    int countRoomSize(@Param("buildingId") String buildingId, @Param("floorIds") List<String> floorIds);
}
