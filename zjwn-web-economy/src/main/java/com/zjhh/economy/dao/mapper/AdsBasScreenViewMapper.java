package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsBasScreenView;
import com.zjhh.economy.vo.cockpit.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 大屏内容 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
public interface AdsBasScreenViewMapper extends CustomerBaseMapper<AdsBasScreenView> {

    /**
     *  经济指标趋势
     * @return
     */
    List<EconomicIndicatorVo> listEconomicIndicator(@Param("preDatekey") String preDatekey,@Param("datekey") String datekey);

    /**
     *  产业结构-税收
     * @return
     */
    List<IndustryStructVo> listIndustryStruct(@Param("datekey") String datekey);

    /**
     * 产业结构-企业数量
     * @return
     */
    List<IndustryEntStructVo> listIndustryEntStruct(@Param("datekey") String datekey);

    /**
     * 企业纳税情况 - 纳税情况
     * @retu
     */
    List<EntTaxPaidVo> listEntTaxPaid(@Param("datekey") String datekey);

    /**
     * 企业纳税情况-top10
     * @return
     */
    List<EntTaxPaidRankVo> listEntTaxPaidRank(@Param("datekey") String datekey);

    /**
     * 楼宇预警
     * @return
     */
    List<BuildingWarningVo> listBuildingWarning(@Param("datekey") String datekey);

    /**
     * 企业注册数
     * @return
     */
    List<EntCountVo> listEntRegisterCount(@Param("preDatekey") String preDatekey,@Param("datekey") String datekey);

    /**
     * 楼宇TOp
     * @return
     */
    List<BuildingRankVo> listBuildingRank(@Param("datekey") String datekey);

    /**
     * 楼宇概览
     * @return
     */
    BuildingSummaryVo getBuildingSummary(@Param("datekey") String datekey);

    /**
     * 已投入使用地图
     * @return
     */
    List<BuildingMapInfoUsedVo> listBuildingMapInfoUsed(@Param("datekey") String datekey);

    /**
     * 未投入使用地图
     * @return
     */
    List<BuildingMapInfoUnusedVo> listBuildingMapInfoUnused(@Param("datekey") String datekey);

    /**
     * 3D
     * @return
     */
    List<BuildingMapInfo3DVo> listBuildingMapInfo3D(@Param("datekey") String datekey);


    List<ShareCockpitVo> listShareCockpit(@Param("datekey") String datekey);

}
