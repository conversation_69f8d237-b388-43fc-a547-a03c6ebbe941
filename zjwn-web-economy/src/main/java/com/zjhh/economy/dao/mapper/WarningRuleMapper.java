package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.WarningRule;
import com.zjhh.economy.request.MonitorRuleReq;
import com.zjhh.economy.vo.MonitorRuleDetailVo;
import com.zjhh.economy.vo.WarningRuleVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 预警规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */

public interface WarningRuleMapper extends CustomerBaseMapper<WarningRule> {

    Page<WarningRuleVo> pageWarningRule(@Param("page") Page<WarningRuleVo> page, @Param("req")MonitorRuleReq req);

    MonitorRuleDetailVo getWarningRuleDetail(@Param("id") String id);



}
