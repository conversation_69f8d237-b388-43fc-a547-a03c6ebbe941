package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.DmPm;
import com.zjhh.economy.request.LabelReq;
import com.zjhh.economy.request.TargetPageReq;
import com.zjhh.economy.vo.DocDateVo;
import com.zjhh.economy.vo.MoveRegisterLabelVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 码表配置项 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface DmPmMapper extends CustomerBaseMapper<DmPm> {

    String getTitleName(@Param("code") String code);

    String getRecursiveTitleName(@Param("code") String code);


    DocDateVo getDate(@Param("req")TargetPageReq req);

    Integer getMaxSort(@Param("type") String type);

    List<LabelReq> listLabel(@Param("type") String type);

    List<TreeSelectVo> listCommunity();

    List<MoveRegisterLabelVo> listMoveRegisterLabel(@Param("moveRegisterId") String moveRegisterId);

    List<String> listMoveRegisterLabelByName(@Param("names") List<String> names);

}
