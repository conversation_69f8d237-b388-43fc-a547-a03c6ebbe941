package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_dms_report_data_set")
public class AdsDmsReportDataSet implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String reportTypeCode;

    /**
     * 业务数据编码
     */
    private String dataTypeCode;

    /**
     * 业务数据名称
     */
    private String dataSetName;

    private Integer version;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
