package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmEnterpriseLabel;
import com.zjhh.economy.vo.EnterpriseLabelVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsPmEnterpriseLabelMapper extends CustomerBaseMapper<AdsPmEnterpriseLabel> {

    List<String> listEnterpriseLabels(@Param("enterpriseId") String enterpriseId);

    List<EnterpriseLabelVo> listEnterpriseLabelsCode(@Param("enterpriseId") String enterpriseId);

    void delLabel(@Param("ids") List<String> ids);




}
