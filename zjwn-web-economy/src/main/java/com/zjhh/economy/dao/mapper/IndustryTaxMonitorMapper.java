package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.IndustryTaxMonitor;
import com.zjhh.economy.request.IndTaxMonitorReq;
import com.zjhh.economy.vo.IndTaxMonitorVo;
import com.zjhh.economy.vo.WarningSummaryVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 产业税收监测 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface IndustryTaxMonitorMapper extends CustomerBaseMapper<IndustryTaxMonitor> {


    Page<IndTaxMonitorVo> pageIndTaxMonitor(@Param("page") Page<IndTaxMonitorVo> page, @Param("req")IndTaxMonitorReq req,@Param("buildingId") String buildingId, @Param("projectId") String projectId);

    WarningSummaryVo getWarningSummaryByInd(@Param("year") String year, @Param("month") String month, @Param("lastYear") String lastYear, @Param("lastMonth") String lastMonth);

}
