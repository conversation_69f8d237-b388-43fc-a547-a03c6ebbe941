package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmBuildingExtend;
import com.zjhh.economy.vo.BuildingExtendVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 楼宇定位 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsPmBuildingExtendMapper extends CustomerBaseMapper<AdsPmBuildingExtend> {

    /**
     * 获取扩展
     *
     * @param buildingId
     * @return
     */
    List<BuildingExtendVo> listBuildingExtend(@Param("buildingId") String buildingId);

    void delBuildingExtendByLabel(@Param("ids") List<String> ids);
}
