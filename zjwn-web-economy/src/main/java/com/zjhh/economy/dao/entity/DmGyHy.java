package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("dm_gy_hy")
public class DmGyHy implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;

    private String name;

    private String parentCode;

    private Integer xh;


}
