package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 码表配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("dm_pm")
public class DmPm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 编码
     */
    private String code;

    /**
     * 类型
     */
    private String name;

    /**
     * 配置项类别：Base-为基础类别，显示哪些配置项
     */
    private String type;

    /**
     * 排序
     */
    private Integer sort;

    private String parentCode;


}
