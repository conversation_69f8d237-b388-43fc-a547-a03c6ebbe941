package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmImportRecord;
import com.zjhh.economy.vo.ImportMoveVo;
import com.zjhh.economy.vo.ImportSettleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsPmImportRecordMapper extends CustomerBaseMapper<AdsPmImportRecord> {

    List<ImportSettleVo> listImportSettle(@Param("importId") String importId);

    List<ImportMoveVo> listImportMove(@Param("importId") String importId);
}
