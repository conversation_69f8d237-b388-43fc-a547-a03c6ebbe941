package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmEnterpriseVisit;
import com.zjhh.economy.request.QueryEnterpriseVisitReq;
import com.zjhh.economy.vo.EnterpriseVisitDetailVo;
import com.zjhh.economy.vo.EnterpriseVisitVo;
import org.apache.ibatis.annotations.Param;

public interface AdsPmEnterpriseVisitMapper extends CustomerBaseMapper<AdsPmEnterpriseVisit> {


    Page<EnterpriseVisitVo> pageEnterpriseVisit(@Param("page")Page<EnterpriseVisitVo> page, @Param("req")QueryEnterpriseVisitReq req);

    EnterpriseVisitDetailVo getEnterpriseVisitDetail(@Param("id") String id);



}
