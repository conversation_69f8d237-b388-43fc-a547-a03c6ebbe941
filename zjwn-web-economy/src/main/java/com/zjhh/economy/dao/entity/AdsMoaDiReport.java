package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_moa_di_report")
public class AdsMoaDiReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 时间参数
     */
    private String datekey;

    /**
     * 报告名称
     */
    private String reportName;

    /**
     * 报告编码
     */
    private String reportTypeCode;

    /**
     * 生成方式(1-自动；2-手动)
     */
    private Integer makeMode;

    /**
     * 附件地址-关联ads_dms_comm
     */
    private String fileGuid;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 责任部门编码
     */
    private String unitCode;

    /**
     * 责任部门名称
     */
    private String unitName;

    /**
     * 预留字段1
     */
    private String hold1;

    /**
     * 预留字段2
     */
    private String hold2;

    /**
     * 预留字段3
     */
    private String hold3;

    /**
     * 预留字段4
     */
    private String hold4;

    /**
     * 预留字段5
     */
    private String hold5;

    /**
     * 预留字段6
     */
    private String hold6;

    /**
     * 预留字段7
     */
    private String hold7;

    /**
     * 预留字段8
     */
    private String hold8;

    /**
     * 预留字段9
     */
    private String hold9;

    /**
     * 预留字段10
     */
    private String hold10;

    /**
     * 显示信息
     */
    private String displayInfo;

    /**
     * 链接模块
     */
    private String linkModule;


}
