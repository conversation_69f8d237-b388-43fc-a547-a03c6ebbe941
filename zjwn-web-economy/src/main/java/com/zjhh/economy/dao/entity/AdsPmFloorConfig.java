package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 楼层平面图配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_floor_config")
public class AdsPmFloorConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 楼层平面图配置
     */
    private String planeConfig;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 楼层id
     */
    private String floorId;


}
