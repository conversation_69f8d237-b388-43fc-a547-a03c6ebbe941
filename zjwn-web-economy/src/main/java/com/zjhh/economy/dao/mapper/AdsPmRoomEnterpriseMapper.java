package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmRoomEnterprise;
import com.zjhh.economy.request.QuerySettleInfoPageReq;
import com.zjhh.economy.request.WyWorkPlatformReq;
import com.zjhh.economy.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 房间企业关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
public interface AdsPmRoomEnterpriseMapper extends CustomerBaseMapper<AdsPmRoomEnterprise> {

    List<SettleInfoListVo> pageSettleInfo(@Param("req") QuerySettleInfoPageReq req);

    List<SettleInfoMenuVo> listSettleInfo(@Param("enterpriseId") String enterpriseId, @Param("ids") List<String> ids);

    List<SettleInfoShowVo> listSettleInfoShow(@Param("enterpriseId") String enterpriseId);

    List<SettleInfoShowVo> listMoveOutInfoShow(@Param("enterpriseId") String enterpriseId);

    LocalDate getStartCheckInDate(@Param("enterpriseId") String enterpriseId);

    SettleInfoAnalyseVo getRentSummary(@Param("enterpriseId") String enterpriseId);

    List<SettleTrendVo> listSettleTrendVo(@Param("enterpriseId") String enterpriseId);

    BigDecimal calculateSize(@Param("enterpriseId") String enterpriseId);

    Integer calculateRoom(@Param("enterpriseId") String enterpriseId);

    SettleInfoDetailVo getSettleDetailInfo(@Param("id") String id);

    WorkPlatformRoomVo getWorkPlatformRoom();

    WorkPlatformRoomVo getWorkPlatformRoomByAuth(@Param("req")WyWorkPlatformReq req);

    WorkPlatformEntManageVo getWorkPlatformEntManage(@Param("currentYearMonth") String currentYearMonth);

    WorkPlatformEntManageVo getWorkPlatformEntManageByAuth(@Param("currentYearMonth") String currentYearMonth,@Param("req")WyWorkPlatformReq req);


    List<Map<String, Object>> listEntMoveLessThanThirsty(@Param("currentDate") String currentDate);
    
    List<Map<String, Object>> listEntConfirmMove(@Param("days") Integer days, @Param("currentDate") String currentDate);

    List<Map<String, Object>> listEmptyRoomMoreThanSixty(@Param("currentDate") String currentDate);

    List<Map<String, Object>> listNoInputTax(@Param("lastYearMonth") String lastYearMonth);

    Integer getBriefRegisterByTotalCount(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Integer getBriefRegisterBySettledCount(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Integer getBriefRegisterBySettleRoomCount(@Param("startDate") String startDate, @Param("endDate") String endDate);

    BigDecimal getBriefRegisterBySettleArea(@Param("startDate") String startDate, @Param("endDate") String endDate);


    Integer getBriefRegisterByMoveTotalCount(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Integer getBriefRegisterByLocalCount(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Integer getBriefRegisterByNotLocalCount(@Param("startDate") String startDate, @Param("endDate") String endDate);


    RoomSettleInfoDetailVo getSettleDetailInfoByRoomDetail(@Param("enterpriseId") String enterpriseId, @Param("roomId") String roomId);


    List<Map<String, Object>> listEntMoveLessThanNinety(@Param("currentDate") String currentDate);

    List<Map<String, Object>> listEntMoveLessThanSixty(@Param("currentDate") String currentDate);


    EntCompareSettledInfoVo getCompareSettledInfo(@Param("enterpriseId") String enterpriseId);

    EntCompareMoveOutInfoVo getCompareMoveOutInfo(@Param("enterpriseId") String enterpriseId);

    Boolean checkEnterpriseIsMoved(@Param("enterpriseId") String enterpriseId);

    LocalDate getMovedDate(@Param("enterpriseId") String enterpriseId);
}
