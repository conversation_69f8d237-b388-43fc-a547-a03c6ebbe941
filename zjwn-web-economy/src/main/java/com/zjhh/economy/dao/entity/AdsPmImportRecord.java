package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_import_record")
public class AdsPmImportRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;


    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 统一社会信用代码
     */
    private String uscc;

    /**
     * 法定代表人
     */
    private String legalPerson;

    /**
     * 电话
     */
    private String phone;

    /**
     * 企业联系人
     */
    private String entContactPerson;

    /**
     * 企业电话
     */
    private String entPhone;

    /**
     * 项目
     */
    private String projectName;

    /**
     * 楼宇
     */
    private String buildingName;

    /**
     * 楼层
     */
    private String floorNo;

    /**
     * 房号
     */
    private String roomNo;

    /**
     * 入驻开始日期
     */
    private LocalDate checkInDate;

    /**
     * 租赁到期日
     */
    private LocalDate expectMoveOutDate;

    /**
     * 入驻面积
     */
    private String area;

    /**
     * 装修开始日期
     */
    private LocalDate renovationStartDate;

    /**
     * 装修结束日期
     */
    private LocalDate renovationEndDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 实际搬离日期
     */
    private LocalDate realityMoveOutDate;

    /**
     * 去向登记
     */
    private String destination;

    /**
     * 迁出原因
     */
    private String moveOutReason;

    /**
     * 匹配结果
     */
    private String matchResult;

    /**
     * 导入id
     */
    private String importId;

}
