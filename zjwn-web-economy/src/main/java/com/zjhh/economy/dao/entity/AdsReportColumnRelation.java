package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_report_column_relation")
public class AdsReportColumnRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 字段ID
     */
    private String fieldId;

    /**
     * 存储类型
     */
    private Integer type;

    private String userCode;


    private Integer sort;


    private Integer reportType;






}
