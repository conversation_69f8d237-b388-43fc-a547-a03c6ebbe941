package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("work_platform_warning")
public class WorkPlatformWarning implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private Integer warningType;

    private Integer handleType;

    private LocalDate remindDate;

    private String remindDesc;

    private String businessId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
