package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 产业税收
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_industry_tax_monitor")
public class IndustryTaxMonitor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String rq;

    private String buildingId;

    private String industryCode;

    private BigDecimal bqAmt;

    private BigDecimal sqAmt;

    private BigDecimal sqljAmt;

    private BigDecimal bqljAmt;

    private Integer type;

    private LocalDateTime createTime;


}
