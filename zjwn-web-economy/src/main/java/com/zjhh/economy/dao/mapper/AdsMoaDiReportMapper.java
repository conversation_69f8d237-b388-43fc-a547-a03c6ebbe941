package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsMoaDiReport;
import com.zjhh.economy.request.report.WarningReportPageReq;
import com.zjhh.economy.vo.report.WarningReportListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 监督预警分析报告 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-19
 */
public interface AdsMoaDiReportMapper extends CustomerBaseMapper<AdsMoaDiReport> {


    List<String> listDiReportFileDir(@Param("reportTypeCodes") List<String> reportTypeCodes, @Param("datekey") String datekey);

    Page<WarningReportListVo> pageWarningReportList(@Param("page") Page<WarningReportListVo> page, @Param("req")
    WarningReportPageReq req);
}

