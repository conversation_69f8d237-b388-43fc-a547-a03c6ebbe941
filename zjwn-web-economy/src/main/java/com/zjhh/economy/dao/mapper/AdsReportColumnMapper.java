package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsReportColumn;
import com.zjhh.economy.vo.ReportColumnVo;
import com.zjhh.economy.vo.ReportSummaryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsReportColumnMapper extends CustomerBaseMapper<AdsReportColumn> {


    List<ReportSummaryVo> listReportSummaryColumn(@Param("userCode") String userCode,@Param("type") String type,@Param("queryType") Integer queryType);

    List<ReportColumnVo> listSelectedColumnByArchive(@Param("userCode") String userCode, @Param("type") Integer type) ;

    List<ReportColumnVo> listSelectedColumnByCondition(@Param("userCode") String userCode, @Param("type") Integer type) ;

    List<ReportColumnVo> listAllColumn(@Param("type") Integer type);

}
