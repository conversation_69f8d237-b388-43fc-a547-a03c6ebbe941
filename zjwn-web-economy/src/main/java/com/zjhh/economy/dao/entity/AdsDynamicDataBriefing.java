package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_dynamic_data_briefing")
public class AdsDynamicDataBriefing implements Serializable {
    private static final long serialVersionUID = -6479188741160971319L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String briefingDesc;

    private Integer period;

    private LocalDate generationDate;
}
