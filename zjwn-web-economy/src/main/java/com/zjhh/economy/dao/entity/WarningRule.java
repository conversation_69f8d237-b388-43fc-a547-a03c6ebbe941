package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_warning_rule")
public class WarningRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 监测周期
     */
    private Integer monitorRulePeriod;

    /**
     * 同比环比
     */
    private Integer monitorRuleCompare;

    /**
     * 上升下架
     */
    private Integer monitorRuleChange;


    private BigDecimal monitorRuleZf;

    /**
     * 启用
     */
    private Boolean enabled;

    /**
     * 启用日期
     */
    private LocalDate enabledDate;

    /**
     * 监控type
     */
    private Integer monitorRuleType;


    private LocalDateTime createTime;


}
