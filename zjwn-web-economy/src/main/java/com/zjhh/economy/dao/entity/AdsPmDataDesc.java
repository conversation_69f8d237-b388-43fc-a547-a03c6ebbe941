package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 预警规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_data_desc")
public class AdsPmDataDesc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 说明code
     */
    private String code;
    /**
     * 说明中文
     */
    private String codeName;

    /**
     *  说明描述
     */
    private String dataDesc;


}
