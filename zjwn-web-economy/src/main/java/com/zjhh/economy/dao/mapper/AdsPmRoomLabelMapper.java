package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmRoomLabel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsPmRoomLabelMapper extends CustomerBaseMapper<AdsPmRoomLabel> {

    /**
     * 获取房间标签
     *
     * @param roomId
     * @return
     */
    List<SingleSelectVo> listRoomLabels(@Param("roomId") String roomId);

    void delRoomLabel(@Param("ids") List<String> ids);

}
