package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmRoom;
import com.zjhh.economy.request.PageRoomReq;
import com.zjhh.economy.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 房源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsPmRoomMapper extends CustomerBaseMapper<AdsPmRoom> {

    /**
     * 楼宇编号
     *
     * @param buildingId
     * @return
     */
    List<RoomStateFloorVo> listFloor(@Param("buildingId") String buildingId);

    /**
     * 房源列表分页
     *
     * @param req
     * @return
     */
    Page<RoomVo> page(Page<RoomVo> page, @Param("req") PageRoomReq req);

    /**
     * 根据楼层获取添加房间信息
     *
     * @param floorId
     * @return
     */
    AddRoomVo getAddRoom(@Param("floorId") String floorId);

    /**
     * 获取房间详情
     *
     * @param roomId
     * @return
     */
    RoomDetailVo getDetail(@Param("roomId") String roomId);

    /**
     * 获取楼宇标签名称
     *
     * @param roomId
     * @return
     */
    List<String> listRoomLabel(@Param("roomId") String roomId);

    /**
     * 获取房间历史入驻信息
     *
     * @param roomId
     * @return
     */
    List<RoomDetailEnterpriseHistoryVo> listEnterpriseHistory(@Param("roomId") String roomId);

    /**
     * 获取房间入驻企业信息
     *
     * @param roomId
     * @return
     */
    List<RoomDetailEnterpriseVo> listEnterprise(@Param("roomId") String roomId);

    /**
     * 获取编辑详情
     *
     * @param roomId
     * @return
     */
    RoomUpdateDetailVo getUpdateDetail(@Param("roomId") String roomId);

    List<SettleRoomMenuVo> listRoom(@Param("floorId") String floorId);

    /**
     * 通过房间id获取房间信息
     *
     * @param roomId
     * @return
     */
    FloorRoomInfoVo getRoomInfo(@Param("roomId") String roomId);

    /**
     * 通过楼层id获取房间信息列表
     *
     * @param floorId
     * @return
     */
    List<FloorRoomInfoVo> listRoomInfo(@Param("floorId") String floorId);

    ImportRoomVo getImportRoom(@Param("projectName") String projectName, @Param("buildingName") String buildingName, @Param("floorNo") String floorNo, @Param("roomNo") String roomNo);

}
