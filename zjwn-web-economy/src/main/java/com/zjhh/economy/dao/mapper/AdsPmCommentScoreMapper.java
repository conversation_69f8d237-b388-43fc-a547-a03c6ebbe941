package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmCommentScore;
import com.zjhh.economy.request.BuildingCommentDetailReq;
import com.zjhh.economy.request.BuildingCommentReq;
import com.zjhh.economy.vo.BuildingCommentDetailVo;
import com.zjhh.economy.vo.BuildingCommentVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsPmCommentScoreMapper extends CustomerBaseMapper<AdsPmCommentScore> {


    List<BuildingCommentDetailVo> pageBuildingCommentDetail( @Param("req")BuildingCommentDetailReq req);

    Page<BuildingCommentVo> pageBuildingCommentVo(@Param("page")Page<BuildingCommentVo> page, @Param("req") BuildingCommentReq req);


}
