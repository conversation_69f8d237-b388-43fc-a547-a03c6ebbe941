package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 房屋权属人表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_room_ownership")
public class AdsPmRoomOwnership implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String roomId;

    /**
     * 权属人
     */
    @Schema(description = "权属人")
    private String person;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String phone;


}
