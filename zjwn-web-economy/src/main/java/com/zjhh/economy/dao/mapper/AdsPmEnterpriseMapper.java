package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmEnterprise;
import com.zjhh.economy.request.EntKeywordSearchReq;
import com.zjhh.economy.request.QueryEnterprisePageReq;
import com.zjhh.economy.request.analyzereport.EntSearchReq;
import com.zjhh.economy.vo.EnterpriseDetailVo;
import com.zjhh.economy.vo.EnterpriseListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 企业信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
public interface AdsPmEnterpriseMapper extends CustomerBaseMapper<AdsPmEnterprise> {

    String getMaxEntSerialNo(@Param("date") String date);

    EnterpriseDetailVo getEnterpriseDetail(@Param("enterpriseId") String enterpriseId);

    Page<EnterpriseListVo> pageEnterpriseList(@Param("page") Page<EnterpriseListVo> page, @Param("req")QueryEnterprisePageReq req);

    Boolean checkEntExists(@Param("enterpriseName") String enterpriseName,@Param("oldName") String oldName,@Param("id") String id );

    List<TreeSelectVo> listSearchEnt(@Param("req")EntKeywordSearchReq req);

    Integer getBriefEntUpdateCount(@Param("startDate") String startDate, @Param("endDate") String endDate);


    List<TreeSelectVo> listMenuEnt(@Param("req")EntSearchReq req);

    List<TreeSelectVo> listMenuMobileEnt(@Param("req")EntKeywordSearchReq req);



}
