package com.zjhh.economy.dao.mapper;


import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmEconomicScore;
import com.zjhh.economy.vo.cockpit.EconomicIndicatorScoreVo;
import org.apache.ibatis.annotations.Param;

public interface AdsPmEconomicScoreMapper extends CustomerBaseMapper<AdsPmEconomicScore> {


    EconomicIndicatorScoreVo getEconomicIndicatorScore(@Param("buildingId") String buildingId,@Param("queryDate") String queryDate);


}
