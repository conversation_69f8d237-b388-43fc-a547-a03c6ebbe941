package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.WorkPlatformWarning;
import com.zjhh.economy.request.WarningPageReq;
import com.zjhh.economy.request.WyWorkPlatformPageReq;
import com.zjhh.economy.request.WyWorkPlatformReq;
import com.zjhh.economy.vo.WarningRemindListVo;
import com.zjhh.economy.vo.WorkPlatformWarningRemindVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkPlatformWarningMapper extends CustomerBaseMapper<WorkPlatformWarning> {

    WorkPlatformWarning checkWorkPlatformWarning(@Param("businessId") String businessId,@Param("warningType") Integer warningType);

    List<WorkPlatformWarningRemindVo> listWorkPlatformWarningRemind();

    List<WorkPlatformWarningRemindVo> listWorkPlatformWarningRemindByAuth(@Param("req") WyWorkPlatformReq req);

    Page<WarningRemindListVo> pageWarningRemindList(@Param("page") Page<WarningRemindListVo> page, @Param("req")WarningPageReq req);
    Page<WarningRemindListVo> pageWarningRemindListByAuth(@Param("page") Page<WarningRemindListVo> page, @Param("req")WyWorkPlatformPageReq req);


}
