package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警处理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_handle_warning")
public class HandleWarning implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 处理类型
     */
    private Integer handleType;

    /**
     * 处理描述
     */
    private String handleDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 预警ID
     */
    private String warningId;


}
