package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 企业税收
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_ent_tax_monitor")
public class EntTaxMonitor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 监测预警
     */
    private String warningMonitor;

    /**
     * 企业ID
     */
    private String ent_id;

    /**
     * 预警详情
     */
    private String warningDetail;


    /**
     * 预警日期
     */
    private LocalDate warningDate;

    /**
     * 预警规则ID
     */
    private String warningRuleId;


    /**
     * 本期金额
     */
    private BigDecimal bqAmt;


    /**
     * 上期金额
     */
    private BigDecimal sqAmt;


    /**
     * 本期累计金额
     */
    private BigDecimal bqljAmt;


    /**
     * 上期累计金额
     */
    private BigDecimal sqljAmt;


    /**
     * 同比增幅
     */
    private BigDecimal tbZf;


    /**
     * 环比增幅
     */
    private BigDecimal hbZf;


}
