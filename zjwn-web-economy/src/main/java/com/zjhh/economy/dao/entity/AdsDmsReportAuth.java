package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_dms_report_auth")
public class AdsDmsReportAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String reportTypeCode;

    /**
     * 行政区划编码
     */
    private String orgCode;


}
