package com.zjhh.economy.dao.mapper;

import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsBusinessDocument;
import com.zjhh.economy.vo.DocumentVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsBusinessDocumentMapper extends CustomerBaseMapper<AdsBusinessDocument> {

    List<String> listDocument(@Param("businessId") String businessId, @Param("documentType") String documentType);

    DocumentVo getMobileContactPerson();





}
