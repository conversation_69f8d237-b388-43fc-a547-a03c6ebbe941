package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmImport;
import com.zjhh.economy.request.ImportExternalDataListReq;
import com.zjhh.economy.vo.ImportExternalDataVo;
import org.apache.ibatis.annotations.Param;

public interface AdsPmImportMapper extends CustomerBaseMapper<AdsPmImport> {

    Page<ImportExternalDataVo> pageImportExternalData(Page<ImportExternalDataVo> page, @Param("req") ImportExternalDataListReq req);


}
