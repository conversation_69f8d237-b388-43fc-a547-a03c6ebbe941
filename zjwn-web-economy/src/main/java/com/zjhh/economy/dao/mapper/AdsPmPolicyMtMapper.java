package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmPolicyMt;
import com.zjhh.economy.request.policymanagement.PolicyManagementPageReq;
import com.zjhh.economy.vo.policymanagement.PolicyManagementCashVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 政策管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-19
 */
public interface AdsPmPolicyMtMapper extends CustomerBaseMapper<AdsPmPolicyMt> {

    /**
     * 政策管理详情
     *
     * @param policyId
     * @return
     */
    PolicyManagementVo getPolicyManagement(@Param("policyId") String policyId);

    /**
     * 查询政策管理
     *
     * @param page
     * @param req
     * @return
     */
    Page<PolicyManagementPageVo> pagePolicyManagement(@Param("page") Page<PolicyManagementPageVo> page, @Param("req") PolicyManagementPageReq req);

    List<PolicyManagementPageVo> listPolicyManagementByWorkPlatform();

    List<PolicyManagementCashVo> listPolicyManagementCash(@Param("policyId") String policyId);

    List<PolicyManagementPageVo> listPolicyByCockpit();
}
