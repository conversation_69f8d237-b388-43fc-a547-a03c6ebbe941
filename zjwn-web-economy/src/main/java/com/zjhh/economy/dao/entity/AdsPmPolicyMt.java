package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_policy_mt")
public class AdsPmPolicyMt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 政策名称
     */
    private String policyName;

    /**
     * 文号
     */
    private String policyNo;

    /**
     * 发布机构
     */
    private String publishAgency;

    /**
     * 发布日期
     */
    private LocalDate publishDate;

    /**
     * 成文日期
     */
    private LocalDate writtenDate;

    /**
     * 政策详情
     */
    private String policyDetail;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private String updateUser;


}
