package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.OperationLogPageReq;
import com.zjhh.economy.vo.operationlog.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 各模块变更操作前后，查询相关信息
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
public interface ChangeInfoMapper {

    /**
     * 获取文件名
     *
     * @param bizId
     * @param docType
     * @return
     */
    List<String> listDocTitles(@Param("bizId") String bizId, @Param("docType") String docType);

    /**
     * 根据type和code获取dm_pm表名称
     *
     * @param type
     * @param code
     * @return
     */
    String getDmPmName(@Param("type") String type, @Param("code") String code);

    /**
     * 操作日志分页查询
     *
     * @param page
     * @param req
     * @return
     */
    Page<OperationLogVo> pageOperationLog(@Param("page") Page<OperationLogVo> page, @Param("req") OperationLogPageReq req);

    /**
     * 项目详情
     */
    ProjectDetailForLog getProjectDetailForLog(@Param("projectId") String projectId);

    /**
     * 楼宇详情
     *
     * @param buildingId
     * @return
     */
    BuildingDetailForLog getBuildingDetailForLog(@Param("buildingId") String buildingId);

    /**
     * 企业诉求详情
     *
     * @param demandId
     * @return
     */
    DemandDetailForLog getDemandDetailForLog(@Param("demandId") String demandId);

    /**
     * 企业诉求处理结果
     *
     * @param handleId
     * @return
     */
    DemandHandleForLog getDemandHandleForLog(@Param("handleId") String handleId);

    /**
     * 房间信息
     *
     * @param roomId
     * @return
     */
    RoomDetailForLog getRoomDetailForLog(@Param("roomId") String roomId);

    /**
     * 入驻信息
     *
     * @param settleId
     * @return
     */
    RoomSettleInfoForLog getRoomSettleInfoForLog(@Param("settleId") String settleId);

    /**
     * 楼层列表
     *
     * @param buildingId
     * @return
     */
    List<FloorListForLog> listFloorListForLog(@Param("buildingId") String buildingId);
}
