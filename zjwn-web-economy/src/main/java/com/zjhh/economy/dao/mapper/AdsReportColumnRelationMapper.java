package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.ColumnVO;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsReportColumnRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsReportColumnRelationMapper extends CustomerBaseMapper<AdsReportColumnRelation> {

    List<ColumnVO> listExportColumn(@Param("reportType") Integer reportType, @Param("userCode") String userCode);

}
