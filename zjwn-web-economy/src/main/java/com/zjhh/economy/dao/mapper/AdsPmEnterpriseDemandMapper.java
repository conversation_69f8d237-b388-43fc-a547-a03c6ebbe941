package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmEnterpriseDemand;
import com.zjhh.economy.request.DatekeyReq;
import com.zjhh.economy.request.EnterpriseDemandReq;
import com.zjhh.economy.request.WorkPlatformEnterpriseDemandReq;
import com.zjhh.economy.request.WyWorkPlatformReq;
import com.zjhh.economy.vo.EnterpriseDemandDetailVo;
import com.zjhh.economy.vo.EnterpriseDemandVo;
import com.zjhh.economy.vo.WorkPlatformDemandVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AdsPmEnterpriseDemandMapper extends CustomerBaseMapper<AdsPmEnterpriseDemand> {

    Page<EnterpriseDemandVo> pageEnterpriseDemand(@Param("page") Page<EnterpriseDemandVo> page, @Param("req")EnterpriseDemandReq req);

    EnterpriseDemandDetailVo getEnterpriseDemandDetail(@Param("id") String id);

    WorkPlatformDemandVo getWorkPlatformDemand(@Param("req")DatekeyReq req);

    WorkPlatformDemandVo getWorkPlatformDemandByAuth(@Param("req") WyWorkPlatformReq req);

    List<EnterpriseDemandVo> listEnterpriseDemandByAuth(@Param("req") WorkPlatformEnterpriseDemandReq req);

    Map<String,Object> getDemandBrief(@Param("startDate") String startDate, @Param("endDate") String endDate);



}
