package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业入驻历史信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_enterprise_settle")
public class AdsPmEnterpriseSettle implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String enterpriseId;

    private String roomId;

    /**
     * 入驻面积
     */
    private BigDecimal area;


    private LocalDateTime createTime;

    private LocalDate checkInDate;

    private LocalDate realityMoveOutDate;


}
