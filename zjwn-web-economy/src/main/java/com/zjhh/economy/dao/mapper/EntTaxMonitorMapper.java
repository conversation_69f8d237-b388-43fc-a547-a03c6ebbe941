package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.EntTaxMonitor;
import com.zjhh.economy.request.EntTaxMonitorReq;
import com.zjhh.economy.vo.EntTaxMonitorVo;
import com.zjhh.economy.vo.WarningSummaryVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 企业税收监测 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface EntTaxMonitorMapper extends CustomerBaseMapper<EntTaxMonitor> {


    Page<EntTaxMonitorVo> pageEntTaxMonitor(@Param("page") Page<EntTaxMonitorVo> page, @Param("req") EntTaxMonitorReq req,@Param("buildingId") String buildingId, @Param("projectId") String projectId);

    WarningSummaryVo getWarningSummaryByEnt(@Param("year") String year, @Param("month") String month, @Param("lastYear") String lastYear, @Param("lastMonth") String lastMonth);


}
