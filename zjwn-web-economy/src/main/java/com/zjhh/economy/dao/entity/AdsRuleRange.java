package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_rule_range")
public class AdsRuleRange implements Serializable {
    /**
     * 唯一主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 建筑ID
     */
    private String buildingId;

    /**
     * rule
     */
    private String ruleId;


}
