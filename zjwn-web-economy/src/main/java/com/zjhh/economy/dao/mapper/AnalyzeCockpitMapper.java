package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.economy.request.MobileCompareReq;
import com.zjhh.economy.request.analyzecockpit.*;
import com.zjhh.economy.vo.MobileCompareVo;
import com.zjhh.economy.vo.analyzecockpit.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface AnalyzeCockpitMapper {


    /**
     * @param req
     * @return
     */
    AreaAnalyzeVo getAreaAnalyze(@Param("req") SpaceAnalyzeReq req);

    List<SettledTrendVo> listSettledTrendByMonth(@Param("req") AnalyzeDateReq req);

    List<SettledTrendVo> listSettledTrendByQuarter(@Param("req") AnalyzeDateReq req);

    List<SettledTrendVo> listSettledTrendByYear(@Param("req") AnalyzeDateReq req);


    TaxAnalyzeVo getTaxAnalyze(@Param("req") SpaceAnalyzeReq req);


    List<TaxTrendVo> listTaxTrendByMonth(@Param("req") AnalyzeDateReq req);

    List<TaxTrendVo> listTaxTrendByQuarter(@Param("req") AnalyzeDateReq req);

    List<TaxTrendVo> listTaxTrendByYear(@Param("req") AnalyzeDateReq req);

    SettledEntAnalyzeVo getSettledEntAnalyze(@Param("req") SpaceAnalyzeReq req);

    List<SettledEntUnitTrendVo> listSettledEntUnitTrendByMonth(@Param("req") AnalyzeDateReq req);

    List<SettledEntUnitTrendVo> listSettledEntUnitTrendByQuarter(@Param("req") AnalyzeDateReq req);

    List<SettledEntUnitTrendVo> listSettledEntUnitTrendByYear(@Param("req") AnalyzeDateReq req);

    List<IndustryFocusAnalyzeVo> listIndustryFocusAnalyzeTaxRank(@Param("req") IndustryAnalyzeReq req);

    /**
     * TODO
     *
     * @param req
     * @return
     */
    List<IndustryFocusAnalyzeVo> listIndustryFocusAnalyzeEntCountRank(@Param("req") IndustryAnalyzeReq req);

    /**
     * TODO
     *
     * @param req
     * @return
     */
    List<IndustryFocusAnalyzeVo> listIndustryFocusAnalyzeTaxSettledAreaRank(@Param("req") IndustryAnalyzeReq req);


    CockpitProjectSummaryVo getCockpitProjectSummary(@Param("req") AnalyzeCockpitCommonReq req);


    CockpitBuildingSummaryVo getCockpitBuildingSummary(@Param("req") AnalyzeCockpitCommonReq req);


    /**
     * @param req
     * @return
     */
    SpaceAnalyzeSummaryVo getSpaceAnalyzeSummary(@Param("req") AnalyzeCockpitCommonReq req);

    /**
     * @param req
     * @return
     */
    List<SpaceResourceVo> listSpaceResourceByCommunity(@Param("req") AnalyzeCockpitCommonReq req);

    /**
     * @param req
     * @return
     */
    List<SpaceResourceVo> listSpaceResourceByBuilding(@Param("req") AnalyzeCockpitCommonReq req);

    /**
     * @param req
     * @return
     */
    List<SpaceResourceVo> listSpaceResourceByProject(@Param("req") AnalyzeCockpitCommonReq req);

    List<RoomResourceVo> listRoomResource(@Param("req") AnalyzeCockpitCommonReq req);

    List<RoomResourceVo> listEntRentRoomResource(@Param("req") AnalyzeCockpitCommonReq req);

    //TODO 查询待优化
    List<RoomResourceVo> listEmptyRoomResource(@Param("req") AnalyzeCockpitCommonReq req);

    List<RoomPeriodVo> listEmptyPeriod(@Param("req") AnalyzeCockpitCommonReq req);

    Integer getEmptyPeriodCount(@Param("req") AnalyzeCockpitCommonReq req);

    List<FloorEmptyAreaRankVo> listFloorEmptyAreaRank(@Param("req") AnalyzeCockpitCommonReq req);


    List<IndustryFocusAnalyzeVo> listIndustryFocusAnalyze(@Param("req") IndustryAnalyzeReq req);

    EntSettledSummaryVo getEntSettledSummary(@Param("req") AnalyzeCockpitCommonReq req);

    List<EntTaxRankVo> listEntTaxRank(@Param("req") AnalyzeCockpitCommonReq req);

    List<EntSettledAreaVo> listEntSettledAreaRank(@Param("req") AnalyzeCockpitCommonReq req);


    List<EntStableRankVo> listEntStableRank(@Param("req") AnalyzeCockpitCommonReq req);

    List<EntLocaledDisVo> listEntLocaledDis(@Param("req") AnalyzeCockpitCommonReq req);

    List<UnitPropertyDisVo> listUnitPropertyDis(@Param("req") AnalyzeCockpitCommonReq req);

    List<EntTaxIncomeVo> listEntTaxIncome(@Param("req") AnalyzeCockpitCommonReq req);

    List<RegisterBusinessSameVo> listRegisterBusinessSame(@Param("req") AnalyzeCockpitCommonReq req);

    List<RegisterTypeVo> listRegisterType(@Param("req") AnalyzeCockpitCommonReq req);

    EconomicSummaryVo getEconomicSummary(@Param("req") AnalyzeCockpitCommonReq req);

    TaxIncomeReasonVo getTaxIncomeReasonByTax(@Param("req") AnalyzeCockpitCommonReq req);

    BigDecimal getTaxIncomeReasonByEntTax(@Param("req") AnalyzeCockpitCommonReq req);

    BigDecimal getTaxIncomeReasonByMove(@Param("req") AnalyzeCockpitCommonReq req);

    List<TaxTrendVo> listStreetTaxTrendByMonth(@Param("req") AnalyzeDateReq req);

    List<TaxTrendVo> listStreetTaxTrendByQuarter(@Param("req") AnalyzeDateReq req);

    List<TaxTrendVo> listStreetTaxTrendByYear(@Param("req") AnalyzeDateReq req);


    List<UnitIncomeTrendVo> listUnitIncomeTrendByMonth(@Param("req") AnalyzeDateReq req);

    List<UnitIncomeTrendVo> listUnitIncomeByQuarter(@Param("req") AnalyzeDateReq req);

    List<UnitIncomeTrendVo> listUnitIncomeTrendByYear(@Param("req") AnalyzeDateReq req);

    List<IndustryTaxTrendVo> listIndustryTaxTrendByMonth(@Param("req") IndustryTrendReq req);

    List<IndustryContributeVo> listIndustryEntCountTrendByMonth(@Param("req") IndustryTrendReq req);

    List<IndustrySettledAreaVo> listIndustrySettledAreaTrendByMonth(@Param("req") IndustryTrendReq req);

    List<LocaledRegisterTrendVo> listLocaledRegisterTrendByMonth(@Param("req") AnalyzeDateReq req);

    List<LocaledRegisterTrendVo> listLocaledRegisterTrendByQuarter(@Param("req") AnalyzeDateReq req);

    List<LocaledRegisterTrendVo> listLocaledRegisterTrendByYear(@Param("req") AnalyzeDateReq req);

    List<TaxCategoryVo> listTaxCategoryBySs(@Param("req") EconomicTaxTrendReq req);

    List<TaxCategoryVo> listTaxCategoryByStreet(@Param("req") EconomicTaxTrendReq req);

    List<TreeSelectVo> listIndustryTop5(@Param("req") IndustryTrendReq req);

    List<AppEconomicVo> listAppEconomicByCommunity(@Param("req") AppEconomicReq req);

    List<AppEconomicVo> listAppEconomicByProject(@Param("req") AppEconomicReq req);

    List<AppEconomicVo> listAppEconomicByBuilding(@Param("req") AppEconomicReq req);

    BigDecimal getTaxByBuilding(@Param("buildingId") String buildingId, @Param("queryDate") String queryDate);

    BigDecimal getTop3IndustryTaxByBuilding(@Param("buildingId") String buildingId, @Param("queryDate") String queryDate);

    BigDecimal getSettledRateByBuilding(@Param("buildingId") String buildingId, @Param("queryDate") String queryDate);

    BigDecimal getLocalRateByBuilding(@Param("buildingId") String buildingId, @Param("queryDate") String queryDate);

    BigDecimal getUnitOutputByBuilding(@Param("buildingId") String buildingId, @Param("queryDate") String queryDate);

    BigDecimal getTaxRateByBuilding(@Param("buildingId") String buildingId, @Param("queryDate") String queryDate, @Param("lastQueryDate") String lastQueryDate);


    Map<String, BigDecimal> getTaxByMaxMin(@Param("queryDate") String queryDate);

    Map<String, BigDecimal> getTop3IndustryTaxByMaxMin(@Param("queryDate") String queryDate);

    Map<String, BigDecimal> getSettledRateByMaxMin(@Param("queryDate") String queryDate);

    Map<String, BigDecimal> getLocalRateByMaxMin(@Param("queryDate") String queryDate);

    Map<String, BigDecimal> getUnitOutputByMaxMin(@Param("queryDate") String queryDate);

    Map<String, BigDecimal> getTaxRateByMaxMin(@Param("queryDate") String queryDate, @Param("lastQueryDate") String lastQueryDate);

    List<String> listBuildingIds();

    String getTaxMaxDate();

    MobileCompareVo getMobileCompare(@Param("req") MobileCompareReq req);


}
