package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 分析报告页面
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_dms_report_page")
public class AdsDmsReportPage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String reportTypeCode;

    private String pageJson;

    private Integer version;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
