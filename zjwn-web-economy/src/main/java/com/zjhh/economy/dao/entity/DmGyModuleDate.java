package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 模块对应日期设置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("dm_gy_module_date")
public class DmGyModuleDate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 目标界面
     */
    private String targetPage;

    /**
     * 模块描述
     */
    private String moduleDesc;

    /**
     * 更新频率类型代码,区分大小写(年：Year；季度：Quarter；月：Month；日：Day)
     */
    private String updateType;

    /**
     * 更新频率(年、季、月、日)
     */
    private String updateDesc;

    /**
     * 最小日期(yyyymmdd格式)
     */
    private String minDate;

    /**
     * 最大日期(yyyymmdd格式)
     */
    private String maxDate;


}
