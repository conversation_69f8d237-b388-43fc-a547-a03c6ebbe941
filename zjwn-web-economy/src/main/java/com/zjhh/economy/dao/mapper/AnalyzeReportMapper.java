package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.analyzereport.*;
import com.zjhh.economy.request.report.BuildingSettleEntBasicReq;
import com.zjhh.economy.request.report.EntBusinessInfoReq;
import com.zjhh.economy.request.report.ReportDateReq;
import com.zjhh.economy.vo.analyzereport.*;
import com.zjhh.economy.vo.report.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AnalyzeReportMapper {


    List<SpaceResourceAnalyzeVo> listSpaceResourceAnalyzeByCommunity(@Param("req") SpaceResourceAnalyzeReq req);

    List<SpaceResourceAnalyzeVo> listSpaceResourceAnalyzeByProject(@Param("req") SpaceResourceAnalyzeReq req);

    List<SpaceResourceAnalyzeVo> listSpaceResourceAnalyzeByBuilding(@Param("req") SpaceResourceAnalyzeReq req);


    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByCommunity(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByProject(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByBuilding(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByCommunityByQuarter(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByProjectByQuarter(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByBuildingByQuarter(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByCommunityByYear(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByProjectByYear(@Param("req") SpaceResourceDimensionReq req);

    List<SpaceResourceDimensionVo> listSpaceResourceDimensionByBuildingByYear(@Param("req") SpaceResourceDimensionReq req);


    List<SettledAreaTrendTableVo> listSettledAreaTrendTableByCommunity(@Param("req") SettledAreaTrendTableReq req);

    List<SettledAreaTrendTableVo> listSettledAreaTrendTableByProject(@Param("req") SettledAreaTrendTableReq req);

    List<SettledAreaTrendTableVo> listSettledAreaTrendTableByBuilding(@Param("req") SettledAreaTrendTableReq req);


    List<EmptyRoomCompareVo> listEmptyRoomCompareByCommunity(@Param("req") SpaceResourceAnalyzeReq req);

    List<EmptyRoomCompareVo> listEmptyRoomCompareByProject(@Param("req") SpaceResourceAnalyzeReq req);

    List<EmptyRoomCompareVo> listEmptyRoomCompareByBuilding(@Param("req") SpaceResourceAnalyzeReq req);


    List<EmptyRoomDetailTableVo> listEmptyRoomDetailTableByCommunity(@Param("req") EmptyRoomTableReq req);

    List<EmptyRoomDetailTableVo> listEmptyRoomDetailTableByProject(@Param("req") EmptyRoomTableReq req);

    List<EmptyRoomDetailTableVo> listEmptyRoomDetailTableByBuilding(@Param("req") EmptyRoomTableReq req);

    Page<EmptyBuildingFloorTableVo> listEmptyBuildingFloorTable(@Param("page") Page<EmptyBuildingFloorTableVo> page,@Param("req")EmptyBuildingFloorReq req);


    List<TaxIncomeCompareVo> listTaxIncomeCompareByCommunity(@Param("req") SpaceResourceAnalyzeReq req);

    List<TaxIncomeCompareVo> listTaxIncomeCompareByProject(@Param("req") SpaceResourceAnalyzeReq req);

    List<TaxIncomeCompareVo> listTaxIncomeCompareByBuilding(@Param("req") SpaceResourceAnalyzeReq req);


    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByCommunity(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByProject(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByBuilding(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByCommunityByQuarter(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByProjectByQuarter(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByBuildingByQuarter(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByCommunityByYear(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByProjectByYear(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<TaxIncomeTrendAnalyzeVo> listTaxIncomeTrendByBuildingByYear(@Param("req") TaxIncomeTrendAnalyzeReq req);

    List<EntStructAssembleVo> listEntStructMoveByCommunity(@Param("req") EntStructReq req);

    List<EntStructAssembleVo> listEntStructMoveByProject(@Param("req") EntStructReq req);

    List<EntStructAssembleVo> listEntStructMoveByBuilding(@Param("req") EntStructReq req);

    List<EntStructAssembleVo> listEntStructLocaledByCommunity(@Param("req") EntStructReq req);

    List<EntStructAssembleVo> listEntStructLocaledByProject(@Param("req") EntStructReq req);

    List<EntStructAssembleVo> listEntStructLocaledByBuilding(@Param("req") EntStructReq req);

    List<EntStructTrendVo> listEntStructTrendByCommunity(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByProject(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByBuilding(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByCommunityByQuarter(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByProjectByQuarter(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByBuildingByQuarter(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByCommunityByYear(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByProjectByYear(@Param("req") EntStructTrendReq req);

    List<EntStructTrendVo> listEntStructTrendByBuildingByYear(@Param("req") EntStructTrendReq req);

    List<EntStructTrendTableVo> listEntStructTrendTableByCommunity(@Param("req") EntStructTrendTableReq req);

    List<EntStructTrendTableVo> listEntStructTrendTableByProject(@Param("req") EntStructTrendTableReq req);

    List<EntStructTrendTableVo> listEntStructTrendTableByBuilding(@Param("req") EntStructTrendTableReq req);

    List<TaxIncomeDetailTableVo> listTaxIncomeDetailTableByCommunity(@Param("req") TaxIncomeDetailReq req);

    List<TaxIncomeDetailTableVo> listTaxIncomeDetailTableByProject(@Param("req") TaxIncomeDetailReq req);

    List<TaxIncomeDetailTableVo> listTaxIncomeDetailTableByBuilding(@Param("req") TaxIncomeDetailReq req);

    List<TaxStructVo> listTaxStructVo(@Param("req") TaxStructReq req);


    String getTaxMaxDate();


    Page<BuildingSettledEntBasicVo> listBuildingSettledEntBasicVo(@Param("page") Page<BuildingSettledEntBasicVo> page, @Param("req") BuildingSettleEntBasicReq  req);


    Page<BuildingInvestigationVo> listBuildingInvestigationVo(@Param("page") Page<BuildingInvestigationVo> page, @Param("req") ReportDateReq req);


    Page<BuildingDynamicInfoVo> listBuildingDynamicInfoVo(@Param("page") Page<BuildingDynamicInfoVo> page,@Param("req") ReportDateReq req);

    Page<BuildingDynamicAnalyzeVo> listBuildingDynamicAnalyzeVo(@Param("page") Page<BuildingDynamicAnalyzeVo> page,@Param("req") ReportDateReq req);

    Page<EntBusinessInfoVo> listEntBusinessInfoVo(@Param("page") Page<EntBusinessInfoVo> page,@Param("req") EntBusinessInfoReq req);





}
