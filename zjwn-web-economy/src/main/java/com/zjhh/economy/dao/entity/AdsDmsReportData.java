package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_dms_report_data")
public class AdsDmsReportData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 报告类型编码
     */
    private String reportTypeCode;

    /**
     * 数据源编码
     */
    private String dataTypeCode;

    /**
     * 时间参数
     */
    private String datekey;

    /**
     * 数据1
     */
    private String data1;

    /**
     * 数据2
     */
    private String data2;

    /**
     * 数据3
     */
    private String data3;

    /**
     * 数据4
     */
    private String data4;

    /**
     * 数据5
     */
    private String data5;

    /**
     * 数据6
     */
    private String data6;

    /**
     * 数据7
     */
    private String data7;

    /**
     * 数据8
     */
    private String data8;

    /**
     * 数据9
     */
    private String data9;

    /**
     * 数据10
     */
    private String data10;

    /**
     * 数据11
     */
    private String data11;

    /**
     * 数据12
     */
    private String data12;

    /**
     * 数据13
     */
    private String data13;

    /**
     * 数据14
     */
    private String data14;

    /**
     * 数据15
     */
    private String data15;

    /**
     * 数据16
     */
    private String data16;

    /**
     * 数据17
     */
    private String data17;

    /**
     * 数据18
     */
    private String data18;

    /**
     * 数据19
     */
    private String data19;

    /**
     * 数据20
     */
    private String data20;

    /**
     * 数据21
     */
    private String data21;

    /**
     * 数据22
     */
    private String data22;

    /**
     * 数据23
     */
    private String data23;

    /**
     * 数据24
     */
    private String data24;

    /**
     * 数据25
     */
    private String data25;

    /**
     * 数据26
     */
    private String data26;

    /**
     * 数据27
     */
    private String data27;

    /**
     * 数据28
     */
    private String data28;

    /**
     * 数据29
     */
    private String data29;

    /**
     * 数据30
     */
    private String data30;

    /**
     * 数据31
     */
    private String data31;

    /**
     * 数据32
     */
    private String data32;

    /**
     * 数据33
     */
    private String data33;

    /**
     * 数据34
     */
    private String data34;

    /**
     * 数据35
     */
    private String data35;

    /**
     * 数据36
     */
    private String data36;

    /**
     * 数据37
     */
    private String data37;

    /**
     * 数据38
     */
    private String data38;

    /**
     * 数据39
     */
    private String data39;

    /**
     * 数据40
     */
    private String data40;

    /**
     * 数据41
     */
    private String data41;

    /**
     * 数据42
     */
    private String data42;

    /**
     * 数据43
     */
    private String data43;

    /**
     * 数据44
     */
    private String data44;

    /**
     * 数据45
     */
    private String data45;

    /**
     * 数据46
     */
    private String data46;

    /**
     * 数据47
     */
    private String data47;

    /**
     * 数据48
     */
    private String data48;

    /**
     * 数据49
     */
    private String data49;

    /**
     * 数据50
     */
    private String data50;

    /**
     * 数据51
     */
    private String data51;

    /**
     * 数据52
     */
    private String data52;

    /**
     * 数据53
     */
    private String data53;

    /**
     * 数据54
     */
    private String data54;

    /**
     * 数据55
     */
    private String data55;

    /**
     * 数据56
     */
    private String data56;

    /**
     * 数据57
     */
    private String data57;

    /**
     * 数据58
     */
    private String data58;

    /**
     * 数据59
     */
    private String data59;

    /**
     * 数据60
     */
    private String data60;

    /**
     * 数据61
     */
    private String data61;

    /**
     * 数据62
     */
    private String data62;

    /**
     * 数据63
     */
    private String data63;

    /**
     * 数据64
     */
    private String data64;

    /**
     * 数据65
     */
    private String data65;

    /**
     * 数据66
     */
    private String data66;

    /**
     * 数据67
     */
    private String data67;

    /**
     * 数据68
     */
    private String data68;

    /**
     * 数据69
     */
    private String data69;

    /**
     * 数据70
     */
    private String data70;

    /**
     * 数据71
     */
    private String data71;

    /**
     * 数据72
     */
    private String data72;

    /**
     * 数据73
     */
    private String data73;

    /**
     * 数据74
     */
    private String data74;

    /**
     * 数据75
     */
    private String data75;

    /**
     * 数据76
     */
    private String data76;

    /**
     * 数据77
     */
    private String data77;

    /**
     * 数据78
     */
    private String data78;

    /**
     * 数据79
     */
    private String data79;

    /**
     * 数据80
     */
    private String data80;

    /**
     * 数据81
     */
    private String data81;

    /**
     * 数据82
     */
    private String data82;

    /**
     * 数据83
     */
    private String data83;

    /**
     * 数据84
     */
    private String data84;

    /**
     * 数据85
     */
    private String data85;

    /**
     * 数据86
     */
    private String data86;

    /**
     * 数据87
     */
    private String data87;

    /**
     * 数据88
     */
    private String data88;

    /**
     * 数据89
     */
    private String data89;

    /**
     * 数据90
     */
    private String data90;

    /**
     * 数据91
     */
    private String data91;

    /**
     * 数据92
     */
    private String data92;

    /**
     * 数据93
     */
    private String data93;

    /**
     * 数据94
     */
    private String data94;

    /**
     * 数据95
     */
    private String data95;

    /**
     * 数据96
     */
    private String data96;

    /**
     * 数据97
     */
    private String data97;

    /**
     * 数据98
     */
    private String data98;

    /**
     * 数据99
     */
    private String data99;

    /**
     * 数据100
     */
    private String data100;

    /**
     * 显示信息
     */
    private String displayInfo;

    /**
     * 链接模块
     */
    private String linkModule;

    /**
     * 类别-图表需要设置
     */
    private String category;
}
