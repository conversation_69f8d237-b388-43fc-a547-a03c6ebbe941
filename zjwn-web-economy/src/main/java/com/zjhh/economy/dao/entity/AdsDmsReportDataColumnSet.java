package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_dms_report_data_column_set")
public class AdsDmsReportDataColumnSet implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;


    private String dataSetId;

    /**
     * 字段编码
     */
    private String columnKey;

    /**
     * 字段名称
     */
    private String columnName;

    private Integer version;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
