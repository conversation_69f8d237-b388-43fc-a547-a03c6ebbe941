package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 房间企业关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_room_enterprise")
public class AdsPmRoomEnterprise implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String roomId;

    private String projectId;

    private String buildingId;

    private String floorId;

    private String enterpriseId;

    /**
     * 入住开始日期
     */
    private LocalDate checkInDate;

    /**
     * 预计搬离日期
     */
    private LocalDate expectMoveOutDate;

    /**
     * 入驻面积
     */
    private BigDecimal area;

    /**
     * 装修开始日期
     */
    private LocalDate renovationStartDate;

    /**
     * 装修结束日期
     */
    private LocalDate renovationEndDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 实际搬离日期
     */
    private LocalDate realityMoveOutDate;

    /**
     * 是否已经搬离
     */
    private Boolean moved;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 附件ID
     */
    private String documentId;


}
