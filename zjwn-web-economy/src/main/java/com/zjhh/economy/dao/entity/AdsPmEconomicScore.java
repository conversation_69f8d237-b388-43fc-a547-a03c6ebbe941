package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_economic_score")
public class AdsPmEconomicScore implements Serializable {


    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String datekey;

    private BigDecimal taxIncomeScore;

    private BigDecimal settledRateScore;

    private BigDecimal entCultivateScore;

    private BigDecimal taxRateScore;

    private BigDecimal unitOutputScore;

    private BigDecimal industryScaleScore;

    private BigDecimal score;

    private String buildingId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
