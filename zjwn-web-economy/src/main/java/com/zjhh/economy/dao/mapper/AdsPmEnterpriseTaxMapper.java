package com.zjhh.economy.dao.mapper;

import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmEnterpriseTax;
import com.zjhh.economy.request.BuildingArchiveReq;
import com.zjhh.economy.request.QueryTaxAnalyseReq;
import com.zjhh.economy.vo.BuildingArchiveVo;
import com.zjhh.economy.vo.TaxAnalyseChartVo;
import com.zjhh.economy.vo.TaxAnalyseTableVo;
import com.zjhh.economy.vo.TaxSummaryVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 企业税收 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
public interface AdsPmEnterpriseTaxMapper extends CustomerBaseMapper<AdsPmEnterpriseTax> {

    BigDecimal selectTotalTax(@Param("enterpriseId") String enterpriseId, @Param("datekey") String datekey);

    List<TaxAnalyseChartVo> listTaxAnalyseChartByMonth(@Param("req") QueryTaxAnalyseReq req);

    List<TaxAnalyseChartVo> listTaxAnalyseChartByYear(@Param("req") QueryTaxAnalyseReq req);

    List<TaxAnalyseTableVo> listTaxAnalyseTable(@Param("req") QueryTaxAnalyseReq req);

    TaxAnalyseTableVo listTaxAnalyseSummaryTable(@Param("req") QueryTaxAnalyseReq req);

    TaxSummaryVo getTaxSummaryByEnt(@Param("year") String year, @Param("month") String month, @Param("lastYear") String lastYear, @Param("lastMonth") String lastMonth);

    Page<BuildingArchiveVo> pageBuildingArchive(@Param("page") Page<BuildingArchiveVo> page, @Param("req")BuildingArchiveReq req);

    String getTaxMaxDate();


}
