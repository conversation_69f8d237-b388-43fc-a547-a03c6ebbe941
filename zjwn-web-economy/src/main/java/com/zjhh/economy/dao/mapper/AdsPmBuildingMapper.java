package com.zjhh.economy.dao.mapper;

import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.db.dao.mapper.CustomerBaseMapper;
import com.zjhh.economy.dao.entity.AdsPmBuilding;
import com.zjhh.economy.request.PageBuildingReq;
import com.zjhh.economy.vo.BuildingDetailVo;
import com.zjhh.economy.vo.BuildingProjectVo;
import com.zjhh.economy.vo.BuildingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 楼宇表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface AdsPmBuildingMapper extends CustomerBaseMapper<AdsPmBuilding> {

    /**
     * 获取最大的编码
     *
     * @param projectSerialNo
     * @return
     */
    Integer findMaxXh(@Param("projectSerialNo") String projectSerialNo);

    /**
     * 楼宇分页
     *
     * @param req
     * @return
     */
    Page<BuildingVo> page(@Param("page") Page<BuildingVo> page, @Param("req") PageBuildingReq req);

    /**
     * 项目选择
     *
     * @return
     */
    List<BuildingProjectVo> listBuildingProject();

    /**
     * 获取房间数
     *
     * @param buildingId
     * @return
     */
    int countRoomSize(@Param("buildingId") String buildingId);

    /**
     * 楼宇详情
     *
     * @param buildingId
     * @return
     */
    BuildingDetailVo getDetail(@Param("buildingId") String buildingId);

    List<TreeSelectVo> listBuilding();

    List<TreeSelectVo> listBuildingSearch(String keyword);

    List<TreeSelectVo> listBuildingByAuth();
}
