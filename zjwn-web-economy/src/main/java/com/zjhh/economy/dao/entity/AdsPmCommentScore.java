package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_comment_score")
public class AdsPmCommentScore implements Serializable {


    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String commentIndicatorId;

    private String datekey;

    private float commentScore;

    private String buildingId;

    private Boolean edited;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


//    private Float comm
}
