package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 操作配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_operation_config")
public class AdsOperationConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 操作模块
     */
    private String module;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 列字段
     */
    private String colField;

    /**
     * 列描述
     */
    private String colDesc;

    /**
     * 是否显示
     */
    private Boolean show;

    /**
     * 排序
     */
    private Integer sort;


}
