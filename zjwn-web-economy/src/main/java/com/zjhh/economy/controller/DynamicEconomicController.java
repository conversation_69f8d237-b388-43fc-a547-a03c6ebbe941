package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.economy.service.MobileGovEconomicService;
import com.zjhh.economy.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 动态经济
 */
@Slf4j
@Tag(name = "政府动态经济")
@ApiSupport(order = 2010)
@SaCheckLogin
@RestController
@RequestMapping("dynamic/eco")
public class DynamicEconomicController extends BaseController {

    @Resource
    private MobileGovEconomicService mobileGovEconomicService;

    @Operation(summary = "日期主页面选择")
    @ApiOperationSupport(order = 1)
    @PostMapping("main")
    public ReData<List<AppDynamicSummaryVo>> listAppDynamicEco() {
        return ReData.success(mobileGovEconomicService.listDynamicEco());
    }

    @Operation(summary = "经济运行概括")
    @ApiOperationSupport(order = 2)
    @PostMapping("jjyxgk")
    public ReData<TabOneVo> getJjyxgk(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.getTabOne(req.getId()));
    }

    @Operation(summary = "主要经济指标完成情况")
    @ApiOperationSupport(order = 3)
    @PostMapping("jjzb")
    public ReData<List<TabTwoVo>> listJjzb(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.listTabTwo(req.getId()));
    }

    @Operation(summary = "房地产投资情况")
    @ApiOperationSupport(order = 4)
    @PostMapping("fdctzqk")
    public ReData<List<TabTreeVo>> listFdctzqk(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.listTabTree(req.getId()));
    }

    @Operation(summary = "规上服务业主要经济指标完成情况")
    @ApiOperationSupport(order = 5)
    @PostMapping("gsfw")
    public ReData<List<TabFourVo>> listGsfw(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.listTabFour(req.getId()));
    }


    @Operation(summary = "规上批零、住餐企业销售额(营业额)情况")
    @ApiOperationSupport(order = 6)
    @PostMapping("gspl")
    public ReData<List<TabFiveVo>> listGspl(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.listTabFive(req.getId()));
    }

    @Operation(summary = "楼宇动态信息")
    @ApiOperationSupport(order = 7)
    @PostMapping("lydt")
    public ReData<List<TabSixVo>> listLydt(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.listTabSix(req.getId()));
    }

    @Operation(summary = "纳税总额50万元以上企业名单")
    @ApiOperationSupport(order = 8)
    @PostMapping("nsze")
    public ReData<List<TabSevenVo>> listNsze(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.listTabSeven(req.getId()));
    }

    @Operation(summary = "财政总收入分税种结构分析")
    @ApiOperationSupport(order = 9)
    @PostMapping("szjg")
    public ReData<List<TabEightVo>> listSzjg(@Validated @RequestBody IdReq req) {
        return ReData.success(mobileGovEconomicService.listTabEight(req.getId()));
    }
}
