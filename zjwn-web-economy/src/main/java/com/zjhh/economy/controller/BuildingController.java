package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsDmsColumns;
import com.zjhh.economy.request.AddBuildingReq;
import com.zjhh.economy.request.PageBuildingReq;
import com.zjhh.economy.request.UpdateBuildingReq;
import com.zjhh.economy.service.BuildingService;
import com.zjhh.economy.vo.BuildingDetailVo;
import com.zjhh.economy.vo.BuildingProjectVo;
import com.zjhh.economy.vo.BuildingVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/3/11 19:10
 */
@Slf4j
@Tag(name = "楼宇管理")
@ApiSupport(order = 2002)
@SaCheckLogin
@RestController
@RequestMapping("building")
public class BuildingController extends BaseController {

    @Resource
    private BuildingService buildingService;

    @Operation(summary = "楼宇分页")
    @ApiOperationSupport(order = 1)
    @PostMapping("page")
    public ReData<Page<BuildingVo>> page(@Validated @RequestBody PageBuildingReq req) {
        return ReData.success(buildingService.page(req));
    }

    @Operation(summary = "添加楼宇")
    @ApiOperationSupport(order = 2)
    @PostMapping("add")
    public ReData<String> add(@Validated @RequestBody AddBuildingReq req) {
        buildingService.add(req);
        return ReData.success();
    }

    @Operation(summary = "修改楼宇")
    @ApiOperationSupport(order = 3)
    @PostMapping("update")
    public ReData<String> update(@Validated @RequestBody UpdateBuildingReq req) {
        buildingService.update(req);
        return ReData.success();
    }

    @Operation(summary = "删除楼宇")
    @ApiOperationSupport(order = 4)
    @PostMapping("delete")
    public ReData<String> delete(@Validated @RequestBody IdReq req) {
        buildingService.delete(req.getId());
        return ReData.success();
    }

    @Operation(summary = "显示列")
    @ApiOperationSupport(order = 5)
    @PostMapping("list/show/column")
    public ReData<List<AdsDmsColumns>> listShowColumn() {
        return ReData.success(buildingService.listShowColumn());
    }

    @Operation(summary = "项目选择")
    @ApiOperationSupport(order = 6)
    @PostMapping("list/building/project")
    public ReData<List<BuildingProjectVo>> listBuildingProject() {
        return ReData.success(buildingService.listBuildingProject());
    }

    @Operation(summary = "楼宇详情")
    @ApiOperationSupport(order = 7)
    @PostMapping("get/detail")
    public ReData<BuildingDetailVo> getDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(buildingService.getDetail(req.getId()));
    }

    @Operation(summary = "楼宇列表导出")
    @ApiOperationSupport(order = 8)
    @PostMapping("export")
    public void export(@Validated @RequestBody PageBuildingReq req, HttpServletResponse response) throws IOException {
        Page<BuildingVo> page = buildingService.page(req);
        List<AdsDmsColumns> columns = buildingService.listShowColumn();
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> resList = new ArrayList<>();
        columns.forEach(column -> heads.add(Collections.singletonList(column.getColumnName())));
        if (CollUtil.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(buildingVo -> {
                Map<String, Object> map = BeanUtil.beanToMap(buildingVo);
                List<Object> res = new ArrayList<>();
                columns.forEach(column -> res.add(map.get(column.getColumnId())));
                resList.add(res);
            });
        }
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(new ThreeTableVo(
                new ThreeTableVo.TopTitleOneVo("楼宇列表", null, null),
                new ThreeTableVo.MidContentTwoVo(heads, resList)));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "楼宇列表", "楼宇列表");
    }
}
