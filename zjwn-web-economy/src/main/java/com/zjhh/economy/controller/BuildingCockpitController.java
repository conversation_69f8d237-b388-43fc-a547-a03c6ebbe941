package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.economy.service.CockpitService;
import com.zjhh.economy.service.PolicyManagementService;
import com.zjhh.economy.vo.cockpit.*;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 驾驶舱
 */
@Slf4j
@Tag(name = "楼宇驾驶舱")
@ApiSupport(order = 2010)
@SaCheckLogin
@RestController
@RequestMapping("building/cockpit")
public class BuildingCockpitController extends BaseController {


    @Resource
    private CockpitService cockpitService;

    @Resource
    private PolicyManagementService policyManagementService;

    @Operation(summary = "经济指标趋势")
    @ApiOperationSupport(order = 1)
    @PostMapping("indicator")
    public ReData<List<EconomicIndicatorVo>> listIndicator() {
        return ReData.success(cockpitService.listEconomicIndicator());
    }

    @Operation(summary = "产业结构-税收")
    @ApiOperationSupport(order = 2)
    @PostMapping("struct/tax")
    public ReData<List<IndustryStructVo>> listIndustryStruct() {
        return ReData.success(cockpitService.listIndustryStruct());
    }

    @Operation(summary = "产业结构-企业数量")
    @ApiOperationSupport(order = 3)
    @PostMapping("struct/ent")
    public ReData<List<IndustryEntStructVo>>listIndustryEntStruct() {
        return ReData.success(cockpitService.listIndustryEntStruct());
    }

    @Operation(summary = "企业纳税情况-纳税情况")
    @ApiOperationSupport(order = 4)
    @PostMapping("tax/paid")
    public ReData<List<EntTaxPaidVo>> listEntTaxPaid() {
        return ReData.success(cockpitService.listEntTaxPaid());
    }

    @Operation(summary = "企业纳税情况-Top10")
    @ApiOperationSupport(order = 5)
    @PostMapping("tax/paid/rank")
    public ReData<List<EntTaxPaidRankVo>> listEntTaxPaidRank() {
        return ReData.success(cockpitService.listEntTaxPaidRank());
    }

    @Operation(summary = "楼宇预警")
    @ApiOperationSupport(order = 5)
    @PostMapping("building/warning")
    public ReData<List<BuildingWarningVo>> listWarning() {
        return ReData.success(cockpitService.listBuildingWarning());
    }

    @Operation(summary = "企业注册数")
    @ApiOperationSupport(order = 7)
    @PostMapping("ent/registerCount")
    public ReData<List<EntCountVo>> listRegisterCount() {
        return ReData.success(cockpitService.listEntRegisterCount());
    }


    @Operation(summary = "楼宇Top")
    @ApiOperationSupport(order = 8)
    @PostMapping("building/top")
    public ReData<List<BuildingRankVo>> listBuildingRank() {
        return ReData.success(cockpitService.listBuildingRank());
    }

    @Operation(summary = "地图-概览")
    @ApiOperationSupport(order = 9)
    @PostMapping("map/summary")
    public ReData<BuildingSummaryVo> getBuildingSummary() {
        return ReData.success(cockpitService.getBuildingSummary());
    }

    @Operation(summary = "地图-已投入")
    @ApiOperationSupport(order = 10)
    @PostMapping("map/used")
    public ReData<List<BuildingMapInfoUsedVo>> listMapInfoUsed() {
        return ReData.success(cockpitService.listBuildingMapInfoUsed());
    }

    @Operation(summary = "地图-新建")
    @ApiOperationSupport(order = 11)
    @PostMapping("map/unused")
    public ReData<List<BuildingMapInfoUnusedVo>> listMapInfoUnused() {
        return ReData.success(cockpitService.listBuildingMapInfoUnused());
    }

    @Operation(summary = "地图-3d")
    @ApiOperationSupport(order = 9)
    @PostMapping("map/3d")
    public ReData<List<BuildingMapInfo3DVo>> listBuilding3D() {
        return ReData.success(cockpitService.listBuildingMapInfo3D());
    }

    @Operation(summary = "政策")
    @ApiOperationSupport(order = 10)
    @PostMapping("policy")
    public ReData<List<PolicyManagementPageVo>> listPolicyByCockpit() {
        return ReData.success(policyManagementService.listPolicyByCockpit());
    }

    @Operation(summary = "共融共享")
    @ApiOperationSupport(order = 1)
    @PostMapping("grgx")
    public ReData<List<ShareCockpitVo>> listShareCockpit() {
        return ReData.success(cockpitService.listShareCockpit());
    }
}
