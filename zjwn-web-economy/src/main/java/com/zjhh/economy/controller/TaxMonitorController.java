package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.*;
import com.zjhh.economy.service.WarningMonitorService;
import com.zjhh.economy.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "税收监测")
@ApiSupport(order = 2006)
@SaCheckLogin
@RestController
@RequestMapping("tax/monitor")
public class TaxMonitorController extends BaseController {


    @Resource
    private WarningMonitorService warningMonitorService;


    @Operation(summary = "规则列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("page/rule")
    public ReData<Page<WarningRuleVo>> pageRule(@Validated @RequestBody MonitorRuleReq req) {
        return ReData.success(warningMonitorService.pageWarningRule(req));
    }

    @Operation(summary = "保存")
    @ApiOperationSupport(order = 2)
    @PostMapping("save/rule")
    public ReData<String> saveRule(@Validated @RequestBody AddMonitorRuleReq req) {
        warningMonitorService.saveMonitorRule(req);
        return ReData.success();
    }

    @Operation(summary = "启动")
    @ApiOperationSupport(order = 3)
    @PostMapping("enable/rule")
    public ReData<String> enableRule(@Validated @RequestBody IdReq req) {
        warningMonitorService.enableMonitor(req);
        return ReData.success();
    }

    @Operation(summary = "删除规则")
    @ApiOperationSupport(order = 4)
    @PostMapping("del/rule")
    public ReData<String> delRule(@Validated @RequestBody IdReq req) {
        warningMonitorService.delMonitorRule(req);
        return ReData.success();
    }

    @Operation(summary = "规则详情")
    @ApiOperationSupport(order = 5)
    @PostMapping("detail/rule")
    public ReData<MonitorRuleDetailVo> getRuleDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(warningMonitorService.getRuleDetail(req));
    }


    @Operation(summary = "处理预警")
    @ApiOperationSupport(order = 5)
    @PostMapping("handle/warning")
    public ReData<String> handleWarning(@Validated @RequestBody HandleWarningReq req) {
        warningMonitorService.handleWarning(req);
        return ReData.success();
    }

    @Operation(summary = "处理详情")
    @ApiOperationSupport(order = 6)
    @PostMapping("handle/detail")
    public ReData<HandleDetailVo> getHandleDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(warningMonitorService.getHandleDetail(req));
    }


    @Operation(summary = "企业税收监测")
    @ApiOperationSupport(order = 7)
    @PostMapping("page/ent/monitor")
    public ReData<Page<EntTaxMonitorVo>> pageEntMonitor(@Validated @RequestBody EntTaxMonitorReq req) {
        return ReData.success(warningMonitorService.pageEntTaxMonitor(req));
    }

    @Operation(summary = "产业税收监测")
    @ApiOperationSupport(order = 8)
    @PostMapping("page/ind/monitor")
    public ReData<Page<IndTaxMonitorVo>> pageIndMonitor(@Validated @RequestBody IndTaxMonitorReq req) {
        return ReData.success(warningMonitorService.pageIndTaxMonitor(req));
    }


    @Operation(summary = "删除预警")
    @ApiOperationSupport(order = 9)
    @PostMapping("del/warning")
    public ReData<String> delWarning(@Validated @RequestBody DelWarningReq req) {
        warningMonitorService.delWarning(req);
        return ReData.success();
    }


    @Operation(summary = "汇总标头")
    @ApiOperationSupport(order = 10)
    @PostMapping("tax/summary")
    public ReData<TaxSummaryVo> getTaxSummary(@Validated @RequestBody TaxSummaryReq req) {
        return ReData.success(warningMonitorService.getTaxSummary(req));
    }


}
