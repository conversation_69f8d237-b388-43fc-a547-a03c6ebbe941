package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.ColumnResultVO;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.BuildingCommentDetailReq;
import com.zjhh.economy.request.BuildingCommentReq;
import com.zjhh.economy.request.UpdateCommentScoreReq;
import com.zjhh.economy.request.analyzereport.*;
import com.zjhh.economy.service.AnalyzeReportService;
import com.zjhh.economy.service.EntManageService;
import com.zjhh.economy.vo.BuildingCommentDetailVo;
import com.zjhh.economy.vo.BuildingCommentVo;
import com.zjhh.economy.vo.analyzereport.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

;


@Slf4j
@Tag(name = "统计分析")
@ApiSupport(order = 2010)
@SaCheckLogin
@RestController
@RequestMapping("analyze/report")
public class AnalyzeReportController extends BaseController {


    @Resource
    private AnalyzeReportService analyzeReportService;

    @Resource
    private EntManageService entManageService;


    @Operation(summary = "运营效果对比分析")
    @ApiOperationSupport(order = 1)
    @PostMapping("settledArea")
    public ReData<List<SpaceResourceAnalyzeVo>> listSpaceResourceAnalyze(@Validated @RequestBody SpaceResourceAnalyzeReq req) {
        return ReData.success(analyzeReportService.listSpaceResourceAnalyze(req));
    }


    @Operation(summary = "运营效果趋势分析")
    @ApiOperationSupport(order = 2)
    @PostMapping("compare/settledArea")
    public ReData<List<SpaceResourceDimensionVo>> listSpaceResourceDimension(@Validated @RequestBody SpaceResourceDimensionReq req) {
        return ReData.success(analyzeReportService.listSpaceResourceDimension(req));
    }


    @Operation(summary = "运营效果明细")
    @ApiOperationSupport(order = 3)
    @PostMapping("table/settledArea")
    public ReData<List<SettledAreaTrendTableVo>> listSettledAreaTrendTable(@Validated @RequestBody SettledAreaTrendTableReq req) {
        return ReData.success(analyzeReportService.listSettledAreaTrendTable(req));
    }


    @Operation(summary = "房源空置对比分析")
    @ApiOperationSupport(order = 4)
    @PostMapping("compare/emptyRoom")
    public ReData<List<EmptyRoomCompareVo>> listEmptyRoomCompare(@Validated @RequestBody SpaceResourceAnalyzeReq req) {
        return ReData.success(analyzeReportService.listEmptyRoomCompare(req));
    }


    @Operation(summary = "房源空置明细")
    @ApiOperationSupport(order = 5)
    @PostMapping("emptyRoom/detail")
    public ReData<List<EmptyRoomDetailTableVo>> listEmptyDetailTable(@Validated @RequestBody EmptyRoomTableReq req) {
        return ReData.success(analyzeReportService.listEmptyDetailTable(req));
    }

    @Operation(summary = "楼宇空置分析")
    @ApiOperationSupport(order = 6)
    @PostMapping("emptyBuilding")
    public ReData<Page<EmptyBuildingFloorTableVo>> listEmptyBuildingTable(@Validated @RequestBody EmptyBuildingFloorReq req) {
        return ReData.success(analyzeReportService.listEmptyBuildingFloor(req));
    }


    @Operation(summary = "经济效益分析-税收收入分析")
    @ApiOperationSupport(order = 7)
    @PostMapping("taxIncome")
    public ReData<List<TaxIncomeCompareVo>> listTaxIncome(@Validated @RequestBody SpaceResourceAnalyzeReq req) {
        return ReData.success(analyzeReportService.listTaxIncomeCompare(req));
    }

    @Operation(summary = "经济效益分析-税收趋势分析")
    @ApiOperationSupport(order = 8)
    @PostMapping("taxIncome/trend")
    public ReData<List<TaxIncomeTrendAnalyzeVo>> listTaxIncomeTrendAnalyze(@Validated @RequestBody TaxIncomeTrendAnalyzeReq req) {
        return ReData.success(analyzeReportService.listTaxIncomeTrendAnalyze(req));
    }

    @Operation(summary = "楼宇企业分析-企业结构分析")
    @ApiOperationSupport(order = 9)
    @PostMapping("ent/struct")
    public ReData<List<EntStructAssembleVo>> listEntStruct(@Validated @RequestBody EntStructReq req) {
        return ReData.success(analyzeReportService.listEntStructAssemble(req));
    }


    @Operation(summary = "楼宇企业分析-企业结构趋势分析")
    @ApiOperationSupport(order = 10)
    @PostMapping("ent/struct/trend")
    public ReData<List<EntStructTrendVo>> listEntStructTrend(@Validated @RequestBody EntStructTrendReq req) {
        return ReData.success(analyzeReportService.listEntStructTrend(req));
    }


    @Operation(summary = "楼宇企业分析-企业结构趋势表")
    @ApiOperationSupport(order = 11)
    @PostMapping("ent/struct/trend/table")
    public ReData<List<EntStructTrendTableVo>> listEntStructTrendTable(@Validated @RequestBody EntStructTrendTableReq req) {
        return ReData.success(analyzeReportService.listEntStructTrendTable(req));
    }

    @Operation(summary = "经济效益分析-税收明细表")
    @ApiOperationSupport(order = 12)
    @PostMapping("taxIncome/trend/detail")
    public ReData<ColumnResultVO> listTaxIncomeTrendDetail(@Validated @RequestBody TaxIncomeDetailReq req) {
        return ReData.success(analyzeReportService.listTaxIncomeDetailTable(req));
    }

    @Operation(summary = "经济效益分析-税收结构表")
    @ApiOperationSupport(order = 13)
    @PostMapping("tax/struct/table")
    public ReData<ColumnResultVO> listTaxStructTable(@Validated @RequestBody TaxStructReq req) {
        return ReData.success(analyzeReportService.listTaxStructAnalyze(req));
    }


    @Operation(summary = "楼宇星级评价列表")
    @ApiOperationSupport(order = 14)
    @PostMapping("page/building/comment")
    public ReData<Page<BuildingCommentVo>> pageBuildingComment(@Validated @RequestBody BuildingCommentReq  req) {
        return ReData.success(analyzeReportService.pageBuildingComment(req));
    }

    @Operation(summary = "楼宇星级评价列表-明细")
    @ApiOperationSupport(order = 15)
    @PostMapping("list/building/comment/detail")
    public ReData<List<BuildingCommentDetailVo>> pageBuildingCommentDetail(@Validated @RequestBody BuildingCommentDetailReq req) {
        return ReData.success(analyzeReportService.listBuildingCommentDetail(req));
    }

    @Operation(summary = "修改评分")
    @ApiOperationSupport(order = 16)
    @PostMapping("edit/comment/score")
    public ReData<String> editCommentScore(@Validated @RequestBody UpdateCommentScoreReq req) {
        analyzeReportService.updateCommentScore(req);
        return ReData.success();
    }

    @Operation(summary = "企业对比分析")
    @ApiOperationSupport(order = 17)
    @PostMapping("ent/compare")
    public ReData<EntCompareVo> getEntCompare(@Validated @RequestBody IdReq req) {
        return ReData.success(entManageService.getEntCompareInfo(req.getId()));
    }

    @Operation(summary = "企业对比分析导出")
    @ApiOperationSupport(order = 18)
    @PostMapping("ent/compare/export")
    public void getEntCompare(@Validated @RequestBody EntCompareExportReq req, HttpServletResponse response) throws IOException {
        entManageService.exportEntCompareInfo(req,response);
    }


    @Operation(summary = "企业下拉框")
    @ApiOperationSupport(order = 19)
    @PostMapping("list/menu/ent")
    public ReData<List<TreeSelectVo>> listMenuEnt(@Validated @RequestBody EntSearchReq req) {
        return ReData.success(entManageService.listMenuEnt(req));
    }

}
