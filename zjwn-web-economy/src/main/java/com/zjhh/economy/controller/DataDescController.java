package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.economy.request.DataDescReq;
import com.zjhh.economy.service.DataDescService;
import com.zjhh.economy.vo.DataDescVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/3/11 15:51
 */
@Slf4j
@Tag(name = "数据说明")
@ApiSupport(order = 2004)
@SaCheckLogin
@RestController
@RequestMapping("data/desc")
public class DataDescController extends BaseController {

    @Resource
    private DataDescService dataDescService;

    @Operation(summary = "说句描述")
    @ApiOperationSupport(order = 1)
    @PostMapping("get")
    public ReData<DataDescVo> getDataDesc(@Validated @RequestBody DataDescReq req) {
        return ReData.success(dataDescService.getDesc(req.getCode()));
    }

}
