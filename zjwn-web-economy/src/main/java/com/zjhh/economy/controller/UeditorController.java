package com.zjhh.economy.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.economy.ueditor.ActionEnter;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;

/**
 * 富文本
 *
 * <AUTHOR>
 * @date 2022-03-29 5:03 下午
 */
@Slf4j
@Tag(name = "富文本")
@RestController
@RequestMapping("ueditor")
@ApiSupport(order = 213)
public class UeditorController extends BaseController {

    @RequestMapping(value = "exec")
    @Operation(summary = "上传")
    @ApiOperationSupport(order = 1)
    @ResponseBody
    public String exec(HttpServletRequest request) throws UnsupportedEncodingException {
        request.setCharacterEncoding("utf-8");
        String rootPath = request.getServletContext().getRealPath("/");
        return new ActionEnter(request, rootPath).exec();
    }

    @RequestMapping(value = "exec/two")
    @Operation(summary = "上传")
    @ApiOperationSupport(order = 2)
    public String exec2(HttpServletRequest request) throws UnsupportedEncodingException {
        request.setCharacterEncoding("utf-8");
        String rootPath = request.getServletContext().getRealPath("/");
        String resultStr = new ActionEnter(request, rootPath).exec();
        JSONObject jsonObject = JSONObject.parseObject(resultStr);
        //获取配置时state为null。
        if (StrUtil.isBlank(jsonObject.getString("state"))) {
            return jsonObject.toString();
        }
        JSONObject resultJson = new JSONObject();
        //不识别大写的success。ApiInfo中定义的success是大写
        if (StrUtil.equals(jsonObject.getString("state"), "SUCCESS")) {
            resultJson.put("errno", 0);
            JSONObject innerJson = new JSONObject();
            innerJson.put("url", "/building/file/" + jsonObject.getString("url"));
            resultJson.put("data", innerJson);
        } else {
            resultJson.put("errno", 1);
            resultJson.put("message", "文件上传错误！");
        }
        return resultJson.toString();
    }
}
