package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.policymanagement.PolicyManagementAddOrUpdateReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementCashBatchReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementPageReq;
import com.zjhh.economy.service.PolicyManagementService;
import com.zjhh.economy.vo.policymanagement.PolicyManagementCashVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Slf4j
@Tag(name = "政策管理")
@ApiSupport(order = 9527)
@SaCheckLogin
@RestController
@RequestMapping("policy/management")
public class PolicyManagementController extends BaseController {

    @Resource
    private PolicyManagementService policyManagementService;

    @Operation(summary = "录入政策")
    @ApiOperationSupport(order = 1)
    @PostMapping("add")
    public ReData<String> addPolicyManagement(@Validated @RequestBody PolicyManagementAddOrUpdateReq req) {
        policyManagementService.addPolicyManagement(req);
        return ReData.success();
    }

    @Operation(summary = "编辑政策")
    @ApiOperationSupport(order = 2)
    @PostMapping("update")
    public ReData<String> updatePolicyManagement(@Validated @RequestBody PolicyManagementAddOrUpdateReq req) {
        policyManagementService.updatePolicyManagement(req);
        return ReData.success();
    }

    @Operation(summary = "兑现提交")
    @ApiOperationSupport(order = 3)
    @PostMapping("cash")
    public ReData<String> addPolicyManagementCash(@Validated @RequestBody PolicyManagementCashBatchReq req) {
        policyManagementService.addPolicyManagementCash(req);
        return ReData.success();
    }

    @Operation(summary = "详情")
    @ApiOperationSupport(order = 4)
    @PostMapping("detail")
    public ReData<PolicyManagementVo> getPolicyManagement(@Validated @RequestBody IdReq req) {
        return ReData.success(policyManagementService.getPolicyManagement(req));
    }

    @Operation(summary = "删除")
    @ApiOperationSupport(order = 5)
    @PostMapping("delete")
    public ReData<String> deletePolicyManagement(@Validated @RequestBody IdReq req) {
        policyManagementService.deletePolicyManagement(req);
        return ReData.success();
    }

    @Operation(summary = "查询")
    @ApiOperationSupport(order = 6)
    @PostMapping("page")
    public ReData<Page<PolicyManagementPageVo>> pagePolicyManagement(@Validated @RequestBody PolicyManagementPageReq req) {
        return ReData.success(policyManagementService.pagePolicyManagement(req));
    }

    @Operation(summary = "兑现列表")
    @ApiOperationSupport(order = 7)
    @PostMapping("cash/list")
    public ReData<List<PolicyManagementCashVo>> listPolicyManagementCash(@Validated @RequestBody IdReq req) {
        return ReData.success(policyManagementService.listPolicyManagementCash(req));
    }
}
