package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.economy.request.MobileCompareReq;
import com.zjhh.economy.request.analyzecockpit.*;
import com.zjhh.economy.service.AnalyzeCockpitService;
import com.zjhh.economy.service.BusinessService;
import com.zjhh.economy.vo.MobileCompareVo;
import com.zjhh.economy.vo.analyzecockpit.*;
import com.zjhh.economy.vo.cockpit.EconomicIndicatorScoreVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

;

/**
 * 楼宇分析 大屏
 */
@Slf4j
@Tag(name = "楼宇分析大屏")
@ApiSupport(order = 2010)
@SaCheckLogin
@RestController
@RequestMapping("analyze/cockpit")
public class AnalyzeCockpitController extends BaseController {


    @Resource
    private AnalyzeCockpitService analyzeCockpitService;


    @Resource
    private BusinessService businessService;

    @Operation(summary = "项目总览-面积分析")
    @ApiOperationSupport(order = 1)
    @PostMapping("project/area")
    public ReData<AreaAnalyzeVo> getAreaAnalyze(@Validated @RequestBody SpaceAnalyzeReq req) {
        return ReData.success(analyzeCockpitService.getAreaAnalyze(req));
    }

    @Operation(summary = "项目总览-入驻走势")
    @ApiOperationSupport(order = 2)
    @PostMapping("project/settled/trend")
    public ReData<List<SettledTrendVo>> listSettledTrend(@Validated @RequestBody AnalyzeDateReq req) {
        return ReData.success(analyzeCockpitService.listSettledTrend(req));
    }

    @Operation(summary = "项目总览-税收分析")
    @ApiOperationSupport(order = 3)
    @PostMapping("project/tax")
    public ReData<TaxAnalyzeVo> getTaxAnalyze(@Validated @RequestBody SpaceAnalyzeReq req) {
        return ReData.success(analyzeCockpitService.getTaxAnalyze(req));
    }

    @Operation(summary = "项目总览-税收整体趋势")
    @ApiOperationSupport(order = 4)
    @PostMapping("project/tax/trend")
    public ReData<List<TaxTrendVo>> listTaxTrend(@Validated @RequestBody AnalyzeDateReq req) {
        return ReData.success(analyzeCockpitService.listTaxTrend(req));
    }

    @Operation(summary = "项目总览-企业分析")
    @ApiOperationSupport(order = 5)
    @PostMapping("project/ent")
    public ReData<SettledEntAnalyzeVo> getEntAnalyze(@Validated @RequestBody SpaceAnalyzeReq req) {
        return ReData.success(analyzeCockpitService.getSettledEntAnalyze(req));
    }

    @Operation(summary = "项目总览-入驻企业单位走势")
    @ApiOperationSupport(order = 6)
    @PostMapping("project/ent/trend")
    public ReData<List<LocaledRegisterTrendVo>> listSettledEntTrend(@Validated @RequestBody AnalyzeDateReq req) {
        return ReData.success(analyzeCockpitService.listLocaledRegisterTrend(req));
    }

    @Operation(summary = "项目总览-产业聚焦分析-纳税规模")
    @ApiOperationSupport(order = 7)
    @PostMapping("project/industry/tax/rank")
    public ReData<List<IndustryFocusAnalyzeVo>> listTaxScaledRank(@Validated @RequestBody IndustryAnalyzeReq req) {
        return ReData.success(analyzeCockpitService.listTaxScaledRank(req));
    }

    @Operation(summary = "项目总览-产业聚焦分析-企业数量")
    @ApiOperationSupport(order = 7)
    @PostMapping("project/industry/count/rank")
    public ReData<List<IndustryFocusAnalyzeVo>> listEntCountRank(@Validated @RequestBody IndustryAnalyzeReq req) {
        return ReData.success(analyzeCockpitService.listEntCountRank(req));
    }

    @Operation(summary = "项目总览-产业聚焦分析-入驻面积")
    @ApiOperationSupport(order = 8)
    @PostMapping("project/industry/area/rank")
    public ReData<List<IndustryFocusAnalyzeVo>> listSettledAreaRank(@Validated @RequestBody IndustryAnalyzeReq req) {
        return ReData.success(analyzeCockpitService.listSettledAreaRank(req));
    }

    @Operation(summary = "项目总览-项目简介")
    @ApiOperationSupport(order = 9)
    @PostMapping("project/introduction")
    public ReData<CockpitProjectSummaryVo> getProjectIntroduction(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getCockpitProjectSummary(req));
    }

    @Operation(summary = "项目总览-楼宇简介")
    @ApiOperationSupport(order = 10)
    @PostMapping("project/building/introduction")
    public ReData<CockpitBuildingSummaryVo> getBuildingIntroduction(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getCockpitBuildingSummary(req));
    }

    @Operation(summary = "空间分析-空间分析汇总")
    @ApiOperationSupport(order = 11)
    @PostMapping("space/summary")
    public ReData<SpaceAnalyzeSummaryVo> getSpaceAnalyzeSummary(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getSpaceAnalyzeSummary(req));
    }

    @Operation(summary = "空间分析-入驻走势")
    @ApiOperationSupport(order = 12)
    @PostMapping("space/settled/trend")
    public ReData<List<SettledTrendVo>> listSpaceSettledTrend(@Validated @RequestBody AnalyzeDateReq req) {
        return ReData.success(analyzeCockpitService.listSettledTrendBySpace(req));
    }

    @Operation(summary = "空间分析-空间资源分析")
    @ApiOperationSupport(order = 13)
    @PostMapping("space/resource")
    public ReData<List<SpaceResourceVo>> listSpaceResource(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listSpaceResourceBySpace(req));
    }

    @Operation(summary = "空间分析-房源面积分布")
    @ApiOperationSupport(order = 14)
    @PostMapping("space/room/distributed")
    public ReData<List<RoomResourceVo>> listSpaceRoomResource(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listRoomResourceBySpace(req));
    }

    @Operation(summary = "空间分析-企业租用面积")
    @ApiOperationSupport(order = 15)
    @PostMapping("space/ent/rent/area")
    public ReData<List<RoomResourceVo>> listEntRentArea(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listEntSettledArea(req));
    }

    @Operation(summary = "空间分析-空置房源面积分布")
    @ApiOperationSupport(order = 16)
    @PostMapping("space/room/empty/distributed")
    public ReData<List<RoomResourceVo>> listRoomEmptyDistributed(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listEmptyRoomArea(req));
    }

    @Operation(summary = "空间分析-空置周期分析")
    @ApiOperationSupport(order = 17)
    @PostMapping("space/room/empty/period")
    public ReData<EmptyPeriodSummaryVo> listRoomEmptyPeriod(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getEmptyPeriodSummary(req));
    }

    @Operation(summary = "空间分析-楼层空置面积排行")
    @ApiOperationSupport(order = 18)
    @PostMapping("space/floor/empty/area")
    public ReData<List<FloorEmptyAreaRankVo>> listFloorEmptyArea(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listFloorEmptyAreaRank(req));
    }

    @Operation(summary = "经济分析-汇总")
    @ApiOperationSupport(order = 19)
    @PostMapping("economic/summary")
    public ReData<EconomicSummaryVo> getEconomicSummary(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getEconomicSummary(req));
    }


    @Operation(summary = "经济分析-增长原因")
    @ApiOperationSupport(order = 20)
    @PostMapping("economic/reason")
    public ReData<TaxIncomeReasonVo> getTaxIncomeReason(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getTaxIncomeReason(req));
    }

    @Operation(summary = "经济分析-税收整体走势")
    @ApiOperationSupport(order = 21)
    @PostMapping("economic/tax/trend")
    public ReData<List<TaxTrendVo>> listEconomicTaxTrend(@Validated @RequestBody EconomicTaxTrendReq req) {
        return ReData.success(analyzeCockpitService.listEconomicTaxTrend(req));
    }

    @Operation(summary = "经济分析-税种结构分析")
    @ApiOperationSupport(order = 22)
    @PostMapping("economic/tax/category")
    public ReData<List<TaxCategoryVo>> listEconomicTaxCategory(@Validated @RequestBody EconomicTaxTrendReq req) {
        return ReData.success(analyzeCockpitService.listTaxCategory(req));
    }

    @Operation(summary = "经济分析-单位产出走势对比")
    @ApiOperationSupport(order = 23)
    @PostMapping("economic/unit/income")
    public ReData<List<UnitIncomeTrendVo>> listEconomicUnitIncome(@Validated @RequestBody AnalyzeDateReq req) {
        return ReData.success(analyzeCockpitService.listUnitIncomeTrend(req));
    }

    @Operation(summary = "产业分析-产业聚焦分析")
    @ApiOperationSupport(order = 24)
    @PostMapping("industry/focus")
    public ReData<List<IndustryFocusAnalyzeVo>> listIndustryFocus(@Validated @RequestBody IndustryAnalyzeReq req) {
        return ReData.success(analyzeCockpitService.listIndustryFocus(req));
    }

    @Operation(summary = "产业分析-各产业纳税规模走势")
    @ApiOperationSupport(order = 25)
    @PostMapping("industry/tax/scaled")
    public ReData<List<IndustryTaxTrendVo>> listIndustryTaxScaled(@Validated @RequestBody IndustryTrendReq req) {
        return ReData.success(analyzeCockpitService.listIndustryTaxTrend(req));
    }

    @Operation(summary = "产业分析-各个产业企业数量走势")
    @ApiOperationSupport(order = 26)
    @PostMapping("industry/ent/count")
    public ReData<List<IndustryContributeVo>> listIndustryEntCount(@Validated @RequestBody IndustryTrendReq req) {
        return ReData.success(analyzeCockpitService.listIndustryContribute(req));
    }

    @Operation(summary = "产业分析-各个产业入驻面积走势")
    @ApiOperationSupport(order = 27)
    @PostMapping("industry/settled/area")
    public ReData<List<IndustrySettledAreaVo>> listIndustrySettledArea(@Validated @RequestBody IndustryTrendReq req) {
        return ReData.success(analyzeCockpitService.listIndustrySettledArea(req));
    }

    @Operation(summary = "入驻企业分析-汇总")
    @ApiOperationSupport(order = 28)
    @PostMapping("ent/settled/summary")
    public ReData<EntSettledSummaryVo> getEntSettledSummary(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getEntSettledSummary(req));
    }

    @Operation(summary = "入驻企业分析-企业税收榜")
    @ApiOperationSupport(order = 29)
    @PostMapping("ent/settled/tax/rank")
    public ReData<List<EntTaxRankVo>> listEntSettledTaxRank(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listEntRank(req));
    }

    @Operation(summary = "入驻企业分析-企业入驻面积排行榜")
    @ApiOperationSupport(order = 30)
    @PostMapping("ent/settled/area/rank")
    public ReData<List<EntSettledAreaVo>> listEntSettledAreaRank(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listEntSettled(req));
    }

    @Operation(summary = "入驻企业分析-稳定性排行榜")
    @ApiOperationSupport(order = 31)
    @PostMapping("ent/settled/stable/rank")
    public ReData<List<EntStableRankVo>> listEntStableRank(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listEntStableRank(req));
    }

    @Operation(summary = "入驻企业分析-企业属地分布")
    @ApiOperationSupport(order = 32)
    @PostMapping("ent/settled/localed/dis")
    public ReData<List<EntLocaledDisVo>> listEntSettledTaxTrend(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listEntLocaledDis(req));
    }

    @Operation(summary = "入驻企业分析-属地注册率趋势")
    @ApiOperationSupport(order = 33)
    @PostMapping("ent/settled/localed/register")
    public ReData<List<LocaledRegisterTrendVo>> listEntSettledLocaledRegister(@Validated @RequestBody AnalyzeDateReq req) {
        return ReData.success(analyzeCockpitService.listLocaledRegisterTrend(req));
    }

    @Operation(summary = "入驻企业分析-单位性质分布")
    @ApiOperationSupport(order = 34)
    @PostMapping("ent/settled/unit/dis")
    public ReData<List<UnitPropertyDisVo>> listEntSettledUnitDis(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listUnitPropertyDis(req));
    }

    @Operation(summary = "入驻企业分析-企业纳税情况")
    @ApiOperationSupport(order = 35)
    @PostMapping("ent/settled/tax/income")
    public ReData<List<EntTaxIncomeVo>> listEntSettledTaxIncome(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listEntTaxIncome(req));
    }

    @Operation(summary = "入驻企业分析-注经一直分布")
    @ApiOperationSupport(order = 36)
    @PostMapping("ent/settled/address/same")
    public ReData<List<RegisterBusinessSameVo>> listEntSettledAddressSame(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listRegisterBusinessSame(req));
    }

    @Operation(summary = "入驻企业分析-登记类型")
    @ApiOperationSupport(order = 37)
    @PostMapping("ent/settled/register/type")
    public ReData<List<RegisterTypeVo>> listEntSettledRegisterType(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.listRegisterType(req));
    }

    @Operation(summary = "默认产业")
    @ApiOperationSupport(order = 38)
    @PostMapping("default/industries")
    public ReData<List<TreeSelectVo>> listDefaultIndustriesSettledArea(@Validated @RequestBody IndustryTrendReq req) {
        return ReData.success(analyzeCockpitService.listDefaultIndustries(req));
    }

    @Operation(summary = "app-总览具体情况")
    @ApiOperationSupport(order = 39)
    @PostMapping("app/summary")
    public ReData<AppSummaryVo> getAppSummary(@Validated @RequestBody AnalyzeCockpitCommonReq req) {
        return ReData.success(analyzeCockpitService.getAppSummary(req));
    }


    @Operation(summary = "app-经济分析税收表格")
    @ApiOperationSupport(order = 40)
    @PostMapping("app/economic/table")
    public ReData<List<AppEconomicVo>> listAppEconomicTable(@Validated @RequestBody AppEconomicReq req) {
        return ReData.success(analyzeCockpitService.listAppEconomic(req));
    }

    @Operation(summary = "楼经济指数评分")
    @ApiOperationSupport(order = 41)
    @PostMapping("economic/indicator/score")
    public ReData<EconomicIndicatorScoreVo> getEconomicIndicatorScore(@Validated @RequestBody IdReq req) {
        return ReData.success(analyzeCockpitService.getEconomicIndicatorScore(req.getId()));
    }



    @Operation(summary = "test-warning")
    @ApiOperationSupport(order = 41)
    @PostMapping("test/warning")
    public ReData<String> test(@Validated @RequestBody AppEconomicReq req) {
        businessService.generationWarning();

        return ReData.success();
    }

    @Operation(summary = "test-brief")
    @ApiOperationSupport(order = 42)
    @PostMapping("test/brief")
    public ReData<String> testBrief(@Validated @RequestBody AppEconomicReq req) {

        businessService.generationDataBrief();

        return ReData.success();
    }

    @Operation(summary = "test-score")
    @ApiOperationSupport(order = 43)
    @PostMapping("test/score")
    public ReData<String> testScore(@Validated @RequestBody AppEconomicReq req) {

        analyzeCockpitService.generationBuildingScore();
        return ReData.success();
    }

    @Operation(summary = "获取移动端对对比维度")
    @ApiOperationSupport(order = 43)
    @PostMapping("app/compare/type")
    public ReData<MobileCompareVo> getAppCompareType(@Validated @RequestBody MobileCompareReq req) {
        return ReData.success( analyzeCockpitService.getMobileCompareItem(req));
    }






}
