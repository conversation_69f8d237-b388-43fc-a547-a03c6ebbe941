package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.DownloadTempReq;
import com.zjhh.economy.request.ExportRecordReq;
import com.zjhh.economy.request.ImportExternalDataListReq;
import com.zjhh.economy.request.ImportExternalDataReq;
import com.zjhh.economy.service.ImportExternalDataService;
import com.zjhh.economy.vo.DocumentVo;
import com.zjhh.economy.vo.ImportExternalDataVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022-05-25 3:44 下午
 */
@Slf4j
@Tag(name = "外部数据导入")
@RestController
@RequestMapping("import/external/data")
@ApiSupport(order = 1202)
@SaCheckLogin
public class ImportExternalDataController extends BaseController {


    @Resource
    private ImportExternalDataService importExternalDataService;

    @Operation(summary = "导入列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("list")
    public ReData<Page<ImportExternalDataVo>> pageImportExternalData(@RequestBody @Validated ImportExternalDataListReq req) {
        return ReData.success(importExternalDataService.pageImportExternalData(req));
    }

    @Operation(summary = "导入")
    @ApiOperationSupport(order = 2)
    @PostMapping("import")
    public ReData<String> importExternalData(ImportExternalDataReq req) throws IOException {
        importExternalDataService.importExternalData(req);
        return ReData.success();
    }

    @Operation(summary = "下载记录")
    @ApiOperationSupport(order = 3)
    @PostMapping("export")
    public void importExternalData(HttpServletResponse response, @RequestBody @Validated ExportRecordReq req) throws IOException {
        importExternalDataService.exportRecords(response,req);
    }


    @Operation(summary = "获取模版文件")
    @ApiOperationSupport(order = 4)
    @PostMapping("download/temp")
    public ReData<DocumentVo> getTemplate( @RequestBody @Validated DownloadTempReq req)  {
        return ReData.success(importExternalDataService.getTemplate(req.getTempType()));
    }



}


