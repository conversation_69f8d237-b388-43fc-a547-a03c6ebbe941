package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.response.ReData;
import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.dto.ResourceDto;
import com.zjhh.economy.request.UploadDocReq;
import com.zjhh.economy.service.DocumentService;
import com.zjhh.economy.vo.DocumentVo;
import com.zjhh.system.service.DictService;
import com.zjhh.web.base.BaseController;
import com.zjhh.web.config.NonStaticResourceHttpRequestHandler;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @since 2024/3/11 10:43
 */
@Slf4j
@Tag(name = "文件管理")
@ApiSupport(order = 1001)
@RestController
@RequestMapping("document")
public class DocumentController extends BaseController {

    @Resource
    private NonStaticResourceHttpRequestHandler nonStaticResourceHttpRequestHandler;

    @Resource
    private DocumentService documentService;

    @Resource
    private DictService dictService;

    @SaCheckLogin
    @Operation(summary = "上传文档")
    @ApiOperationSupport(order = 1)
    @PostMapping("upload")
    public ReData<DocumentVo> upload(@Validated UploadDocReq req) {
        return ReData.success(documentService.upload(req));
    }

    @Operation(summary = "文档预览")
    @ApiOperationSupport(order = 2)
    @GetMapping("preview/{fileId}")
    public void preview(HttpServletRequest request, HttpServletResponse response, @PathVariable("fileId") String fileId) {
        try {
            ResourceDto dto = documentService.getPreview(fileId);
            Path filePath = Paths.get(dto.getFilePath());
            if (Files.exists(filePath)) {
                String mimeType = Files.probeContentType(filePath);
                if (!StrUtil.isEmpty(mimeType)) {
                    response.setContentType(mimeType);
                }
                request.setAttribute(NonStaticResourceHttpRequestHandler.ATTR_FILE, filePath);
                nonStaticResourceHttpRequestHandler.handleRequest(request, response);
            } else {
                log.error("文件：{}, 不存在！", filePath);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
            }
        } catch (Exception e) {
            log.error("文件预览失败！", e);
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        }
    }

    @Operation(summary = "下载")
    @ApiOperationSupport(order = 3)
    @GetMapping("download/{fileId}")
    public ResponseEntity<org.springframework.core.io.Resource> download(@PathVariable("fileId") String fileId) {
        AdsDocument adsDocument = documentService.getDocument(fileId);
        org.springframework.core.io.Resource resource = new FileSystemResource(
                dictService.getFileUploadPath() + adsDocument.getPath());
        if (!resource.exists()) {
            throw new BizException("该文件不存在！");
        }

        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=" + URLEncodeUtil.encode(adsDocument.getTitle(), StandardCharsets.UTF_8)).body(resource);
    }
}
