package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.AddProjectReq;
import com.zjhh.economy.request.QueryProjectPageReq;
import com.zjhh.economy.request.UpdateProjectReq;
import com.zjhh.economy.service.ProjectManageService;
import com.zjhh.economy.vo.ProjectDetailVo;
import com.zjhh.economy.vo.ProjectListVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

;


/**
 * 项目管理Controller
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Tag(name = "项目管理")
@ApiSupport(order = 2001)
@SaCheckLogin
@RestController
@RequestMapping("project")
public class ProjectManageController extends BaseController {

    @Resource
    private ProjectManageService projectManageService;

    @Operation(summary = "项目列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("page")
    public ReData<Page<ProjectListVo>> pageProject(@Validated @RequestBody QueryProjectPageReq req) {
        return ReData.success(projectManageService.pageProjectList(req));
    }


    @Operation(summary = "新增项目")
    @ApiOperationSupport(order = 2)
    @PostMapping("add")
    public ReData<String> addProject(@Validated @RequestBody AddProjectReq req) {
        projectManageService.addProject(req);
        return ReData.success();
    }


    @Operation(summary = "编辑项目")
    @ApiOperationSupport(order = 3)
    @PostMapping("edit")
    public ReData<String> editProject(@Validated @RequestBody UpdateProjectReq req) {
        projectManageService.updateProject(req);
        return ReData.success();
    }

    @Operation(summary = "删除项目")
    @ApiOperationSupport(order = 4)
    @PostMapping("delete")
    public ReData<String> deleteProject(@Validated @RequestBody IdReq req) {
        projectManageService.delProject(req);
        return ReData.success();
    }

    @Operation(summary = "项目详情")
    @ApiOperationSupport(order = 5)
    @PostMapping("detail")
    public ReData<ProjectDetailVo> getDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(projectManageService.getProjectDetail(req));
    }

    @Operation(summary = "导出")
    @ApiOperationSupport(order = 8)
    @PostMapping("export")
    public void export(HttpServletResponse response, @Validated @RequestBody QueryProjectPageReq req) throws IOException {
        Page<ProjectListVo> page = projectManageService.pageProjectList(req);
        String sheetName = "项目信息";
        String fileName =  "项目信息表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo(fileName, null, null),
                        new ThreeTableVo.MidContentTwoVo(ProjectListVo.class, page.getRecords())));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, fileName, sheetName);

    }
}
