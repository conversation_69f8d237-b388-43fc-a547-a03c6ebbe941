package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.response.ReData;
import com.zjhh.economy.request.chatbi.ChatBiVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/6/10 10:37
 */
@Slf4j
@Tag(name = "chatBi")
@ApiSupport(order = 10001)
@SaCheckLogin
@RestController
@RequestMapping("chat/bi")
public class ChatBiController extends BaseController {

    @Value("${chat-bi.login-name}")
    private String loginName;

    @Value("${chat-bi.login-password}")
    private String loginPassword;

    @Value("${chat-bi.base-url}")
    private String baseUrl;

    @Operation(summary = "chatBI登录")
    @PostMapping("login")
    public ReData<ChatBiVo> chatBiLogin() {
        ChatBiVo chatBiVo = new ChatBiVo();
        chatBiVo.setUrl("/webapp/chat/external");
        JSONObject param = new JSONObject();
        param.put("name", loginName);
        param.put("password", loginPassword);
        String response = HttpUtil.post(baseUrl + "/api/auth/user/login", param.toJSONString());
        JSONObject result = JSONObject.parseObject(response);
        if (result.getInteger("code") == 200) {
            chatBiVo.setBearerToken(result.getString("data"));
            return ReData.success(chatBiVo);
        }
        log.error("登录失败：{}", response);
        throw new BizException("登录chatbi失败！");
    }
}
