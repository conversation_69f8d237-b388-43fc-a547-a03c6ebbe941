package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.collection.CollUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.OperationLogPageReq;
import com.zjhh.economy.service.OperationLogService;
import com.zjhh.economy.vo.operationlog.CommonLogResultVo;
import com.zjhh.economy.vo.operationlog.CommonLogVo;
import com.zjhh.economy.vo.operationlog.OperationLogVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Slf4j
@Tag(name = "操作日志")
@ApiSupport(order = 9999)
@SaCheckLogin
@RestController
@RequestMapping("operation/log")
public class OperationLogController extends BaseController {

    @Resource
    private OperationLogService operationLogService;

    @Operation(summary = "操作模块列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("list/module")
    public ReData<List<SingleSelectVo>> listModule() {
        return ReData.success(operationLogService.listModule());
    }

    @Operation(summary = "获取操作日志详情")
    @ApiOperationSupport(order = 2)
    @PostMapping("get/detail")
    public ReData<CommonLogResultVo> getOperationLogDetail(@RequestBody @Validated IdReq idReq) {
        CommonLogResultVo resultVo = operationLogService.getOperationLogDetail(idReq);
        if (CollUtil.isNotEmpty(resultVo.getFieldMap())) {
            TreeMap<String, String> fieldMap = resultVo.getFieldMap();
            convert(resultVo.getOptBefore(), fieldMap);
            convert(resultVo.getOptAfter(), fieldMap);
        }
        return ReData.success(resultVo);
    }

    private void convert(List<CommonLogVo> dataList, TreeMap<String, String> fieldMap) {
        if (CollUtil.isEmpty(dataList) || CollUtil.isEmpty(fieldMap)) {
            return;
        }
        for (CommonLogVo commonLogVo : dataList) {
            if (commonLogVo == null) {
                continue;
            }
            TreeMap<String, Object> dataMap = commonLogVo.getDataMap();
            for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                dataMap.compute(key, (k, fieldValue) -> value + "：" + (fieldValue == null ? "" : fieldValue.toString()));
            }
        }
    }

    @Operation(summary = "日志列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("page")
    public ReData<Page<OperationLogVo>> pageOperationLogs(@RequestBody @Validated OperationLogPageReq req) {
        return ReData.success(operationLogService.pageOperationLogs(req));
    }

    @Operation(summary = "日志导出")
    @ApiOperationSupport(order = 4)
    @PostMapping("export")
    public void exportOperationLogs(@RequestBody @Validated OperationLogPageReq req, HttpServletResponse response) throws IOException {
        List<OperationLogVo> dataList = operationLogService.exportOperationLogs(req);
        String title = "操作日志";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo(title),
                        new ThreeTableVo.MidContentTwoVo(OperationLogVo.class, dataList)
                )
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title);
    }
}
