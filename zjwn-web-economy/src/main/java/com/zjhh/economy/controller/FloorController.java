package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.economy.request.SaveFloorReq;
import com.zjhh.economy.request.SavePlaneConfigReq;
import com.zjhh.economy.request.UpdatePlaneConfigReq;
import com.zjhh.economy.service.FloorService;
import com.zjhh.economy.vo.FloorConfigMsgVo;
import com.zjhh.economy.vo.FloorConfigSaveVo;
import com.zjhh.economy.vo.FloorRoomInfoVo;
import com.zjhh.economy.vo.FloorVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 19:10
 */
@Slf4j
@Tag(name = "楼层管理")
@ApiSupport(order = 2003)
@SaCheckLogin
@RestController
@RequestMapping("floor")
public class FloorController extends BaseController {

    @Resource
    private FloorService floorService;

    @Operation(summary = "楼层列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("list")
    public ReData<List<FloorVo>> list(@Validated @RequestBody IdReq req) {
        return ReData.success(floorService.list(req.getId()));
    }

    @Operation(summary = "保存楼层信息")
    @ApiOperationSupport(order = 2)
    @PostMapping("save")
    public ReData<String> save(@Validated @RequestBody SaveFloorReq req) {
        floorService.save(req);
        return ReData.success();
    }

    @Operation(summary = "(根据配置id）获取楼层平面图配置")
    @ApiOperationSupport(order = 3)
    @PostMapping("plane/config/get")
    public ReData<FloorConfigMsgVo> getPlaneConfig(@Validated @RequestBody IdReq idReq) {
        return ReData.success(floorService.getPlaneConfig(idReq));
    }

    @Operation(summary = "保存楼层平面图配置")
    @ApiOperationSupport(order = 4)
    @PostMapping("plane/config/save")
    public ReData<FloorConfigSaveVo> savePlaneConfig(@Validated @RequestBody SavePlaneConfigReq req) {
        return ReData.success(floorService.savePlaneConfig(req));
    }

    @Operation(summary = "获取楼层单个房源信息（传roomId）")
    @ApiOperationSupport(order = 5)
    @PostMapping("room/info/get")
    public ReData<FloorRoomInfoVo> getRoomInfo(@Validated @RequestBody IdReq idReq) {
        return ReData.success(floorService.getRoomInfo(idReq));
    }

    @Operation(summary = "获取楼层所有房源信息（传floorId）")
    @ApiOperationSupport(order = 6)
    @PostMapping("room/info/list")
    public ReData<List<FloorRoomInfoVo>> listRoomInfo(@Validated @RequestBody IdReq idReq) {
        return ReData.success(floorService.listRoomInfo(idReq));
    }

    @Operation(summary = "即时更新平面图配置")
    @ApiOperationSupport(order = 7)
    @PostMapping("plane/config/update")
    public ReData<String> updatePlaneConfig(@Validated @RequestBody UpdatePlaneConfigReq req) {
        floorService.updatePlaneConfig(req);
        return ReData.success();
    }

}
