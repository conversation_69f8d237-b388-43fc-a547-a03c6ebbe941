package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.AddRoomReq;
import com.zjhh.economy.request.PageRoomReq;
import com.zjhh.economy.request.UpdateRoomReq;
import com.zjhh.economy.service.RoomService;
import com.zjhh.economy.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 15:51
 */
@Slf4j
@Tag(name = "房源管理")
@ApiSupport(order = 2004)
@SaCheckLogin
@RestController
@RequestMapping("room")
public class RoomController extends BaseController {

    @Resource
    private RoomService roomService;

    @Operation(summary = "房态详情列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("list/floor")
    public ReData<List<RoomStateFloorVo>> listFloor(@Validated @RequestBody IdReq req) {
        return ReData.success(roomService.listFloor(req.getId()));
    }

    @Operation(summary = "房源列表分页")
    @ApiOperationSupport(order = 2)
    @PostMapping("page")
    public ReData<Page<RoomVo>> page(@Validated @RequestBody PageRoomReq req) {
        return ReData.success(roomService.page(req));
    }

    @Operation(summary = "获取房间右侧抽屉详情")
    @ApiOperationSupport(order = 3)
    @PostMapping("get/detail")
    public ReData<RoomDetailVo> getDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(roomService.getDetail(req.getId()));
    }

    @Operation(summary = "根据楼层获取添加房间信息")
    @ApiOperationSupport(order = 4)
    @PostMapping("get/add/room")
    public ReData<AddRoomVo> getAddRoom(@Validated @RequestBody IdReq req) {
        return ReData.success(roomService.getAddRoom(req.getId()));
    }

    @Operation(summary = "添加房间")
    @ApiOperationSupport(order = 5)
    @PostMapping("add")
    public ReData<String> add(@Validated @RequestBody AddRoomReq req) {
        roomService.add(req);
        return ReData.success();
    }

    @Operation(summary = "编辑房间")
    @ApiOperationSupport(order = 6)
    @PostMapping("update")
    public ReData<String> update(@Validated @RequestBody UpdateRoomReq req) {
        roomService.update(req);
        return ReData.success();
    }

    @Operation(summary = "删除房间")
    @ApiOperationSupport(order = 7)
    @PostMapping("delete")
    public ReData<String> delete(@Validated @RequestBody IdReq req) {
        roomService.delete(req.getId());
        return ReData.success();
    }

    @Operation(summary = "获取房间编辑详情")
    @ApiOperationSupport(order = 8)
    @PostMapping("get/update/detail")
    public ReData<RoomUpdateDetailVo> getUpdateDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(roomService.getUpdateDetail(req.getId()));
    }

    @Operation(summary = "房源列表导出")
    @ApiOperationSupport(order = 9)
    @PostMapping("export")
    public void export(@Validated @RequestBody PageRoomReq req, HttpServletResponse response) throws IOException {
        Page<RoomVo> page = roomService.page(req);
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(new ThreeTableVo(
                new ThreeTableVo.TopTitleOneVo("房源列表", null, null),
                new ThreeTableVo.MidContentTwoVo(RoomVo.class, page.getRecords())));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, "房源列表", "房源列表");
    }
}
