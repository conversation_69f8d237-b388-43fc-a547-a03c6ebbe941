package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.*;
import com.zjhh.economy.service.AnalyzeCockpitService;
import com.zjhh.economy.service.BusinessService;
import com.zjhh.economy.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

@Slf4j
@Tag(name = "业务管理")
@ApiSupport(order = 6002)
@SaCheckLogin
@RestController
@RequestMapping("business")
public class BusinessController extends BaseController {

    @Resource
    private BusinessService businessService;

    @Resource
    private AnalyzeCockpitService analyzeCockpitService;



    @Operation(summary = "保存标签")
    @ApiOperationSupport(order = 1)
    @PostMapping("save")
    public ReData<String> saveLabels(@Validated @RequestBody AddLabelReq req) {
        businessService.saveLabel(req);
        return ReData.success();
    }

    @Operation(summary = "列表")
    @ApiOperationSupport(order = 2)
    @PostMapping("list")
    public ReData<List<LabelReq>> listLabels(@Validated @RequestBody QueryLabelsReq req) {
        return ReData.success(businessService.listLabel(req));
    }


    @Operation(summary = "企业诉求列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("page/ent/demand")
    public ReData<Page<EnterpriseDemandVo>> listEntDemand(@Validated @RequestBody EnterpriseDemandReq req) {
        return ReData.success(businessService.pageEnterpriseDemand(req));
    }

    @Operation(summary = "新增或编辑企业诉求")
    @ApiOperationSupport(order = 4)
    @PostMapping("add/ent/demand")
    public ReData<String> addEntDemand(@Validated @RequestBody AddEnterpriseDemandReq req) {
        businessService.addEnterpriseDemand(req);
        return ReData.success();
    }

    @Operation(summary = "企业诉求详情")
    @ApiOperationSupport(order = 5)
    @PostMapping("ent/demand/detail")
    public ReData<EnterpriseDemandDetailVo> getEntDemandDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(businessService.getEnterpriseDemandDetail(req));
    }

    @Operation(summary = "处理诉求")
    @ApiOperationSupport(order = 6)
    @PostMapping("handle/demand")
    public ReData<String> handleDemand(@Validated @RequestBody HandleEnterpriseDemandReq req) {
        businessService.handleDemand(req);
        return ReData.success();
    }

    @Operation(summary = "走访列表")
    @ApiOperationSupport(order = 7)
    @PostMapping("list/visit")
    public ReData<Page<EnterpriseVisitVo>> listVisit(@Validated @RequestBody QueryEnterpriseVisitReq req) {
        return ReData.success(businessService.pageEnterpriseVisit(req));
    }

    @Operation(summary = "新增走访记录")
    @ApiOperationSupport(order = 8)
    @PostMapping("add/visit")
    public ReData<String> addVisit(@Validated @RequestBody AddEnterpriseVisitReq req) {
        businessService.addEnterpriseVisit(req);
        return ReData.success();
    }

    @Operation(summary = "走访详情")
    @ApiOperationSupport(order = 9)
    @PostMapping("visit/detail")
    public ReData<EnterpriseVisitDetailVo> getEnterpriseVisitDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(businessService.getEnterpriseVisitDetail(req.getId()));
    }

    @Operation(summary = "工作台房源管理")
    @ApiOperationSupport(order = 10)
    @PostMapping("work/platform/room")
    public ReData<WorkPlatformRoomVo> getWorkPlatformRoom() {
        return ReData.success(businessService.getWorkPlatformRoom());
    }

    @Operation(summary = "工作台房源企业管理")
    @ApiOperationSupport(order = 11)
    @PostMapping("work/platform/ent")
    public ReData<WorkPlatformEntManageVo> getWorkPlatformEnt() {
        return ReData.success(businessService.getWorkPlatformEntManage());
    }

    @Operation(summary = "工作台诉求总览")
    @ApiOperationSupport(order = 12)
    @PostMapping("work/platform/demand")
    public ReData<WorkPlatformDemandVo> getWorkPlatformDemand(@RequestBody DatekeyReq req) {
        return ReData.success(businessService.getWorkPlatformDemand(req));
    }

    @Operation(summary = "预警提醒")
    @ApiOperationSupport(order = 13)
    @PostMapping("warning/remind")
    public ReData<List<WorkPlatformWarningRemindVo>> listWorkPlatformRemind() {
        return ReData.success(businessService.listWorkPlatformRemind());
    }


    @Operation(summary = "预警提醒列表")
    @ApiOperationSupport(order = 14)
    @PostMapping("page/warning/remind")
    public ReData<Page<WarningRemindListVo>> pageWorkPlatformRemind(@RequestBody @Validated WarningPageReq req) {
        return ReData.success(businessService.pageWarningRemindList(req));
    }


    @Operation(summary = "预警提醒操作")
    @ApiOperationSupport(order = 15)
    @PostMapping("warning/remind/operate")
    public ReData<String> warningRemindOperate(@RequestBody @Validated WarningRemindOperateReq req) {
        businessService.operateRemind(req);
        return ReData.success();
    }

    @Operation(summary = "常用功能树状")
    @ApiOperationSupport(order = 16)
    @PostMapping("tree/function")
    public ReData<List<TreeSelectVo>> treeCommonFunction() {
        return ReData.success(businessService.treeCommonFunction());
    }


    @Operation(summary = "用户常用功能")
    @ApiOperationSupport(order = 17)
    @PostMapping("list/user/function")
    public ReData<List<CommonFunctionVo>> listUserFunction() {
        return ReData.success(businessService.listCommonFunctionByUser());
    }


    @Operation(summary = "编辑常用功能")
    @ApiOperationSupport(order = 18)
    @PostMapping("edit/function")
    public ReData<String> editFunction(@RequestBody CommonFunctionReq req) {
        businessService.commonFunctionEdit(req);
        return ReData.success();
    }

    @Operation(summary = "企业诉求删除")
    @ApiOperationSupport(order = 19)
    @PostMapping("delete/demand")
    public ReData<String> deleteDemand(@Validated @RequestBody IdReq req) {
        businessService.deleteDemand(req);
        return ReData.success();
    }

    @Operation(summary = "企业走访删除")
    @ApiOperationSupport(order = 20)
    @PostMapping("delete/visit")
    public ReData<String> deleteVisit(@Validated @RequestBody IdReq req) {
        businessService.deleteEnterpriseVisit(req);
        return ReData.success();
    }


    @Operation(summary = "简报")
    @ApiOperationSupport(order = 20)
    @PostMapping("list/briefing")
    public ReData<List<String>> listBriefing(@Validated @RequestBody BriefingReq req) {
        return ReData.success(businessService.listBriefing(req.getPeriod()));
    }

    @Operation(summary = "手动生成数据")
    @ApiOperationSupport(order = 22)
    @PostMapping("generation/data")
    public void generationWarning() {
        businessService.generationWarning();
        businessService.generationDataBrief();
        analyzeCockpitService.generationBuildingScore();
    }
    @Operation(summary = "预警导出")
    @ApiOperationSupport(order = 22)
    @PostMapping("export/warning")
    public void exportWarningRemind(HttpServletResponse response, @RequestBody @Validated WarningPageReq req) throws IOException {
        List<WarningRemindListVo> list = businessService.pageWarningRemindList(req).getRecords();
        String sheetName = "预警提醒列表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(new ThreeTableVo(
                new ThreeTableVo.TopTitleOneVo(sheetName, null, null),
                new ThreeTableVo.MidContentTwoVo(WarningRemindListVo.class, list)));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, sheetName, sheetName);
    }


}
