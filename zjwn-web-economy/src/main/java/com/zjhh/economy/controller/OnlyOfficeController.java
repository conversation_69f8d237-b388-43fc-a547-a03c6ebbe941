package com.zjhh.economy.controller;

import cn.hutool.core.net.URLEncodeUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.onlyoffice.context.DocsIntegrationSdkContext;
import com.onlyoffice.model.documenteditor.Callback;
import com.onlyoffice.service.documenteditor.callback.CallbackService;
import com.zjhh.comm.exception.BizException;
import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.onlyoffice.constant.OnlyOfficeConstant;
import com.zjhh.economy.service.DocumentService;
import com.zjhh.system.service.DictService;
import com.zjhh.user.service.impl.UserSession;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @since 2025/8/15 15:09
 */
@Slf4j
@Tag(name = "onlyoffice回调")
@ApiSupport(order = 2000)
@RestController
@RequestMapping("onlyoffice")
public class OnlyOfficeController extends BaseController {

    @Resource
    private DocsIntegrationSdkContext docsIntegrationSdk;

    @Resource
    private DocumentService documentService;

    @Resource
    private DictService dictService;

    @Resource
    private UserSession userSession;

    @PostMapping("/callback/{fileId}/{token}")
    public String callback(@RequestBody final Callback callback, @PathVariable("fileId") String fileId, @PathVariable("token") String token) {
        log.info("onlyoffice回调, callback:{}, fileId:{}", JSONObject.toJSONString(callback), fileId);
        userSession.checkToken(token);
        CallbackService callbackService = docsIntegrationSdk.getCallbackService();
        try {
            Callback verifiedCallback = callbackService.verifyCallback(callback, null);

            callbackService.processCallback(verifiedCallback, fileId);
            return "{\"error\": 0}";
        } catch (Exception e) {
            return "{\"error\": 1}";
        }
    }

    @GetMapping("download/{fileId}/{token}")
    public ResponseEntity<org.springframework.core.io.Resource> download(@PathVariable("fileId") String fileId, @PathVariable("token") String token) {
        userSession.checkToken(token);

        org.springframework.core.io.Resource resource;
        String title;
        if (fileId.startsWith(OnlyOfficeConstant.FILE_ID_TEMP)) {
            String tempFileId = fileId.replace(OnlyOfficeConstant.FILE_ID_TEMP, "");
            String path = "alys_report" + File.separator + "tmp" + File.separator + tempFileId + ".doc";
            resource = new FileSystemResource(
                    dictService.getFileUploadPath() + path);
            title = tempFileId + ".doc";
        } else {
            AdsDocument adsDocument = documentService.getDocument(fileId);
            resource = new FileSystemResource(
                    dictService.getFileUploadPath() + adsDocument.getPath());
            title = adsDocument.getTitle();
        }
        if (!resource.exists()) {
            throw new BizException("该文件不存在！");
        }

        String contentType = "application/octet-stream";

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=" + URLEncodeUtil.encode(title, StandardCharsets.UTF_8))
                .body(resource);
    }
}
