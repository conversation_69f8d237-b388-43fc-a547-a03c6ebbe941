package com.zjhh.economy.controller;

import cn.hutool.core.net.URLEncodeUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.request.AddEnterpriseDemandReq;
import com.zjhh.economy.request.BuildingKeywordSearchReq;
import com.zjhh.economy.request.EntKeywordSearchReq;
import com.zjhh.economy.request.UploadDocReq;
import com.zjhh.economy.request.policymanagement.PolicyManagementPageReq;
import com.zjhh.economy.service.*;
import com.zjhh.economy.vo.DocumentVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementVo;
import com.zjhh.system.service.DictService;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Tag(name = "移动端-政策、企业诉求")
@ApiSupport(order = 6010)
@RestController
@RequestMapping("mobile/policy")
public class MobileEntDemandController extends BaseController {



    @Resource
    private PolicyManagementService policyManagementService;

    @Resource
    private BusinessService businessService;


    @Resource
    private EntManageService entManageService;

    @Resource
    private DropMenuService dropMenuService;

    @Resource
    private DocumentService documentService;

    @Resource
    private DictService dictService;



    @Operation(summary = "政策查询")
    @ApiOperationSupport(order = 1)
    @PostMapping("page")
    public ReData<Page<PolicyManagementPageVo>> pagePolicyManagement(@Validated @RequestBody PolicyManagementPageReq req) {
        return ReData.success(policyManagementService.pagePolicyManagement(req));
    }

    @Operation(summary = "政策详情")
    @ApiOperationSupport(order = 2)
    @PostMapping("detail")
    public ReData<PolicyManagementVo> getPolicyManagement(@Validated @RequestBody IdReq req) {
        return ReData.success(policyManagementService.getPolicyManagement(req));
    }

    @Operation(summary = "新增企业诉求")
    @ApiOperationSupport(order = 3)
    @PostMapping("add/ent/demand")
    public ReData<String> addEntDemand(@Validated @RequestBody AddEnterpriseDemandReq req) {
        businessService.addEnterpriseDemand(req);
        return ReData.success();
    }

    @Operation(summary = "企业模糊搜索")
    @ApiOperationSupport(order = 4)
    @PostMapping("list/ent/search")
    public ReData<List<TreeSelectVo>> listEntSearch(@Validated @RequestBody EntKeywordSearchReq req) {
        return ReData.success(entManageService.listMenuMobileEnt(req));
    }

    @Operation(summary = "楼宇模糊搜索")
    @ApiOperationSupport(order = 5)
    @PostMapping("list/building/search")
    public ReData<List<TreeSelectVo>> listBuildingSearch(@Validated @RequestBody BuildingKeywordSearchReq req) {
        return ReData.success(entManageService.listBuildingSearch(req));
    }

    @Operation(summary = "诉求类型")
    @ApiOperationSupport(order = 6)
    @PostMapping("list/demand/type")
    public ReData<List<SingleSelectVo>> listDemandType() {
        return ReData.success(dropMenuService.listDemandType());
    }

    @Operation(summary = "上传文档")
    @ApiOperationSupport(order = 7)
    @PostMapping("upload")
    public ReData<DocumentVo> upload(@Validated UploadDocReq req) {

        return ReData.success(documentService.uploadNotToken(req));
    }


    @Operation(summary = "下载")
    @ApiOperationSupport(order = 8)
    @GetMapping("download/{fileId}")
    public ResponseEntity<org.springframework.core.io.Resource> download(@PathVariable("fileId") String fileId) {
        AdsDocument adsDocument = documentService.getDocument(fileId);
        org.springframework.core.io.Resource resource = new FileSystemResource(
                dictService.getFileUploadPath() + adsDocument.getPath());
        if (!resource.exists()) {
            throw new BizException("该文件不存在！");
        }

        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=" + URLEncodeUtil.encode(adsDocument.getTitle(), StandardCharsets.UTF_8)).body(resource);
    }

    @Operation(summary = "获取联络人")
    @ApiOperationSupport(order = 8)
    @PostMapping("get/mobileContactPerson")
    public ReData<DocumentVo> getTemplate()  {
        return ReData.success(businessService.getMobileContactPerson());
    }






}
