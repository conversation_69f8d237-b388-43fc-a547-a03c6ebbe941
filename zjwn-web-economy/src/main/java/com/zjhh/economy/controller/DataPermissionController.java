package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.comm.vo.TreeSelectedVo;
import com.zjhh.economy.dao.entity.AdsPmBuilding;
import com.zjhh.economy.request.datapermission.AuthDataPermissionReq;
import com.zjhh.economy.request.datapermission.TreeDataPermissionReq;
import com.zjhh.economy.service.DataPermissionService;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Tag(name = "数据权限")
@ApiSupport(order = 9999)
@SaCheckLogin
@RestController
@RequestMapping("data/permission")
public class DataPermissionController extends BaseController {

    @Resource
    private DataPermissionService dataPermissionService;

    @Operation(summary = "组织结构和用户树形结构")
    @ApiOperationSupport(order = 1)
    @PostMapping("tree/organize/user")
    public ReData<List<TreeSelectVo>> treeOrganizeUser() {
        return ReData.success(dataPermissionService.treeOrganizeUser());
    }

    @Operation(summary = "获取用户数据权限树形")
    @ApiOperationSupport(order = 2)
    @PostMapping("tree/data/permission")
    public ReData<TreeSelectedVo> treeDataPermission(@RequestBody @Valid TreeDataPermissionReq req) {
        return ReData.success(dataPermissionService.treeDataPermission(req));
    }

    @Operation(summary = "数据权限授权")
    @ApiOperationSupport(order = 3)
    @PostMapping("auth/data/permission")
    public ReData<String> authDataPermission(@RequestBody @Valid AuthDataPermissionReq req) {
        dataPermissionService.authDataPermission(req);
        return ReData.success();
    }

    /**
     * 测试代码
     *
     * @return
     */
    @Operation(summary = "测试代码")
    @ApiOperationSupport(order = 4)
    @PostMapping("test")
    public ReData<List<AdsPmBuilding>> test(){
        return ReData.success(dataPermissionService.test());
    }

} 