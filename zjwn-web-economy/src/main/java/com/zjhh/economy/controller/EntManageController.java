package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.*;
import com.zjhh.economy.service.EntManageService;
import com.zjhh.economy.vo.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;

;

@Slf4j
@Tag(name = "企业库")
@ApiSupport(order = 2005)
@SaCheckLogin
@RestController
@RequestMapping("enterprise")
public class EntManageController extends BaseController {

    @Resource
    private EntManageService entManageService;

    @Operation(summary = "企业列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("page")
    public ReData<Page<EnterpriseListVo>> pageEnterprise(@Validated @RequestBody QueryEnterprisePageReq req) {
        return ReData.success(entManageService.pageEnterpriseList(req));
    }


    @Operation(summary = "添加企业")
    @ApiOperationSupport(order = 2)
    @PostMapping("add")
    public ReData<String> addEnterprise(@Validated @RequestBody AddEntReq req) {
        entManageService.addEnterprise(req);
        return ReData.success();
    }

    @Operation(summary = "入驻信息")
    @ApiOperationSupport(order = 3)
    @PostMapping("add/settleInfo")
    public ReData<String> addSettleInfo(@Validated @RequestBody AddSettleInfoReq req) {
        entManageService.addSettleInfo(req);
        return ReData.success();
    }

    @Operation(summary = "人才信息")
    @ApiOperationSupport(order = 4)
    @PostMapping("add/talent")
    public ReData<String> addTalent(@Validated @RequestBody SaveTalentReq req) {
        entManageService.saveTalent(req);
        return ReData.success();
    }

    @Operation(summary = "企业基本信息-头")
    @ApiOperationSupport(order = 5)
    @PostMapping("get/summary")
    public ReData<EnterpriseDetailHeaderVo> getEnterpriseDetailHeader(@Validated @RequestBody IdReq req) {
        return ReData.success(entManageService.getDetailHeader(req.getId()));
    }

    @Operation(summary = "企业基本信息")
    @ApiOperationSupport(order = 5)
    @PostMapping("get/basic")
    public ReData<EnterpriseDetailVo> getEnterpriseDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(entManageService.getDetail(req.getId()));
    }

    @Operation(summary = "税收信息-图")
    @ApiOperationSupport(order = 5)
    @PostMapping("get/tax/chart")
    @SaCheckPermission(value = {"enterprise:tax", "LOGIN_TYPE:MOBILE"}, mode = SaMode.OR)
    public ReData<List<TaxAnalyseChartVo>> getTaxAnalyseChart(@Validated @RequestBody QueryTaxAnalyseReq req) {
        return ReData.success(entManageService.listTaxAnalyseChart(req));
    }

    @Operation(summary = "税收信息-表格")
    @ApiOperationSupport(order = 5)
    @PostMapping("get/tax/table")
    @SaCheckPermission(value = {"enterprise:tax", "LOGIN_TYPE:MOBILE"}, mode = SaMode.OR)
    public ReData<List<Map<String, Object>>> getTaxAnalyseTable(@Validated @RequestBody QueryTaxAnalyseReq req) {
        return ReData.success(entManageService.listTaxAnalyseTable(req));
    }

    @Operation(summary = "关注")
    @ApiOperationSupport(order = 5)
    @PostMapping("add/focus")
    public ReData<String> addFocus(@Validated @RequestBody IdReq req) {
        entManageService.addFocus(req.getId());
        return ReData.success();
    }

    @Operation(summary = "取消关注")
    @ApiOperationSupport(order = 5)
    @PostMapping("cancel/focus")
    public ReData<String> cancelFocus(@Validated @RequestBody IdReq req) {
        entManageService.cancelFocus(req.getId());
        return ReData.success();
    }


    @Operation(summary = "删除企业")
    @ApiOperationSupport(order = 11)
    @PostMapping("delete/enterprise")
    public ReData<String> deleteEnterprise(@Validated @RequestBody IdReq req) {
        entManageService.deleteEnterprise(req.getId());
        return ReData.success();
    }

    @Operation(summary = "删除入驻信息")
    @ApiOperationSupport(order = 11)
    @PostMapping("delete/settleInfo")
    public ReData<String> deleteSettleInfo(@Validated @RequestBody IdReq req) {
        entManageService.deleteSettleInfo(req);
        return ReData.success();
    }

    @Operation(summary = "入住列表")
    @ApiOperationSupport(order = 11)
    @PostMapping("page/settle")
    @SaCheckPermission(value = {"enterprise:settle", "LOGIN_TYPE:MOBILE"}, mode = SaMode.OR)
    public ReData<List<SettleInfoListVo>> pageSettle(@Validated @RequestBody QuerySettleInfoPageReq req) {
        return ReData.success(entManageService.listSettleInfoList(req));
    }

    @Operation(summary = "搬离登记")
    @ApiOperationSupport(order = 14)
    @PostMapping("move/settleInfo")
    public ReData<String> moveSettle(@Validated @RequestBody MoveRegisterReq req) {
        entManageService.moveRegister(req);
        return ReData.success();
    }

    @Operation(summary = "搬离入驻信息菜单")
    @ApiOperationSupport(order = 15)
    @PostMapping("menu/settleInfo")
    public ReData<List<SettleInfoMenuVo>> menuSettleInfo(@Validated @RequestBody MoveSettleDropMenuReq req) {
        return ReData.success(entManageService.moveRegisterMenu(req.getId(),req.getIds()));
    }

    @Operation(summary = "更新企业信息")
    @ApiOperationSupport(order = 15)
    @PostMapping("update/enterprise")
    public ReData<String> updateEnterprise(@Validated @RequestBody UpdateEntReq req) {
        entManageService.updateEnterprise(req);
        return ReData.success();
    }

    @Operation(summary = "更新入驻信息")
    @ApiOperationSupport(order = 17)
    @PostMapping("update/settleInfo")
    public ReData<String> updateSettleINfo(@Validated @RequestBody UpdateSettleInfoReq req) {
        entManageService.updateSettleInfo(req);
        return ReData.success();
    }


    @Operation(summary = "企业列表导出")
    @ApiOperationSupport(order = 18)
    @PostMapping("export/ent")
    public void exportEnt(HttpServletResponse response, @Validated @RequestBody QueryEnterprisePageReq req) throws IOException {
        Page<EnterpriseListVo> page = entManageService.pageEnterpriseList(req);
        String sheetName = "企业列表";
        String fileName =  "企业列表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo(fileName, null, null),
                        new ThreeTableVo.MidContentTwoVo(EnterpriseListVo.class, page.getRecords())));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, fileName, sheetName);

    }

    @Operation(summary = "入驻信息导出")
    @ApiOperationSupport(order = 19)
    @PostMapping("export/settle")
    public void exportSettle(HttpServletResponse response, @Validated @RequestBody QuerySettleInfoPageReq req) throws IOException {
        List<SettleInfoListVo> page = entManageService.listSettleInfoList(req);
        String sheetName = "入驻列表";
        String fileName =  "入驻列表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(
                        new ThreeTableVo.TopTitleOneVo(fileName, null, null),
                        new ThreeTableVo.MidContentTwoVo(SettleInfoListVo.class,page)));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, fileName, sheetName);

    }

//    @Operation(summary = "导出税收表格")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("export/tax")
//    public void exportTax(HttpServletResponse response, @Validated @RequestBody QueryTaxAnalyseReq req) throws IOException {
//        List<TaxAnalyseTableVo> list = entManageService.listTaxAnalyseTable(req);
//        String sheetName = "税收列表";
//        String fileName =  "税收列表";
//        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
//                new ThreeTableVo(
//                        new ThreeTableVo.TopTitleOneVo(fileName, null, "单位: 万元"),
//                        new ThreeTableVo.MidContentTwoVo(TaxAnalyseTableVo.class, list)));
//        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, fileName, sheetName);
//
//    }


    @Operation(summary = "入驻详情")
    @ApiOperationSupport(order = 21)
    @PostMapping("get/settleInfo/detail")
    public ReData<SettleInfoDetailVo> getSettleInfoDetail(@Validated @RequestBody IdReq req) {

        return ReData.success(entManageService.getSettleInfoDetail(req));
    }


    @Operation(summary = "人才详情")
    @ApiOperationSupport(order = 22)
    @PostMapping("get/talent/detail")
    public ReData<TalentDetailVo> getTalentDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(entManageService.getTalentDetail(req));
    }

    @Operation(summary = "企业模糊搜索")
    @ApiOperationSupport(order = 23)
    @PostMapping("list/ent/search")
    public ReData<List<TreeSelectVo>> listEntSearch(@Validated @RequestBody EntKeywordSearchReq req) {
        return ReData.success(entManageService.listEntSearch(req));
    }

    @Operation(summary = "房间详情-入驻详情")
    @ApiOperationSupport(order = 24)
    @PostMapping("room/settled/info")
    public ReData<RoomSettleInfoDetailVo> getRoomSettledInfo(@Validated @RequestBody RoomSettledInfoReq req) {
        return ReData.success(entManageService.getSettledInfoByRoomDetail(req.getEnterpriseId(),req.getRoomId()));
    }


    @Operation(summary = "搬离详情")
    @ApiOperationSupport(order = 25)
    @PostMapping("get/move/detail")
    public ReData<MoveRegisterDetailVo> getMoveRegisterDetail(@Validated @RequestBody IdReq req) {
        return ReData.success(entManageService.getMoveRegisterDetail(req));
    }

    @Operation(summary = "编辑搬离信息")
    @ApiOperationSupport(order = 26)
    @PostMapping("edit/move")
    public ReData<String> editMove(@Validated @RequestBody EditMoveRegisterReq req) {
        entManageService.editMoveRegister(req);
        return ReData.success();
    }


    @Operation(summary = "企业信息变更列表")
    @ApiOperationSupport(order = 27)
    @PostMapping("list/change")
    public ReData<List<EnterpriseChangeListVo>> listChange(@Validated @RequestBody IdReq req) {
        return ReData.success(entManageService.listEnterpriseChange(req.getId()));
    }



    @Operation(summary = "企业信息变更")
    @ApiOperationSupport(order = 28)
    @PostMapping("info/change")
    public ReData<String> infoChange(@Validated @RequestBody EnterpriseChangeReq req) {
        entManageService.addOrUpdateEnterpriseChange(req);
        return ReData.success();
    }

    @Operation(summary = "删除企业变更信息")
    @ApiOperationSupport(order = 29)
    @PostMapping("del/change")
    public ReData<String> delChange(@Validated @RequestBody IdReq req) {
        entManageService.delEnterpriseChange(req);
        return ReData.success();
    }










}
