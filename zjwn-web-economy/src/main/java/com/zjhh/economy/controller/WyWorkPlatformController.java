package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.WarningRemindOperateReq;
import com.zjhh.economy.request.WorkPlatformEnterpriseDemandReq;
import com.zjhh.economy.request.WyWorkPlatformPageReq;
import com.zjhh.economy.request.WyWorkPlatformReq;
import com.zjhh.economy.service.BusinessService;
import com.zjhh.economy.service.PolicyManagementService;
import com.zjhh.economy.service.WyWorkPlatformService;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo;
import com.zjhh.economy.vo.policymanagement.PolicyManagementVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

@Slf4j
@Tag(name = "物业工作台")
@ApiSupport(order = 6002)
@SaCheckLogin
@RestController
@RequestMapping("wy/work/platform")
public class WyWorkPlatformController extends BaseController {

    @Resource
    private WyWorkPlatformService wyWorkPlatformService;

    @Resource
    private BusinessService businessService;

    @Resource
    private PolicyManagementService policyManagementService;

    @Operation(summary = "物业楼宇下拉框")
    @ApiOperationSupport(order = 1)
    @PostMapping("menu/building")
    public ReData<List<TreeSelectVo>> listTreeBuilding() {
        return ReData.success(wyWorkPlatformService.listAuthBuildingMenu());
    }

    @Operation(summary = "房源管理")
    @ApiOperationSupport(order = 2)
    @PostMapping("room")
    public ReData<WorkPlatformRoomVo> getWorkPlatformRoom(@RequestBody WyWorkPlatformReq req) {
        return ReData.success(wyWorkPlatformService.getWorkPlatformRoomByAuth(req));
    }

    @Operation(summary = "企业管理")
    @ApiOperationSupport(order = 3)
    @PostMapping("ent")
    public ReData<WorkPlatformEntManageVo> getWorkPlatformEnt(@RequestBody WyWorkPlatformReq req) {
        return ReData.success(wyWorkPlatformService.getWorkPlatformEntManageByAuth(req));
    }

    @Operation(summary = "企业诉求")
    @ApiOperationSupport(order = 4)
    @PostMapping("demand")
    public ReData<WorkPlatformDemandVo> getWorkPlatformDemand(@RequestBody WyWorkPlatformReq req) {
        return ReData.success(wyWorkPlatformService.getWorkPlatformDemandByAuth(req));
    }

    @Operation(summary = "预警提醒")
    @ApiOperationSupport(order = 5)
    @PostMapping("list/remind")
    public ReData<List<WorkPlatformWarningRemindVo>> listWorkPlatformRemind(@RequestBody  WyWorkPlatformReq req) {
        return ReData.success(wyWorkPlatformService.listWorkPlatformRemindByAuth(req));
    }

    @Operation(summary = "预警提醒列表")
    @ApiOperationSupport(order = 6)
    @PostMapping("page/remind")
    public ReData<Page<WarningRemindListVo>> pageWarningRemind(@RequestBody  WyWorkPlatformPageReq req) {
        return ReData.success(wyWorkPlatformService.pageWorkPlatformRemindByAuth(req));
    }

    @Operation(summary = "预警提醒操作")
    @ApiOperationSupport(order = 7)
    @PostMapping("operate/remind")
    public ReData<String> operateRemind(@RequestBody WarningRemindOperateReq req) {
        businessService.operateRemind(req);
        return ReData.success();
    }

    @Operation(summary = "诉求记录")
    @ApiOperationSupport(order = 8)
    @PostMapping("list/demand")
    public ReData<List<EnterpriseDemandVo>> listDemand(@RequestBody WorkPlatformEnterpriseDemandReq req) {
        return ReData.success(wyWorkPlatformService.listEnterpriseDemandByAuth(req));
    }

    @Operation(summary = "政策动态")
    @ApiOperationSupport(order = 9)
    @PostMapping("list/policy")
    public ReData<List<PolicyManagementPageVo>> listPolicy() {
        return ReData.success(wyWorkPlatformService.listPolicyManagement());
    }

    @Operation(summary = "政策详情")
    @ApiOperationSupport(order = 10)
    @PostMapping("detail/policy")
    public ReData<PolicyManagementVo> getPolicyDetail(@RequestBody IdReq req) {
        return ReData.success(policyManagementService.getPolicyManagement(req));
    }

    @Operation(summary = "预警导出")
    @ApiOperationSupport(order = 22)
    @PostMapping("export/warning")
    public void exportWarningRemind(HttpServletResponse response, @RequestBody @Validated WyWorkPlatformPageReq req) throws IOException {
        List<WarningRemindListVo> list = wyWorkPlatformService.pageWorkPlatformRemindByAuth(req).getRecords();
        String sheetName = "预警提醒列表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(new ThreeTableVo(
                new ThreeTableVo.TopTitleOneVo(sheetName, null, null),
                new ThreeTableVo.MidContentTwoVo(WarningRemindListVo.class, list)));
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, sheetName, sheetName);
    }

}
