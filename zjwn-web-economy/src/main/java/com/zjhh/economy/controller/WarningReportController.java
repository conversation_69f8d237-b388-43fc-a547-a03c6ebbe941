package com.zjhh.economy.controller;

import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.response.ReData;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.report.ReportGuidReq;
import com.zjhh.economy.request.report.WarningReportPageReq;
import com.zjhh.economy.request.report.WarningReportUploadReq;
import com.zjhh.economy.service.WarningReportService;
import com.zjhh.economy.vo.report.WarningReportListVo;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022-05-25 3:44 下午
 */
@Slf4j
@Tag(name = "预警报告分析")
@RestController
@RequestMapping("warning/report")
@ApiSupport(order = 1202)
public class WarningReportController extends BaseController {

    @Resource
    private WarningReportService warningReportService;

    @Operation(summary = "报告列表")
    @ApiOperationSupport(order = 1)
    @PostMapping("page")
    public ReData<Page<WarningReportListVo>> getWarningReportList(@RequestBody @Validated WarningReportPageReq req) {
        return ReData.success(warningReportService.listWarningReport(req));
    }

    @Operation(summary = "上传报告")
    @ApiOperationSupport(order = 2)
    @PostMapping("upload/report")
    public ReData<String> uploadReport(@RequestBody @Validated WarningReportUploadReq req) {
        warningReportService.uploadReport(req);
        return ReData.success();
    }

    @Operation(summary = "删除报告")
    @ApiOperationSupport(order = 3)
    @PostMapping("delete/report")
    public ReData<String> deleteReport(@RequestBody @Validated ReportGuidReq report) {
        warningReportService.deleteReport(report.getGuid());
        return ReData.success();
    }


}


