package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.easyexcel.CustomSheetStrategy;
import com.zjhh.comm.easyexcel.EasyExcelUtil;
import com.zjhh.comm.easyexcel.ThreeTableVo;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.ColumnVO;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.request.BuildingArchiveReq;
import com.zjhh.economy.request.BuildingConditionReq;
import com.zjhh.economy.request.QueryReportColumnReq;
import com.zjhh.economy.request.SaveReportColumnReq;
import com.zjhh.economy.request.report.BuildingSettleEntBasicReq;
import com.zjhh.economy.request.report.EntBusinessInfoReq;
import com.zjhh.economy.request.report.ReportDateReq;
import com.zjhh.economy.service.ReportService;
import com.zjhh.economy.vo.BuildingConditionVo;
import com.zjhh.economy.vo.ReportResultVo;
import com.zjhh.economy.vo.ReportSummaryVo;
import com.zjhh.economy.vo.report.*;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "统计报表")
@ApiSupport(order = 2007)
@SaCheckLogin
@RestController
@RequestMapping("report")
public class ReportController extends BaseController {
    @Resource
    private ReportService reportService;



    @Operation(summary = "楼宇项目情况")
    @ApiOperationSupport(order = 1)
    @PostMapping("building/condition")
    public ReData<ReportResultVo> getReportResult(@Validated @RequestBody BuildingConditionReq req) {
        return ReData.success(reportService.pageBuildingCondition(req));
    }

    @Operation(summary = "楼宇项目情况-查询存储字段")
    @ApiOperationSupport(order = 2)
    @PostMapping("condition/column")
    public ReData<List<ReportSummaryVo>> listQueryColumns(@Validated @RequestBody QueryReportColumnReq req) {
        return ReData.success(reportService.listReportSummaryColumnByBuildingCondition(req));
    }

    @Operation(summary = "楼宇项目情况-保存字段")
    @ApiOperationSupport(order = 3)
    @PostMapping("save/column")
    public ReData<String> saveReportColumn(@Validated @RequestBody SaveReportColumnReq req) {
        reportService.saveReportColumn(req);
        return ReData.success();
    }

    @Operation(summary = "一楼一档-存储字段查询")
    @ApiOperationSupport(order = 4)
    @PostMapping("archive/column")
    public ReData<List<ReportSummaryVo>> listArchiveColumn(@Validated @RequestBody QueryReportColumnReq req) {
        return ReData.success(reportService.listReportSummaryColumnByBuildingArchive(req));
    }


    @Operation(summary = "一楼一档-保存字段")
    @ApiOperationSupport(order = 5)
    @PostMapping("save/column/archive")
    public ReData<String> saveReportColumnArchive(@Validated @RequestBody SaveReportColumnReq req) {
        reportService.saveReportColumnByArchive(req);
        return ReData.success();
    }

    @Operation(summary = "一楼一档")
    @ApiOperationSupport(order = 6)
    @PostMapping("page/archives")
    public ReData<ReportResultVo> getReportResultbyArchivce(@Validated @RequestBody BuildingArchiveReq req) {
        return ReData.success(reportService.pageBuildingArchives(req));
    }

    @Operation(summary = "楼宇项目情况导出")
    @ApiOperationSupport(order = 7)
    @PostMapping("export/condition")
    public void exportBuildingCondition(HttpServletResponse response, @Validated @RequestBody BuildingConditionReq req) throws IOException {
        ReportResultVo maps  = reportService.exportBuildingCondition(req);

        List<Map<String,Object>> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(maps.getBuildingConditions().getRecords())) {
            maps.getBuildingConditions().getRecords().forEach(record -> {
                list.add(BeanUtil.beanToMap(record));
            });
        }
        List<ColumnVO> columnVOS = reportService.exportColumnVO(1);
        List<List<String>> headList = EasyExcelUtil.columnsToExcelHead(columnVOS);
        List<List<Object>> dataList = EasyExcelUtil.tableDataToList(columnVOS,list);
        String title = "楼宇项目情况";

        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(new ThreeTableVo.TopTitleOneVo(title, null),
                        new ThreeTableVo.MidContentTwoVo(headList, dataList))
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title,null);
    }

    @Operation(summary = "一楼一档导出")
    @ApiOperationSupport(order = 7)
    @PostMapping("export/archive")
    public void exportBuildingArchive(HttpServletResponse response, @Validated @RequestBody BuildingArchiveReq req) throws IOException {
        ReportResultVo maps  = reportService.pageBuildingArchives(req);
        List<Map<String,Object>> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(maps.getBuildingArchives().getRecords())) {
            maps.getBuildingArchives().getRecords().forEach(record -> {
                list.add(BeanUtil.beanToMap(record));
            });
        }
        List<ColumnVO> columnVOS = reportService.exportColumnVO(2);
        List<List<String>> headList = EasyExcelUtil.columnsToExcelHead(columnVOS);
        List<List<Object>> dataList = EasyExcelUtil.tableDataToList(columnVOS,list);
        String title = "一楼一档";

        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(new ThreeTableVo.TopTitleOneVo(title, null),
                        new ThreeTableVo.MidContentTwoVo(headList, dataList))
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title,null);
    }


    @Operation(summary = "商务楼宇入驻单位基本情况表")
    @ApiOperationSupport(order = 9)
    @PostMapping("page/swlyrz")
    public ReData<Page<BuildingSettledEntBasicVo>> listSwlyrz(@Validated @RequestBody BuildingSettleEntBasicReq req) {
        return ReData.success(reportService.listBuildingSettledEntBasic(req));
    }


    @Operation(summary = "商务楼宇入驻单位基本情况表导出")
    @ApiOperationSupport(order = 10)
    @PostMapping("export/swlyrz")
    public void exportSwlyrz(HttpServletResponse response, @Validated @RequestBody BuildingSettleEntBasicReq req) throws IOException {
        Page<BuildingSettledEntBasicVo> list = reportService.listBuildingSettledEntBasic(req);
        String title = "商务楼宇入驻单位基本情况表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(new ThreeTableVo.TopTitleOneVo(title, null),
                        new ThreeTableVo.MidContentTwoVo(BuildingSettledEntBasicVo.class, list.getRecords()))
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title,null);
    }


    @Operation(summary = "商务楼宇调查基本情况表")
    @ApiOperationSupport(order = 11)
    @PostMapping("page/swlydc")
    public ReData<Page<BuildingInvestigationVo>> listSwlydc(@Validated @RequestBody ReportDateReq req) {
        return ReData.success(reportService.listBuildingInvestigationVo(req));
    }


    @Operation(summary = "商务楼宇调查基本情况表导出")
    @ApiOperationSupport(order = 12)
    @PostMapping("export/swlydc")
    public void exportSwlyrz(HttpServletResponse response, @Validated @RequestBody ReportDateReq req) throws IOException {
        Page<BuildingInvestigationVo> list = reportService.listBuildingInvestigationVo(req);
        String title = "商务楼宇调查基本情况表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(new ThreeTableVo.TopTitleOneVo(title, null),
                        new ThreeTableVo.MidContentTwoVo(BuildingInvestigationVo.class, list.getRecords()))
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title,null);
    }

    @Operation(summary = "楼宇动态信息表")
    @ApiOperationSupport(order = 13)
    @PostMapping("page/lydtxx")
    public ReData<Page<BuildingDynamicInfoVo>> listLydtxx(@Validated @RequestBody ReportDateReq req) {
        return ReData.success(reportService.listBuildingDynamicInfoVo(req));
    }


    @Operation(summary = "楼宇动态信息表导出")
    @ApiOperationSupport(order = 14)
    @PostMapping("export/lydtxx")
    public void exportLydtxx(HttpServletResponse response, @Validated @RequestBody ReportDateReq req) throws IOException {
        Page<BuildingDynamicInfoVo> list = reportService.listBuildingDynamicInfoVo(req);

        String title = "楼宇动态表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(new ThreeTableVo.TopTitleOneVo(title, null),
                        new ThreeTableVo.MidContentTwoVo(BuildingDynamicInfoVo.class, list.getRecords()))
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title,null);
    }


    @Operation(summary = "楼宇动态分析表")
    @ApiOperationSupport(order = 15)
    @PostMapping("page/lydtfx")
    public ReData<Page<BuildingDynamicAnalyzeVo>> listLydtfx(@Validated @RequestBody ReportDateReq req) {
        return ReData.success(reportService.listBuildingDynamicAnalyzeVo(req));
    }


    @Operation(summary = "楼宇动态分析表导出")
    @ApiOperationSupport(order = 16)
    @PostMapping("export/lydtfx")
    public void exportLydtxxfx(HttpServletResponse response, @Validated @RequestBody ReportDateReq req) throws IOException {
        Page<BuildingDynamicAnalyzeVo> list = reportService.listBuildingDynamicAnalyzeVo(req);
        String title = "楼宇动态分析表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(new ThreeTableVo.TopTitleOneVo(title, null),
                        new ThreeTableVo.MidContentTwoVo(BuildingDynamicAnalyzeVo.class, list.getRecords()))
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title,null);
    }


    @Operation(summary = "企业工商信息表")
    @ApiOperationSupport(order = 17)
    @PostMapping("page/qygsxx")
    public ReData<Page<EntBusinessInfoVo>> listQygsxx(@Validated @RequestBody EntBusinessInfoReq req) {
        return ReData.success(reportService.listEntBusinessInfoVo(req));
    }


    @Operation(summary = "企业工商信息表导出")
    @ApiOperationSupport(order = 18)
    @PostMapping("export/qygsxx")
    public void exportQygsxx(HttpServletResponse response, @Validated @RequestBody EntBusinessInfoReq req) throws IOException {
        Page<EntBusinessInfoVo> list = reportService.listEntBusinessInfoVo(req);
        String title = "企业工商信息表";
        CustomSheetStrategy customSheetStrategy = new CustomSheetStrategy(
                new ThreeTableVo(new ThreeTableVo.TopTitleOneVo(title, null),
                        new ThreeTableVo.MidContentTwoVo(EntBusinessInfoVo.class, list.getRecords()))
        );
        EasyExcelUtil.exportOneSheetExcel(response, customSheetStrategy, title, title,null);
    }


    @Operation(summary = "楼宇名称下拉框")
    @ApiOperationSupport(order = 19)
    @PostMapping("menu/building")
    public ReData<List<TreeSelectVo>> listBuildingMenu() {
        return ReData.success(reportService.listBuildingMenu());
    }

    @Operation(summary = "机构类型")
    @ApiOperationSupport(order = 20)
    @PostMapping("menu/institution")
    public ReData<List<TreeSelectVo>> listInstitutionMenu() {
        return ReData.success(reportService.listInstitutionMenu());
    }

    @Operation(summary = "企业经营状态")
    @ApiOperationSupport(order = 21)
    @PostMapping("menu/entStatus")
    public ReData<List<TreeSelectVo>> listEntStatusMenu() {
        return ReData.success(reportService.listEntStatusMenu());
    }

    @Operation(summary = "变更情况")
    @ApiOperationSupport(order = 21)
    @PostMapping("menu/changeSituation")
    public ReData<List<TreeSelectVo>> listChangeSituationMenu() {
        return ReData.success(reportService.listChangeSituationMenu());
    }

    @Operation(summary = "企业类型")
    @ApiOperationSupport(order = 21)
    @PostMapping("menu/entType")
    public ReData<List<TreeSelectVo>> listEntTypeMenu() {
        return ReData.success(reportService.listTreeEntTypeMenu());
    }

    @Operation(summary = "行业")
    @ApiOperationSupport(order = 21)
    @PostMapping("menu/industry")
    public ReData<List<TreeSelectVo>> listTreeIndustryMenu() {
        return ReData.success(reportService.listTreeIndustryMenu());
    }

    @Operation(summary = "楼宇项目情况合计")
    @ApiOperationSupport(order = 25)
    @PostMapping("building/condition/total")
    public ReData<BuildingConditionVo> getBuildingConditionTotal(@Validated @RequestBody BuildingConditionReq req) {
        return ReData.success(reportService.getBuildingConditionTotal(req));
    }



}
