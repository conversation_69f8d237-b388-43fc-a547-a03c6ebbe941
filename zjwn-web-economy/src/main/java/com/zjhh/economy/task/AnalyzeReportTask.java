package com.zjhh.economy.task;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.economy.dao.entity.AdsDmsReportPage;
import com.zjhh.economy.dao.mapper.AdsDmsReportPageMapper;
import com.zjhh.economy.request.earlywarning.CreateReportDocReq;
import com.zjhh.economy.service.EarlyWarningRuleService;
import jakarta.annotation.Resource;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.util.Collections;

/**
 * 分析报告定时任务类
 *
 * <AUTHOR>
 * @since 2022/5/23 17:51
 */
public class AnalyzeReportTask extends QuartzJobBean {

    @Resource
    private EarlyWarningRuleService earlyWarningRuleService;

    @Resource
    private AdsDmsReportPageMapper adsDmsReportPageMapper;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
        CreateReportDocReq req = new CreateReportDocReq();
        req.setType(1);
        LocalDateTime date = LocalDateTime.now().plusMonths(-1);
        req.setDatekey(DateUtil.format(date, DatePattern.SIMPLE_MONTH_PATTERN));
        String reportTypeCode = jobDataMap.getString("reportTypeCode");
        req.setReportTypeCodes(Collections.singletonList(reportTypeCode));
        QueryWrapper<AdsDmsReportPage> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsReportPage::getReportTypeCode, reportTypeCode);
        if (adsDmsReportPageMapper.selectCount(wrapper) > 0) {
            earlyWarningRuleService.createReportDoc(req);
        }
    }
}
