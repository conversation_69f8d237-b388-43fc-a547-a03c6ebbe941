package com.zjhh.economy.task;


import com.zjhh.economy.service.AnalyzeCockpitService;
import com.zjhh.economy.service.BusinessService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@EnableScheduling
public class WorkPlatformTask {

    @Resource
    private BusinessService businessService;

    @Resource
    private AnalyzeCockpitService analyzeCockpitService;


    @Scheduled(cron="0 0 0 * * ?")
    void warningTask() {
        businessService.generationWarning();
    }

    @Scheduled(cron="0 0 0 * * ?")
    void dataBriefTask() {
        businessService.generationDataBrief();
    }

    @Scheduled(cron="0 0 0 * * ?")
    void economicIndicator() {
        analyzeCockpitService.generationBuildingScore();
    }
}
