package com.zjhh.economy.onlyoffice.template.model;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 文本填充数据模型
 * 用于封装文本参数的数据源和字段值
 */
@Data
@Builder
public class TextFillData {
    /**
     * 数据源名称（可选，为空时使用根级字段）
     */
    private String dataSource;
    
    /**
     * 字段值映射
     * key: 字段名称，value: 字段值
     */
    private Map<String, Object> fields;
    
    /**
     * 是否启用数据格式化（数字、日期等）
     */
    @Builder.Default
    private boolean enableFormatting = true;
    
    /**
     * 创建单个字段的文本填充数据
     */
    public static TextFillData createSingleField(String dataSource, String fieldName, Object value) {
        return TextFillData.builder()
                .dataSource(dataSource)
                .fields(Map.of(fieldName, value))
                .build();
    }
    
    /**
     * 获取所有字段数据（向下兼容）
     */
    public Map<String, Object> getData() {
        return fields;
    }
}