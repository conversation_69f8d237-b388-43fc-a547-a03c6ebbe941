package com.zjhh.economy.onlyoffice.template.model;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 模板填充数据容器
 * 统一管理所有类型的填充数据
 */
@Data
@Builder
public class TemplateFillData {
    /**
     * 文本填充数据映射
     * key: 参数组名称（如"text_param_1", "text_param_2"），value: 文本填充数据
     */
    @Builder.Default
    private Map<String, TextFillData> textData = new HashMap<>();
    
    /**
     * 表格填充数据映射
     * key: 参数组名称（如"table_1", "table_2"），value: 表格填充数据
     */
    @Builder.Default
    private Map<String, TableFillData> tableData = new HashMap<>();
    
    /**
     * 图表填充数据映射
     * key: 参数组名称（如"chart_1", "chart_2"），value: 图表填充数据
     */
    @Builder.Default
    private Map<String, ChartFillData> chartData = new HashMap<>();
    
    /**
     * 添加文本填充数据
     */
    public TemplateFillData addTextData(String groupName, TextFillData data) {
        this.textData.put(groupName, data);
        return this;
    }
    
    /**
     * 添加表格填充数据
     */
    public TemplateFillData addTableData(String groupName, TableFillData data) {
        this.tableData.put(groupName, data);
        return this;
    }
    
    /**
     * 添加图表填充数据
     */
    public TemplateFillData addChartData(String groupName, ChartFillData data) {
        this.chartData.put(groupName, data);
        return this;
    }
    
    /**
     * 创建空的填充数据容器
     */
    public static TemplateFillData empty() {
        return TemplateFillData.builder().build();
    }
    
    /**
     * 获取文本数据映射表
     */
    public Map<String, TextFillData> getTextDataMap() {
        return textData;
    }
    
    /**
     * 获取表格数据映射表
     */
    public Map<String, TableFillData> getTableDataMap() {
        return tableData;
    }
    
    /**
     * 获取图表数据映射表
     */
    public Map<String, ChartFillData> getChartDataMap() {
        return chartData;
    }

}