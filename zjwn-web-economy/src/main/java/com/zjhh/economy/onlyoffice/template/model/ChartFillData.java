package com.zjhh.economy.onlyoffice.template.model;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 图表填充数据模型
 * 用于封装图表参数的完整数据结构
 */
@Data
@Builder
public class ChartFillData {
    /**
     * 数据源名称
     */
    private String dataSource;
    
    /**
     * 图表类别标签
     */
    private List<String> categories;
    
    /**
     * 数据序列列表
     */
    private List<ChartSeries> series;
    
    /**
     * 图表标题（可选）
     */
    private String title;
    
    /**
     * 图表类型（可选，如column、line、pie等）
     */
    private String chartType;
    
    /**
     * 获取序列数量
     */
    public int getSeriesCount() {
        return series != null ? series.size() : 0;
    }
    
    /**
     * 获取类别数量
     */
    public int getCategoryCount() {
        return categories != null ? categories.size() : 0;
    }
    
    /**
     * 获取指定索引的序列
     */
    public ChartSeries getSeries(int index) {
        if (series != null && index >= 0 && index < series.size()) {
            return series.get(index);
        }
        return null;
    }
    
    /**
     * 获取指定索引的类别
     */
    public String getCategory(int index) {
        if (categories != null && index >= 0 && index < categories.size()) {
            return categories.get(index);
        }
        return null;
    }
    
    /**
     * 根据名称查找序列
     */
    public ChartSeries findSeriesByName(String name) {
        if (series == null || name == null) {
            return null;
        }
        return series.stream()
                .filter(s -> name.equals(s.getName()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 验证图表数据完整性
     */
    public boolean validateData() {
        if (categories == null || categories.isEmpty() || 
            series == null || series.isEmpty()) {
            return false;
        }
        
        int categoryCount = categories.size();
        for (ChartSeries chartSeries : series) {
            if (!chartSeries.isValid() || chartSeries.getValueCount() != categoryCount) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 检查是否有有效数据
     */
    public boolean hasData() {
        return categories != null && !categories.isEmpty() && 
               series != null && !series.isEmpty();
    }
    
    /**
     * 创建图表填充数据
     */
    public static ChartFillData create(String dataSource, List<String> categories, List<ChartSeries> series) {
        return ChartFillData.builder()
                .dataSource(dataSource)
                .categories(categories)
                .series(series)
                .build();
    }
    
    /**
     * 创建带标题的图表填充数据
     */
    public static ChartFillData createWithTitle(String dataSource, List<String> categories, 
                                               List<ChartSeries> series, String title) {
        return ChartFillData.builder()
                .dataSource(dataSource)
                .categories(categories)
                .series(series)
                .title(title)
                .build();
    }
    
    /**
     * 创建完整的图表填充数据
     */
    public static ChartFillData createComplete(String dataSource, List<String> categories, 
                                             List<ChartSeries> series, String title, String chartType) {
        return ChartFillData.builder()
                .dataSource(dataSource)
                .categories(categories)
                .series(series)
                .title(title)
                .chartType(chartType)
                .build();
    }
}