package com.zjhh.economy.onlyoffice.template.parser;

import com.zjhh.economy.onlyoffice.template.exception.TemplateProcessException;
import com.zjhh.economy.onlyoffice.template.model.ParameterType;
import com.zjhh.economy.onlyoffice.template.model.TemplateParameter;
import com.zjhh.economy.onlyoffice.template.model.TemplateParseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Word模板参数解析器 (基于Apache POI)
 * 使用纯POI API进行参数解析，完全替换Aspose.Words
 */
@Component
@Slf4j
public class WordTemplateParameterParser {

    private static final Pattern SIMPLE_PARAM_PATTERN = Pattern.compile("\\{\\{([^#/][^}]*)}}");

    /**
     * 从输入流解析模板文档，提取所有参数
     */
    public TemplateParseResult parseTemplate(InputStream inputStream) {
        try (XWPFDocument document = new XWPFDocument(inputStream)) {
            return parseTemplate(document);
        } catch (IOException e) {
            log.error("读取模板文件失败", e);
            throw new TemplateProcessException("读取模板文件失败", e);
        }
    }

    /**
     * 解析模板文档，提取所有参数
     *
     * @param document POI Word文档对象
     * @return 参数解析结果
     */
    public TemplateParseResult parseTemplate(XWPFDocument document) {
        try {
            List<TemplateParameter> parameters = new ArrayList<>();

            // 解析文本参数
            extractTextParameters(document, parameters);

            // 解析表格参数
            extractTableParameters(document, parameters);

            // 解析图表参数
            extractChartParameters(document, parameters);

            return new TemplateParseResult(parameters);
        } catch (Exception e) {
            log.error("模板解析失败", e);
            throw new TemplateProcessException("模板解析失败", e);
        }
    }

    /**
     * 提取文本参数 - 每个参数创建独立分组，避免数据源混淆
     */
    private void extractTextParameters(XWPFDocument document, List<TemplateParameter> parameters) {
        Set<String> processedParams = new HashSet<>();
        int textParamIndex = 0;

        // 遍历所有段落
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            // 检查段落是否在表格中
            if (isInTable(paragraph)) {
                continue; // 跳过表格中的段落，这些由表格参数处理
            }

            String text = paragraph.getText();
            Matcher matcher = SIMPLE_PARAM_PATTERN.matcher(text);

            while (matcher.find()) {
                String paramName = matcher.group(1).trim();

                // 避免重复处理相同的参数
                if (processedParams.contains(paramName)) {
                    continue;
                }
                processedParams.add(paramName);

                // 为每个参数创建独立的参数组
                textParamIndex++;
                DataSourceInfo dataSourceInfo = parseDataSourceInfo(List.of(paramName));

                parameters.add(TemplateParameter.builder()
                        .groupName("text_param_" + textParamIndex)
                        .type(ParameterType.TEXT)
                        .location("文本段落")
                        .dataSource(dataSourceInfo.dataSource)
                        .fieldNames(dataSourceInfo.fieldNames)
                        .build());
                log.debug("发现文本参数: {}, 数据源={}", paramName, dataSourceInfo.dataSource);
            }
        }
    }

    /**
     * 检查段落是否在表格中
     */
    private boolean isInTable(XWPFParagraph paragraph) {
        // POI中，如果段落在表格中，它的父容器会是XWPFTableCell
        try {
            return paragraph.getPartType() == null || paragraph.getPartType().toString().contains("tableCell");
        } catch (Exception e) {
            log.debug("检查段落是否在表格中时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 提取表格参数 - 按表格分组，每个表格的参数放在一起
     */
    private void extractTableParameters(XWPFDocument document, List<TemplateParameter> parameters) {
        List<XWPFTable> tables = document.getTables();

        for (int tableIndex = 0; tableIndex < tables.size(); tableIndex++) {
            XWPFTable table = tables.get(tableIndex);
            List<String> tableParams = new ArrayList<>();

            // 遍历表格的所有行和单元格
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    String cellText = cell.getText();
                    Matcher matcher = SIMPLE_PARAM_PATTERN.matcher(cellText);

                    while (matcher.find()) {
                        String paramName = matcher.group(1).trim();
                        if (!tableParams.contains(paramName)) {
                            tableParams.add(paramName);
                        }
                    }
                }
            }

            // 如果表格中有参数，创建一个表格参数组
            if (!tableParams.isEmpty()) {
                DataSourceInfo dataSourceInfo = parseDataSourceInfo(tableParams);

                parameters.add(TemplateParameter.builder()
                        .groupName("table_" + (tableIndex + 1))
                        .type(ParameterType.TABLE)
                        .location("表格" + (tableIndex + 1))
                        .dataSource(dataSourceInfo.dataSource)
                        .fieldNames(dataSourceInfo.fieldNames)
                        .build());
                log.debug("发现表格参数组: table_{}, 数据源={}, 字段={}", tableIndex + 1, dataSourceInfo.dataSource, dataSourceInfo.fieldNames);
            }
        }
    }

    /**
     * 提取图表参数 - 按图表分组，每个图表的参数放在一起
     */
    private void extractChartParameters(XWPFDocument document, List<TemplateParameter> parameters) {
        // 获取所有图表
        List<XWPFChart> charts = document.getCharts();
        
        for (int chartIndex = 0; chartIndex < charts.size(); chartIndex++) {
            XWPFChart chart = charts.get(chartIndex);
            List<String> chartParams = new ArrayList<>();

            // 解析图表中的参数
            extractParametersFromChart(chart, chartParams, chartIndex);

            // 如果图表中有参数，创建一个图表参数组
            if (!chartParams.isEmpty()) {
                DataSourceInfo dataSourceInfo = parseDataSourceInfo(chartParams);

                parameters.add(TemplateParameter.builder()
                        .groupName("chart_" + (chartIndex + 1))
                        .type(ParameterType.CHART)
                        .location("图表" + (chartIndex + 1))
                        .dataSource(dataSourceInfo.dataSource)
                        .fieldNames(dataSourceInfo.fieldNames)
                        .build());
                log.debug("发现图表参数组: chart_{}, 数据源={}, 字段={}", chartIndex + 1, dataSourceInfo.dataSource, dataSourceInfo.fieldNames);
            }
        }
    }

    /**
     * 从图表中提取参数
     */
    private void extractParametersFromChart(XWPFChart chart, List<String> chartParams, int chartIndex) {
        try {
            CTChart ctChart = chart.getCTChart();
            
            // 解析图表标题
            if (ctChart.getTitle() != null && ctChart.getTitle().getTx() != null) {
                String titleText = extractTextFromCTTx(ctChart.getTitle().getTx());
                extractParametersFromText(titleText, chartParams);
            }

            // 解析图表的绘图区域
            CTPlotArea plotArea = ctChart.getPlotArea();
            if (plotArea != null) {
                // 解析各种图表类型的系列数据
                extractParametersFromPlotArea(plotArea, chartParams, chartIndex);
            }

            log.debug("图表{}参数提取完成，找到参数: {}", chartIndex + 1, chartParams);

        } catch (Exception e) {
            log.error("解析图表{}参数时出错: {}", chartIndex + 1, e.getMessage(), e);
        }
    }

    /**
     * 从绘图区域提取参数
     */
    private void extractParametersFromPlotArea(CTPlotArea plotArea, List<String> chartParams, int chartIndex) {
        // 柱状图
        if (plotArea.getBarChartList() != null) {
            for (CTBarChart barChart : plotArea.getBarChartList()) {
                extractParametersFromBarChart(barChart, chartParams);
            }
        }

        // 折线图
        if (plotArea.getLineChartList() != null) {
            for (CTLineChart lineChart : plotArea.getLineChartList()) {
                extractParametersFromLineChart(lineChart, chartParams);
            }
        }

        // 饼图
        if (plotArea.getPieChartList() != null) {
            for (CTPieChart pieChart : plotArea.getPieChartList()) {
                extractParametersFromPieChart(pieChart, chartParams);
            }
        }

        // 面积图
        if (plotArea.getAreaChartList() != null) {
            for (CTAreaChart areaChart : plotArea.getAreaChartList()) {
                extractParametersFromAreaChart(areaChart, chartParams);
            }
        }

        // 散点图
        if (plotArea.getScatterChartList() != null) {
            for (CTScatterChart scatterChart : plotArea.getScatterChartList()) {
                extractParametersFromScatterChart(scatterChart, chartParams);
            }
        }

        // 雷达图
        if (plotArea.getRadarChartList() != null) {
            for (CTRadarChart radarChart : plotArea.getRadarChartList()) {
                extractParametersFromRadarChart(radarChart, chartParams);
            }
        }

        // 环形图
        if (plotArea.getDoughnutChartList() != null) {
            for (CTDoughnutChart doughnutChart : plotArea.getDoughnutChartList()) {
                extractParametersFromDoughnutChart(doughnutChart, chartParams);
            }
        }

        // 特殊处理组合图表（第8个图表）
        if (chartIndex == 7) {
            log.info("组合图表(chart_8)详细分析 - 当前找到的参数: {}", chartParams);
            // 组合图表可能包含多种图表类型，需要全面检查
            extractParametersFromCombinationChart(plotArea, chartParams);
        }
    }

    /**
     * 从柱状图提取参数
     */
    private void extractParametersFromBarChart(CTBarChart barChart, List<String> chartParams) {
        for (CTBarSer series : barChart.getSerList()) {
            // 提取系列名称参数
            if (series.getTx() != null) {
                String seriesName = extractTextFromCTSerTx(series.getTx());
                extractParametersFromText(seriesName, chartParams);
            }

            // 提取类别轴参数
            if (series.getCat() != null && series.getCat().getStrRef() != null) {
                String catRef = series.getCat().getStrRef().getF();
                extractParametersFromExcelRef(catRef, chartParams);
            }

            // 提取数值轴参数
            if (series.getVal() != null && series.getVal().getNumRef() != null) {
                String valRef = series.getVal().getNumRef().getF();
                extractParametersFromExcelRef(valRef, chartParams);
            }
        }
    }

    /**
     * 从折线图提取参数
     */
    private void extractParametersFromLineChart(CTLineChart lineChart, List<String> chartParams) {
        for (CTLineSer series : lineChart.getSerList()) {
            if (series.getTx() != null) {
                String seriesName = extractTextFromCTSerTx(series.getTx());
                extractParametersFromText(seriesName, chartParams);
            }

            if (series.getCat() != null && series.getCat().getStrRef() != null) {
                String catRef = series.getCat().getStrRef().getF();
                extractParametersFromExcelRef(catRef, chartParams);
            }

            if (series.getVal() != null && series.getVal().getNumRef() != null) {
                String valRef = series.getVal().getNumRef().getF();
                extractParametersFromExcelRef(valRef, chartParams);
            }
        }
    }

    /**
     * 从饼图提取参数
     */
    private void extractParametersFromPieChart(CTPieChart pieChart, List<String> chartParams) {
        for (CTPieSer series : pieChart.getSerList()) {
            if (series.getTx() != null) {
                String seriesName = extractTextFromCTSerTx(series.getTx());
                extractParametersFromText(seriesName, chartParams);
            }

            if (series.getCat() != null && series.getCat().getStrRef() != null) {
                String catRef = series.getCat().getStrRef().getF();
                extractParametersFromExcelRef(catRef, chartParams);
            }

            if (series.getVal() != null && series.getVal().getNumRef() != null) {
                String valRef = series.getVal().getNumRef().getF();
                extractParametersFromExcelRef(valRef, chartParams);
            }
        }
    }

    /**
     * 从面积图提取参数
     */
    private void extractParametersFromAreaChart(CTAreaChart areaChart, List<String> chartParams) {
        for (CTAreaSer series : areaChart.getSerList()) {
            if (series.getTx() != null) {
                String seriesName = extractTextFromCTSerTx(series.getTx());
                extractParametersFromText(seriesName, chartParams);
            }

            if (series.getCat() != null && series.getCat().getStrRef() != null) {
                String catRef = series.getCat().getStrRef().getF();
                extractParametersFromExcelRef(catRef, chartParams);
            }

            if (series.getVal() != null && series.getVal().getNumRef() != null) {
                String valRef = series.getVal().getNumRef().getF();
                extractParametersFromExcelRef(valRef, chartParams);
            }
        }
    }

    /**
     * 从散点图提取参数
     */
    private void extractParametersFromScatterChart(CTScatterChart scatterChart, List<String> chartParams) {
        for (CTScatterSer series : scatterChart.getSerList()) {
            if (series.getTx() != null) {
                String seriesName = extractTextFromCTSerTx(series.getTx());
                extractParametersFromText(seriesName, chartParams);
            }

            if (series.getXVal() != null && series.getXVal().getNumRef() != null) {
                String xRef = series.getXVal().getNumRef().getF();
                extractParametersFromExcelRef(xRef, chartParams);
            }

            if (series.getYVal() != null && series.getYVal().getNumRef() != null) {
                String yRef = series.getYVal().getNumRef().getF();
                extractParametersFromExcelRef(yRef, chartParams);
            }
        }
    }

    /**
     * 从雷达图提取参数
     */
    private void extractParametersFromRadarChart(CTRadarChart radarChart, List<String> chartParams) {
        for (CTRadarSer series : radarChart.getSerList()) {
            if (series.getTx() != null) {
                String seriesName = extractTextFromCTSerTx(series.getTx());
                extractParametersFromText(seriesName, chartParams);
            }

            if (series.getCat() != null && series.getCat().getStrRef() != null) {
                String catRef = series.getCat().getStrRef().getF();
                extractParametersFromExcelRef(catRef, chartParams);
            }

            if (series.getVal() != null && series.getVal().getNumRef() != null) {
                String valRef = series.getVal().getNumRef().getF();
                extractParametersFromExcelRef(valRef, chartParams);
            }
        }
    }

    /**
     * 从环形图提取参数
     */
    private void extractParametersFromDoughnutChart(CTDoughnutChart doughnutChart, List<String> chartParams) {
        for (CTPieSer series : doughnutChart.getSerList()) {
            if (series.getTx() != null) {
                String seriesName = extractTextFromCTSerTx(series.getTx());
                extractParametersFromText(seriesName, chartParams);
            }

            if (series.getCat() != null && series.getCat().getStrRef() != null) {
                String catRef = series.getCat().getStrRef().getF();
                extractParametersFromExcelRef(catRef, chartParams);
            }

            if (series.getVal() != null && series.getVal().getNumRef() != null) {
                String valRef = series.getVal().getNumRef().getF();
                extractParametersFromExcelRef(valRef, chartParams);
            }
        }
    }

    /**
     * 处理组合图表的特殊情况
     */
    private void extractParametersFromCombinationChart(CTPlotArea plotArea, List<String> chartParams) {
        log.info("开始处理组合图表，当前参数列表: {}", chartParams);
        
        // 确保所有图表类型都被检查到
        int totalSeries = 0;
        
        if (plotArea.getBarChartList() != null) {
            for (CTBarChart barChart : plotArea.getBarChartList()) {
                totalSeries += barChart.getSerList().size();
            }
        }
        
        if (plotArea.getLineChartList() != null) {
            for (CTLineChart lineChart : plotArea.getLineChartList()) {
                totalSeries += lineChart.getSerList().size();
            }
        }
        
        log.info("组合图表总系列数: {}", totalSeries);
        
        // 如果参数数量少于预期，尝试从Excel工作表中提取
        if (chartParams.size() < 3) {
            log.warn("组合图表参数数量不足，尝试其他方式提取: 当前{}个，期望至少3个", chartParams.size());
            // 这里可以添加更多的参数提取逻辑
        }
    }

    /**
     * 从CTSerTx提取文本内容
     */
    private String extractTextFromCTSerTx(CTSerTx serTx) {
        StringBuilder text = new StringBuilder();
        
        if (serTx.getStrRef() != null && serTx.getStrRef().getStrCache() != null) {
            // 从缓存的字符串引用中提取
            for (CTStrVal strVal : serTx.getStrRef().getStrCache().getPtList()) {
                text.append(strVal.getV());
            }
        }
        
        if (serTx.getV() != null) {
            // 直接文本值
            text.append(serTx.getV());
        }
        
        return text.toString();
    }

    /**
     * 从CTTx提取文本内容
     */
    private String extractTextFromCTTx(CTTx tx) {
        StringBuilder text = new StringBuilder();
        
        if (tx.getStrRef() != null && tx.getStrRef().getStrCache() != null) {
            for (CTStrVal strVal : tx.getStrRef().getStrCache().getPtList()) {
                text.append(strVal.getV());
            }
        }
        
        if (tx.getRich() != null) {
            // 富文本内容 - 修复类型转换问题
            try {
                // 富文本解析可能需要特殊处理，暂时跳过
                log.debug("跳过富文本内容解析，避免类型转换问题");
            } catch (Exception e) {
                log.debug("富文本解析失败: {}", e.getMessage());
            }
        }
        
        return text.toString();
    }

    /**
     * 从Excel引用字符串中提取参数
     */
    private void extractParametersFromExcelRef(String excelRef, List<String> chartParams) {
        if (excelRef != null && !excelRef.isEmpty()) {
            Matcher matcher = SIMPLE_PARAM_PATTERN.matcher(excelRef);
            while (matcher.find()) {
                String paramName = matcher.group(1).trim();
                if (!chartParams.contains(paramName)) {
                    chartParams.add(paramName);
                    log.debug("从Excel引用中发现参数: {}", paramName);
                }
            }
        }
    }

    /**
     * 从文本中提取参数
     */
    private void extractParametersFromText(String text, List<String> chartParams) {
        if (text != null && !text.isEmpty()) {
            Matcher matcher = SIMPLE_PARAM_PATTERN.matcher(text);
            while (matcher.find()) {
                String paramName = matcher.group(1).trim();
                if (!chartParams.contains(paramName)) {
                    chartParams.add(paramName);
                    log.debug("从文本中发现参数: {}", paramName);
                }
            }
        }
    }

    /**
     * 解析参数列表中的数据源信息
     */
    private DataSourceInfo parseDataSourceInfo(List<String> parameterNames) {
        String dataSource = null;
        List<String> fieldNames = new ArrayList<>();

        for (String paramName : parameterNames) {
            if (paramName.contains(".")) {
                // 包含数据源格式：数据源名.字段名
                String[] parts = paramName.split("\\.", 2);
                String currentDataSource = parts[0];
                String fieldName = parts[1];

                // 确保同一组参数使用相同的数据源
                if (dataSource == null) {
                    dataSource = currentDataSource;
                } else if (!dataSource.equals(currentDataSource)) {
                    log.warn("参数组中发现不同数据源: {} vs {}, 使用第一个数据源", dataSource, currentDataSource);
                }

                fieldNames.add(fieldName);
            } else {
                // 不包含数据源格式，直接作为字段名
                fieldNames.add(paramName);
            }
        }

        return new DataSourceInfo(dataSource, fieldNames);
    }

    /**
     * 数据源信息内部类
     */
    private static class DataSourceInfo {
        final String dataSource;
        final List<String> fieldNames;

        DataSourceInfo(String dataSource, List<String> fieldNames) {
            this.dataSource = dataSource;
            this.fieldNames = fieldNames;
        }
    }
}