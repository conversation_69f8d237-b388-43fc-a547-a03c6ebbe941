package com.zjhh.economy.onlyoffice.template.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 模板参数定义
 */
@Data
@Builder
public class TemplateParameter {
    /**
     * 参数组名称（用于标识和区分不同的表格或图表，如：text_params, table_1, table_2, chart_1, chart_2）
     */
    private String groupName;
    
    /**
     * 参数类型
     */
    private ParameterType type;
    
    /**
     * 参数位置描述
     */
    private String location;
    
    /**
     * 数据源名称（从参数中提取的数据源，如：数据源1, 数据源2）
     */
    private String dataSource;
    
    /**
     * 该组中包含的字段名称列表（去除数据源前缀后的字段名）
     */
    private List<String> fieldNames;
}