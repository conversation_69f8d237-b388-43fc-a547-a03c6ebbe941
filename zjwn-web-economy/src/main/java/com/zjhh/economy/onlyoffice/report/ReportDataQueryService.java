package com.zjhh.economy.onlyoffice.report;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zjhh.economy.dao.entity.AdsDmsReportData;
import com.zjhh.economy.dao.mapper.AdsDmsReportDataMapper;
import com.zjhh.economy.onlyoffice.report.dto.ChartDataResult;
import com.zjhh.economy.onlyoffice.template.model.ChartSeries;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告数据查询服务
 */
@Slf4j
@Service
public class ReportDataQueryService {

    @Resource
    private AdsDmsReportDataMapper reportDataMapper;

    @Resource
    private ReportDataSourceTranslator translator;

    /**
     * 查询文本参数数据(单条记录)
     *
     * @param reportTypeCode 报告类型编码
     * @param dataSetName    业务数据名称
     * @param columnName     字段名称
     * @return 字段值
     */
    public Object queryTextParameterValue(String reportTypeCode, String datekey, String dataSetName, String columnName) {
        try {
            // 1. 翻译业务名称为编码
            String dataTypeCode = translator.translateDataSourceName(reportTypeCode, dataSetName);
            if (!StringUtils.hasText(dataTypeCode)) {
                return null;
            }

            String columnKey = translator.translateColumnName(reportTypeCode, dataTypeCode, columnName);
            if (!StringUtils.hasText(columnKey)) {
                return null;
            }

            // 2. 查询数据表第一条记录
            LambdaQueryWrapper<AdsDmsReportData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AdsDmsReportData::getReportTypeCode, reportTypeCode)
                    .eq(AdsDmsReportData::getDataTypeCode, dataTypeCode)
                    .eq(AdsDmsReportData::getDatekey, datekey)
                    .orderByAsc(AdsDmsReportData::getId)
                    .last("LIMIT 1");

            AdsDmsReportData reportData = reportDataMapper.selectOne(wrapper);
            if (reportData == null) {
                log.warn("未找到数据: reportTypeCode={}, dataTypeCode={}", reportTypeCode, dataTypeCode);
                return null;
            }

            // 3. 根据columnKey返回对应的data字段值
            Object value = getFieldValue(reportData, columnKey);
            log.debug("文本参数查询成功: {}.{} = {}", dataSetName, columnName, value);
            return value;

        } catch (Exception e) {
            log.error("查询文本参数失败: reportTypeCode={}, dataSetName={}, columnName={}",
                    reportTypeCode, dataSetName, columnName, e);
            return null;
        }
    }

    /**
     * 查询表格参数数据(多条记录)
     *
     * @param reportTypeCode 报告类型编码
     * @param dataSetName    业务数据名称
     * @param columnNames    字段名称列表
     * @return 表格数据列表
     */
    public List<Map<String, Object>> queryTableParameterData(String reportTypeCode, String datekey, String dataSetName,
                                                             List<String> columnNames) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 1. 翻译所有字段名称
            String dataTypeCode = translator.translateDataSourceName(reportTypeCode, dataSetName);
            if (!StringUtils.hasText(dataTypeCode)) {
                return result;
            }

            Map<String, String> columnKeyMap = translator.translateColumnNames(reportTypeCode, dataTypeCode, columnNames);
            if (columnKeyMap.isEmpty()) {
                return result;
            }

            // 2. 查询所有相关数据
            LambdaQueryWrapper<AdsDmsReportData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AdsDmsReportData::getReportTypeCode, reportTypeCode)
                    .eq(AdsDmsReportData::getDataTypeCode, dataTypeCode)
                    .eq(AdsDmsReportData::getDatekey, datekey)
                    .orderByAsc(AdsDmsReportData::getId);

            List<AdsDmsReportData> reportDataList = reportDataMapper.selectList(wrapper);
            if (reportDataList.isEmpty()) {
                log.warn("未找到表格数据: reportTypeCode={}, dataTypeCode={}", reportTypeCode, dataTypeCode);
                return result;
            }

            // 3. 将结果按字段名称映射返回
            for (AdsDmsReportData reportData : reportDataList) {
                Map<String, Object> row = new HashMap<>();
                boolean hasData = false;

                for (String columnName : columnNames) {
                    String columnKey = columnKeyMap.get(columnName);
                    if (columnKey != null) {
                        Object value = getFieldValue(reportData, columnKey);
                        row.put(columnName, value);
                        if (value != null && StringUtils.hasText(value.toString())) {
                            hasData = true;
                        }
                    }
                }

                // 只添加有效数据行
                if (hasData) {
                    result.add(row);
                }
            }

            log.debug("表格参数查询成功: {} -> {} 行数据", dataSetName, result.size());
            return result;

        } catch (Exception e) {
            log.error("查询表格参数失败: reportTypeCode={}, dataSetName={}, columnNames={}",
                    reportTypeCode, dataSetName, columnNames, e);
            return result;
        }
    }

    /**
     * 查询图表参数数据
     *
     * @param reportTypeCode 报告类型编码
     * @param dataSetName    业务数据名称
     * @param columnNames    字段名称列表
     * @return 图表数据结果
     */
    public ChartDataResult queryChartParameterData(String reportTypeCode, String datekey, String dataSetName,
                                                   List<String> columnNames) {
        try {
            // 1. 翻译所有字段名称
            String dataTypeCode = translator.translateDataSourceName(reportTypeCode, dataSetName);
            if (!StringUtils.hasText(dataTypeCode)) {
                log.warn("数据源翻译失败: {}", dataSetName);
                return ChartDataResult.builder().build();
            }

            Map<String, String> columnKeyMap = translator.translateColumnNames(reportTypeCode, dataTypeCode, columnNames);
            if (columnKeyMap.isEmpty()) {
                log.warn("字段翻译失败: {}, columns={}", dataSetName, columnNames);
                return ChartDataResult.builder().build();
            }

            // 2. 查询所有相关数据
            LambdaQueryWrapper<AdsDmsReportData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AdsDmsReportData::getReportTypeCode, reportTypeCode)
                    .eq(AdsDmsReportData::getDataTypeCode, dataTypeCode)
                    .eq(AdsDmsReportData::getDatekey, datekey)
                    .orderByAsc(AdsDmsReportData::getId);

            List<AdsDmsReportData> reportDataList = reportDataMapper.selectList(wrapper);
            if (reportDataList.isEmpty()) {
                log.warn("未找到图表数据: reportTypeCode={}, dataTypeCode={}", reportTypeCode, dataTypeCode);
                return ChartDataResult.builder().build();
            }

            // 3. 构建图表数据结构
            List<String> categories = reportDataList.stream()
                    .map(data -> StringUtils.hasText(data.getCategory()) ? data.getCategory() : data.getDatekey())
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            List<ChartSeries> series = new ArrayList<>();
            for (String columnName : columnNames) {
                String columnKey = columnKeyMap.get(columnName);
                if (columnKey != null) {
                    List<Double> values = new ArrayList<>();

                    for (AdsDmsReportData reportData : reportDataList) {
                        Object value = getFieldValue(reportData, columnKey);
                        Double numValue = convertToDouble(value);
                        values.add(numValue != null ? numValue : 0.0);
                    }

                    if (!values.isEmpty()) {
                        ChartSeries chartSeries = ChartSeries.create(columnName, values);
                        series.add(chartSeries);
                    }
                }
            }

            ChartDataResult result = ChartDataResult.builder()
                    .categories(categories)
                    .series(series)
                    .title(dataSetName + "图表")
                    .build();

            log.debug("图表参数查询成功: {} -> {} 个类别, {} 个系列",
                    dataSetName, categories.size(), series.size());
            return result;

        } catch (Exception e) {
            log.error("查询图表参数失败: reportTypeCode={}, dataSetName={}, columnNames={}",
                    reportTypeCode, dataSetName, columnNames, e);
            return ChartDataResult.builder().build();
        }
    }

    /**
     * 通过反射获取字段值
     */
    private Object getFieldValue(AdsDmsReportData reportData, String columnKey) {
        try {
            Field field = AdsDmsReportData.class.getDeclaredField(columnKey);
            field.setAccessible(true);
            return field.get(reportData);
        } catch (Exception e) {
            log.warn("获取字段值失败: {}", columnKey);
            return null;
        }
    }

    /**
     * 转换为Double类型
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }

            String strValue = value.toString().trim();
            if (!StringUtils.hasText(strValue)) {
                return null;
            }

            return Double.parseDouble(strValue);

        } catch (NumberFormatException e) {
            log.debug("数值转换失败: {}", value);
            return null;
        }
    }
}