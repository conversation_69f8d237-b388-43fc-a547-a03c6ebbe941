package com.zjhh.economy.onlyoffice.template.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 解析结果
 */
@Data
@AllArgsConstructor
public class TemplateParseResult {
    private List<TemplateParameter> parameters;
    
    /**
     * 获取所有文本参数
     */
    public List<TemplateParameter> getTextParameters() {
        return parameters.stream()
                .filter(p -> p.getType() == ParameterType.TEXT)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有表格参数
     */
    public List<TemplateParameter> getTableParameters() {
        return parameters.stream()
                .filter(p -> p.getType() == ParameterType.TABLE)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有图表参数
     */
    public List<TemplateParameter> getChartParameters() {
        return parameters.stream()
                .filter(p -> p.getType() == ParameterType.CHART)
                .collect(Collectors.toList());
    }
}