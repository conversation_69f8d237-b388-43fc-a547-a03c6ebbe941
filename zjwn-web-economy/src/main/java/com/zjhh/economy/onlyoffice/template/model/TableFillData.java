package com.zjhh.economy.onlyoffice.template.model;

import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 表格填充数据模型
 * 用于封装表格参数的数据源和行数据
 */
@Data
@Builder
public class TableFillData {
    /**
     * 数据源名称
     */
    private String dataSource;
    
    /**
     * 表格行数据列表
     * 每个元素代表一行数据，支持Map或Java对象
     */
    private List<Map<String, Object>> rows;
    
    /**
     * 字段名称列表（用于验证数据完整性）
     */
    private List<String> fieldNames;
    
    /**
     * 是否启用数据格式化
     */
    @Builder.Default
    private boolean enableFormatting = true;
    
    /**
     * 获取行数
     */
    public int getRowCount() {
        return rows != null ? rows.size() : 0;
    }
    
    /**
     * 获取指定行的数据
     */
    public Map<String, Object> getRow(int index) {
        if (rows != null && index >= 0 && index < rows.size()) {
            return rows.get(index);
        }
        return null;
    }
    
    /**
     * 获取指定行指定字段的值
     */
    public Object getCellValue(int rowIndex, String fieldName) {
        Map<String, Object> row = getRow(rowIndex);
        return row != null ? row.get(fieldName) : null;
    }
    
    /**
     * 检查数据完整性（所有行都包含指定的字段）
     */
    public boolean validateData() {
        if (rows == null || rows.isEmpty() || fieldNames == null || fieldNames.isEmpty()) {
            return false;
        }
        
        for (Map<String, Object> row : rows) {
            for (String fieldName : fieldNames) {
                if (!row.containsKey(fieldName)) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 获取字段数量
     */
    public int getFieldCount() {
        return fieldNames != null ? fieldNames.size() : 0;
    }
    
    /**
     * 检查是否包含指定字段
     */
    public boolean hasField(String fieldName) {
        return fieldNames != null && fieldNames.contains(fieldName);
    }
    
    /**
     * 创建表格填充数据
     */
    public static TableFillData create(String dataSource, List<Map<String, Object>> rows) {
        return TableFillData.builder()
                .dataSource(dataSource)
                .rows(rows)
                .build();
    }
    
    /**
     * 创建带字段验证的表格填充数据
     */
    public static TableFillData createWithFields(String dataSource, List<Map<String, Object>> rows, List<String> fieldNames) {
        return TableFillData.builder()
                .dataSource(dataSource)
                .rows(rows)
                .fieldNames(fieldNames)
                .build();
    }
}