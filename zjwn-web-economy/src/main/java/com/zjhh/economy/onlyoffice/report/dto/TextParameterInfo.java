package com.zjhh.economy.onlyoffice.report.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文本参数信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TextParameterInfo {
    /**
     * 参数组名称
     */
    private String groupName;
    
    /**
     * 字段名称
     */
    private String columnName;
    
    /**
     * 数据源名称
     */
    private String dataSourceName;
    
    /**
     * 原始参数文本（如：{{销售数据.产品名称}}）
     */
    private String originalParameter;
    
    /**
     * 默认值
     */
    private Object defaultValue;
    
    public TextParameterInfo(String groupName, String columnName) {
        this.groupName = groupName;
        this.columnName = columnName;
    }
    
    public TextParameterInfo(String groupName, String columnName, String dataSourceName) {
        this.groupName = groupName;
        this.columnName = columnName;
        this.dataSourceName = dataSourceName;
    }
    
    @Override
    public String toString() {
        return String.format("TextParam{group='%s', column='%s', dataSource='%s'}", 
                           groupName, columnName, dataSourceName);
    }
}