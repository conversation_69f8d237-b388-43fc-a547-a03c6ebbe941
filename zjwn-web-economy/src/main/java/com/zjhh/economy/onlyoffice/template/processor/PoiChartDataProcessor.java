package com.zjhh.economy.onlyoffice.template.processor;

import com.zjhh.economy.onlyoffice.template.model.ChartFillData;
import com.zjhh.economy.onlyoffice.template.model.ChartSeries;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFChart;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Apache POI图表数据处理器
 * 负责使用POI替换Word文档中的图表数据
 * <p>
 * 支持的图表类型：
 * - 柱状图 (Bar Chart)
 * - 折线图 (Line Chart)
 * - 饼图 (Pie Chart)
 */
@Component
@Slf4j
public class PoiChartDataProcessor {

    /**
     * 处理图表数据（新的统一接口）
     *
     * @param chart      POI图表对象
     * @param chartData  图表填充数据
     * @param chartIndex 图表索引
     */
    public void processChart(XWPFChart chart, ChartFillData chartData, int chartIndex) {
        try {
            updateChartDataSource(chart, chartData);
        } catch (Exception e) {
            log.error("POI图表{}数据处理失败", chartIndex + 1, e);
            throw new RuntimeException("POI图表数据处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Apache POI替换图表数据（旧接口，保持兼容性）
     *
     * @param chart     POI图表对象
     * @param chartData 图表填充数据
     */
    public void replaceChartDataWithPoi(XWPFChart chart, ChartFillData chartData) {
        try {
            updateChartDataSource(chart, chartData);
        } catch (Exception e) {
            log.error("POI图表数据替换失败", e);
            throw new RuntimeException("POI图表数据替换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新Word文档中图表的数据源
     *
     * <p>核心实现原理：</p>
     * <ul>
     *   <li>1. 更新图表背后的Excel数据源，不修改图表结构</li>
     *   <li>2. Word会自动根据数据源和现有图表类型进行渲染</li>
     *   <li>3. 支持所有图表类型：柱形图、折线图、饼图、条形图、面积图、散点图、雷达图、组合图</li>
     *   <li>4. Excel数据格式：A1空白，B1-开始为系列名，A2-开始为分类名，B2-开始为数据</li>
     * </ul>
     *
     * @param chart     POI图表对象
     * @param chartData 图表填充数据
     */
    private void updateChartDataSource(XWPFChart chart, ChartFillData chartData) {
        CTChart ctChart = chart.getCTChart();

        // 1. 更新图表背后的Excel数据源（这是核心功能）
        updateChartExcelDataSource(chart, chartData);

        // 2. 更新图表标题
        if (chartData.getTitle() != null && !chartData.getTitle().trim().isEmpty()) {
            updateChartTitle(ctChart, chartData.getTitle());
        }

        // 3. 强制刷新图表缓存
        forceChartRefresh(ctChart, chartData);
    }

    /**
     * 更新图表的Excel数据源
     *
     * <p>通过查找并更新嵌入的Excel工作簿来更新图表数据。
     * 如果更新失败，会记录错误但不影响程序继续执行。</p>
     *
     * @param chart     POI图表对象
     * @param chartData 图表填充数据
     */
    private void updateChartExcelDataSource(XWPFChart chart, ChartFillData chartData) {
        try {
            // 方法1：查找并更新嵌入的Excel工作簿
            updateEmbeddedWorkbook(chart, chartData);

        } catch (Exception e) {
            log.error("EmbeddedPackagePart更新异常，使用备用方案: {}", e.getMessage());
            // 备用方案：继续处理，不影响图表显示
        }
    }

    /**
     * 更新嵌入的Excel工作簿
     *
     * <p>基于poi-tl的实现原理，遍历图表的所有关系，查找Excel工作簿并更新数据。
     * 支持两种类型的Excel关系：</p>
     * <ul>
     *   <li>直接的Excel工作表关系 (spreadsheetml.sheet)</li>
     *   <li>嵌入式包关系 (oleObject/Embedded)</li>
     * </ul>
     *
     * @param chart     POI图表对象
     * @param chartData 图表填充数据
     * @throws Exception 当更新过程中发生错误时抛出异常
     */
    private void updateEmbeddedWorkbook(XWPFChart chart, ChartFillData chartData) throws Exception {
        // 遍历图表的所有关系，查找Excel工作簿
        for (org.apache.poi.ooxml.POIXMLDocumentPart relation : chart.getRelations()) {
            String contentType = relation.getPackagePart().getContentType();
            // 查找Excel工作表类型的关系
            if (contentType.contains("spreadsheetml.sheet") || contentType.contains("application/vnd.ms-excel")) {
                updateExcelRelation(relation, chartData);
                return;
            }

            // 查找嵌入式包类型
            if (contentType.contains("oleObject") || relation.getClass().getSimpleName().contains("Embedded")) {
                updateEmbeddedRelation(relation, chartData);
                return;
            }
        }
    }

    /**
     * 更新Excel关系中的数据
     */
    private void updateExcelRelation(org.apache.poi.ooxml.POIXMLDocumentPart relation, ChartFillData chartData) throws Exception {
        try (java.io.InputStream inputStream = relation.getPackagePart().getInputStream()) {

            // 读取现有的Excel工作簿
            org.apache.poi.xssf.usermodel.XSSFWorkbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook(inputStream);

            // 更新工作簿数据
            updateWorkbookData(workbook, chartData);

            // 将更新后的工作簿写回关系中
            try (java.io.OutputStream outputStream = relation.getPackagePart().getOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
            }

            workbook.close();
        } catch (Exception e) {
            log.error("更新Excel关系数据失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 更新嵌入式关系中的数据
     */
    private void updateEmbeddedRelation(org.apache.poi.ooxml.POIXMLDocumentPart relation, ChartFillData chartData) {
        try {
            // 这里需要特殊处理嵌入式对象
            // 由于嵌入式对象可能是OLE格式，需要谨慎处理
            java.io.InputStream inputStream = relation.getPackagePart().getInputStream();

            // 尝试读取为Excel格式
            byte[] data = inputStream.readAllBytes();
            inputStream.close();

            // 检查是否是Excel数据
            if (isExcelData(data)) {
                java.io.ByteArrayInputStream bais = new java.io.ByteArrayInputStream(data);
                org.apache.poi.xssf.usermodel.XSSFWorkbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook(bais);

                updateWorkbookData(workbook, chartData);

                // 写回更新后的数据
                java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
                workbook.write(baos);

                try (java.io.OutputStream outputStream = relation.getPackagePart().getOutputStream()) {
                    outputStream.write(baos.toByteArray());
                    outputStream.flush();
                }

                workbook.close();
                baos.close();
                bais.close();
            }

        } catch (Exception e) {
            log.error("更新嵌入式关系数据失败: {}", e.getMessage());
        }
    }

    /**
     * 检查数据是否是Excel格式
     */
    private boolean isExcelData(byte[] data) {
        if (data == null || data.length < 4) {
            return false;
        }

        // 检查XLSX文件的魔数标识 (ZIP格式)
        return data[0] == 0x50 && data[1] == 0x4B && data[2] == 0x03 && data[3] == 0x04;
    }

    /**
     * 更新工作簿中的实际数据
     *
     * <p>将图表数据按照Word图表的标准格式写入Excel工作表：</p>
     * <ul>
     *   <li>A1单元格保持空白</li>
     *   <li>B1开始为系列名称</li>
     *   <li>A2开始为分类名称</li>
     *   <li>B2开始为数据值</li>
     * </ul>
     * <p>同时启用强制重算以确保数据更新生效。</p>
     *
     * @param workbook  Excel工作簿对象
     * @param chartData 图表填充数据
     */
    private void updateWorkbookData(org.apache.poi.xssf.usermodel.XSSFWorkbook workbook, ChartFillData chartData) {
        // 获取或创建第一个工作表
        org.apache.poi.xssf.usermodel.XSSFSheet sheet;
        if (workbook.getNumberOfSheets() > 0) {
            sheet = workbook.getSheetAt(0);
            // 清空现有数据
            clearSheetData(sheet);
        } else {
            sheet = workbook.createSheet("Chart Data");
        }

        // 写入新数据
        writeChartDataToSheet(sheet, chartData);

        // 强制重新计算和标记为已修改
        try {
            workbook.setForceFormulaRecalculation(true);
            sheet.setForceFormulaRecalculation(true);
        } catch (Exception e) {
            log.warn("设置强制重算失败: {}", e.getMessage());
        }
    }

    /**
     * 清空工作表数据
     */
    private void clearSheetData(org.apache.poi.xssf.usermodel.XSSFSheet sheet) {
        // 从最后一行开始删除，避免索引问题
        for (int i = sheet.getLastRowNum(); i >= 0; i--) {
            org.apache.poi.ss.usermodel.Row row = sheet.getRow(i);
            if (row != null) {
                sheet.removeRow(row);
            }
        }
    }

    /**
     * 将图表数据写入工作表
     */
    private void writeChartDataToSheet(org.apache.poi.xssf.usermodel.XSSFSheet sheet, ChartFillData chartData) {
        List<String> categories = chartData.getCategories();
        List<ChartSeries> seriesList = chartData.getSeries();
        // 第一行：标题行
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
        // A1单元格留空，这是Word图表的标准格式
        // 不需要为A1设置任何值，让它保持真正的空白


        // 写入系列名称作为列标题 - 从B1开始
        for (int i = 0; i < seriesList.size(); i++) {
            ChartSeries series = seriesList.get(i);
            if (series.isValid()) {
                int colIndex = i + 1; // B=1, C=2, D=3
                headerRow.createCell(colIndex).setCellValue(series.getName());

            }
        }

        // 写入分类和数据
        for (int rowIndex = 0; rowIndex < categories.size(); rowIndex++) {
            org.apache.poi.ss.usermodel.Row dataRow = sheet.createRow(rowIndex + 1);

            // A列：分类名称
            dataRow.createCell(0).setCellValue(categories.get(rowIndex));


            // B列及以后：各系列的数据值
            for (int seriesIndex = 0; seriesIndex < seriesList.size(); seriesIndex++) {
                ChartSeries series = seriesList.get(seriesIndex);
                if (series.isValid() && rowIndex < series.getValues().size()) {
                    int colIndex = seriesIndex + 1; // B=1, C=2, D=3
                    double value = series.getValues().get(rowIndex);
                    dataRow.createCell(colIndex).setCellValue(value);

                }
            }
        }

        // 自动调整列宽
        for (int col = 0; col <= seriesList.size(); col++) {
            try {
                sheet.autoSizeColumn(col);
            } catch (Exception e) {
                log.error("自动调整列宽失败，列: {}", col);
            }
        }
    }

    /**
     * 更新图表标题
     */
    private void updateChartTitle(CTChart ctChart, String title) {
        try {
            CTTitle ctTitle = ctChart.getTitle();
            if (ctTitle == null) {
                ctTitle = ctChart.addNewTitle();
            }

            CTTx ctTx = ctTitle.getTx();
            if (ctTx == null) {
                ctTx = ctTitle.addNewTx();
            }

            org.openxmlformats.schemas.drawingml.x2006.main.CTTextBody textBody = ctTx.getRich();
            if (textBody == null) {
                textBody = ctTx.addNewRich();
            }

            // 清除现有段落
            textBody.setPArray(new org.openxmlformats.schemas.drawingml.x2006.main.CTTextParagraph[0]);

            // 添加新的标题文本
            org.openxmlformats.schemas.drawingml.x2006.main.CTTextParagraph paragraph = textBody.addNewP();
            org.openxmlformats.schemas.drawingml.x2006.main.CTRegularTextRun textRun = paragraph.addNewR();
            textRun.setT(title);
        } catch (Exception e) {
            log.error("更新图表标题失败: {}", e.getMessage());
        }
    }

    /**
     * 强制刷新图表缓存
     * 更新图表中的缓存数据，确保Word显示最新的Excel数据
     */
    private void forceChartRefresh(CTChart ctChart, ChartFillData chartData) {
        try {
            // 获取绘图区域
            CTPlotArea plotArea = ctChart.getPlotArea();
            if (plotArea == null) {
                return;
            }

            // 更新所有图表类型的缓存数据
            updateAllChartSeriesCache(plotArea, chartData);
        } catch (Exception e) {
            log.error("强制刷新图表缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 更新所有图表系列的缓存数据
     */
    private void updateAllChartSeriesCache(CTPlotArea plotArea, ChartFillData chartData) {
        List<String> categories = chartData.getCategories();
        List<ChartSeries> seriesList = chartData.getSeries();

        // 更新柱状图缓存
        for (CTBarChart barChart : plotArea.getBarChartArray()) {
            for (int i = 0; i < barChart.getSerArray().length && i < seriesList.size(); i++) {
                CTBarSer ser = barChart.getSerArray()[i];
                ChartSeries seriesData = seriesList.get(i);
                if (seriesData.isValid()) {
                    updateBarSeriesCache(ser, seriesData, categories, i);
                }
            }
        }

        // 更新折线图缓存
        for (CTLineChart lineChart : plotArea.getLineChartArray()) {
            for (int i = 0; i < lineChart.getSerArray().length && i < seriesList.size(); i++) {
                CTLineSer ser = lineChart.getSerArray()[i];
                ChartSeries seriesData = seriesList.get(i);
                if (seriesData.isValid()) {
                    updateLineSeriesCache(ser, seriesData, categories, i);
                }
            }
        }

        // 更新饼图缓存
        for (CTPieChart pieChart : plotArea.getPieChartArray()) {
            if (pieChart.getSerArray().length > 0 && !seriesList.isEmpty()) {
                CTPieSer ser = pieChart.getSerArray()[0];
                ChartSeries seriesData = seriesList.getFirst();
                if (seriesData.isValid()) {
                    updatePieSeriesCache(ser, seriesData, categories);
                }
            }
        }

        // 更新面积图缓存
        for (CTAreaChart areaChart : plotArea.getAreaChartArray()) {
            for (int i = 0; i < areaChart.getSerArray().length && i < seriesList.size(); i++) {
                CTAreaSer ser = areaChart.getSerArray()[i];
                ChartSeries seriesData = seriesList.get(i);
                if (seriesData.isValid()) {
                    updateAreaSeriesCache(ser, seriesData, categories, i);
                }
            }
        }

        // 更新散点图缓存
        for (CTScatterChart scatterChart : plotArea.getScatterChartArray()) {
            for (int i = 0; i < scatterChart.getSerArray().length && i < seriesList.size(); i++) {
                CTScatterSer ser = scatterChart.getSerArray()[i];
                ChartSeries seriesData = seriesList.get(i);
                if (seriesData.isValid()) {
                    updateScatterSeriesCache(ser, seriesData, categories, i);
                }
            }
        }

        // 更新雷达图缓存
        for (CTRadarChart radarChart : plotArea.getRadarChartArray()) {
            for (int i = 0; i < radarChart.getSerArray().length && i < seriesList.size(); i++) {
                CTRadarSer ser = radarChart.getSerArray()[i];
                ChartSeries seriesData = seriesList.get(i);
                if (seriesData.isValid()) {
                    updateRadarSeriesCache(ser, seriesData, categories, i);
                }
            }
        }

        // 更新甜甜圈图缓存
        for (CTDoughnutChart doughnutChart : plotArea.getDoughnutChartArray()) {
            if (doughnutChart.getSerArray().length > 0 && !seriesList.isEmpty()) {
                CTPieSer ser = doughnutChart.getSerArray()[0];
                ChartSeries seriesData = seriesList.getFirst();
                if (seriesData.isValid()) {
                    updatePieSeriesCache(ser, seriesData, categories);
                }
            }
        }
    }

    /**
     * 更新柱状图系列缓存
     */
    private void updateBarSeriesCache(CTBarSer barSer, ChartSeries seriesData, List<String> categories, int seriesIndex) {
        updateStandardSeriesCache(
            () -> barSer.getTx() != null ? barSer.getTx().getStrRef() : null,
            () -> barSer.getCat() != null ? barSer.getCat().getStrRef() : null,
            () -> barSer.getVal() != null ? barSer.getVal().getNumRef() : null,
            seriesData, categories, seriesIndex);
    }

    /**
     * 更新折线图系列缓存
     */
    private void updateLineSeriesCache(CTLineSer lineSer, ChartSeries seriesData, List<String> categories, int seriesIndex) {
        updateStandardSeriesCache(
            () -> lineSer.getTx() != null ? lineSer.getTx().getStrRef() : null,
            () -> lineSer.getCat() != null ? lineSer.getCat().getStrRef() : null,
            () -> lineSer.getVal() != null ? lineSer.getVal().getNumRef() : null,
            seriesData, categories, seriesIndex);
    }

    /**
     * 更新饼图系列缓存
     */
    private void updatePieSeriesCache(CTPieSer pieSer, ChartSeries seriesData, List<String> categories) {
        // 更新系列名称缓存
        if (pieSer.getTx() != null && pieSer.getTx().getStrRef() != null) {
            updateSeriesNameCache(pieSer.getTx().getStrRef(), seriesData.getName());
        }

        // 更新分类缓存（饼图的各个扇形标签）- 不使用公式引用，直接缓存数据
        if (pieSer.getCat() != null && pieSer.getCat().getStrRef() != null) {
            CTStrData catStrData = pieSer.getCat().getStrRef().getStrCache();
            if (catStrData == null) {
                catStrData = pieSer.getCat().getStrRef().addNewStrCache();
            }
            catStrData.addNewPtCount().setVal(categories.size());

            // 清除现有数据点
            catStrData.setPtArray(new CTStrVal[0]);

            for (int j = 0; j < categories.size(); j++) {
                CTStrVal catVal = catStrData.addNewPt();
                catVal.setIdx(j);
                catVal.setV(categories.get(j));
            }
        }

        // 更新数值缓存 - 饼图不使用公式引用，直接缓存数据
        if (pieSer.getVal() != null && pieSer.getVal().getNumRef() != null) {
            CTNumData numData = pieSer.getVal().getNumRef().getNumCache();
            if (numData == null) {
                numData = pieSer.getVal().getNumRef().addNewNumCache();
            }
            numData.addNewPtCount().setVal(seriesData.getValues().size());

            // 清除现有数据点
            numData.setPtArray(new CTNumVal[0]);

            for (int j = 0; j < seriesData.getValues().size(); j++) {
                CTNumVal numVal = numData.addNewPt();
                numVal.setIdx(j);
                numVal.setV(String.valueOf(seriesData.getValues().get(j)));
            }
        }
    }

    /**
     * 更新面积图系列缓存
     */
    private void updateAreaSeriesCache(CTAreaSer areaSer, ChartSeries seriesData, List<String> categories, int seriesIndex) {
        updateStandardSeriesCache(
            () -> areaSer.getTx() != null ? areaSer.getTx().getStrRef() : null,
            () -> areaSer.getCat() != null ? areaSer.getCat().getStrRef() : null,
            () -> areaSer.getVal() != null ? areaSer.getVal().getNumRef() : null,
            seriesData, categories, seriesIndex);
    }

    /**
     * 更新雷达图系列缓存
     */
    private void updateRadarSeriesCache(CTRadarSer radarSer, ChartSeries seriesData, List<String> categories, int seriesIndex) {
        updateStandardSeriesCache(
            () -> radarSer.getTx() != null ? radarSer.getTx().getStrRef() : null,
            () -> radarSer.getCat() != null ? radarSer.getCat().getStrRef() : null,
            () -> radarSer.getVal() != null ? radarSer.getVal().getNumRef() : null,
            seriesData, categories, seriesIndex);
    }

    /**
     * 更新散点图系列缓存
     */
    private void updateScatterSeriesCache(CTScatterSer scatterSer, ChartSeries seriesData, List<String> categories, int seriesIndex) {
        // 更新系列名称缓存
        if (scatterSer.getTx() != null && scatterSer.getTx().getStrRef() != null) {
            updateSeriesNameCache(scatterSer.getTx().getStrRef(), seriesData.getName());
        }

        // 更新X轴数据缓存（散点图使用分类索引）
        if (scatterSer.getXVal() != null && scatterSer.getXVal().getNumRef() != null) {
            updateXAxisCache(scatterSer.getXVal().getNumRef(), categories.size());
        }

        // 更新Y轴数据缓存（系列数据值）
        if (scatterSer.getYVal() != null && scatterSer.getYVal().getNumRef() != null) {
            updateValueCache(scatterSer.getYVal().getNumRef(), seriesData.getValues(), seriesIndex, categories.size());
        }
    }

    /**
     * 通用的标准图表系列缓存更新方法（适用于柱状图、折线图、面积图、雷达图）
     * 
     * @param getTx 获取系列名称引用的函数
     * @param getCat 获取分类引用的函数  
     * @param getVal 获取数值引用的函数
     * @param seriesData 系列数据
     * @param categories 分类列表
     * @param seriesIndex 系列索引
     */
    private void updateStandardSeriesCache(
            java.util.function.Supplier<CTStrRef> getTx,
            java.util.function.Supplier<CTStrRef> getCat, 
            java.util.function.Supplier<CTNumRef> getVal,
            ChartSeries seriesData, 
            List<String> categories, 
            int seriesIndex) {
        
        // 更新系列名称缓存
        CTStrRef txRef = getTx.get();
        if (txRef != null) {
            updateSeriesNameCache(txRef, seriesData.getName());
        }

        // 更新分类缓存
        CTStrRef catRef = getCat.get();
        if (catRef != null) {
            updateCategoryCache(catRef, categories);
        }

        // 更新数值缓存
        CTNumRef valRef = getVal.get();
        if (valRef != null) {
            updateValueCache(valRef, seriesData.getValues(), seriesIndex, categories.size());
        }
    }

    /**
     * 通用的系列名称缓存更新方法
     * 
     * @param strRef 字符串引用对象
     * @param seriesName 系列名称
     */
    private void updateSeriesNameCache(CTStrRef strRef, String seriesName) {
        if (strRef == null) return;
        
        CTStrData strData = strRef.getStrCache();
        if (strData == null) {
            strData = strRef.addNewStrCache();
        }
        strData.addNewPtCount().setVal(1);
        
        // 清除现有数据点
        strData.setPtArray(new CTStrVal[0]);
        
        CTStrVal strVal = strData.addNewPt();
        strVal.setIdx(0);
        strVal.setV(seriesName);
    }
    
    /**
     * 通用的分类缓存更新方法
     * 
     * @param strRef 字符串引用对象
     * @param categories 分类列表
     */
    private void updateCategoryCache(CTStrRef strRef, List<String> categories) {
        if (strRef == null || categories == null || categories.isEmpty()) return;
        
        // 更新分类公式引用范围
        String catRange = String.format("Sheet1!$A$2:$A$%d", categories.size() + 1);
        strRef.setF(catRange);
        
        CTStrData catStrData = strRef.getStrCache();
        if (catStrData == null) {
            catStrData = strRef.addNewStrCache();
        }
        catStrData.addNewPtCount().setVal(categories.size());
        
        // 清除现有数据点
        catStrData.setPtArray(new CTStrVal[0]);
        
        for (int i = 0; i < categories.size(); i++) {
            CTStrVal catVal = catStrData.addNewPt();
            catVal.setIdx(i);
            catVal.setV(categories.get(i));
        }
    }
    
    /**
     * 通用的数值缓存更新方法
     * 
     * @param numRef 数值引用对象
     * @param values 数值列表
     * @param seriesIndex 系列索引
     * @param categoryCount 分类数量（用于计算范围）
     */
    private void updateValueCache(CTNumRef numRef, List<Double> values, int seriesIndex, int categoryCount) {
        if (numRef == null || values == null || values.isEmpty()) return;
        
        // 更新数值公式引用范围
        String colName = getColumnName(seriesIndex + 2);
        String valRange = String.format("Sheet1!$%s$2:$%s$%d", colName, colName, categoryCount + 1);
        numRef.setF(valRange);
        
        CTNumData numData = numRef.getNumCache();
        if (numData == null) {
            numData = numRef.addNewNumCache();
        }
        numData.addNewPtCount().setVal(values.size());
        
        // 清除现有数据点
        numData.setPtArray(new CTNumVal[0]);
        
        for (int i = 0; i < values.size(); i++) {
            CTNumVal numVal = numData.addNewPt();
            numVal.setIdx(i);
            numVal.setV(String.valueOf(values.get(i)));
        }
    }
    
    /**
     * 通用的X轴数值缓存更新方法（散点图专用）
     * 
     * @param numRef 数值引用对象
     * @param categoryCount 分类数量
     */
    private void updateXAxisCache(CTNumRef numRef, int categoryCount) {
        if (numRef == null || categoryCount <= 0) return;
        
        // 更新X轴公式引用范围
        String xRange = String.format("Sheet1!$A$2:$A$%d", categoryCount + 1);
        numRef.setF(xRange);
        
        CTNumData xNumData = numRef.getNumCache();
        if (xNumData == null) {
            xNumData = numRef.addNewNumCache();
        }
        xNumData.addNewPtCount().setVal(categoryCount);
        
        // 清除现有数据点
        xNumData.setPtArray(new CTNumVal[0]);
        
        for (int i = 0; i < categoryCount; i++) {
            CTNumVal xNumVal = xNumData.addNewPt();
            xNumVal.setIdx(i);
            xNumVal.setV(String.valueOf(i + 1)); // 转换为数值索引
        }
    }

    /**
     * 将数字转换为Excel列名（A, B, C, ..., Z, AA, AB, ...）
     * 
     * @param column 列索引（1=A, 2=B, 3=C, ...）
     * @return Excel列名
     */
    private String getColumnName(int column) {
        StringBuilder result = new StringBuilder();
        while (column > 0) {
            column--; // 转换为0基索引
            result.insert(0, (char) ('A' + (column % 26)));
            column /= 26;
        }
        return result.toString();
    }

}