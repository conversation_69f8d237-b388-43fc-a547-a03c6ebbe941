package com.zjhh.economy.onlyoffice.manager;

import cn.hutool.core.util.IdUtil;
import com.onlyoffice.manager.document.DefaultDocumentManager;
import com.onlyoffice.manager.settings.SettingsManager;
import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.dao.mapper.AdsDocumentMapper;
import com.zjhh.economy.onlyoffice.constant.OnlyOfficeConstant;

/**
 * <AUTHOR>
 * @since 2025/8/14 12:23
 */
public class DocumentManagerImpl extends DefaultDocumentManager {

    private final AdsDocumentMapper adsDocumentMapper;

    public DocumentManagerImpl(SettingsManager settingsManager, final AdsDocumentMapper adsDocumentMapper) {
        super(settingsManager);
        this.adsDocumentMapper = adsDocumentMapper;
    }

    @Override
    public String getDocumentKey(String fileId, boolean embedded) {
        return IdUtil.getSnowflakeNextIdStr();
    }

    @Override
    public String getDocumentName(String fileId) {
        if (fileId.startsWith(OnlyOfficeConstant.FILE_ID_TEMP)) {
            return IdUtil.getSnowflakeNextIdStr() + ".docx";
        } else {
            AdsDocument adsDocument = adsDocumentMapper.selectById(fileId);
            return adsDocument.getTitle();
        }
    }
}
