package com.zjhh.economy.onlyoffice.report;

import com.zjhh.economy.onlyoffice.report.dto.*;
import com.zjhh.economy.onlyoffice.template.model.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模板数据填充构建器
 */
@Slf4j
@Component
public class TemplateFillDataBuilder {

    @Resource
    private ReportDataQueryService queryService;

    /**
     * 构建完整的模板填充数据
     *
     * @param reportTypeCode 报告类型编码
     * @param datekey        日期参数
     * @param analysisResult 参数分析结果
     * @return 模板填充数据
     */
    public TemplateFillData buildFillData(String reportTypeCode, String datekey, ParameterAnalysisResult analysisResult) {
        if (!StringUtils.hasText(reportTypeCode)) {
            log.error("报告类型编码为空");
            return TemplateFillData.empty();
        }

        if (analysisResult == null || analysisResult.isEmpty()) {
            log.warn("参数分析结果为空");
            return TemplateFillData.empty();
        }

        TemplateFillData fillData = TemplateFillData.empty();

        try {
            log.info("开始构建模板填充数据: reportTypeCode={}, 参数总数={}",
                    reportTypeCode, analysisResult.getTotalParameterCount());

            // 1. 处理文本参数
            buildTextFillData(reportTypeCode, datekey, analysisResult.getTextParams(), fillData);

            // 2. 处理表格参数
            buildTableFillData(reportTypeCode, datekey, analysisResult.getTableParams(), fillData);

            // 3. 处理图表参数
            buildChartFillData(reportTypeCode, datekey, analysisResult.getChartParams(), fillData);
            return fillData;

        } catch (Exception e) {
            log.error("构建模板填充数据失败: reportTypeCode={}", reportTypeCode, e);
            return TemplateFillData.empty();
        }
    }

    /**
     * 构建文本填充数据
     */
    private void buildTextFillData(String reportTypeCode,
                                   String datekey,
                                   Map<String, List<TextParameterInfo>> textParams,
                                   TemplateFillData fillData) {

        if (textParams == null || textParams.isEmpty()) {
            return;
        }

        for (Map.Entry<String, List<TextParameterInfo>> entry : textParams.entrySet()) {
            String dataSourceName = entry.getKey();
            List<TextParameterInfo> paramList = entry.getValue();

            for (TextParameterInfo paramInfo : paramList) {
                try {
                    Object value = queryService.queryTextParameterValue(
                            reportTypeCode, datekey, dataSourceName, paramInfo.getColumnName());

                    // 处理空值
                    if (value == null) {
                        value = paramInfo.getDefaultValue() != null ?
                                paramInfo.getDefaultValue() : "";
                    }

                    TextFillData textData = TextFillData.createSingleField(
                            dataSourceName, paramInfo.getColumnName(), value);
                    fillData.addTextData(paramInfo.getGroupName(), textData);
                } catch (Exception e) {
                    log.error("处理文本参数失败: {}", paramInfo, e);
                    // 添加错误占位符
                    TextFillData errorData = TextFillData.createSingleField(
                            dataSourceName, paramInfo.getColumnName(), "[数据获取失败]");
                    fillData.addTextData(paramInfo.getGroupName(), errorData);
                }
            }
        }
    }

    /**
     * 构建表格填充数据
     */
    private void buildTableFillData(String reportTypeCode,
                                    String datekey,
                                    Map<String, List<TableParameterInfo>> tableParams,
                                    TemplateFillData fillData) {

        if (tableParams == null || tableParams.isEmpty()) {
            return;
        }

        for (Map.Entry<String, List<TableParameterInfo>> entry : tableParams.entrySet()) {
            String dataSourceName = entry.getKey();
            List<TableParameterInfo> paramList = entry.getValue();
            // 按表格索引分组
            Map<Integer, List<TableParameterInfo>> tableGroups = paramList.stream()
                    .collect(Collectors.groupingBy(
                            p -> p.getTableIndex() != null ? p.getTableIndex() : 1));

            for (Map.Entry<Integer, List<TableParameterInfo>> tableEntry : tableGroups.entrySet()) {
                Integer tableIndex = tableEntry.getKey();
                List<TableParameterInfo> tableParamList = tableEntry.getValue();

                try {
                    // 提取所有字段名称
                    List<String> columnNames = tableParamList.stream()
                            .map(TableParameterInfo::getColumnName)
                            .collect(Collectors.toList());

                    // 查询表格数据
                    List<Map<String, Object>> tableData = queryService.queryTableParameterData(
                            reportTypeCode, datekey, dataSourceName, columnNames);

                    if (tableData.isEmpty()) {
                        // 创建空表格数据
                        tableData = createEmptyTableData(columnNames);
                    }

                    // 构建表格填充数据
                    TableFillData tableFillData = TableFillData.builder()
                            .dataSource(dataSourceName)
                            .rows(tableData)
                            .fieldNames(columnNames)
                            .build();

                    // 使用第一个参数的组名作为表格组名
                    String groupName = tableParamList.getFirst().getGroupName();
                    fillData.addTableData(groupName, tableFillData);
                } catch (Exception e) {
                    log.error("处理表格参数失败: 数据源={}, 表格索引={}", dataSourceName, tableIndex, e);
                    // 添加错误表格数据
                    List<String> columnNames = tableParamList.stream()
                            .map(TableParameterInfo::getColumnName)
                            .collect(Collectors.toList());
                    List<Map<String, Object>> errorData = createErrorTableData(columnNames);

                    TableFillData errorTableData = TableFillData.builder()
                            .dataSource(dataSourceName)
                            .rows(errorData)
                            .fieldNames(columnNames)
                            .build();

                    String groupName = tableParamList.getFirst().getGroupName();
                    fillData.addTableData(groupName, errorTableData);
                }
            }
        }
    }

    /**
     * 构建图表填充数据
     */
    private void buildChartFillData(String reportTypeCode,
                                    String datekey,
                                    Map<String, List<ChartParameterInfo>> chartParams,
                                    TemplateFillData fillData) {

        if (chartParams == null || chartParams.isEmpty()) {
            return;
        }

        for (Map.Entry<String, List<ChartParameterInfo>> entry : chartParams.entrySet()) {
            String dataSourceName = entry.getKey();
            List<ChartParameterInfo> paramList = entry.getValue();
            // 按图表索引分组
            Map<Integer, List<ChartParameterInfo>> chartGroups = paramList.stream()
                    .collect(Collectors.groupingBy(
                            p -> p.getChartIndex() != null ? p.getChartIndex() : 1));

            for (Map.Entry<Integer, List<ChartParameterInfo>> chartEntry : chartGroups.entrySet()) {
                Integer chartIndex = chartEntry.getKey();
                List<ChartParameterInfo> chartParamList = chartEntry.getValue();

                try {
                    // 提取所有字段名称
                    List<String> columnNames = chartParamList.stream()
                            .map(ChartParameterInfo::getColumnName)
                            .collect(Collectors.toList());

                    // 查询图表数据
                    ChartDataResult chartResult = queryService.queryChartParameterData(
                            reportTypeCode, datekey, dataSourceName, columnNames);

                    if (chartResult.getCategories() == null || chartResult.getSeries() == null) {
                        // 创建空图表数据
                        chartResult = createEmptyChartData(dataSourceName, columnNames);
                    }

                    // 构建图表填充数据
                    ChartFillData chartFillData = ChartFillData.builder()
                            .dataSource(dataSourceName)
                            .categories(chartResult.getCategories())
                            .series(chartResult.getSeries())
                            .title(chartResult.getTitle())
                            .build();

                    // 使用第一个参数的组名作为图表组名
                    String groupName = chartParamList.getFirst().getGroupName();
                    fillData.addChartData(groupName, chartFillData);

                } catch (Exception e) {
                    log.error("处理图表参数失败: 数据源={}, 图表索引={}", dataSourceName, chartIndex, e);
                    // 添加错误图表数据
                    List<String> columnNames = chartParamList.stream()
                            .map(ChartParameterInfo::getColumnName)
                            .collect(Collectors.toList());
                    ChartDataResult errorChart = createErrorChartData(dataSourceName, columnNames);

                    ChartFillData errorChartData = ChartFillData.builder()
                            .dataSource(dataSourceName)
                            .categories(errorChart.getCategories())
                            .series(errorChart.getSeries())
                            .title(errorChart.getTitle())
                            .build();

                    String groupName = chartParamList.getFirst().getGroupName();
                    fillData.addChartData(groupName, errorChartData);
                }
            }
        }
    }

    /**
     * 创建空表格数据
     */
    private List<Map<String, Object>> createEmptyTableData(List<String> columnNames) {
        Map<String, Object> emptyRow = new HashMap<>();
        for (String columnName : columnNames) {
            emptyRow.put(columnName, "-");
        }
        return List.of(emptyRow);
    }

    /**
     * 创建错误表格数据
     */
    private List<Map<String, Object>> createErrorTableData(List<String> columnNames) {
        Map<String, Object> errorRow = new HashMap<>();
        for (String columnName : columnNames) {
            errorRow.put(columnName, "[数据获取失败]");
        }
        return List.of(errorRow);
    }

    /**
     * 创建空图表数据
     */
    private ChartDataResult createEmptyChartData(String dataSourceName, List<String> columnNames) {
        List<String> categories = List.of("无数据");
        List<ChartSeries> series = new ArrayList<>();

        for (String columnName : columnNames) {
            ChartSeries chartSeries = ChartSeries.create(columnName, List.of(0.0));
            series.add(chartSeries);
        }

        return ChartDataResult.builder()
                .categories(categories)
                .series(series)
                .title(dataSourceName + " - 暂无数据")
                .build();
    }

    /**
     * 创建错误图表数据
     */
    private ChartDataResult createErrorChartData(String dataSourceName, List<String> columnNames) {
        List<String> categories = List.of("数据异常");
        List<ChartSeries> series = new ArrayList<>();

        for (String columnName : columnNames) {
            ChartSeries chartSeries = ChartSeries.create(columnName + "(异常)", List.of(0.0));
            series.add(chartSeries);
        }

        return ChartDataResult.builder()
                .categories(categories)
                .series(series)
                .title(dataSourceName + " - 数据获取失败")
                .build();
    }

}