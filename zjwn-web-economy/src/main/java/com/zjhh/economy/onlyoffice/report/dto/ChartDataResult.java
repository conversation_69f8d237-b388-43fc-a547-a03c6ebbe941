package com.zjhh.economy.onlyoffice.report.dto;

import com.zjhh.economy.onlyoffice.template.model.ChartSeries;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图表数据查询结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChartDataResult {
    /**
     * 图表类别（X轴）
     */
    private List<String> categories;
    
    /**
     * 数据系列
     */
    private List<ChartSeries> series;
    
    /**
     * 图表标题
     */
    private String title;
    
    /**
     * X轴标题
     */
    private String xAxisTitle;
    
    /**
     * Y轴标题  
     */
    private String yAxisTitle;
}