package com.zjhh.economy.onlyoffice.template.model;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 图表数据序列
 */
@Data
@Builder
public class ChartSeries {
    /**
     * 序列名称
     */
    private String name;
    
    /**
     * 序列数值
     */
    private List<Double> values;
    
    /**
     * 序列颜色（可选）
     */
    private String color;
    
    /**
     * 序列类型（可选，如line、column等）
     */
    private String type;
    
    /**
     * 获取数值数量
     */
    public int getValueCount() {
        return values != null ? values.size() : 0;
    }
    
    /**
     * 获取指定索引的数值
     */
    public Double getValue(int index) {
        if (values != null && index >= 0 && index < values.size()) {
            return values.get(index);
        }
        return null;
    }
    
    /**
     * 检查数据有效性
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() && 
               values != null && !values.isEmpty();
    }
    
    /**
     * 获取数值总和
     */
    public double getSum() {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }
        return values.stream().mapToDouble(Double::doubleValue).sum();
    }
    
    /**
     * 获取数值平均值
     */
    public double getAverage() {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }
        return getSum() / values.size();
    }
    
    /**
     * 获取最大值
     */
    public double getMax() {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }
        return values.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
    }
    
    /**
     * 获取最小值
     */
    public double getMin() {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }
        return values.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
    }
    
    /**
     * 创建数据序列
     */
    public static ChartSeries create(String name, List<Double> values) {
        return ChartSeries.builder()
                .name(name)
                .values(values)
                .build();
    }
    
    /**
     * 创建带颜色的数据序列
     */
    public static ChartSeries createWithColor(String name, List<Double> values, String color) {
        return ChartSeries.builder()
                .name(name)
                .values(values)
                .color(color)
                .build();
    }
}