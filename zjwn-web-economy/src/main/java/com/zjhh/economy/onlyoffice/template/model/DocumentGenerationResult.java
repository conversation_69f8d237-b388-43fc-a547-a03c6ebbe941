package com.zjhh.economy.onlyoffice.template.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 文档生成结果
 */
@Data
@AllArgsConstructor
public class DocumentGenerationResult {

    private TemplateParseResult parseResult;    // 参数解析结果
    private byte[] documentBytes;               // 生成的文档字节数组
    private boolean success;                    // 生成是否成功
    private String errorMessage;                // 错误信息

    /**
     * 创建成功的结果
     */
    public static DocumentGenerationResult success(byte[] documentBytes) {
        return new DocumentGenerationResult(null, documentBytes, true, null);
    }

    /**
     * 创建带解析结果的成功结果
     */
    public static DocumentGenerationResult success(TemplateParseResult parseResult, byte[] documentBytes) {
        return new DocumentGenerationResult(parseResult, documentBytes, true, null);
    }

    /**
     * 创建失败的结果
     */
    public static DocumentGenerationResult failure(String errorMessage) {
        return new DocumentGenerationResult(null, null, false, errorMessage);
    }

    /**
     * 获取参数列表
     */
    public List<TemplateParameter> getParameters() {
        return parseResult != null ? parseResult.getParameters() : null;
    }

    /**
     * 获取文档大小
     */
    public long getDocumentSize() {
        return documentBytes != null ? documentBytes.length : 0;
    }

}