package com.zjhh.economy.onlyoffice.report.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表格参数信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TableParameterInfo {
    /**
     * 参数组名称
     */
    private String groupName;
    
    /**
     * 字段名称
     */
    private String columnName;
    
    /**
     * 数据源名称
     */
    private String dataSourceName;
    
    /**
     * 原始参数文本（如：{{销售数据.产品名称}}）
     */
    private String originalParameter;
    
    /**
     * 表格索引（用于区分多个表格）
     */
    private Integer tableIndex;
    
    /**
     * 列排序（在表格中的列顺序）
     */
    private Integer columnOrder;
    
    public TableParameterInfo(String groupName, String columnName) {
        this.groupName = groupName;
        this.columnName = columnName;
    }
    
    public TableParameterInfo(String groupName, String columnName, String dataSourceName) {
        this.groupName = groupName;
        this.columnName = columnName;
        this.dataSourceName = dataSourceName;
    }
    
    @Override
    public String toString() {
        return String.format("TableParam{group='%s', column='%s', dataSource='%s', tableIndex=%d}", 
                           groupName, columnName, dataSourceName, tableIndex);
    }
}