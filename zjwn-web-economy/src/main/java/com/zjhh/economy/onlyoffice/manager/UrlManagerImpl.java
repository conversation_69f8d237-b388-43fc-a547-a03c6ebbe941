package com.zjhh.economy.onlyoffice.manager;

import cn.dev33.satoken.stp.StpUtil;
import com.onlyoffice.manager.settings.SettingsManager;
import com.onlyoffice.manager.url.DefaultUrlManager;
import com.zjhh.user.service.impl.UserSession;

/**
 * <AUTHOR>
 * @since 2025/8/14 12:27
 */
public class UrlManagerImpl extends DefaultUrlManager {

    private final String serverUrl;

    private final String contextPath;

    public UrlManagerImpl(final SettingsManager settingsManager, String serverUrl, String contextPath) {
        super(settingsManager);
        this.serverUrl = serverUrl;
        this.contextPath = contextPath;
    }

    @Override
    public String getFileUrl(final String fileId) {
        return this.serverUrl + this.contextPath + "/api/onlyoffice/download/" + fileId + "/" + StpUtil.getTokenValue();
    }

    @Override
    public String getCallbackUrl(final String fileId) {
        return this.serverUrl + this.contextPath + "/api/onlyoffice/callback/" + fileId + "/" + StpUtil.getTokenValue();
    }

}
