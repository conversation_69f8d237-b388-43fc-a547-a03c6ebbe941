package com.zjhh.economy.onlyoffice.service;

import cn.hutool.extra.spring.SpringUtil;
import com.onlyoffice.manager.document.DocumentManager;
import com.onlyoffice.manager.security.JwtManager;
import com.onlyoffice.manager.settings.SettingsManager;
import com.onlyoffice.manager.url.UrlManager;
import com.onlyoffice.model.common.User;
import com.onlyoffice.model.documenteditor.config.document.Permissions;
import com.onlyoffice.model.documenteditor.config.document.Type;
import com.onlyoffice.model.documenteditor.config.editorconfig.CoEditing;
import com.onlyoffice.model.documenteditor.config.editorconfig.Customization;
import com.onlyoffice.model.documenteditor.config.editorconfig.Mode;
import com.onlyoffice.model.documenteditor.config.editorconfig.customization.*;
import com.onlyoffice.model.documenteditor.config.editorconfig.customization.review.ReviewDisplay;
import com.onlyoffice.service.documenteditor.config.DefaultConfigService;
import com.zjhh.economy.onlyoffice.constant.OnlyOfficeConstant;
import com.zjhh.user.service.impl.UserSession;
import com.zjhh.user.vo.LoginVo;


/**
 * <AUTHOR>
 * @since 2025/8/14 12:23
 */
public class ConfigServiceImpl extends DefaultConfigService {
    public ConfigServiceImpl(final DocumentManager documentManager, final UrlManager urlManager,
                             final JwtManager jwtManager, final SettingsManager settingsManager) {
        super(documentManager, urlManager, jwtManager, settingsManager);
    }

    @Override
    public Permissions getPermissions(final String fileId) {
        Permissions permissions = Permissions.builder()
                .download(true)
                .chat(false)
                .comment(false)
                .build();
        if (fileId.startsWith(OnlyOfficeConstant.FILE_ID_TEMP)) {
            permissions.setEdit(false);
            return permissions;
        }
        permissions.setEdit(true);
        return permissions;
    }

    @Override
    public CoEditing getCoEditing(String fileId, Mode mode, Type type) {
        return CoEditing.builder().mode(com.onlyoffice.model.documenteditor.config.editorconfig.coediting.Mode.STRICT)
                .change(false).build();
    }

    @Override
    public Customization getCustomization(String fileId) {
        return Customization.builder()
                .forcesave(true)
                .comments(false)
                .compactHeader(true)
                .help(false)
                .plugins(false)
                .goback(
                        Goback.builder().build()
                )
                .features(
                        Features.builder()
                                .featuresTips(false)
                                .build()
                )
                .build();
    }

    @Override
    public User getUser() {
        UserSession userSession = SpringUtil.getBean(UserSession.class);
        if (userSession != null) {
            LoginVo loginVo = userSession.getSessionLoginVo();
            return User.builder()
                    .id(loginVo.getUserId())
                    .name(loginVo.getUsername())
                    .build();
        }
        return User.builder()
                .id("1")
                .name("User")
                .build();
    }
}
