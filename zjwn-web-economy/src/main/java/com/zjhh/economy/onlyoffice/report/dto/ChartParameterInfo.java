package com.zjhh.economy.onlyoffice.report.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图表参数信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChartParameterInfo {
    /**
     * 参数组名称
     */
    private String groupName;
    
    /**
     * 字段名称
     */
    private String columnName;
    
    /**
     * 数据源名称
     */
    private String dataSourceName;
    
    /**
     * 原始参数文本（如：{{销售数据.产品名称}}）
     */
    private String originalParameter;
    
    /**
     * 图表索引（用于区分多个图表）
     */
    private Integer chartIndex;
    
    /**
     * 系列名称（在图表中的系列名称）
     */
    private String seriesName;
    
    /**
     * 系列类型（line, bar, area等）
     */
    private String seriesType;
    
    public ChartParameterInfo(String groupName, String columnName) {
        this.groupName = groupName;
        this.columnName = columnName;
    }
    
    public ChartParameterInfo(String groupName, String columnName, String dataSourceName) {
        this.groupName = groupName;
        this.columnName = columnName;
        this.dataSourceName = dataSourceName;
    }
    
    @Override
    public String toString() {
        return String.format("ChartParam{group='%s', column='%s', dataSource='%s', chartIndex=%d, series='%s'}", 
                           groupName, columnName, dataSourceName, chartIndex, seriesName);
    }
}