package com.zjhh.economy.onlyoffice.report.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 参数分析结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParameterAnalysisResult {
    /**
     * 文本参数映射 (数据源名称 -> 参数信息列表)
     */
    private Map<String, List<TextParameterInfo>> textParams;
    
    /**
     * 表格参数映射 (数据源名称 -> 参数信息列表)
     */
    private Map<String, List<TableParameterInfo>> tableParams;
    
    /**
     * 图表参数映射 (数据源名称 -> 参数信息列表)
     */
    private Map<String, List<ChartParameterInfo>> chartParams;
    
    /**
     * 获取所有涉及的数据源名称
     */
    public java.util.Set<String> getAllDataSourceNames() {
        java.util.Set<String> allNames = new java.util.HashSet<>();
        if (textParams != null) allNames.addAll(textParams.keySet());
        if (tableParams != null) allNames.addAll(tableParams.keySet());
        if (chartParams != null) allNames.addAll(chartParams.keySet());
        return allNames;
    }
    
    /**
     * 获取参数总数
     */
    public int getTotalParameterCount() {
        int count = 0;
        if (textParams != null) {
            count += textParams.values().stream().mapToInt(List::size).sum();
        }
        if (tableParams != null) {
            count += tableParams.values().stream().mapToInt(List::size).sum();
        }
        if (chartParams != null) {
            count += chartParams.values().stream().mapToInt(List::size).sum();
        }
        return count;
    }
    
    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return (textParams == null || textParams.isEmpty()) &&
               (tableParams == null || tableParams.isEmpty()) &&
               (chartParams == null || chartParams.isEmpty());
    }
}