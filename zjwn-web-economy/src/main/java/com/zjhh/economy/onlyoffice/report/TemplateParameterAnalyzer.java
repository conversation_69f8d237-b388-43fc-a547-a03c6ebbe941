package com.zjhh.economy.onlyoffice.report;

import com.zjhh.economy.onlyoffice.report.dto.ChartParameterInfo;
import com.zjhh.economy.onlyoffice.report.dto.ParameterAnalysisResult;
import com.zjhh.economy.onlyoffice.report.dto.TableParameterInfo;
import com.zjhh.economy.onlyoffice.report.dto.TextParameterInfo;
import com.zjhh.economy.onlyoffice.template.model.TemplateParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 模板参数分析器 - 解析参数并分类
 */
@Slf4j
@Component
public class TemplateParameterAnalyzer {

    /**
     * 参数名称解析正则表达式
     * 匹配格式：业务数据名称.字段名称
     */
    private static final Pattern PARAMETER_PATTERN = Pattern.compile("^(.+)\\.(.+)$");

    /**
     * 图表参数识别正则表达式
     * 匹配x开头的参数，如x11, x12等
     */
    private static final Pattern CHART_PATTERN = Pattern.compile("^x\\d+$");

    /**
     * 解析模板参数，按类型分组
     * @param parameters 模板参数列表
     * @return 参数分析结果
     */
    public ParameterAnalysisResult analyzeParameters(List<TemplateParameter> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            log.warn("参数列表为空");
            return ParameterAnalysisResult.builder()
                    .textParams(new HashMap<>())
                    .tableParams(new HashMap<>())
                    .chartParams(new HashMap<>())
                    .build();
        }

        Map<String, List<TextParameterInfo>> textParams = new HashMap<>();
        Map<String, List<TableParameterInfo>> tableParams = new HashMap<>();
        Map<String, List<ChartParameterInfo>> chartParams = new HashMap<>();

        try {
            for (TemplateParameter param : parameters) {
                processParameter(param, textParams, tableParams, chartParams);
            }

            ParameterAnalysisResult result = ParameterAnalysisResult.builder()
                    .textParams(textParams)
                    .tableParams(tableParams)
                    .chartParams(chartParams)
                    .build();

            log.info("参数分析完成: 文本参数{}组, 表格参数{}组, 图表参数{}组, 总参数{}个",
                    textParams.size(), tableParams.size(), chartParams.size(), result.getTotalParameterCount());

            return result;

        } catch (Exception e) {
            log.error("参数分析失败", e);
            return ParameterAnalysisResult.builder()
                    .textParams(new HashMap<>())
                    .tableParams(new HashMap<>())
                    .chartParams(new HashMap<>())
                    .build();
        }
    }

    /**
     * 处理单个参数
     */
    private void processParameter(TemplateParameter param, 
                                Map<String, List<TextParameterInfo>> textParams,
                                Map<String, List<TableParameterInfo>> tableParams,
                                Map<String, List<ChartParameterInfo>> chartParams) {
        
        if (param.getFieldNames() == null || param.getFieldNames().isEmpty()) {
            log.warn("参数字段名称为空: {}", param.getGroupName());
            return;
        }

        // 处理每个字段名称
        for (String fieldName : param.getFieldNames()) {
            String[] parsedName = parseParameterName(param.getDataSource(), fieldName);
            if (parsedName == null) {
                log.warn("参数名称解析失败: dataSource={}, fieldName={}", param.getDataSource(), fieldName);
                continue;
            }

            String dataSourceName = parsedName[0];
            String columnName = parsedName[1];
            String originalParameter = String.format("{{%s.%s}}", dataSourceName, columnName);

            switch (param.getType()) {
                case TEXT:
                    TextParameterInfo textInfo = new TextParameterInfo(
                            param.getGroupName(), columnName, dataSourceName);
                    textInfo.setOriginalParameter(originalParameter);
                    
                    textParams.computeIfAbsent(dataSourceName, k -> new ArrayList<>()).add(textInfo);
                    log.debug("添加文本参数: {}", textInfo);
                    break;

                case TABLE:
                    TableParameterInfo tableInfo = new TableParameterInfo(
                            param.getGroupName(), columnName, dataSourceName);
                    tableInfo.setOriginalParameter(originalParameter);
                    tableInfo.setTableIndex(extractTableIndex(param.getGroupName()));
                    
                    tableParams.computeIfAbsent(dataSourceName, k -> new ArrayList<>()).add(tableInfo);
                    log.debug("添加表格参数: {}", tableInfo);
                    break;

                case CHART:
                    ChartParameterInfo chartInfo = new ChartParameterInfo(
                            param.getGroupName(), columnName, dataSourceName);
                    chartInfo.setOriginalParameter(originalParameter);
                    chartInfo.setChartIndex(extractChartIndex(param.getGroupName()));
                    chartInfo.setSeriesName(determineSeriesName(columnName));
                    
                    chartParams.computeIfAbsent(dataSourceName, k -> new ArrayList<>()).add(chartInfo);
                    log.debug("添加图表参数: {}", chartInfo);
                    break;

                default:
                    log.warn("未知参数类型: {}", param.getType());
                    break;
            }
        }
    }

    /**
     * 解析参数名称
     * @param dataSource 数据源
     * @param fieldName 字段名称
     * @return [业务数据名称, 字段名称] 或 null
     */
    private String[] parseParameterName(String dataSource, String fieldName) {
        // 优先使用数据源字段的组合
        if (StringUtils.hasText(dataSource) && StringUtils.hasText(fieldName)) {
            // 检查dataSource是否已经包含完整的"业务数据名称.字段名称"格式
            Matcher matcher = PARAMETER_PATTERN.matcher(dataSource);
            if (matcher.matches()) {
                return new String[]{matcher.group(1), matcher.group(2)};
            }
            
            // 检查fieldName是否包含完整格式
            matcher = PARAMETER_PATTERN.matcher(fieldName);
            if (matcher.matches()) {
                return new String[]{matcher.group(1), matcher.group(2)};
            }
            
            // 如果都不包含，则将dataSource作为业务数据名称，fieldName作为字段名称
            return new String[]{dataSource, fieldName};
        }

        // 如果只有fieldName有值，尝试解析
        if (StringUtils.hasText(fieldName)) {
            Matcher matcher = PARAMETER_PATTERN.matcher(fieldName);
            if (matcher.matches()) {
                return new String[]{matcher.group(1), matcher.group(2)};
            }
        }

        // 如果只有dataSource有值，尝试解析
        if (StringUtils.hasText(dataSource)) {
            Matcher matcher = PARAMETER_PATTERN.matcher(dataSource);
            if (matcher.matches()) {
                return new String[]{matcher.group(1), matcher.group(2)};
            }
        }

        return null;
    }

    /**
     * 提取表格索引
     * @param groupName 组名（如：table_1, table_2）
     * @return 表格索引
     */
    private Integer extractTableIndex(String groupName) {
        if (!StringUtils.hasText(groupName)) {
            return 1;
        }
        
        try {
            // 匹配table_数字格式
            Pattern pattern = Pattern.compile("table[_-]?(\\d+)", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(groupName);
            if (matcher.find()) {
                return Integer.parseInt(matcher.group(1));
            }
        } catch (NumberFormatException e) {
            log.debug("表格索引解析失败: {}", groupName);
        }
        
        return 1;
    }

    /**
     * 提取图表索引
     * @param groupName 组名（如：chart_1, chart_2）
     * @return 图表索引
     */
    private Integer extractChartIndex(String groupName) {
        if (!StringUtils.hasText(groupName)) {
            return 1;
        }
        
        try {
            // 匹配chart_数字格式
            Pattern pattern = Pattern.compile("chart[_-]?(\\d+)", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(groupName);
            if (matcher.find()) {
                return Integer.parseInt(matcher.group(1));
            }
        } catch (NumberFormatException e) {
            log.debug("图表索引解析失败: {}", groupName);
        }
        
        return 1;
    }

    /**
     * 确定系列名称
     * @param columnName 列名
     * @return 系列名称
     */
    private String determineSeriesName(String columnName) {
        if (!StringUtils.hasText(columnName)) {
            return "数据系列";
        }
        
        // 如果是图表参数格式（x11, x12等），转换为更友好的名称
        Matcher matcher = CHART_PATTERN.matcher(columnName);
        if (matcher.matches()) {
            return "系列" + columnName.substring(1);
        }
        
        return columnName;
    }

    /**
     * 按数据源分组参数
     * @param result 分析结果
     * @return 数据源分组结果
     */
    public Map<String, Map<String, Object>> groupParametersByDataSource(ParameterAnalysisResult result) {
        Map<String, Map<String, Object>> grouped = new HashMap<>();
        
        if (result == null) {
            return grouped;
        }

        Set<String> allDataSources = result.getAllDataSourceNames();
        
        for (String dataSource : allDataSources) {
            Map<String, Object> dataSourceParams = new HashMap<>();
            
            // 添加文本参数
            List<TextParameterInfo> textList = result.getTextParams().get(dataSource);
            if (textList != null && !textList.isEmpty()) {
                dataSourceParams.put("textParams", textList);
            }
            
            // 添加表格参数
            List<TableParameterInfo> tableList = result.getTableParams().get(dataSource);
            if (tableList != null && !tableList.isEmpty()) {
                dataSourceParams.put("tableParams", tableList);
            }
            
            // 添加图表参数
            List<ChartParameterInfo> chartList = result.getChartParams().get(dataSource);
            if (chartList != null && !chartList.isEmpty()) {
                dataSourceParams.put("chartParams", chartList);
            }
            
            if (!dataSourceParams.isEmpty()) {
                grouped.put(dataSource, dataSourceParams);
            }
        }
        
        return grouped;
    }

    /**
     * 验证参数完整性
     * @param result 分析结果
     * @return 验证报告
     */
    public Map<String, Object> validateParameters(ParameterAnalysisResult result) {
        Map<String, Object> validation = new HashMap<>();
        
        if (result == null || result.isEmpty()) {
            validation.put("valid", false);
            validation.put("message", "参数分析结果为空");
            return validation;
        }

        List<String> warnings = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        // 检查数据源名称
        Set<String> dataSources = result.getAllDataSourceNames();
        for (String dataSource : dataSources) {
            if (!StringUtils.hasText(dataSource)) {
                errors.add("发现空的数据源名称");
            } else if (dataSource.contains(" ")) {
                warnings.add("数据源名称包含空格: " + dataSource);
            }
        }

        // 检查参数分布
        int totalParams = result.getTotalParameterCount();
        if (totalParams == 0) {
            errors.add("未发现任何有效参数");
        } else if (totalParams > 1000) {
            warnings.add("参数数量过多: " + totalParams + "，可能影响性能");
        }

        // 检查表格参数的一致性
        for (Map.Entry<String, List<TableParameterInfo>> entry : result.getTableParams().entrySet()) {
            String dataSource = entry.getKey();
            List<TableParameterInfo> tableParams = entry.getValue();
            
            // 按表格索引分组检查
            Map<Integer, List<TableParameterInfo>> byIndex = tableParams.stream()
                    .collect(Collectors.groupingBy(p -> p.getTableIndex() != null ? p.getTableIndex() : 1));
            
            for (Map.Entry<Integer, List<TableParameterInfo>> indexEntry : byIndex.entrySet()) {
                Integer tableIndex = indexEntry.getKey();
                List<TableParameterInfo> params = indexEntry.getValue();
                
                if (params.size() < 2) {
                    warnings.add(String.format("表格 %s.table_%d 只有 %d 个字段，可能不完整", 
                                             dataSource, tableIndex, params.size()));
                }
            }
        }

        boolean isValid = errors.isEmpty();
        validation.put("valid", isValid);
        validation.put("totalParameters", totalParams);
        validation.put("dataSourceCount", dataSources.size());
        validation.put("warnings", warnings);
        validation.put("errors", errors);
        
        if (isValid) {
            validation.put("message", "参数验证通过");
        } else {
            validation.put("message", "参数验证失败: " + String.join(", ", errors));
        }

        return validation;
    }
}