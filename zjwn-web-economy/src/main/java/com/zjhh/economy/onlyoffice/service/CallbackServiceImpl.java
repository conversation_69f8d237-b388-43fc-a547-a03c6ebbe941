package com.zjhh.economy.onlyoffice.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.onlyoffice.manager.security.JwtManager;
import com.onlyoffice.manager.settings.SettingsManager;
import com.onlyoffice.model.documenteditor.Callback;
import com.onlyoffice.service.documenteditor.callback.DefaultCallbackService;
import com.zjhh.economy.dao.entity.AdsDocument;
import com.zjhh.economy.dao.mapper.AdsDocumentMapper;
import com.zjhh.system.service.DictService;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/8/14 12:23
 */
@Slf4j
public class CallbackServiceImpl extends DefaultCallbackService {

    private final AdsDocumentMapper adsDocumentMapper;

    private final DictService dictService;

    public CallbackServiceImpl(final JwtManager jwtManager, final SettingsManager settingsManager,
                               final AdsDocumentMapper adsDocumentMapper, final DictService dictService) {
        super(jwtManager, settingsManager);
        this.adsDocumentMapper = adsDocumentMapper;
        this.dictService = dictService;
    }

    @Override
    public void handlerForcesave(final Callback callback, final String fileId) throws Exception {
        save(callback, fileId);
    }

    @Override
    public void handlerSave(final Callback callback, final String fileId) throws Exception {
        save(callback, fileId);
    }

    /**
     * 保存
     *
     * @param callback
     * @param fileId
     * @throws Exception
     */
    private void save(final Callback callback, final String fileId) throws Exception {

        AdsDocument adsDocument = adsDocumentMapper.selectById(fileId);
        if (adsDocument == null) {
            throw new Exception("文档不存在: " + fileId);
        }

        String url = callback.getUrl();
        if (StrUtil.isBlank(url)) {
            throw new Exception("回调URL为空");
        }

        try {
            // 从OnlyOffice下载编辑后的文件
            byte[] fileData = downloadFileFromUrl(url);

            // 获取原文件路径
            String originalFilePath = dictService.getFileUploadPath() + adsDocument.getPath();

            // 写入新文件内容
            FileUtil.writeBytes(fileData, originalFilePath);

            // 更新文档信息
            adsDocument.setSize((long) fileData.length);
            adsDocument.setUpdateTime(LocalDateTime.now());
            adsDocumentMapper.updateById(adsDocument);

            log.info("文档保存成功, fileId: {}, size: {}", fileId, fileData.length);

        } catch (Exception e) {
            log.error("保存文档失败, fileId: {}, url: {}", fileId, url, e);
            throw new Exception("保存文档失败: " + e.getMessage(), e);
        }
    }


    /**
     * 从URL下载文件
     */
    private byte[] downloadFileFromUrl(String url) throws Exception {
        try {
            HttpRequest request = HttpUtil.createGet(url);
            HttpResponse response = request.execute();

            if (!response.isOk()) {
                throw new Exception("下载文件失败, HTTP状态码: " + response.getStatus());
            }

            return response.bodyBytes();
        } catch (Exception e) {
            log.error("从URL下载文件失败: {}", url, e);
            throw new Exception("下载文件失败: " + e.getMessage(), e);
        }
    }

}
