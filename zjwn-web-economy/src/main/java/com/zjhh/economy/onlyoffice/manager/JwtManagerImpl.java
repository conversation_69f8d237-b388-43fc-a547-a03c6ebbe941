package com.zjhh.economy.onlyoffice.manager;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.onlyoffice.manager.security.DefaultJwtManager;
import com.onlyoffice.manager.settings.SettingsManager;

import java.time.Instant;
import java.util.Calendar;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/9/3 16:37
 */
public class JwtManagerImpl extends DefaultJwtManager {

    private final Integer jwtExpireTime;

    public JwtManagerImpl(SettingsManager settingsManager, final Integer jwtExpireTime) {
        super(settingsManager);
        this.jwtExpireTime = jwtExpireTime;
    }

    @Override
    public String createToken(final Map<String, ?> payloadMap, final String key) {
        Algorithm algorithm = Algorithm.HMAC256(key);

        Calendar calendar = Calendar.getInstance();
        Instant issuedAt = calendar.toInstant();
        calendar.add(Calendar.SECOND, jwtExpireTime);
        Instant expiresAt = calendar.toInstant();

        return JWT.create()
                .withIssuedAt(issuedAt)
                .withExpiresAt(expiresAt)
                .withPayload(payloadMap)
                .sign(algorithm);
    }
}
