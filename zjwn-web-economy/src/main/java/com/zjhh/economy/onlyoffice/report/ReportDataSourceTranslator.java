package com.zjhh.economy.onlyoffice.report;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zjhh.economy.dao.entity.AdsDmsReportDataColumnSet;
import com.zjhh.economy.dao.entity.AdsDmsReportDataSet;
import com.zjhh.economy.dao.mapper.AdsDmsReportDataColumnSetMapper;
import com.zjhh.economy.dao.mapper.AdsDmsReportDataSetMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源翻译服务 - 将业务名称转换为编码
 */
@Slf4j
@Service
public class ReportDataSourceTranslator {

    @Resource
    private AdsDmsReportDataSetMapper dataSetMapper;

    @Resource
    private AdsDmsReportDataColumnSetMapper columnSetMapper;

    /**
     * 根据业务数据名称获取数据源编码
     *
     * @param dataSetName 业务数据名称
     * @return 数据源编码
     */
    public String translateDataSourceName(String reportTypeCode, String dataSetName) {
        if (!StringUtils.hasText(dataSetName)) {
            log.warn("数据源名称为空");
            return null;
        }

        try {
            LambdaQueryWrapper<AdsDmsReportDataSet> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AdsDmsReportDataSet::getDataSetName, dataSetName)
                    .eq(AdsDmsReportDataSet::getReportTypeCode, reportTypeCode);

            AdsDmsReportDataSet dataSet = dataSetMapper.selectOne(wrapper);
            if (dataSet != null) {
                String dataTypeCode = dataSet.getDataTypeCode();
                log.debug("数据源翻译成功: {} -> {}", dataSetName, dataTypeCode);
                return dataTypeCode;
            }

            log.warn("未找到数据源: {}", dataSetName);
            return null;

        } catch (Exception e) {
            log.error("数据源翻译失败: {}", dataSetName, e);
            return null;
        }
    }

    /**
     * 根据数据源和字段名称获取字段编码
     *
     * @param dataTypeCode 数据源编码
     * @param columnName   字段名称
     * @return 字段编码
     */
    public String translateColumnName(String reportTypeCode, String dataTypeCode, String columnName) {
        if (!StringUtils.hasText(reportTypeCode) || !StringUtils.hasText(dataTypeCode) || !StringUtils.hasText(columnName)) {
            return null;
        }

        try {
            // 首先获取数据源ID
            LambdaQueryWrapper<AdsDmsReportDataSet> dataSetWrapper = new LambdaQueryWrapper<>();
            dataSetWrapper.eq(AdsDmsReportDataSet::getReportTypeCode, reportTypeCode)
                    .eq(AdsDmsReportDataSet::getDataTypeCode, dataTypeCode);
            AdsDmsReportDataSet dataSet = dataSetMapper.selectOne(dataSetWrapper);

            if (dataSet == null) {
                return null;
            }

            // 查询字段编码
            LambdaQueryWrapper<AdsDmsReportDataColumnSet> columnWrapper = new LambdaQueryWrapper<>();
            columnWrapper.eq(AdsDmsReportDataColumnSet::getDataSetId, dataSet.getId())
                    .eq(AdsDmsReportDataColumnSet::getColumnName, columnName);

            AdsDmsReportDataColumnSet columnSet = columnSetMapper.selectOne(columnWrapper);
            if (columnSet != null) {
                return columnSet.getColumnKey();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 批量翻译字段名称
     *
     * @param dataTypeCode 数据源编码
     * @param columnNames  字段名称列表
     * @return 字段名称->字段编码映射
     */
    public Map<String, String> translateColumnNames(String reportTypeCode, String dataTypeCode, List<String> columnNames) {
        Map<String, String> result = new HashMap<>();

        if (!StringUtils.hasText(dataTypeCode) || columnNames == null || columnNames.isEmpty()) {
            return result;
        }

        try {
            // 首先获取数据源ID
            LambdaQueryWrapper<AdsDmsReportDataSet> dataSetWrapper = new LambdaQueryWrapper<>();
            dataSetWrapper.eq(AdsDmsReportDataSet::getReportTypeCode, reportTypeCode)
                    .eq(AdsDmsReportDataSet::getDataTypeCode, dataTypeCode);
            AdsDmsReportDataSet dataSet = dataSetMapper.selectOne(dataSetWrapper);

            if (dataSet == null) {
                return result;
            }

            // 批量查询字段编码
            LambdaQueryWrapper<AdsDmsReportDataColumnSet> columnWrapper = new LambdaQueryWrapper<>();
            columnWrapper.eq(AdsDmsReportDataColumnSet::getDataSetId, dataSet.getId())
                    .in(AdsDmsReportDataColumnSet::getColumnName, columnNames);

            List<AdsDmsReportDataColumnSet> columnSets = columnSetMapper.selectList(columnWrapper);

            for (AdsDmsReportDataColumnSet columnSet : columnSets) {
                String columnName = columnSet.getColumnName();
                String columnKey = columnSet.getColumnKey();
                result.put(columnName, columnKey);
            }
            return result;
        } catch (Exception e) {
            return result;
        }
    }
}