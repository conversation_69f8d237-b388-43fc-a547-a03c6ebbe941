package com.zjhh.economy.onlyoffice.template.filler;

import cn.hutool.core.util.StrUtil;
import com.zjhh.economy.onlyoffice.template.exception.TemplateProcessException;
import com.zjhh.economy.onlyoffice.template.model.ChartFillData;
import com.zjhh.economy.onlyoffice.template.model.TableFillData;
import com.zjhh.economy.onlyoffice.template.model.TemplateFillData;
import com.zjhh.economy.onlyoffice.template.model.TextFillData;
import com.zjhh.economy.onlyoffice.template.processor.PoiChartDataProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Word文档数据填充器 (基于Apache POI)
 */
@Component
@Slf4j
public class WordDocumentDataFiller {

    private static final Pattern PARAM_PATTERN = Pattern.compile("\\{\\{([^}]+)}}");

    @Resource
    private PoiChartDataProcessor chartDataProcessor;

    /**
     * 内部类：保存模板行的结构信息
     */
    private record TemplateRowStructure(List<String> cellTemplateTexts) {
        private TemplateRowStructure(List<String> cellTemplateTexts) {
            this.cellTemplateTexts = new ArrayList<>(cellTemplateTexts);
        }

    }

    /**
     * 填充文档数据
     *
     * @param document POI文档对象
     * @param fillData 填充数据
     */
    public void fillDocument(XWPFDocument document, TemplateFillData fillData) {
        try {
            fillTextData(document, fillData);
            fillTableData(document, fillData);
            fillChartData(document, fillData);
        } catch (Exception e) {
            log.error("文档数据填充失败", e);
            throw new TemplateProcessException("文档数据填充失败", e);
        }
    }

    /**
     * 填充文本数据
     */
    private void fillTextData(XWPFDocument document, TemplateFillData fillData) {
        Map<String, TextFillData> textDataMap = fillData.getTextDataMap();
        if (textDataMap == null || textDataMap.isEmpty()) {
            return;
        }

        // 遍历所有段落
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            if (!isInTable(paragraph)) {
                fillParagraphText(paragraph, textDataMap);
            }
        }

        // 处理页眉页脚
        fillHeaderFooterText(document, textDataMap);
    }

    /**
     * 检查段落是否在表格中
     */
    private boolean isInTable(XWPFParagraph paragraph) {
        try {
            return paragraph.getPartType() != null && paragraph.getPartType().toString().contains("tableCell");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 填充段落文本
     */
    private void fillParagraphText(XWPFParagraph paragraph, Map<String, TextFillData> textDataMap) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) {
            return;
        }

        // 合并所有run的文本进行处理
        StringBuilder fullText = new StringBuilder();
        for (XWPFRun run : runs) {
            String runText = run.getText(0);
            if (StrUtil.isNotBlank(runText)) {
                fullText.append(runText);
            }
        }

        String originalText = fullText.toString();
        if (!originalText.contains("{{")) {
            return;
        }

        String replacedText = replaceParameters(originalText, textDataMap);

        if (!originalText.equals(replacedText)) {
            // 清除原有的run内容
            for (int i = runs.size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }

            // 创建新的run并设置替换后的文本
            XWPFRun newRun = paragraph.createRun();
            newRun.setText(replacedText);

        }
    }

    /**
     * 填充页眉页脚文本
     */
    private void fillHeaderFooterText(XWPFDocument document, Map<String, TextFillData> textDataMap) {
        // 处理页眉
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                fillParagraphText(paragraph, textDataMap);
            }
        }

        // 处理页脚
        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                fillParagraphText(paragraph, textDataMap);
            }
        }
    }

    /**
     * 填充表格数据
     */
    private void fillTableData(XWPFDocument document, TemplateFillData fillData) {
        Map<String, TableFillData> tableDataMap = fillData.getTableDataMap();
        if (tableDataMap == null || tableDataMap.isEmpty()) {
            return;
        }

        List<XWPFTable> tables = document.getTables();
        for (int tableIndex = 0; tableIndex < tables.size(); tableIndex++) {
            String groupName = "table_" + (tableIndex + 1);
            TableFillData tableData = tableDataMap.get(groupName);
            if (tableData != null) {
                fillSingleTable(tables.get(tableIndex), tableData, groupName);
            }
        }
    }

    /**
     * 填充单个表格
     */
    private void fillSingleTable(XWPFTable table, TableFillData tableData, String groupName) {
        List<Map<String, Object>> tableRows = tableData.getRows();
        if (tableRows == null || tableRows.isEmpty()) {
            return;
        }

        List<XWPFTableRow> existingRows = table.getRows();

        // 查找包含模板参数的数据行（模板行）
        XWPFTableRow templateRow = findTemplateRow(existingRows, tableData.getDataSource());
        int templateRowIndex = -1;

        if (templateRow != null) {
            // 先保存原始模板行的结构，然后再填充数据
            TemplateRowStructure originalTemplate = saveTemplateRowStructure(templateRow);

            // 使用第一行数据填充模板行
            if (!tableRows.isEmpty()) {
                Map<String, Object> firstRowData = tableRows.getFirst();
                fillTableRow(templateRow, firstRowData);
            }

            // 为剩余的数据行创建新行（从第二行数据开始）
            for (int dataIndex = 1; dataIndex < tableRows.size(); dataIndex++) {
                Map<String, Object> rowData = tableRows.get(dataIndex);

                // 基于保存的原始模板创建新行
                XWPFTableRow newRow = createRowFromOriginalTemplate(table, originalTemplate);
                fillTableRow(newRow, rowData);
            }
        } else {
            // 没有找到模板行，使用原来的逻辑（兼容性处理）
            // 处理现有行
            for (int rowIndex = 0; rowIndex < existingRows.size() && rowIndex < tableRows.size(); rowIndex++) {
                XWPFTableRow row = existingRows.get(rowIndex);
                Map<String, Object> rowData = tableRows.get(rowIndex);
                fillTableRow(row, rowData);
            }

            // 如果数据行比表格行多，需要添加新行
            for (int rowIndex = existingRows.size(); rowIndex < tableRows.size(); rowIndex++) {
                XWPFTableRow newRow = table.createRow();
                Map<String, Object> rowData = tableRows.get(rowIndex);

                // 确保新行有足够的单元格（根据字段数量估算）
                if (tableData.getFieldNames() != null) {
                    while (newRow.getTableCells().size() < tableData.getFieldNames().size()) {
                        newRow.createCell();
                    }
                }
                fillTableRow(newRow, rowData);
            }
        }

    }

    /**
     * 填充表格行
     */
    private void fillTableRow(XWPFTableRow row, Map<String, Object> rowData) {
        List<XWPFTableCell> cells = row.getTableCells();
        // 对于每个单元格，使用整个行数据进行参数替换
        for (XWPFTableCell cell : cells) {
            fillTableCell(cell, rowData);
        }
    }

    /**
     * 填充表格单元格
     */
    private void fillTableCell(XWPFTableCell cell, Map<String, Object> cellData) {
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            String originalText = paragraph.getText();
            if (originalText != null && originalText.contains("{{")) {
                String replacedText = replaceParametersWithMap(originalText, cellData);
                if (!originalText.equals(replacedText)) {
                    // 清除原有内容
                    List<XWPFRun> runs = paragraph.getRuns();
                    for (int j = runs.size() - 1; j >= 0; j--) {
                        paragraph.removeRun(j);
                    }

                    // 设置新内容
                    XWPFRun newRun = paragraph.createRun();
                    newRun.setText(replacedText);
                }
            }
        }
    }

    /**
     * 查找包含指定数据源模板参数的表格行
     *
     * @param rows       所有表格行
     * @param dataSource 数据源名称
     * @return 包含该数据源参数的行，如果没有找到则返回null
     */
    private XWPFTableRow findTemplateRow(List<XWPFTableRow> rows, String dataSource) {
        for (XWPFTableRow row : rows) {
            if (rowContainsDataSource(row, dataSource)) {
                return row;
            }
        }
        return null;
    }

    /**
     * 检查表格行是否包含指定数据源的模板参数
     *
     * @param row        表格行
     * @param dataSource 数据源名称
     * @return true如果行中包含该数据源的参数
     */
    private boolean rowContainsDataSource(XWPFTableRow row, String dataSource) {
        for (XWPFTableCell cell : row.getTableCells()) {
            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                String text = paragraph.getText();
                if (text != null && text.contains("{{" + dataSource + ".")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 基于模板行创建新的表格行
     *
     * @param table       目标表格
     * @param templateRow 模板行
     * @return 新创建的行
     */
    private XWPFTableRow createRowBasedOnTemplate(XWPFTable table, XWPFTableRow templateRow) {
        XWPFTableRow newRow = table.createRow();

        // 确保新行有和模板行相同数量的单元格
        List<XWPFTableCell> templateCells = templateRow.getTableCells();
        while (newRow.getTableCells().size() < templateCells.size()) {
            newRow.createCell();
        }

        // 复制模板行的格式和内容结构到新行
        List<XWPFTableCell> newCells = newRow.getTableCells();
        for (int i = 0; i < templateCells.size() && i < newCells.size(); i++) {
            XWPFTableCell templateCell = templateCells.get(i);
            XWPFTableCell newCell = newCells.get(i);

            // 复制单元格的段落结构和模板文本
            copyTemplateCellStructure(templateCell, newCell);
        }

        return newRow;
    }

    /**
     * 复制模板单元格的结构到新单元格
     *
     * @param templateCell 模板单元格
     * @param newCell      新单元格
     */
    private void copyTemplateCellStructure(XWPFTableCell templateCell, XWPFTableCell newCell) {
        // 清除新单元格的默认段落
        List<XWPFParagraph> newParagraphs = newCell.getParagraphs();
        for (int i = newParagraphs.size() - 1; i >= 0; i--) {
            if (i > 0) { // 保留第一个段落
                newCell.removeParagraph(i);
            }
        }

        // 复制模板单元格的所有段落
        List<XWPFParagraph> templateParagraphs = templateCell.getParagraphs();
        for (int i = 0; i < templateParagraphs.size(); i++) {
            XWPFParagraph templatePara = templateParagraphs.get(i);
            XWPFParagraph newPara;

            if (i == 0 && !newCell.getParagraphs().isEmpty()) {
                // 使用第一个现有段落
                newPara = newCell.getParagraphs().getFirst();
            } else {
                // 创建新段落
                newPara = newCell.addParagraph();
            }

            // 复制段落的文本内容（包括模板参数）
            copyParagraphContent(templatePara, newPara);
        }
    }

    /**
     * 复制段落内容
     *
     * @param templatePara 模板段落
     * @param newPara      新段落
     */
    private void copyParagraphContent(XWPFParagraph templatePara, XWPFParagraph newPara) {
        // 清除新段落的现有runs
        List<XWPFRun> runs = newPara.getRuns();
        for (int i = runs.size() - 1; i >= 0; i--) {
            newPara.removeRun(i);
        }

        // 复制模板段落的文本内容
        String templateText = templatePara.getText();
        if (templateText != null && !templateText.trim().isEmpty()) {
            XWPFRun newRun = newPara.createRun();
            newRun.setText(templateText);
        }
    }

    /**
     * 保存模板行的原始结构
     *
     * @param templateRow 模板行
     * @return 保存的模板结构
     */
    private TemplateRowStructure saveTemplateRowStructure(XWPFTableRow templateRow) {
        List<String> cellTemplateTexts = new ArrayList<>();

        for (XWPFTableCell cell : templateRow.getTableCells()) {
            StringBuilder cellText = new StringBuilder();
            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                String paraText = paragraph.getText();
                if (paraText != null) {
                    cellText.append(paraText);
                }
            }
            cellTemplateTexts.add(cellText.toString());
        }

        return new TemplateRowStructure(cellTemplateTexts);
    }

    /**
     * 基于保存的原始模板结构创建新行
     *
     * @param table            目标表格
     * @param originalTemplate 原始模板结构
     * @return 新创建的行
     */
    private XWPFTableRow createRowFromOriginalTemplate(XWPFTable table, TemplateRowStructure originalTemplate) {
        XWPFTableRow newRow = table.createRow();
        List<String> templateTexts = originalTemplate.cellTemplateTexts();

        // 确保新行有足够的单元格
        while (newRow.getTableCells().size() < templateTexts.size()) {
            newRow.createCell();
        }

        // 设置每个单元格的模板文本
        List<XWPFTableCell> newCells = newRow.getTableCells();
        for (int i = 0; i < templateTexts.size() && i < newCells.size(); i++) {
            XWPFTableCell cell = newCells.get(i);
            String templateText = templateTexts.get(i);

            // 清除单元格的默认段落内容，设置模板文本
            List<XWPFParagraph> paragraphs = cell.getParagraphs();
            if (!paragraphs.isEmpty()) {
                XWPFParagraph firstPara = paragraphs.getFirst();
                // 清除现有runs
                List<XWPFRun> runs = firstPara.getRuns();
                for (int j = runs.size() - 1; j >= 0; j--) {
                    firstPara.removeRun(j);
                }
                // 设置模板文本
                if (templateText != null && !templateText.trim().isEmpty()) {
                    XWPFRun newRun = firstPara.createRun();
                    newRun.setText(templateText);
                }
            }
        }

        return newRow;
    }

    /**
     * 填充图表数据
     */
    private void fillChartData(XWPFDocument document, TemplateFillData fillData) {
        Map<String, ChartFillData> chartDataMap = fillData.getChartDataMap();
        if (chartDataMap == null || chartDataMap.isEmpty()) {
            return;
        }

        List<XWPFChart> charts = document.getCharts();
        for (int chartIndex = 0; chartIndex < charts.size(); chartIndex++) {
            String groupName = "chart_" + (chartIndex + 1);
            ChartFillData chartData = chartDataMap.get(groupName);

            if (chartData != null) {
                try {
                    chartDataProcessor.processChart(charts.get(chartIndex), chartData, chartIndex);
                } catch (Exception e) {
                    log.error("处理图表{}时出错: {}", groupName, e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 使用TextFillData替换参数
     */
    private String replaceParameters(String text, Map<String, TextFillData> textDataMap) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        Matcher matcher = PARAM_PATTERN.matcher(text);
        StringBuilder result = new StringBuilder();

        while (matcher.find()) {
            String paramName = matcher.group(1).trim();
            String replacement = null;

            // 尝试解析 数据源.字段名 格式
            if (paramName.contains(".")) {
                String[] parts = paramName.split("\\.", 2);
                if (parts.length == 2) {
                    String dataSource = parts[0].trim();
                    String fieldName = parts[1].trim();

                    // 查找匹配数据源的TextFillData
                    for (Map.Entry<String, TextFillData> entry : textDataMap.entrySet()) {
                        TextFillData textData = entry.getValue();
                        if (dataSource.equals(textData.getDataSource()) &&
                                textData.getFields() != null &&
                                textData.getFields().containsKey(fieldName)) {
                            Object value = textData.getFields().get(fieldName);
                            replacement = value != null ? value.toString() : "";
                            break;
                        }
                    }
                }
            } else {
                // 不包含数据源前缀，直接在所有TextFillData中查找字段
                for (Map.Entry<String, TextFillData> entry : textDataMap.entrySet()) {
                    TextFillData textData = entry.getValue();
                    if (textData.getFields() != null && textData.getFields().containsKey(paramName)) {
                        Object value = textData.getFields().get(paramName);
                        replacement = value != null ? value.toString() : "";
                        break;
                    }
                }
            }

            if (StrUtil.isBlank(replacement)) {
                replacement = "{{" + paramName + "}}"; // 保持原样
            }
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 使用Map数据替换参数
     */
    private String replaceParametersWithMap(String text, Map<String, Object> dataMap) {
        if (text == null || text.isEmpty() || dataMap == null) {
            return text;
        }

        Matcher matcher = PARAM_PATTERN.matcher(text);
        StringBuilder result = new StringBuilder();

        while (matcher.find()) {
            String paramName = matcher.group(1).trim();
            Object value = null;

            // 先尝试直接查找完整参数名
            if (dataMap.containsKey(paramName)) {
                value = dataMap.get(paramName);
            } else if (paramName.contains(".")) {
                // 如果参数包含数据源前缀，尝试提取字段名
                String[] parts = paramName.split("\\.", 2);
                if (parts.length == 2) {
                    String fieldName = parts[1].trim();
                    if (dataMap.containsKey(fieldName)) {
                        value = dataMap.get(fieldName);
                    }
                }
            }

            String replacement = value != null ? value.toString() : "";

            if (value == null) {
                replacement = "{{" + paramName + "}}"; // 保持原样
            }

            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }

        matcher.appendTail(result);
        return result.toString();
    }
}