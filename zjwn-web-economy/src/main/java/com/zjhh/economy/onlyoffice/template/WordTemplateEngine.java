package com.zjhh.economy.onlyoffice.template;

import com.zjhh.economy.onlyoffice.template.exception.TemplateProcessException;
import com.zjhh.economy.onlyoffice.template.filler.WordDocumentDataFiller;
import com.zjhh.economy.onlyoffice.template.model.DocumentGenerationResult;
import com.zjhh.economy.onlyoffice.template.model.TemplateFillData;
import com.zjhh.economy.onlyoffice.template.model.TemplateParseResult;
import com.zjhh.economy.onlyoffice.template.parser.WordTemplateParameterParser;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Word模板引擎 (基于Apache POI)
 * <p>
 * 核心功能：
 * 1. 模板参数解析
 * 2. 数据填充
 * 3. 文档生成
 */
@Slf4j
@Component
public class WordTemplateEngine {

    @Resource
    private WordTemplateParameterParser parameterParser;

    @Resource
    private WordDocumentDataFiller documentDataFiller;

    /**
     * 解析模板参数
     *
     * @param templateBytes 模板文件字节数组
     * @return 参数解析结果
     */
    public TemplateParseResult parseTemplate(byte[] templateBytes) {
        return parseTemplate(new ByteArrayInputStream(templateBytes));
    }

    /**
     * 解析并生成文档
     *
     * @param templateBytes 模板文件字节数组
     * @param fillData      填充数据
     * @return 生成结果，包含参数信息和文档内容
     */
    public DocumentGenerationResult parseAndGenerateWithModel(byte[] templateBytes, TemplateFillData fillData) {
        return processTemplate(new ByteArrayInputStream(templateBytes), fillData);
    }

    /**
     * 生成文档（InputStream版本）
     *
     * @param templateInputStream 模板文件输入流
     * @param fillData            填充数据
     * @return 生成的文档字节数组
     */
    public byte[] generateDocumentWithModel(InputStream templateInputStream, TemplateFillData fillData) {
        return generateDocument(templateInputStream, fillData).getDocumentBytes();
    }

    /**
     * 生成文档（byte[]版本）
     *
     * @param templateBytes 模板文件字节数组
     * @param fillData      填充数据
     * @return 生成的文档字节数组
     */
    public byte[] generateDocumentWithModel(byte[] templateBytes, TemplateFillData fillData) {
        return generateDocument(new ByteArrayInputStream(templateBytes), fillData).getDocumentBytes();
    }

    // === 核心实现方法 ===

    /**
     * 解析模板文档，提取参数信息
     *
     * @param templateInputStream 模板文件输入流
     * @return 模板解析结果
     */
    public TemplateParseResult parseTemplate(InputStream templateInputStream) {
        try {
            return parameterParser.parseTemplate(templateInputStream);
        } catch (Exception e) {
            log.error("模板解析失败", e);
            throw new TemplateProcessException("模板解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成文档
     *
     * @param templateInputStream 模板文件输入流
     * @param fillData            填充数据
     * @return 文档生成结果
     */
    public DocumentGenerationResult generateDocument(InputStream templateInputStream, TemplateFillData fillData) {
        try {
            // 创建文档对象
            XWPFDocument document = new XWPFDocument(templateInputStream);

            // 填充数据
            documentDataFiller.fillDocument(document, fillData);

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            document.close();

            byte[] documentBytes = outputStream.toByteArray();
            outputStream.close();
            return DocumentGenerationResult.success(documentBytes);
        } catch (IOException e) {
            log.error("文档IO操作失败", e);
            throw new TemplateProcessException("文档IO操作失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("文档生成失败", e);
            throw new TemplateProcessException("文档生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理模板 - 解析并生成文档
     *
     * @param templateInputStream 模板文件输入流
     * @param fillData            填充数据
     * @return 文档生成结果
     */
    public DocumentGenerationResult processTemplate(InputStream templateInputStream, TemplateFillData fillData) {
        try {
            // 读取模板字节数组（需要使用两次）
            byte[] templateBytes = templateInputStream.readAllBytes();

            // 解析模板参数
            TemplateParseResult parseResult;
            try (ByteArrayInputStream parseStream = new ByteArrayInputStream(templateBytes)) {
                parseResult = parseTemplate(parseStream);
            }

            // 生成文档
            DocumentGenerationResult generateResult;
            try (ByteArrayInputStream generateStream = new ByteArrayInputStream(templateBytes)) {
                generateResult = generateDocument(generateStream, fillData);
            }

            // 合并结果
            if (generateResult.isSuccess()) {
                generateResult.setParseResult(parseResult);
            }

            return generateResult;

        } catch (IOException e) {
            log.error("模板处理IO失败", e);
            throw new TemplateProcessException("模板处理IO失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("模板处理失败", e);
            throw new TemplateProcessException("模板处理失败: " + e.getMessage(), e);
        }
    }


}