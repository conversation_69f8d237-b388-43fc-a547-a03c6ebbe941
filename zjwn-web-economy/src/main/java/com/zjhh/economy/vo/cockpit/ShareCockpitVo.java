package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  共融共享Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ShareCockpitVo extends BaseVo {
    private static final long serialVersionUID = -4995845074161218641L;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "文章链接")
    private String link;

    @Schema(description = "日期")
    private String publishDate;
}
