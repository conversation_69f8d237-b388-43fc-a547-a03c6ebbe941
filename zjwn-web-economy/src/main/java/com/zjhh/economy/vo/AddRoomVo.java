package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/8 17:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddRoomVo extends BaseVo {

    private static final long serialVersionUID = 5874252882235622136L;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "楼宇id")
    private String buildingId;

    @Schema(description = "楼层id")
    private String floorId;
}
