package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class MobileEntAnalyzeSummaryVo extends BaseVo {
    private static final long serialVersionUID = 7627467276825845642L;

    @Schema(description = "本月注销")
    private String byZx;

    @Schema(description = "本年注销")
    private String bqZx;

    @Schema(description = "注销环比")
    private BigDecimal zxHb;

    @Schema(description = "注销同比")
    private BigDecimal zxTb;

    @Schema(description = "本月新增")
    private String byXz;

    @Schema(description = "本年新增")
    private String bqXz;

    @Schema(description = "新增环比")
    private BigDecimal xzHb;

    @Schema(description = "新增同比")
    private BigDecimal xzTb;

    @Schema(description = "本月迁出")
    private String byQc;

    @Schema(description = "本年迁出")
    private String bqQc;

    @Schema(description = "迁出环比")
    private BigDecimal qcHb;

    @Schema(description = "迁出同比")
    private BigDecimal qcTb;

    @Schema(description = "本月迁入")
    private String byQr;

    @Schema(description = "本年迁入")
    private String bqQr;

    @Schema(description = "迁入环比")
    private BigDecimal qrHb;

    @Schema(description = "迁入同比")
    private BigDecimal qrTb;


}
