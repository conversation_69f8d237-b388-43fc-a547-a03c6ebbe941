package com.zjhh.economy.vo.policymanagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.vo.DocumentDetailVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyManagementVo extends BaseVo {
    private static final long serialVersionUID = -9219127574701538345L;

    @Schema(description = "政策id")
    private String id;

    @Schema(description = "政策名称")
    private String policyName;

    @Schema(description = "文号")
    private String policyNo;

    @Schema(description = "发布机构")
    private String publishAgency;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "发布时间")
    private LocalDate publishDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "成文日期")
    private LocalDate writtenDate;

    @Schema(description = "政策详情")
    private String policyDetail;

    @Schema(description = "附件")
    private List<DocumentDetailVo> files;

    @Schema(description = "兑现列表")
    private List<PolicyManagementCashVo> cashList;

    @Schema(description = "兑现企业数量")
    private Integer cashEntNum;

    @Schema(description = "兑现金额（万元）")
    private BigDecimal cashTotalAmount;
}
