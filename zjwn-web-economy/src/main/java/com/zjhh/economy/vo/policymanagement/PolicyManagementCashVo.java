package com.zjhh.economy.vo.policymanagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyManagementCashVo extends BaseVo {
    private static final long serialVersionUID = -4915277957012114945L;

    @Schema(description = "兑现id")
    private String id;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "企业id")
    private String entId;

    @Schema(description = "企业名称")
    private String entName;

    @Schema(description = "兑现金额（元）")
    private BigDecimal cashAmount;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "兑现时间")
    private LocalDate cashDate;

    @Schema(description = "备注")
    private String remark;
}
