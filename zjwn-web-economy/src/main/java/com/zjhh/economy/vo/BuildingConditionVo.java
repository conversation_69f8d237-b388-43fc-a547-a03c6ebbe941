package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingConditionVo extends BaseVo {
    private static final long serialVersionUID = -2968673564589702297L;


    @Schema(description = "项目编号")
    private String serialNo;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "商务总面积")
    private BigDecimal businessArea;

    @Schema(description = "已入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;

    @Schema(description = "楼栋数")
    private Integer buildingCount;

    @Schema(description = "总房源数")
    private Integer totalRoomCount;

    @Schema(description = "空置房源数")
    private Integer emptyRoomCount;

    @Schema(description = "入驻单位总数")
    private Integer unitCount;

    @Schema(description = "入驻企业数")
    private Integer entCount;

    @Schema(description = "入驻率")
    private BigDecimal settledRate;

    @Schema(description = "注册单位总数")
    private Integer registerUnitCount;

    @Schema(description = "注册企业数")
    private Integer registerEntCount;

    @Schema(description = "注册率")
    private BigDecimal registerRate;

    @Schema(description = "税收")
    private BigDecimal taxIncome;

    @Schema(description = "单位税收")
    private BigDecimal unitTaxIncome;

    @Schema(description = "除去房地产税收")
    private BigDecimal withoutBuildingTax;

    @Schema(description = "除去房地产平方税收")
    private BigDecimal withoutBuildingSquareTax;

    @Schema(description = "楼宇主导产业")
    private String mainIndustry;


}
