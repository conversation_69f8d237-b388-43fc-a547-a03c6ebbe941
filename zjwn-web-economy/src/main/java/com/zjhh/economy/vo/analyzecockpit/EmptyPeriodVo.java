package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 空置周期Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyPeriodVo extends BaseVo {
    private static final long serialVersionUID = 5924544441961376928L;

    @Schema(description = "总房源数")
    private Integer totalRoomCount;

    @Schema(description = "周期")
    private List<RoomPeriodVo> periods;

}
