package com.zjhh.economy.vo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseListVo extends BaseVo {
    private static final long serialVersionUID = -4914450492875694786L;


    @Schema(description = "主键")
    @ExcelIgnore
    private String id;


    @Schema(description = "企业编号")
    @ExcelProperty(index = 0, value = "企业编号")
    private String serialNo;

    @ExcelProperty(index = 1, value = "企业名称")
    @Schema(description = "企业名称")
    private String enterpriseName;

    @ExcelProperty(index = 2, value = "社会信用代码")
    @Schema(description = "社会信用代码")
    private String uscc;

    @ExcelProperty(index = 10, value = "电话")
    @Schema(description = "电话")
    private String phone;
    /**
     * 是否规上企业
     */


    @ExcelIgnore
    @Schema(description = "是否规上")
    private Boolean onScaled;

    @ExcelProperty(index = 13, value = "是否规上企业")
    private String onScaledStr;


    /**
     * 是否属地企业
     */

    @ExcelIgnore
    @Schema(description = "是否属地企业")
    private Boolean territorialized;

    @ExcelProperty(index = 15, value = "是否属地企业")
    private String territorializedStr;

    /**
     * 注册资本
     */

    @ExcelIgnore
    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    @ExcelProperty(index = 6, value = "注册资本")
    private String registerCapitalStr;

    @ExcelIgnore
    private Integer currencyType;

    /**
     * 成立日期
     */
    @ExcelProperty(index = 4, value = "成立日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate foundDate;

    @ExcelProperty(index = 5, value = "企业类型")
    private String enterpriseTypeName;

    /**
     * 法人
     */

    @ExcelProperty(index = 3, value = "法定代表人(负责人、执行事务合伙人)")
    @Schema(description = "法定代表人")
    private String legalPerson;

    @ExcelProperty(index = 7, value = "行业")
    @Schema(description = "行业")
    private String industryName;

    @ExcelProperty(index = 8, value = "经营范围")
    @Schema(description = "经营范围")
    private String businessScope;

    @ExcelProperty(index = 9, value = "注册地址")
    @Schema(description = "注册地址")
    private String registeredAddress;


    @ExcelIgnore
    @Schema(description = "是否重点关注")
    private Boolean focused;

    @ExcelProperty(index = 11, value = "入驻信息")
    @Schema(description = "入驻信息")
    private String settleInfo;

    @ExcelProperty(index = 12, value = "搬离信息")
    @Schema(description = "搬离信息")
    private String moveOutInfo;

    @ExcelProperty(index = 14, value = "入驻日期")
    @Schema(description = "入驻日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate registerDate;

    @ExcelIgnore
    private Integer settleStatus;

    @ExcelIgnore
    private String settleStatusStr;

    @ExcelIgnore
    private Boolean deleted;

    @ExcelProperty(index = 16, value = "企业标签")
    @Schema(description = "企业标签")
    private String enterpriseLabels;


    @Schema(description = "企业标签数组")
    @ExcelIgnore
    private List<String> enterpriseLabelsList;


    @ExcelIgnore
    @JsonFormat(timezone = "yyyy-MM-dd")
    private LocalDate createTime;

    @ExcelIgnore
    private String currencyTypeStr;


    public String getSettleStatusStr() {
        switch (settleStatus) {
            case 1:
                return "入驻中";
            case 2:
                return "已搬离";
            default:
                return "未分配";
        }
    }

    public Boolean getDeleted() {
        if (settleStatus == 1 || settleStatus == 2) {
            return false;
        } else {
            return true;
        }
    }

    public List<String> getEnterpriseLabelsList() {
        if (StrUtil.isNotBlank(enterpriseLabels)) {
            return Arrays.asList(enterpriseLabels.split("、"));
        }else {
            return null;
        }
    }

    public String getOnScaledStr() {
        if (ObjectUtil.isNull(onScaled)) {
            return null;
        }
        return onScaled ? "是" : "否";
    }

    public String getTerritorializedStr() {
        if (ObjectUtil.isNull(territorialized)) {
            return null;
        }
        return territorialized ? "是" : "否";
    }

    public String getRegisterCapitalStr() {
        if (ObjectUtil.isNull(registeredCapital)) {
            return "";
        }else if (ObjectUtil.isNotNull(currencyType)) {
           return registeredCapital+ " " + currencyTypeStr;
        } else {
            return String.valueOf(registeredCapital);
        }
    }
}
