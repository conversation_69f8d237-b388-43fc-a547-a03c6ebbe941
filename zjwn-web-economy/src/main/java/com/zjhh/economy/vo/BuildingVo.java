package com.zjhh.economy.vo;

import cn.hutool.core.util.ObjectUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingVo extends BaseVo {

    private static final long serialVersionUID = 3181292889903316288L;

    @Schema(description = "楼宇id")
    private String buildingId;

    @Schema(description = "楼宇编号")
    private String serialNo;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "所属项目")
    private String projectName;

    @Schema(description = "建筑面积")
    private BigDecimal buildingArea;

    @Schema(description = "商务面积")
    private BigDecimal businessArea;

    @Schema(description = "空置面积")
    private BigDecimal vacantArea;

    @Schema(description = "楼层数")
    private Integer floorSize;

    @Schema(description = "总房源数")
    private Integer roomSize;

    @Schema(description = "空置房源数")
    private Integer vacantRoomSize;

    @Schema(description = "已入驻企业数")
    private Integer checkInEnterpriseSize;

    @Schema(description = "规上企业数量")
    private Integer onScaledEnterpriseSize;

    @Schema(description = "空置率")
    private BigDecimal vacantRate;

    @Schema(description = "负责人")
    private String head;

    @Schema(description = "联系方式")
    private String phone;

    public BigDecimal getVacantRate() {
        if (ObjectUtil.isNull(businessArea) || businessArea.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO;
        }
        return vacantArea.multiply(new BigDecimal(100)).divide(businessArea, 2, RoundingMode.HALF_UP);
    }
}
