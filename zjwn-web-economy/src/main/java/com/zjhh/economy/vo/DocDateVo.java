package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文档管理日期
 *
 * <AUTHOR>
 * @since 2021/11/4 16:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocDateVo extends BaseVo {

    private static final long serialVersionUID = -2642525686244657501L;

    @Schema(description = "显示类型：Year为下拉年度选择框；Quarter为下拉年度+季度的选择框；Month为下拉月份选择框；Day为下拉日期段的选择框")
    private String updateType;

    @Schema(description = "最小日期")
    private String minDate;

    @Schema(description = "最大日期")
    private String maxDate;
}
