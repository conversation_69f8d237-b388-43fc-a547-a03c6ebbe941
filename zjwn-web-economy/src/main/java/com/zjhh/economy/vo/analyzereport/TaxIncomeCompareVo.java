package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 税收收入分析对比
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaxIncomeCompareVo extends BaseVo {
    private static final long serialVersionUID = -1843381235568616421L;

    @Schema(description = "显示名称")
    private String name;

    @Schema(description = "税收收入")
    private BigDecimal taxIncome;

    @Schema(description = "街道收入")
    private BigDecimal streetTaxIncome;

    @Schema(description = "同比增幅")
    private BigDecimal zf;


}
