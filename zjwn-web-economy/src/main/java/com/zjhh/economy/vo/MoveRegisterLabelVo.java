package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoveRegisterLabelVo extends BaseVo {
    private static final long serialVersionUID = 8545874040147326456L;

    @Schema(description = "标签名称")
    private String labelName;

    @Schema(description = "标签编码")
    private String labelCode;
}
