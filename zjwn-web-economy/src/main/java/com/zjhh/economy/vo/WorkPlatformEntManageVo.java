package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 工作台企业管理
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkPlatformEntManageVo extends BaseVo {
    private static final long serialVersionUID = -2773374609251106402L;

    @Schema(description = "入驻企业总数")
    private Integer totalEntCount;

    @Schema(description = "本月入驻企业数")
    private Integer entMonthCount;

    @Schema(description = "属地率")
    private BigDecimal localRate;

}
