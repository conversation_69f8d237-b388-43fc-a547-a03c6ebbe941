package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettleInfoDetailVo extends BaseVo {


    @Schema(description = "主键")
    @ExcelIgnore
    private String id;

    @Schema(description = "项目名称")
    @ExcelProperty(index = 0, value = "项目")
    private String projectName;

    @Schema(description = "楼宇名称")
    @ExcelProperty(index = 1, value = "楼宇")
    private String buildingName;

    @ExcelIgnore
    private String buildingId;
    @ExcelIgnore
    private String projectId;
    @ExcelIgnore
    private String floorId;
    @ExcelIgnore
    private String roomId;

    @Schema(description = "楼层")
    @ExcelProperty(index = 2, value = "楼层")
    private String floorName;

    @Schema(description = "房间号")
    @ExcelProperty(index = 3, value = "房号")
    private String roomNo;

    @Schema(description = "开始入驻日期")
    @ExcelProperty(index = 4, value = "开始入驻日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkInDate;

    @Schema(description = "预计搬离日期")
    @ExcelProperty(index = 5, value = "预计搬离日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectMoveOutDate;

    @Schema(description = "实际搬离日期")
    @ExcelProperty(index = 6, value = "实际搬离日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate realityMoveOutDate;

    @Schema(description = "入驻面积")
    @ExcelProperty(index = 7, value = "入驻面积（㎡）")
    private BigDecimal area;

    @Schema(description = "备注")
    @ExcelProperty(index = 8, value = "备注")
    private String remark;

    /**
     * 装修开始日期
     */
    @Schema(description = "装修开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate renovationStartDate;

    /**
     * 装修结束日期
     */
    @Schema(description = "装修结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate renovationEndDate;

    @Schema(description = "文件ID")
    @ExcelIgnore
    private String documentId;

    @Schema(description = "文件名称")
    @ExcelIgnore
    private String documentName;
    @ExcelIgnore
    private String fileSize;
    @ExcelIgnore
    private long size;

    public String getFileSize() {
        BigDecimal fileSize = BigDecimal.valueOf(size);
        BigDecimal tmp = BigDecimal.valueOf(1024);
        if (fileSize.compareTo(tmp) > 0) {
            fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
            if (fileSize.compareTo(tmp) > 0) {
                fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                if (fileSize.compareTo(tmp) > 0) {
                    fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                    return fileSize + "GB";
                }
                return fileSize + "MB";
            }
            return fileSize + "KB";
        }
        return fileSize + "B";
    }



}
