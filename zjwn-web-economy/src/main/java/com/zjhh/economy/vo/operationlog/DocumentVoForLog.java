package com.zjhh.economy.vo.operationlog;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 变更前后的附加比对，显示
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentVoForLog extends BaseVo {
    private static final long serialVersionUID = -1305647759194178880L;

    @Schema(description = "文件ID")
    private String documentId;

    @Schema(description = "文件名称")
    private String documentName;
}
