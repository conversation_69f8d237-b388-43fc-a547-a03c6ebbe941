package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 租用趋势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SettleTrendVo extends BaseVo {

    private static final long serialVersionUID = -6479860740966411667L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "房间数")
    private Integer roomCount;

    @Schema(description = "入驻面积")
    private BigDecimal area;
}
