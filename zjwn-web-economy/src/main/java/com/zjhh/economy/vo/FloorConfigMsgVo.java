package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class FloorConfigMsgVo extends BaseVo {
    private static final long serialVersionUID = -2285781000494594181L;

    @Schema(description = "楼层平面图配置")
    private String planeConfig;

    @Schema(description = "平面图配置id")
    private String planeConfigId;
}
