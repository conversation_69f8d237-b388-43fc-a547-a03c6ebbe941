package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class EntExportVo extends BaseVo {

    @Schema(description = "企业名称")
    @ExcelProperty(index = 1, value = "企业名称")
    private String enterpriseName;

    @Schema(description = "企业编号")
    @ExcelProperty(index = 0, value = "企业编号")
    private String serialNo;

    /**
     * 社会信用代码
     */
    @Schema(description = "社会信用代码")
    @ExcelProperty(index = 2, value = "社会信用代码")
    private String uscc;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    @ExcelProperty(index = 4, value = "成立日期")
    private LocalDate foundDate;

    /**
     * 法人
     */
    @Schema(description = "法人")
    @ExcelProperty(index = 3, value = "法定代表人(负责人、执行事务合伙人)")
    private String legalPerson;

    /**
     * 企业类型
     */
    @Schema(description = "企业类型名称")
    @ExcelProperty(index = 5, value = "企业类型")
    private String enterpriseTypeName;

    /**
     * 行业
     */
    @Schema(description = "行业名称")
    @ExcelProperty(index = 7, value = "行业")
    private String industryName;


    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    @ExcelProperty(index = 6, value = "注册资本")
    private BigDecimal registeredCapital;


    /**
     * 住所
     */
    @Schema(description = "住所")
    @ExcelProperty(index = 9, value = "住所")
    private String residence;

    /**
     * 电话
     */
    @Schema(description = "电话")
    @ExcelProperty(index = 10, value = "电话")
    private String phone;

    /**
     * 是否规上企业
     */
    @Schema(description = "是否规上企业")
    @ExcelIgnore
    private Boolean onScaled;

    @ExcelProperty(index = 13, value = "是否规上企业")
    private String onScaledStr;

    /**
     * 是否属地企业
     */
    @Schema(description = "是否属地企业")
    @ExcelIgnore
    private Boolean territorialized;

    @ExcelProperty(index = 14, value = "是否属地企业")
    private String territorializedStr;


    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    @ExcelProperty(index = 8, value = "经营范围")
    private String businessScope;


    @Schema(description = "企业标签")
    private String enterpriseLabels;

    @ExcelProperty(index = 11, value = "入驻信息")
    private String settleInfo;

    @ExcelProperty(index = 12, value = "入驻日期")
    private LocalDate settleDate;


    public String getOnScaledStr() {
        return onScaled ? "是" : "否";
    }

    public String getTerritorializedStr() {
        return territorialized ? "是" : "否";
    }
}
