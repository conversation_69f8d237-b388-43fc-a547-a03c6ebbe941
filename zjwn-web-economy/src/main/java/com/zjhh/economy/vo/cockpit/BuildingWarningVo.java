package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 楼宇预警
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingWarningVo extends BaseVo {
    private static final long serialVersionUID = 822689117058732206L;

    @Schema(description = "预警编码")
    private String warningCode;

    @Schema(description = "预警数量")
    private Integer warningCount;
}
