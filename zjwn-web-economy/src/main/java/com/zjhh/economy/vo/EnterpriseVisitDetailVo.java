package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseVisitDetailVo extends BaseVo {
    private static final long serialVersionUID = 2728713365245591689L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "走访目的")
    private String visitPurpose;

    @Schema(description = "走访人员")
    private String visitor;

    @Schema(description = "走访日期")
    private LocalDate visitDate;

    @Schema(description = "对接人")
    private String receptionist;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "附件ID")
    private String documentId;

    @Schema(description = "附件名称")
    private String documentName;

    private String fileSize;

    @Schema(hidden = true)
    private long size;

    public String getFileSize() {
        BigDecimal fileSize = BigDecimal.valueOf(size);
        BigDecimal tmp = BigDecimal.valueOf(1024);
        if (fileSize.compareTo(tmp) > 0) {
            fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
            if (fileSize.compareTo(tmp) > 0) {
                fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                if (fileSize.compareTo(tmp) > 0) {
                    fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                    return fileSize + "GB";
                }
                return fileSize + "MB";
            }
            return fileSize + "KB";
        }
        return fileSize + "B";
    }


}
