package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 空间分析汇总VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpaceAnalyzeSummaryVo extends BaseVo {
    private static final long serialVersionUID = 8232314268873905710L;

    @Schema(description = "总商务面积")
    private BigDecimal totalBusinessArea;

    @Schema(description = "总商务面积环比")
    private BigDecimal totalBusinessAreaHb;

    @Schema(description = "总商务换机同比")
    private BigDecimal totalBusinessAreaTb;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "入驻面积环比")
    private BigDecimal settledAreaHb;

    @Schema(description = "入驻面积同比")
    private BigDecimal settledAreaTb;

    @Schema(description = "入驻率")
    private BigDecimal settledRate;

    @Schema(description = "入驻率环比")
    private BigDecimal settledRateHb;

    @Schema(description = "入驻率同比")
    private BigDecimal settledRateTb;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "空置面积同比")
    private BigDecimal emptyAreaTb;

    @Schema(description = "空置面积环比")
    private BigDecimal emptyAreaHb;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;

    @Schema(description = "空置率环比")
    private BigDecimal emptyRateHb;

    @Schema(description = "空置率同比")
    private BigDecimal emptyRateTb;


}
