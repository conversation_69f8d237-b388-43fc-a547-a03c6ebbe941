package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 经济分析汇总Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EconomicSummaryVo extends BaseVo {
    private static final long serialVersionUID = -6696064926667422140L;

    @Schema(description = "税收收入")
    private BigDecimal taxIncome;

    @Schema(description = "税收收入环比")
    private BigDecimal taxIncomeHb;

    @Schema(description = "税收收入同比")
    private BigDecimal taxIncomeTb;

    @Schema(description = "街道收入")
    private BigDecimal streetTaxIncome;

    @Schema(description = "街道收入环比")
    private BigDecimal streetTaxIncomeHb;

    @Schema(description = "街道收入同比")
    private BigDecimal streetTaxIncomeTb;

    @Schema(description = "单位产出")
    private BigDecimal unitTaxIncome;

    @Schema(description = "单位产出环比")
    private BigDecimal unitTaxIncomeHb;

    @Schema(description = "单位产出同比")
    private BigDecimal unitTaxIncomeTb;

    @Schema(description = "除房地产税收")
    private BigDecimal taxIncomeWithoutFdc;

    @Schema(description = "除房地产税收环比")
    private BigDecimal taxIncomeWithoutFdcHb;

    @Schema(description = "除房地产税收同比")
    private BigDecimal taxIncomeWithoutFdcTb;

    @Schema(description = "除房地产单位产出")
    private BigDecimal unitTaxIncomeWithoutFdc;

    @Schema(description = "除房地产单位产出环比")
    private BigDecimal unitTaxIncomeWithoutFdcHb;

    @Schema(description = "除房地产单位产出同比")
    private BigDecimal unitTaxIncomeWithoutFdcTb;

}
