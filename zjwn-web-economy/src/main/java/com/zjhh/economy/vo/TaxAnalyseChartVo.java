package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 税收分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaxAnalyseChartVo extends BaseVo {
    private static final long serialVersionUID = -6011081476758704704L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "日期")
    private String datekeyStr;

    @Schema(description = "纳税额")
    private BigDecimal amount;

    @Schema(description = "增幅")
    private BigDecimal zf;


}
