package com.zjhh.economy.vo;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentDetailVo extends BaseVo {

    private static final long serialVersionUID = 1573894312215651877L;
    @Schema(description = "文件id")
    private String id;

    @Schema(description = "文件名称")
    private String title;

    @Schema(description = "文件路径")
    private String path;

    @JsonIgnore
    private Long size;

    @Schema(description = "文件大小")
    private String fileSize;

    public String getFileSize() {
        if (Objects.isNull(size)) {
            return null;
        }
        return FileUtil.readableFileSize(size).replace(" ", "");
    }
}
