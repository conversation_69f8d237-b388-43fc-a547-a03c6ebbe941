package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业纳税情况
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class EntTaxPaidVo extends BaseVo {
    private static final long serialVersionUID = 3210975317117352935L;

    @Schema(description = "纳税分级")
    private String level;

    @Schema(description = "企业数量")
    private Integer entCount;

}
