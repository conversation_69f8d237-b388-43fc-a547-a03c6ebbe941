package com.zjhh.economy.vo.report;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 评价Vo
 *
 * <AUTHOR>
 * @date 2022-05-25 2:49 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportCommentVo extends BaseVo {

    private static final long serialVersionUID = -5597408028661112335L;

    /**
     * 主键
     */
    @Schema(description = "id")
    private String id;

    /**
     * 预警分析报告编码
     */
    @Schema(description = "报告ID")
    private String reportId;

    /**
     * 评价内容
     */
    @Schema(description = "评价内容")
    private String commentInfo;

    /**
     * 附件名称
     */
    @Schema(description = "附件名称")
    private String fileName;

    /**
     * 附件地址
     */
    @Schema(description = "附件地址")
    private Long fileGuid;

    @Schema(description = "单位")
    private String unitName;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "是否显示删除")
    private Integer showDelete;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 预留字段1
     */
    private String hold1;

    /**
     * 预留字段2
     */
    private String hold2;

    /**
     * 预留字段3
     */
    private String hold3;

    /**
     * 预留字段4
     */
    private String hold4;

    /**
     * 预留字段5
     */
    private String hold5;

    /**
     * 预留字段6
     */
    private String hold6;

    /**
     * 预留字段7
     */
    private String hold7;

    /**
     * 预留字段8
     */
    private String hold8;

    /**
     * 预留字段9
     */
    private String hold9;

    /**
     * 预留字段10
     */
    private String hold10;

    /**
     * 行政区划代码
     */
    private String admDivCode;

    /**
     * 行政区划名称
     */
    private String admDivName;

    /**
     * 显示信息
     */
    private String displayInfo;

    /**
     * 链接模块
     */
    private String linkModule;

}
