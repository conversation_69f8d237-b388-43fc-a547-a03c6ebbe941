package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业属地分布
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntLocaledDisVo extends BaseVo {
    private static final long serialVersionUID = -3420964222034623214L;

    @Schema(description = "分布名称")
    private String disName;

    @Schema(description = "分布数量")
    private String entCount;

    @Schema(description = "占比")
    private String zb;

}
