package com.zjhh.economy.vo.report;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 部门反馈Vo
 *
 * <AUTHOR>
 * @date 2022-05-25 2:49 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportFeedbackVo extends BaseVo {

    private static final long serialVersionUID = 9082564340806146695L;

    /**
     * 主键
     */
    @Schema(description = "id")
    private String id;

    /**
     * 预警分析报告编码
     */
    @Schema(description = "报告id")
    private String reportId;

    @Schema(description = "是否显示删除")
    private Integer showDelete;

    /**
     * 反馈内容
     */
    @Schema(description = "反馈内容")
    private String feedbackInfo;

    /**
     * 附件名称
     */
    @Schema(description = "附件名称")
    private String fileName;

    /**
     * 附件地址
     */
    @Schema(description = "文件Guido")
    private String fileGuid;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    private String unitCode;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String unitName;

    /**
     * 预留字段1
     */
    private String hold1;

    /**
     * 预留字段2
     */
    private String hold2;

    /**
     * 预留字段3
     */
    private String hold3;

    /**
     * 预留字段4
     */
    private String hold4;

    /**
     * 预留字段5
     */
    private String hold5;

    /**
     * 预留字段6
     */
    private String hold6;

    /**
     * 预留字段7
     */
    private String hold7;

    /**
     * 预留字段8
     */
    private String hold8;

    /**
     * 预留字段9
     */
    private String hold9;

    /**
     * 预留字段10
     */
    private String hold10;

    /**
     * 行政区划代码
     */
    private String admDivCode;

    /**
     * 行政区划名称
     */
    private String admDivName;

    /**
     * 显示信息
     */
    private String displayInfo;

    /**
     * 链接模块
     */
    private String linkModule;
}
