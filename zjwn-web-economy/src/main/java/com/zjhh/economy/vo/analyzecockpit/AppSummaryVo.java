package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppSummaryVo extends BaseVo {
    private static final long serialVersionUID = 2150550715034973247L;

    @Schema(description = "总商务面积")
    private String totalBusinessArea;

    @Schema(description = "总商务换机同比")
    private String totalBusinessAreaTb;

    @Schema(description = "入驻面积")
    private String settledArea;

    @Schema(description = "入驻面积同比")
    private String settledAreaTb;

    @Schema(description = "入驻率")
    private String settledRate;

    @Schema(description = "入驻率同比")
    private String settledRateTb;

    @Schema(description = "税收收入")
    private BigDecimal taxIncome;

    @Schema(description = "税收收入同比")
    private BigDecimal taxIncomeTb;

    @Schema(description = "街道收入")
    private BigDecimal streetTaxIncome;

    @Schema(description = "街道收入同比")
    private BigDecimal streetTaxIncomeTb;

    @Schema(description = "除房地产税收")
    private BigDecimal taxIncomeWithoutFdc;

    @Schema(description = "除房地产税收同比")
    private BigDecimal taxIncomeWithoutFdcTb;


    @Schema(description = "入驻总数")
    private String settledTotalCount;

    @Schema(description = "入驻总数同比")
    private String settledTotalCountTb;

    @Schema(description = "属地企业数")
    private String registerCount;

    @Schema(description = "属地企业数同比")
    private String registerCountTb;

    @Schema(description = "属地企业数注册率")
    private String registerRate;

    @Schema(description = "属地企业数注册率同比")
    private String registerRateTb;

}
