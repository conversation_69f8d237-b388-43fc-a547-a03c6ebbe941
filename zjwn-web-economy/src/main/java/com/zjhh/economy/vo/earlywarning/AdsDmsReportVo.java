package com.zjhh.economy.vo.earlywarning;

import com.zjhh.economy.dao.entity.AdsDmsReport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/24 11:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdsDmsReportVo extends AdsDmsReport {

    @Serial
    private static final long serialVersionUID = 6200286979724357597L;

    @Schema(description = "是否可预览")
    private Boolean preview;

    @Schema(description = "文件名称")
    private String docName;

    @Schema(description = "行政区域编码")
    private List<String> orgCodes;

}
