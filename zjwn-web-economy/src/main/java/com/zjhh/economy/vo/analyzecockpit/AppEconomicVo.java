package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 移动端经济分析Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppEconomicVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -7003825003586300975L;

    @Schema(description = "对比项")
    private String name;

    @Schema(description = "税收收入、单位产出")
    private String amount1;

    @Schema(description = "街道收入、除房地产税收，除房地产单位产出")
    private String amount2;

    @Schema(hidden = true)
    private String currentYear;
}
