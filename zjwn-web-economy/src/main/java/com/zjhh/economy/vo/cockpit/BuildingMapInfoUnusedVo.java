package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新建
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingMapInfoUnusedVo extends BaseVo {
    private static final long serialVersionUID = -7951121371324825645L;

    @Schema(description = "id")
    private String projectId;

    @Schema(description = "名称")
    private String projectName;

    @Schema(description = "经度")
    private String latitude;

    @Schema(description = "纬度")
    private String longitude;

    @Schema(description = "x")
    private String x;

    @Schema(description = "y")
    private String y;

    @Schema(description = "显示位置")
    private String showPosition;



}
