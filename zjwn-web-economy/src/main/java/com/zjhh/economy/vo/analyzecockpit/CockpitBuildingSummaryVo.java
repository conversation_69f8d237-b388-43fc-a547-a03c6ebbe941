package com.zjhh.economy.vo.analyzecockpit;

import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CockpitBuildingSummaryVo extends BaseVo {
    private static final long serialVersionUID = -406087711204796970L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "运行日期")
    private String openingDate;

    @Schema(description = "外部照片")
    private String outsideImgId;

    @Schema(description = "其他照片")
    private String otherImgId;

    @Schema(description = "楼宇地址")
    private String buildingAddress;

    @Schema(description = "楼层")
    private String floor;

    @Schema(description = "房间面积")
    private String roomArea;

    @Schema(description = "负责人")
    private String manager;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "楼宇定位")
    private List<String> buildingLocation;

    @Schema(description = "楼宇标签")
    private List<String> buildingTags;

    @Schema(description = "楼宇简介")
    private String buildingIntro;

    @Schema(description = "楼宇配套")
    private String buildingSupporting;

    @Schema(description = "楼宇相册")
    private List<String> buildingImages;

    public List<String> getBuildingImages() {
        List<String> images = new ArrayList<>();
        if (StrUtil.isNotBlank(otherImgId)) {
            images.add(otherImgId);
        }

        return images;
    }

    public String getOpeningDate() {

        try {
            String inputDateStr = openingDate;
            SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy年M月d日");
            Date date = inputDateFormat.parse(inputDateStr);

            // 将 Date 对象格式化为目标字符串格式
            String outputDateStr = outputDateFormat.format(date);
            return outputDateStr;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
