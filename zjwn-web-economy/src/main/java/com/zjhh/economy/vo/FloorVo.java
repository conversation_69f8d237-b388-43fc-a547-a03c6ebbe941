package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 17:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FloorVo extends BaseVo {

    private static final long serialVersionUID = -2358964255740029071L;

    @Schema(description = "楼层id")
    private String floorId;

    @Schema(description = "建筑楼层")
    private Integer floorNo;

    @Schema(description = "楼层名称")
    private String floorName;

    @Schema(description = "房间数")
    private Integer roomSize;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "楼层平面图id")
    private String planeImgId;

    @Schema(description = "楼层平面图配置id")
    private String planeConfigId;
}
