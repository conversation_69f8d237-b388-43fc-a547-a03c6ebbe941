package com.zjhh.economy.vo.report;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报告评价Req
 *
 * <AUTHOR>
 * @date 2022-05-25 2:37 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportCommentReq extends BaseReq {
    private static final long serialVersionUID = -185445571127175022L;


    @Schema(description = "报告ID")
    private String reportId;

    @Schema(description = "反馈内容")
    private String commentInfo;

    @Schema(description = "附件名称")
    private String fileName;

    @Schema(description = "附件地址")
    private String fileDir;
}
