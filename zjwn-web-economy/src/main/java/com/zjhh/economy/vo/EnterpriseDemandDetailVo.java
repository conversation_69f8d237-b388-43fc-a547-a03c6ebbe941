package com.zjhh.economy.vo;

import cn.hutool.core.util.ObjectUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseDemandDetailVo extends BaseVo {
    private static final long serialVersionUID = -7861672660955897295L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "诉求描述")
    private String demandDesc;

    @Schema(description = "提交日期")
    private LocalDate submitDate;

    @Schema(description = "提交途径")
    private String submitSource;

    @Schema(description = "提交途径")
    private String submitSourceStr;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "需求类型")
    private String demandType;


    @Schema(description = "需求类型")
    private String demandTypeStr;

    @Schema(hidden = true)
    private Integer handleType;

    @Schema(description = "显示处理结果")
    private Boolean showHandle;

    @Schema(description = "楼宇id")
    private String buildingId;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "处理结果")
    private List<DemandHandleVo> handles;

    @Schema(description = "照片")
    private List<String> photos;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "录入人")
    private String createUser;


    public Boolean getShowHandle() {
        return ObjectUtil.isNotEmpty(handleType);
    }
}
