package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 *  企业纳税 情况 Top10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntTaxPaidRankVo extends BaseVo {
    private static final long serialVersionUID = 2225387535533038762L;
    

    @Schema(description = "企业名称")
    private String entName;

    @Schema(description = "纳税额")
    private BigDecimal paidAmount;
}
