package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.request.ProjectImageVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectDetailVo extends BaseVo {

    @Schema(description = "主键")
    private String id;
    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 社区编码
     */
    @Schema(description = "社区编码")
    private String communityCode;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String longitude;

    /**
     * 维度
     */
    @Schema(description = "纬度")
    private String dimension;

    /**
     * 占地面积
     */
    @Schema(description = "战地面积")
    private BigDecimal projectArea;

    /**
     * 物业公司
     */
    @Schema(description = "物业公司")
    private String manageCompany;

    /**
     * 联系
     */
    @Schema(description = "联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 项目介绍
     */
    @Schema(description = "项目介绍")
    private String projectIntro;

    @Schema(description = "项目坐落")
    private String address;

    @Schema(description = "项目类型")
    private String projectType;

    /**
     * 项目图片
     */
    private List<ProjectImageVo> projectImages;
}
