package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class RoomSettleInfoDetailVo extends BaseVo {


    private static final long serialVersionUID = -3676759041531579898L;
    @Schema(description = "主键")
    @ExcelIgnore
    private String id;

    private String buildingId;

    private String projectId;

    private String floorId;

    private String roomId;

    private String enterpriseId;

    private String enterpriseName;

    @Schema(description = "开始入驻日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkInDate;

    @Schema(description = "预计搬离日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectMoveOutDate;

    @Schema(description = "实际搬离日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate realityMoveOutDate;

    @Schema(description = "入驻面积")
    private BigDecimal area;

    @Schema(description = "备注")
    private String remark;

    /**
     * 装修开始日期
     */
    @Schema(description = "装修开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate renovationStartDate;

    /**
     * 装修结束日期
     */
    @Schema(description = "装修结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate renovationEndDate;

    @Schema(description = "文件ID")
    private String documentId;

    @Schema(description = "文件名称")
    private String documentName;

    private String fileSize;

    private long size;

    public String getFileSize() {
        BigDecimal fileSize = BigDecimal.valueOf(size);
        BigDecimal tmp = BigDecimal.valueOf(1024);
        if (fileSize.compareTo(tmp) > 0) {
            fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
            if (fileSize.compareTo(tmp) > 0) {
                fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                if (fileSize.compareTo(tmp) > 0) {
                    fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                    return fileSize + "GB";
                }
                return fileSize + "MB";
            }
            return fileSize + "KB";
        }
        return fileSize + "B";
    }



}
