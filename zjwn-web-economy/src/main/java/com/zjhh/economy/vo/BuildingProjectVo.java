package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 20:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingProjectVo extends BaseVo {

    private static final long serialVersionUID = 3264198200284492102L;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "坐落地址")
    private String address;
}
