package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CommonFunctionVo extends BaseVo {
    private static final long serialVersionUID = 3036813560102949319L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "菜单路径")
    private String menuPath;

    @Schema(description = "查询")
    private String query;

}
