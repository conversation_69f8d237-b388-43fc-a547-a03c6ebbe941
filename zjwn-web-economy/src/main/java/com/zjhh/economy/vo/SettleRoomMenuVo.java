package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 入驻信息房号下拉框
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SettleRoomMenuVo extends BaseVo {

    @Schema(description = "房间ID")
    private String roomId;

    @Schema(description = "房号")
    private String roomNo;

    @Schema(description = "面积")
    private BigDecimal area;
}
