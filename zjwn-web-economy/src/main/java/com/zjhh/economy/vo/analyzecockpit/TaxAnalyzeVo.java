package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxAnalyzeVo extends BaseVo {
    private static final long serialVersionUID = -3099484290030814728L;

    @Schema(description = "收税收入")
    private BigDecimal taxIncome;

    @Schema(description = "除房地产税收")
    private BigDecimal taxIncomeWithoutFdc;

    @Schema(description = "街道收入")
    private BigDecimal streetIncome;

    @Schema(description = "单位产出")
    private BigDecimal unitIncome;

    @Schema(description = "除房地产单位产出")
    private BigDecimal taxIncomeWithoutUnitFdc;


}
