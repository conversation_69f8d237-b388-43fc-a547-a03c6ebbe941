package com.zjhh.economy.vo;

import cn.hutool.core.util.ObjectUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingArchiveVo extends BaseVo {
    private static final long serialVersionUID = -2284699201170002558L;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "企业名称")
    private String entName;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "楼宇")
    private String buildingName;

    @Schema(description = "楼层")
    private String floorNo;

    @Schema(description = "统一社会信用代码")
    private String uscc;

    @Schema(description = "财政分片")
    private String financialSplit;

    @Schema(description = "注册地址")
    private String registerAddress;

    @Schema(description = "经营地址")
    private String businessAddress;

    @Schema(description = "经营范围")
    private String businessScope;

    @Schema(description = "单位性质")
    private String unitPropertyStr;

    @Schema(description = "注册资本")
    private String registerCapital;

    @Schema(description = "货币")
    private String currentType;

    @Schema(description = "行业代码")
    private String industryCode;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "法人")
    private String legalPerson;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "财务人员")
    private String financialPerson;

    @Schema(description = "财务人员电话")
    private String financialPersonPhone;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "经营状态")
    private String businessStatusStr;

    @Schema(description = "经营状态备注")
    private String businessStatusRemark;

    @Schema(description = "入驻时长")
    private String settledTime;

    @Schema(description = "从业人员")
    private String employeeCount;

    @Schema(description = "是否录过表")
    private String recordedStr;

    @Schema(description = "是否属地")
    private String territorializedStr;

    @Schema(description = "是否规上")
    private String onScaledStr;

    @Schema(description = "属地及税收情况")
    private String localTaxStatusStr;

    @Schema(description = "是否在工商系统中")
    private String inSystemStr;

    @Schema(description = "税收收入")
    private BigDecimal taxIncome;

    @Schema(description = "街道税收收入")
    private BigDecimal streetIncome;

    @Schema(description = "上年同期税收收入")
    private BigDecimal sqAmt;

    @Schema(description = "上年同期街道税收收入")
    private BigDecimal streetSqAmt;

    @Schema(description = "同期增长额")
    private BigDecimal tqAddAmt;

    @Schema(description = "同期街道增长额")
    private BigDecimal streetTqAddAmt;

    @Schema(description = "上年累计")
    private BigDecimal sqljAmt;

    @Schema(description = "上月累计")
    private BigDecimal syljAmt;

    @Schema(description = "增值税")
    private BigDecimal zzs;

    @Schema(description = "个人所得税")
    private BigDecimal grsds;

    @Schema(description = "企业所得税")
    private BigDecimal qysds;

    @Schema(description = "城市维护建设税")
    private BigDecimal cswhjss;

    @Schema(description = "房产税")
    private BigDecimal fcs;

    @Schema(description = "城镇土地使用税")
    private BigDecimal cztdsys;

    @Schema(description = "土地增值税")
    private BigDecimal tdzzs;

    @Schema(description = "印花税")
    private BigDecimal yhs;

    @Schema(description = "耕地占用税")
    private BigDecimal gdzys;

    @Schema(description = "消费税")
    private BigDecimal xfs;

    @Schema(description = "资源税")
    private BigDecimal zys;

    @Schema(description = "环境保护税")
    private BigDecimal hjbhs;

    @Schema(description = "国有资源")
    private BigDecimal gyzy;

    @Schema(description = "企业标签")
    private String label;

    @Schema(description = "企业联系人")
    private String entContactPerson;

    @Schema(description = "企业联系电话")
    private String entPhone;

    @Schema(description = "企业类型")
    private String enterpriseType;

    @Schema(description = "房产持有情况")
    private String propertyOwnership;

    private String moveOutInfo;

    private String settledInfo;

    public BigDecimal getTqAddAmt() {
        if (ObjectUtil.isNull(taxIncome)) {
            taxIncome = new BigDecimal(0);
        }

        if (ObjectUtil.isNull(sqAmt)) {
            sqAmt = new BigDecimal(0);
        }
        return taxIncome.subtract(sqAmt);
    }

    public BigDecimal getStreetTqAddAmt() {
        if (ObjectUtil.isNull(streetIncome)) {
            streetIncome = new BigDecimal(0);
        }

        if (ObjectUtil.isNull(streetSqAmt)) {
            streetSqAmt = new BigDecimal(0);
        }
        return streetIncome.subtract(streetSqAmt);
    }
}
