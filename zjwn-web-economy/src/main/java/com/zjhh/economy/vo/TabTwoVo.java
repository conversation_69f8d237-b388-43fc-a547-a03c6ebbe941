package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端经济分析主要经济指标完成情况Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TabTwoVo extends BaseVo {
    private static final long serialVersionUID = 6412124590541023883L;

    @Schema(description = "经济指标")
    private String jjzb;

    @Schema(description = "单位")
    private String dw;

    @Schema(description = "本月完成")
    private String byAmt;

    @Schema(description = "本年完成")
    private String bqAmt;

    @Schema(description = "去年同期")
    private String sqAmt;

    @Schema(description = "增长率")
    private String rate;


}
