package com.zjhh.economy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import com.zjhh.system.annotation.SaFieldPermission;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 企业详情
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseDetailHeaderVo extends BaseVo {
    private static final long serialVersionUID = -3814544145095473882L;

    private String id;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "行业")
    private String industryName;

    @Schema(description = "行业")
    private String fullIndustryName;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "企业联系人")
    private String entContactPerson;

    @Schema(description = "企业联系电话")
    private String entPhone;

    /**
     * 是否规上企业
     */
    @Schema(description = "是否规上企业")
    private Boolean onScaled;

    /**
     * 是否属地企业
     */
    @Schema(description = "是否属地企业")
    private Boolean territorialized;

    @Schema(description = "是否重点关注")
    private Boolean focused;


    /**
     * 企业logo
     */
    @Schema(description = "企业logo")
    private String logoImgId;

    @Schema(description = "企业标签")
    private List<String> enterpriseLabels;

    @Schema(description = "入驻信息")
    private String settleInfo;

    @Schema(description = "税收总量")

    @SaFieldPermission(orPermissions = {"enterprise:totalTax", "LOGIN_TYPE:MOBILE"})
    private BigDecimal totalTax;

    @Schema(description = "入驻面积")
    @SaFieldPermission(orPermissions = {"enterprise:settleArea", "LOGIN_TYPE:MOBILE"})
    private BigDecimal settleArea;

    @Schema(description = "企业单位产出")
    @SaFieldPermission(orPermissions = {"enterprise:output", "LOGIN_TYPE:MOBILE"})
    private BigDecimal output;

    @Schema(description = "入驻房间数")
    @SaFieldPermission(orPermissions = {"enterprise:settleRoomCount", "LOGIN_TYPE:MOBILE"})
    private Integer settleRoomCount;

    @Schema(description = "入驻时长")
    @SaFieldPermission(orPermissions = {"enterprise:settledTime", "LOGIN_TYPE:MOBILE"})
    private String settledTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime updateDate;

    @Schema(description = "搬离信息")
    private String moveOutInfo;

}
