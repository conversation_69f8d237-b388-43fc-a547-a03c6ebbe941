package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 楼宇空置分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyBuildingFloorTableVo extends BaseVo {
    private static final long serialVersionUID = 4137762642638283936L;


    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "楼宇ID")
    private String buildingId;

    @Schema(description = "社区名称")
    private String communityName;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "楼层")
    private String floorName;

    @Schema(description = "总商务面积")
    private BigDecimal totalBusinessArea;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "房间数")
    private Integer roomNum;

    @Schema(description = "空置房间数")
    private Integer emptyRoomNum;

}
