package com.zjhh.economy.vo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorRuleDetailVo extends BaseVo {
    private static final long serialVersionUID = 4281578988959311666L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "监测规则1-月度-2季度-3年度")
    private Integer monitorRulePeriod;

    @Schema(description = "1-同比-2环比")
    private Integer monitorRuleCompare;

    @Schema(description = "1-上升-2下降")
    private Integer monitorRuleChange;

    @Schema(description = "增幅")
    private Integer monitorRuleZf;

    @Schema(description = "监测范围")
    private List<String> monitorRangeIds;

    @Schema(description = "监测返回字符串聚合", hidden = true)
    @JsonIgnore
    private String monitorRange;

    public List<String> getMonitorRangeIds() {
        if (StrUtil.isBlank(monitorRange)) {
            return new ArrayList<>();
        } else {
            return Arrays.asList(monitorRange.split(","));
        }
    }
}
