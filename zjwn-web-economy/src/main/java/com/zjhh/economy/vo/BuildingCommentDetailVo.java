package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingCommentDetailVo extends BaseVo {
    private static final long serialVersionUID = 5984217110234418201L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "指标Id")
    private String commentIndicatorId;

    @Schema(description = "楼宇Id")
    private String buildingId;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "年度")
    private String datekey;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "一级指标")
    private String indicatorOne;

    @Schema(description = "二级指标")
    private String indicatorTwo;

    @Schema(description = "评分要求")
    private String commentDemand;

    @Schema(description = "分值")
    private BigDecimal score;

    @Schema(description = "评价方法")
    private String commentMethod;

    @Schema(description = "评价类型")
    private Integer commentType;

    @Schema(description = "获取分值")
    private BigDecimal commentScore;

    @Schema(description = "可否编辑")
    private Boolean edited;


}
