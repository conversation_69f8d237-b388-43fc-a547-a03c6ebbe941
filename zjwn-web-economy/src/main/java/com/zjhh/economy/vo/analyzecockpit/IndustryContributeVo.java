package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 各产业税收贡献走势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryContributeVo extends BaseVo {
    private static final long serialVersionUID = 2709682073590921875L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "税收收入")
    private String entCount;

    @Schema(description = "占比")
    private String zb;

}
