package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.comm.vo.TreeSelectVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryContributeAssembleVo extends BaseVo {
    private static final long serialVersionUID = -7114582289797122250L;

    private List<TreeSelectVo> industries;

    private List<IndustryContributeVo> list;

}
