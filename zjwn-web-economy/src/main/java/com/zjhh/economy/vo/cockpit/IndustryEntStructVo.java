package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产业结构企业数量
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryEntStructVo extends BaseVo {
    private static final long serialVersionUID = 3345179169662017174L;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "行业编码")
    private String industryCode;

    @Schema(description = "企业总数")
    private BigDecimal entCount;

    @Schema(description = "占比")
    private BigDecimal zb;
}
