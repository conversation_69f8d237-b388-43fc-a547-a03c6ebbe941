package com.zjhh.economy.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingCommentVo extends BaseVo {
    private static final long serialVersionUID = -1695969963107638877L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "楼宇ID")
    private String buildingId;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "社区名称")
    private String communityName;

    @Schema(description = "当前星级")
    @JsonIgnore
    private Integer currentStar;

    @Schema(description = "预计星级")
    @JsonIgnore
    private Integer expectStar;


    @Schema(description = "当前星级转义")
    private String currentStarStr;

    @Schema(description = "预计星级转义")
    private String expectStarStr;


    @Schema(description = "排名")
    private Integer rank;

    @Schema(description = "总分数")
    private BigDecimal totalScore;

    @Schema(description = "建设评分")
    private BigDecimal buildingScore;

    @Schema(description = "建设总分")
    private BigDecimal totalBuildingScore;

    @Schema(description = "服务评分")
    private BigDecimal serviceScore;

    @Schema(description = "服务总分分")
    private BigDecimal totalServiceScore;

    @Schema(description = "贡献评分")
    private BigDecimal contributeScore;

    @Schema(description = "贡献总分")
    private BigDecimal totalContributeScore;

    private String tips;

    public String getTips() {
        return currentStar > expectStar ? "可能存在摘星风险" : "可能存在升星潜力";
    }

    public String getCurrentStarStr() {
        switch (currentStar) {
            case 2:
                return "三星";
            case 3:
                return "四星";
            case 4:
                return "五星";
            default:
                return "无";
        }
    }

    public String getExpectStarStr() {
        switch (expectStar) {
            case 2:
                return "三星";
            case 3:
                return "四星";
            case 4:
                return "五星";
            default:
                return "无";
        }
    }
}
