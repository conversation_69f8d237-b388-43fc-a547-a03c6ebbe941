package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 属地注册率趋势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LocaledRegisterTrendVo extends BaseVo {
    private static final long serialVersionUID = 2321608801576153369L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "企业总数量")
    private String entTotalCount;

    @Schema(description = "属地企业刷领")
    private String localedEntCount;

    @Schema(description = "属地企业占比")
    private String localedZb;

    @Schema(hidden = true)
    private String localedCount;

    @Schema(hidden = true)
    private String zb;

    public String getLocaledCount() {
        return localedEntCount;
    }

    public String getZb() {
        return localedZb;
    }

    public String getDatekey() {
        String quarter = "";
        if (datekey.length() == 5 && !datekey.contains("-")) {
            quarter = datekey.substring(4,5);
        }
        if (quarter.equals("1") && datekey.length() == 5) {
            return datekey.substring(0,4) + "年" + "第一季度";
        }else if (quarter.equals("2")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第二季度";
        } else if (quarter.equals("3")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第三季度";

        }else if (quarter.equals("4")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第四季度";
        }else {
            return datekey;
        }
    }

}
