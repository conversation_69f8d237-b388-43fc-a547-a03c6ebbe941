package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端楼宇动态Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TabSixVo extends BaseVo {


    private static final long serialVersionUID = -6593701505962371768L;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "楼宇名称")
    private String lymc;

    @Schema(description = "楼宇面积")
    private String lymj;

    @Schema(description = "入驻情况-已用面积")
    private String yymj;

    @Schema(description = "入驻情况-入住率")
    private String rzl;

    @Schema(description = "注册情况-法人及/产品个人(家)")
    private String sl;

    @Schema(description = "注册情况-注册数")
    private String zcs;

    @Schema(description = "注册情况-注册数")
    private String zcl;

    @Schema(description = "财税情况-税收")
    private String ss;

    @Schema(description = "财税情况-增幅")
    private String ssZf;

    @Schema(description = "财税情况-单位税收(元/平米)")
    private String dwss;

    @Schema(description = "财税情况-除房地产税收(万元)")
    private String cfdcss;

    @Schema(description = "财税情况-增幅")
    private String cfdcssZf;

    @Schema(description = "财税情况-除房地产单位税收(元/平米)")
    private String cfdcssSs;


}
