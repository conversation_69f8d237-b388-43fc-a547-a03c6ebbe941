package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettledAreaTrendTableVo extends BaseVo {
    private static final long serialVersionUID = -7721850666930649354L;

    @Schema(description = "显示名称")
    private String name;

    @Schema(description = "code")
    private String code;

    @Schema(description = "是否子节点")
    private Boolean hasChildren;

    @Schema(description = "商务总面积")
    private BigDecimal totalBusinessArea;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "入住率")
    private BigDecimal settledRate;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;

    private Integer compareType;

    @Schema(hidden = true)
    private String datekey;

    private List<SettledAreaTrendTableVo> children;
}
