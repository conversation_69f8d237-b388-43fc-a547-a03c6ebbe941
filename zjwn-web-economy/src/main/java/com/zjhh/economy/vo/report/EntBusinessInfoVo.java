package com.zjhh.economy.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EntBusinessInfoVo extends BaseVo {
    private static final long serialVersionUID = -5912054206783917105L;

    /**
     * 企业状态
     */
    @Schema(description = "企业状态")
    @ExcelProperty(index = 0, value = {"企业状态"})
    private String type;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    @ExcelProperty(index = 1, value = {"企业名称"})
    private String enterpriseName;

    /**
     * 注册号
     */
    @Schema(description = "注册号")
    @ExcelProperty(index = 2, value = {"注册号"})
    private String serialNo;

    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    @ExcelProperty(index = 3, value = {"统一社会信用代码"})
    private String uscc;

    /**
     * 法定代表人
     */
    @Schema(description = "法定代表人")
    @ExcelProperty(index = 4, value = {"法定代表人"})
    private String legalPerson;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    @ExcelProperty(index = 5, value = {"成立日期"})
    private String foundDate;

    /**
     * 企业类型
     */
    @Schema(description = "企业类型")
    @ExcelProperty(index = 6, value = {"企业类型"})
    private String enterpriseTypeName;

    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    @ExcelProperty(index = 7, value = {"注册资本"})
    private String registeredCapital;

    /**
     * 行业
     */
    @Schema(description = "所属行业")
    @ExcelProperty(index = 8, value = {"所属行业"})
    private String industryName;

    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    @ExcelProperty(index = 9, value = {"经营范围"})
    private String businessScope;

    /**
     * 住所
     */
    @Schema(description = "企业住所")
    @ExcelProperty(index = 10, value = {"企业住所"})
    private String residence;

    /**
     * 电话
     */
    @Schema(description = "联系电话")
    @ExcelProperty(index = 11, value = {"联系电话"})
    private String phone;

}
