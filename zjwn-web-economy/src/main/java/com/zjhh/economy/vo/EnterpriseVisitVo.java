package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseVisitVo extends BaseVo {
    private static final long serialVersionUID = 5547364707527017370L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "序号")
    private String xh;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "走访目的")
    private String visitPurpose;

    @Schema(description = "走访人员")
    private String visitor;

    @Schema(description = "走访日期")
    private String visitDate;

    @Schema(description = "对接人")
    private String receptionist;


}
