package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端房地产投资情况Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TabTreeVo extends BaseVo {

    private static final long serialVersionUID = -7386874575976919465L;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "项目名称")
    private String xmmc;

    @Schema(description = "项目详细名称")
    private String xmxxmc;

    @Schema(description = "本月投资数")
    private String byTzs;

    @Schema(description = "本年投资数")
    private String bnTzs;


}
