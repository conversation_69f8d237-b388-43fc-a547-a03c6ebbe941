package com.zjhh.economy.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CockpitTreeSelectedVo extends BaseVo {

    private static final long serialVersionUID = -3041018921731553070L;
    private String key;
    private String parentKey;
    private String title;

    private String levelType;

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    @JsonIgnore
    private String projectType;

    private String type;
    private String value;
    private Object extra;
    private Boolean selectable;
    private Map<String, String> slots;
    private Map<String, String> scopedSlots;
    private List<CockpitTreeSelectedVo> children;

    public List<CockpitTreeSelectedVo> getChildren() {
        return CollUtil.isEmpty(this.children) ? null : this.children;
    }

    public Map<String, String> getScopedSlots() {
        Map<String, String> map = new HashMap(1);
        map.put("title", "title");
        return map;
    }

    public String getLevelType() {
        return levelType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else {
            return o != null && this.getClass() == o.getClass() ? ObjectUtil.equal(this.key, ((CockpitTreeSelectedVo) o).key) : false;
        }
    }

    public int hashCode() {
        return this.key.hashCode();
    }

    public Map<String, String> getSlots() {
        if (ObjectUtil.isNotNull(this.extra) && this.extra instanceof Number) {
            Map<String, String> map = new HashMap();
            int val = ((Number) this.extra).intValue();
            if (val == 1) {
                if (StrUtil.equals("0", this.parentKey)) {
                    map.put("icon", "organization");
                } else {
                    map.put("icon", "area");
                }

                return map;
            }

            if (val == 2) {
                map.put("icon", "unit");
                return map;
            }

            if (val == 3) {
                map.put("icon", "user");
                return map;
            }
        }

        return null;
    }

    public static CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder builder() {
        return new CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder();
    }

    public String getKey() {
        return this.key;
    }

    public String getParentKey() {
        return this.parentKey;
    }

    public String getTitle() {
        return this.title;
    }

    public String getValue() {
        return this.value;
    }

    public Object getExtra() {
        return this.extra;
    }

    public Boolean getSelectable() {
        return this.selectable;
    }

    public void setKey(final String key) {
        this.key = key;
    }

    public void setParentKey(final String parentKey) {
        this.parentKey = parentKey;
    }

    public void setTitle(final String title) {
        this.title = title;
    }

    public void setValue(final String value) {
        this.value = value;
    }

    public void setExtra(final Object extra) {
        this.extra = extra;
    }

    public void setSelectable(final Boolean selectable) {
        this.selectable = selectable;
    }

    public void setSlots(final Map<String, String> slots) {
        this.slots = slots;
    }

    public void setScopedSlots(final Map<String, String> scopedSlots) {
        this.scopedSlots = scopedSlots;
    }

    public void setChildren(final List<CockpitTreeSelectedVo> children) {
        this.children = children;
    }

    public String toString() {
        return "CockpitTreeSelectedVo(key=" + this.getKey() + ", parentKey=" + this.getParentKey() + ", title=" + this.getTitle() + ", value=" + this.getValue() + ", extra=" + this.getExtra() + ", selectable=" + this.getSelectable() + ", slots=" + this.getSlots() + ", scopedSlots=" + this.getScopedSlots() + ", children=" + this.getChildren() + ")";
    }

    public CockpitTreeSelectedVo() {
    }

    public CockpitTreeSelectedVo(final String key, final String parentKey, final String title, final String value, final Object extra, final Boolean selectable, final Map<String, String> slots, final Map<String, String> scopedSlots, final List<CockpitTreeSelectedVo> children) {
        this.key = key;
        this.parentKey = parentKey;
        this.title = title;
        this.value = value;
        this.extra = extra;
        this.selectable = selectable;
        this.slots = slots;
        this.scopedSlots = scopedSlots;
        this.children = children;
    }

    public static class CockpitTreeSelectedVoBuilder {
        private String key;
        private String parentKey;
        private String title;
        private String value;
        private Object extra;
        private Boolean selectable;
        private Map<String, String> slots;
        private Map<String, String> scopedSlots;
        private List<CockpitTreeSelectedVo> children;

        CockpitTreeSelectedVoBuilder() {
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder key(final String key) {
            this.key = key;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder parentKey(final String parentKey) {
            this.parentKey = parentKey;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder title(final String title) {
            this.title = title;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder value(final String value) {
            this.value = value;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder extra(final Object extra) {
            this.extra = extra;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder selectable(final Boolean selectable) {
            this.selectable = selectable;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder slots(final Map<String, String> slots) {
            this.slots = slots;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder scopedSlots(final Map<String, String> scopedSlots) {
            this.scopedSlots = scopedSlots;
            return this;
        }

        public CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder children(final List<CockpitTreeSelectedVo> children) {
            this.children = children;
            return this;
        }

        public CockpitTreeSelectedVo build() {
            return new CockpitTreeSelectedVo(this.key, this.parentKey, this.title, this.value, this.extra, this.selectable, this.slots, this.scopedSlots, this.children);
        }

        public String toString() {
            return "CockpitTreeSelectedVo.CockpitTreeSelectedVoBuilder(key=" + this.key + ", parentKey=" + this.parentKey + ", title=" + this.title + ", value=" + this.value + ", extra=" + this.extra + ", selectable=" + this.selectable + ", slots=" + this.slots + ", scopedSlots=" + this.scopedSlots + ", children=" + this.children + ")";
        }
    }
}

