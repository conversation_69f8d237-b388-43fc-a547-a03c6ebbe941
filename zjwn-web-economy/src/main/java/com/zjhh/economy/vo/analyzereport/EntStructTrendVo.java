package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EntStructTrendVo extends BaseVo {
    private static final long serialVersionUID = 1310724976462873575L;

    @Schema(description = "显示名称")
    private String name;

    @Schema(description = "code")
    private String code;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "迁出")
    private Integer moveOutCount;

    @Schema(description = "迁入")
    private Integer moveInCount;

    @Schema(description = "属地")
    private Integer localedCount;

    @Schema(description = "非属地")
    private Integer notLocaledCount;




}
