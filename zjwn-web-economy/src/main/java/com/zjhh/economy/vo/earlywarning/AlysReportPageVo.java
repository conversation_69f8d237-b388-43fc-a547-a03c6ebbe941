package com.zjhh.economy.vo.earlywarning;

import com.onlyoffice.model.documenteditor.Config;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2025/8/14 17:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlysReportPageVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 2096777311717428475L;

    @Schema(description = "onlyoffice配置")
    private Config config;

    @Schema(description = "onlyoffice地址")
    private String documentServerApiUrl;
}
