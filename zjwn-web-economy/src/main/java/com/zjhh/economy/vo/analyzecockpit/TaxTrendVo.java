package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 税收真题趋势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaxTrendVo extends BaseVo {
    private static final long serialVersionUID = -2703681739879867379L;


    private String datekey;

    private BigDecimal taxIncome;

    private BigDecimal sqTaxIncome;

    private BigDecimal zf;

    public String getDatekey() {
        String quarter = "";
        if (datekey.length() == 5 && !datekey.contains("-")) {
            quarter = datekey.substring(4,5);
        }
        if (quarter.equals("1") && datekey.length() == 5) {
            return datekey.substring(0,4) + "年" + "第一季度";
        }else if (quarter.equals("2")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第二季度";
        } else if (quarter.equals("3")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第三季度";

        }else if (quarter.equals("4")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第四季度";
        }else {
            return datekey;
        }
    }

}
