package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FloorRoomInfoVo extends BaseVo {
    private static final long serialVersionUID = 7640500224595495118L;

    @Schema(description = "楼层id")
    private String floorId;

    @Schema(description = "房源id")
    private String roomId;

    @Schema(description = "房号")
    private String roomNo;

    @Schema(description = "房源面积")
    private BigDecimal businessArea;

    /**
     * 企业为空显示"空置"
     */
    @Schema(description = "入驻企业")
    private String entName;

    /**
     * 除了入驻中，其他状态均为空置状态
     */
    @Schema(description = "房间状态。true-已入驻，false-空置中")
    private Boolean roomStatus;

}
