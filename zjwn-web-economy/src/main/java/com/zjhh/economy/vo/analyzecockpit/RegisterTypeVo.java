package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登记类型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RegisterTypeVo extends BaseVo {
    private static final long serialVersionUID = -1903580997700323988L;

    @Schema(description = "登记类型")
    private String registerTypeName;

    @Schema(description = "数量")
    private String entCount;

    @Schema(description = "占比")
    private String zb;
}
