package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 行业分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryAnalyzeVo extends BaseVo {
    private static final long serialVersionUID = 8982136625975826149L;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "金额")
    private BigDecimal amt;


}
