package com.zjhh.economy.vo.operationlog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomSettleInfoForLog extends BaseVo {

    private static final long serialVersionUID = 4062139044330696836L;

    private String id;

    @Schema(description = "企业名称")
    private String enterpriseName;

    private String buildingName;

    private String roomNo;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "入驻开始日期")
    private LocalDate checkInDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "预计搬离日期")
    private LocalDate expectMoveOutDate;

    @Schema(description = "入驻面积（㎡）")
    private BigDecimal area;

    private LocalDate renovationStartDate;

    private LocalDate renovationEndDate;

    @Schema(description = "装修期")
    private String renovationDate;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "附件")
    private String documentName;

    public String getRenovationDate() {
        StringBuilder builder = new StringBuilder();
        if (renovationStartDate != null) {
            builder.append(renovationStartDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
        builder.append(" 至 ");
        if (renovationEndDate != null) {
            builder.append(renovationEndDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
        return builder.toString();
    }
}
