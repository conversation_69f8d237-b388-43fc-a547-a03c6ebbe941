package com.zjhh.economy.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 *  楼宇调查
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingInvestigationVo extends BaseVo {
    private static final long serialVersionUID = -4020736042474115013L;

    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    @ExcelProperty(index = 0,value = "楼宇名称")
    private String projectName;

    /**
     * 楼宇代码
     */
    @Schema(description = "楼宇代码(按社区(村)顺序编码)")
    @ExcelProperty(index = 1,value = "楼宇代码(按社区(村)顺序编码)")
    private String projectXh;

    /**
     * 楼宇地址
     */
    @Schema(description = "楼宇地址")
    @ExcelProperty(index = 2,value = "楼宇地址")
    private String address;

    /**
     * 地址代码
     */
    @Schema(description = "地址代码(所在社区地址代码)")
    @ExcelProperty(index = 3,value = "地址代码(所在社区地址代码)")
    private String divisionCode;

    /**
     * 楼宇类型
     */
    @Schema(description = "楼宇类型")
    @ExcelProperty(index = 4,value = "楼宇类型")
    private String projectType;

    /**
     * 物业管理单位名称
     */
    @Schema(description = "物业管理单位名称")
    @ExcelProperty(index = 5,value = {"物业管理情况","物业管理单位名称"})
    private String tenements;

    /**
     * 组织机构代码
     */
    @Schema(description = "组织机构代码")
    @ExcelProperty(index = 6,value = {"物业管理情况","组织机构代码"})
    private String organizationCode;

    /**
     * 物管单位注册地址
     */
    @Schema(description = "物管单位注册地址")
    @ExcelProperty(index = 7,value = {"物业管理情况","物管单位注册地址"})
    private String tenementsRegisteredAddress;

    /**
     * 单位负责人
     */
    @Schema(description = "单位负责人")
    @ExcelProperty(index = 8,value = {"物业管理情况","单位负责人"})
    private String tenementsContact;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    @ExcelProperty(index = 9,value = {"物业管理情况","联系电话"})
    private String tenementsPhone;

    /**
     * 物业费单价
     */
    @Schema(description = "物业费单价")
    @ExcelProperty(index = 10,value = {"物业管理情况","物业费单价"})
    private String strataFeeUnit;

    /**
     * 楼宇投入运行年月
     */
    @Schema(description = "楼宇投入运行年月")
    @ExcelProperty(index = 11,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","楼宇投入运行年月"})
    private String operationTime;

    /**
     * 用地面积
     */
    @Schema(description = "用地面积")
    @ExcelProperty(index = 12,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","用地面积"})
    private String landArea;

    /**
     * 总建筑
     */
    @Schema(description = "总建筑面积")
    @ExcelProperty(index = 13,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","总建筑面积"})
    private String buildingArea;

    /**
     * 商务办公总面积
     */
    @Schema(description = "商务办公总面积")
    @ExcelProperty(index = 14,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","商务办公","总面积"})
    private BigDecimal workArea;

    /**
     * 商务办公已入驻面积
     */
    @Schema(description = "商务办公已入驻面积")
    @ExcelProperty(index = 15,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","商务办公","已入驻"})
    private BigDecimal workingArea;

    /**
     * 商务办公待入驻面积
     */
    @Schema(description = "商务办公待入驻面积")
    @ExcelProperty(index = 16,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","商务办公","待入驻"})
    private BigDecimal workVacantArea;

    /**
     * 商业面积总面积
     */
    @Schema(description = "商业面积总面积")
    @ExcelProperty(index = 17,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","商业面积","总面积"})
    private BigDecimal businessArea;

    /**
     * 商业面积已入驻面积
     */
    @Schema(description = "商业面积已入驻面积")
    @ExcelProperty(index = 18,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","商业面积","已入驻"})
    private BigDecimal businessSettledArea;

    /**
     * 商业面积待入驻面积
     */
    @Schema(description = "商业面积待入驻面积")
    @ExcelProperty(index = 19,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","商业面积","待入驻"})
    private BigDecimal businessVacantArea;

    /**
     * 地下车库面积
     */
    @Schema(description = "地下车库面积")
    @ExcelProperty(index = 20,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","地下车库面积"})
    private String undergroundArea;

    /**
     * 其他面积
     */
    @Schema(description = "其他面积")
    @ExcelProperty(index = 21,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","其他面积"})
    private String otherArea;

    /**
     * 车位数（个）
     */
    @Schema(description = "车位数（个）")
    @ExcelProperty(index = 22,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","车位数（个）"})
    private String parkingNumber;

    /**
     * 物业费收费标准
     */
    @Schema(description = "物业费收费标准")
    @ExcelProperty(index = 23,value = {"楼宇基本情况（面积计量单位：平方米，物业费收费标准为元/平方米、月）","物业费收费标准"})
    private String charges;

    /**
     * 入驻单位数 - 小计
     */
    @Schema(description = "小计")
    @ExcelProperty(index = 24,value = {"入驻单位数","小计"})
    private Integer settledTotal;

    /**
     * 入驻单位数 - 法人单位
     */
    @Schema(description = "法人单位")
    @ExcelProperty(index = 25,value = {"入驻单位数","法人单位"})
    private Integer settledFr;

    /**
     * 入驻单位数 - 产业活动单位
     */
    @Schema(description = "产业活动单位")
    @ExcelProperty(index = 26,value = {"入驻单位数","产业活动单位"})
    private Integer settledCy;

    /**
     * 入驻单位数 - 个体经营户
     */
    @Schema(description = "个体经营户")
    @ExcelProperty(index = 27,value = {"入驻单位数","个体经营户"})
    private Integer settledGt;

    /**
     * 注册单位数 - 小计
     */
    @Schema(description = "小计")
    @ExcelProperty(index = 28,value = {"注册单位数","小计"})
    private Integer registeredTotal;

    /**
     * 注册单位数 - 法人单位
     */
    @Schema(description = "法人单位")
    @ExcelProperty(index = 29,value = {"注册单位数","法人单位"})
    private Integer registeredFr;

    /**
     * 注册单位数 - 产业活动单位
     */
    @Schema(description = "产业活动单位")
    @ExcelProperty(index = 30,value = {"注册单位数","产业活动单位"})
    private Integer registeredCy;

    /**
     * 注册单位数 - 个体经营户
     */
    @Schema(description = "个体经营户")
    @ExcelProperty(index = 31,value = {"注册单位数","个体经营户"})
    private Integer registeredGt;

    /**
     * 主要经济指标 - 从业人数
     */
    @Schema(description = "从业人数")
    @ExcelProperty(index = 32,value = {"主要经济指标(计量单位: 人、元)","从业人数"})
    private Integer employee;

    /**
     * 主要经济指标 - 营业收入
     */
    @Schema(description = "营业收入")
    @ExcelProperty(index = 33,value = {"主要经济指标(计量单位: 人、元)","营业收入"})
    private BigDecimal operatingIncome;

    /**
     * 主要经济指标 - 税金合计
     */
    @Schema(description = "税金合计")
    @ExcelProperty(index = 34,value = {"主要经济指标(计量单位: 人、元)","税金合计"})
    private BigDecimal totalTaxes;

    /**
     * 主要经济指标 - 国税小计
     */
    @Schema(description = "国税小计")
    @ExcelProperty(index = 35,value = {"主要经济指标(计量单位: 人、元)","国税小计"})
    private BigDecimal nationalTax;

    /**
     * 主要经济指标 - 地税小计
     */
    @Schema(description = "地税小计")
    @ExcelProperty(index = 36,value = {"主要经济指标(计量单位: 人、元)","地税小计"})
    private BigDecimal localTaxes;

    /**
     * 联系人 - 街道(镇)联系人
     */
    @Schema(description = "街道(镇)联系人")
    @ExcelProperty(index = 37,value = {"联系人","街道(镇)联系人"})
    private String streetContacts;

    /**
     * 联系人 - 联系电话（手机）
     */
    @Schema(description = "联系电话（手机）")
    @ExcelProperty(index = 38,value = {"联系人","联系电话（手机）"})
    private String jdPhone;

    /**
     * 联系人 - 社区(村)联系人
     */
    @Schema(description = "社区(村)联系人")
    @ExcelProperty(index = 39,value = {"联系人","社区(村)联系人"})
    private String communityContacts;

    /**
     * 联系人 - 联系电话（手机）
     */
    @Schema(description = "联系电话（手机）")
    @ExcelProperty(index = 40,value = {"联系人","联系电话（手机）"})
    private String sqHone;

    /**
     * 联系人 - 楼宇联系人
     */
    @Schema(description = "楼宇联系人")
    @ExcelProperty(index = 41,value = {"联系人","楼宇联系人"})
    private String buildingContacts;

    /**
     * 联系人 - 联系电话（手机）
     */
    @Schema(description = "联系电话（手机）")
    @ExcelProperty(index = 42,value = {"联系人","联系电话（手机）"})
    private String lyPhone;

    /**
     * 变更情况
     */
    @Schema(description = "变更情况（新增、退出）")
    @ExcelProperty(index = 43,value = {"变更情况（新增、退出）"})
    private String changes;

    /**
     * 入驻率
     */
    @Schema(description = "入驻率")
    @ExcelProperty(index = 44,value = {"入驻率"})
    private BigDecimal settledRate;

    /**
     * 注册率
     */
    @Schema(description = "注册率")
    @ExcelProperty(index = 45,value = {"注册率"})
    private BigDecimal registeredRate;
}
