package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目列表Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectListVo extends BaseVo {
    private static final long serialVersionUID = 6534852662422277127L;


    @Schema(description = "主键")
    @ExcelIgnore
    private String id;

    @Schema(description = "编号")
    @ExcelProperty(index = 0, value = "编号")
    private String serialNo;

    @Schema(description = "项目名称")
    @ExcelProperty(index = 1, value = "项目名称")
    private String projectName;

    @Schema(description = "项目类型")
    @ExcelProperty(index = 2, value = "项目类型")
    private String projectType;

    @Schema(description = "社区编码")
    @ExcelProperty(index = 3, value = "所属社区")
    private String communityName;

    @Schema(description = "总建筑面积")
    @ExcelProperty(index = 4, value = "总建筑面积（㎡）")
    private String totalBuildingArea;

    @Schema(description = "总商务面积（㎡）")
    @ExcelProperty(index = 5, value = "总商务面积（㎡）")
    private String totalBusinessArea;

    @Schema(description = "楼宇数量")
    @ExcelProperty(index = 6, value = "楼宇数量(栋)")
    private Integer buildingCount;

    @Schema(description = "总房源数")
    @ExcelProperty(index = 7, value = "总房源数")
    private Integer totalRoomCount;

    @Schema(description = "空置房源数")
    @ExcelProperty(index = 8, value = "空置房源数")
    private Integer emptyRoomCount;


}
