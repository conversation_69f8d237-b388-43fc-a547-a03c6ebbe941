package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReportSummaryVo extends BaseVo {
    private static final long serialVersionUID = 9139849671889127535L;

    private String name;

    private String field;

    private String userCode;

    private Integer queryType;

    private String parentField;

    private List<ReportColumnVo> columns;

}
