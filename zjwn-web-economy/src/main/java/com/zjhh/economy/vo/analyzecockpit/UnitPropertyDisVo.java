package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单位性质分布
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UnitPropertyDisVo extends BaseVo {
    private static final long serialVersionUID = 2733467848341238398L;


    @Schema(description = "单位性质名称")
    private String unitPropertyName;

    @Schema(description = "单位数量")
    private String unitCount;

    @Schema(description = "单位占比")
    private String zb;

}
