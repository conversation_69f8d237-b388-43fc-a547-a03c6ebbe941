package com.zjhh.economy.vo.operationlog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingDetailForLog extends BaseVo {

    private static final long serialVersionUID = 1160463509131788446L;

    @Schema(description = "楼宇id")
    private String id;

    @Schema(description = "楼宇编号")
    private String serialNo;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "所属项目")
    private String projectName;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "坐落位置")
    private String address;

    @Schema(description = "建筑面积")
    private BigDecimal buildingArea;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "楼宇投入运行时间")
    private LocalDate operationTime;

    @Schema(description = "商务面积")
    private BigDecimal businessArea;

    @Schema(description = "楼宇状态")
    private String buildingStatusName;

    @Schema(description = "楼宇类型")
    private String buildingTypeName;

    @Schema(description = "物业负责人")
    private String head;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "楼宇简介")
    private String introduce;

    @Schema(description = "楼宇外观图")
    private String outsideImg;

    @Schema(description = "其他图片")
    private String otherImg;

    @Schema(description = "楼宇标签")
    private List<String> labelNames;

    @Schema(description = "楼宇定位")
    private List<String> positionNames;

    @Schema(description = "地上停车位")
    private Integer landParkSpace;

    @Schema(description = "地下停车位")
    private Integer undergroundParkSpace;

    @Schema(description = "楼宇配套")
    private List<String> configNames;
}
