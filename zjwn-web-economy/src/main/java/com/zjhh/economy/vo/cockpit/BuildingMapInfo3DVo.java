package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 楼宇3DVo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingMapInfo3DVo extends BaseVo {
    private static final long serialVersionUID = -4388213112491411581L;

    @Schema(description = "楼宇ID")
    private String buildingId;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "楼宇面积")
    private String buildingArea;

    @Schema(description = "已使用面积")
    private String usedArea;

    @Schema(description = "入驻率")
    private String settleRate;

    @Schema(description = "注册数")
    private String registerCount;

    @Schema(description = "注册率")
    private String registerRate;

    @Schema(description = "税收")
    private String taxIncome;

    @Schema(description = "单位税收")
    private String unitTaxIncome;

    @Schema(description = "除房地产税收")
    private String taxWithout;

    @Schema(description = "除房地产单位税收")
    private String unitTaxWithout;

    @Schema(description = "经度")
    private String latitude;

    @Schema(description = "纬度")
    private String longitude;

    private String path;

    private List<String> buildingLabels;

    @Schema(description = "项目ID")
    private String projectId;
}
