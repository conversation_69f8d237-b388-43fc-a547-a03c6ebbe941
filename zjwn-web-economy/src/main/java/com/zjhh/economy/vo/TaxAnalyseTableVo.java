package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 税收分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaxAnalyseTableVo extends BaseVo {
    private static final long serialVersionUID = -6011081476758704704L;

    /**
     * 时间
     */
    private String datekey;

    @Schema(description = "月份")
    @ExcelProperty(index = 0,value = "月份")
    private String monthStr;

    @Schema(description = "合计")
    @ExcelProperty(index = 1,value = "合计")
    private BigDecimal hj;

    @Schema(description = "增幅")
    @ExcelProperty(index = 2,value = "增幅")
    private BigDecimal zf;


    private BigDecimal byAmt;

    private String yskmDm;

    private String yskmMc;
}
