package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端财政总收入分税种结构分析Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TabEightVo extends BaseVo {


    private static final long serialVersionUID = -6593701505962371768L;

    @Schema(description = "科目")
    private String km;

    @Schema(description = "一般公共预算收入-本月收入")
    private String bysr;

    @Schema(description = "一般公共预算收入-本年收入")
    private String bqsr;

    @Schema(description = "中央级收入")
    private String zyjsr;

    @Schema(description = "财政总收入")
    private String czzsr;

    @Schema(description = "一般公共预算收入占比")
    private String zb;

}
