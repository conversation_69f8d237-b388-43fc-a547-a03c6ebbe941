package com.zjhh.economy.vo.analyzecockpit;

import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业稳定性榜单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntStableRankVo extends BaseVo {
    private static final long serialVersionUID = -5692622031512160319L;


    @Schema(description = "企业ID")
    private String enterpriseId;


    @Schema(description = "楼宇名称")
    private String entName;

    @Schema(description = "入驻时长 ")
    private String settledTime;


    public String getSettledTime() {
        if (StrUtil.isNotBlank(settledTime)) {
            if (Integer.valueOf(settledTime) < 0) {
                return "0";
            }else {
                return getPeriod(Integer.valueOf(settledTime));
            }
        }
        return "0";

    }

    private String getPeriod(Integer days) {
//        LocalDate startDate = LocalDate.of(0, 1, 1);
//
//        // 计算转换后的日期
//        LocalDate endDate = startDate.plusDays(days);
//
//        // 计算日期差异
//        Period period = Period.between(startDate, endDate);
//
//        return (period.getYears() != 0 ? period.getYears() + "年" : "") + (period.getMonths() != 0 ? period.getMonths() + "月" : "");
        return settledTime;

    }

}
