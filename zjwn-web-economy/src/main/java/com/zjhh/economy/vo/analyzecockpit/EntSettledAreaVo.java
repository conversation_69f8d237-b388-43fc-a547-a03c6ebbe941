package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业入驻面积榜单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntSettledAreaVo extends BaseVo {
    private static final long serialVersionUID = 884094893778489118L;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String entName;

    @Schema(description = "入驻面积")
    private String settledArea;

    @Schema(description = "占比")
    private String zb;


}
