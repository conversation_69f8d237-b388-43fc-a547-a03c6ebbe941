package com.zjhh.economy.vo.policymanagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyManagementPageVo extends BaseVo {
    private static final long serialVersionUID = 1492228244476330385L;

    @Schema(description = "政策id")
    private String id;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "政策名称")
    private String policyName;

    @Schema(description = "文号")
    private String policyNo;

    @Schema(description = "发布机构")
    private String publishAgency;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "发布时间")
    private LocalDate publishDate;

    @Schema(description = "政策详情")
    private String policyDetail;
}
