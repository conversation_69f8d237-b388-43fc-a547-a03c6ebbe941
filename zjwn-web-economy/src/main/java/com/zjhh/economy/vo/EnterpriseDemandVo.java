package com.zjhh.economy.vo;

import cn.hutool.core.util.ObjectUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 企业诉求Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseDemandVo extends BaseVo {

    private static final long serialVersionUID = 992827858934984932L;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "id")
    private String id;

    @Schema(description = "诉求描述")
    private String demandDesc;

    @Schema(description = "诉求类型")
    private String demandType;

    @Schema(description = "提交途径转义")
    private String submitSourceStr;

    @Schema(description = "企业名称")
    private String enterpriseName;

    private String enterpriseId;

    @Schema(description = "诉求转义")
    private String demandTypeStr;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "处理状态")
    private Integer handleType;

    @Schema(description = "处理状态转移")
    private String handleTypeStr;

    @Schema(description = "提交途径")
    private String submitSource;

    @Schema(description = "提交时间")
    private LocalDate submitDate;

    @Schema(description = "显示处理结果")
    private Boolean showHandle;

    public Boolean getShowHandle() {
        return !ObjectUtil.isEmpty(handleType) && (!ObjectUtil.isNotEmpty(handleType) || !handleType.equals(1));
    }

}
