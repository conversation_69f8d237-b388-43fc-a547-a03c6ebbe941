package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.db.comm.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 报表VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportResultVo extends BaseVo {


    private static final long serialVersionUID = -5566201044119564578L;

    private List<ReportColumnVo> columns;

    private Page<BuildingConditionVo> buildingConditions;

    private Page<BuildingArchiveVo> buildingArchives;



}
