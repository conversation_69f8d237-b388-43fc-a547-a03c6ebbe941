package com.zjhh.economy.vo.report;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门反馈Req
 *
 * <AUTHOR>
 * @date 2022-05-25 11:20 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportFeedbackReq extends BaseReq {

    private static final long serialVersionUID = -5202945950319080625L;

    @Schema(description = "报告ID")
    private String reportId;

    @Schema(description = "反馈内容")
    private String feedbackInfo;

    @Schema(description = "附件名称")
    private String fileName;

    @Schema(description = "附件地址")
    private String fileDir;

}
