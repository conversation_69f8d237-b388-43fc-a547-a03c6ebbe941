package com.zjhh.economy.vo.operationlog;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommonLogResultVo extends BaseVo {
    private static final long serialVersionUID = -4177947804206302880L;

    @Schema(description = "操作前数据")
    private List<CommonLogVo> optBefore;

    @Schema(description = "操作后数据")
    private List<CommonLogVo> optAfter;

    @Schema(description = "字段名称")
    private TreeMap<String, String> fieldMap;
}
