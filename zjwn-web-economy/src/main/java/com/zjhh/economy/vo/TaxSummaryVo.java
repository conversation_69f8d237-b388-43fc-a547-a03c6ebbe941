package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 税收监测概览
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxSummaryVo extends BaseVo {


    private static final long serialVersionUID = 343629166391185336L;

    @Schema(description = "本年累计税收")
    private BigDecimal bqljAmt;

    @Schema(description = "累计税收同比")
    private BigDecimal tbZf;

    @Schema(description = "本月税收")
    private BigDecimal byAmt;

    @Schema(description = "本月税收环比")
    private BigDecimal hbZf;

    @Schema(description = "本年预警总量")
    private Integer bqWarningCount;

    @Schema(description = "本月预警总量")
    private Integer byWarningCount;

    @Schema(description = "本年预警同比")
    private BigDecimal warningTbZf;

    @Schema(description = "本月预警环比")
    private BigDecimal warningHbZf;

}
