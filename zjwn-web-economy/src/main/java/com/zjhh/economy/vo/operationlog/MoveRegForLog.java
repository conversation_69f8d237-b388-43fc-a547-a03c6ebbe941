package com.zjhh.economy.vo.operationlog;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MoveRegForLog extends BaseVo {
    private static final long serialVersionUID = -1134660420350286870L;

    @Schema(description = "实际搬离日期")
    private LocalDate realMoveDate;

    @Schema(description = "去向登记")
    private String moveDirection;

    @Schema(description = "迁出原因")
    private List<String> labels;

    @Schema(description = "搬离原因")
    private String moveReason;
}
