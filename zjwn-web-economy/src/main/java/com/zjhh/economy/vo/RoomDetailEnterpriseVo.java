package com.zjhh.economy.vo;

import cn.hutool.core.util.ObjectUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @since 2024/3/8 17:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomDetailEnterpriseVo extends BaseVo {

    private static final long serialVersionUID = 5669340318429072453L;

    @Schema(description = "入驻id")
    private String settledId;

    @Schema(description = "企业id")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "联系人")
    private String legalPerson;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    @Schema(description = "是否规上企业")
    private Boolean onScaled;

    @Schema(description = "入驻面积")
    private BigDecimal area;

    @Schema(description = "行业类别")
    private String industryName;

    @Schema(description = "是否属地企业")
    private Boolean territorialized;

    @Schema(description = "入驻开始时间")
    private LocalDate checkInDate;

    @Schema(description = "预计搬离时间")
    private LocalDate expectMoveOutDate;

    @Schema(description = "装修开始日期")
    private LocalDate renovationStartDate;

    @Schema(description = "装修结束日期")
    private LocalDate renovationEndDate;

    @Schema(description = "剩余租赁期")
    private Long residueDays;

    @Schema(description = "企业logo")
    private String logoImgId;

    public Long getResidueDays() {
        LocalDate now = LocalDate.now();
        if (ObjectUtil.isNull(expectMoveOutDate)) {
            return null;
        }
        if (now.isAfter(expectMoveOutDate)) {
            return 0L;
        }
        return now.until(expectMoveOutDate, ChronoUnit.DAYS);
    }

}

