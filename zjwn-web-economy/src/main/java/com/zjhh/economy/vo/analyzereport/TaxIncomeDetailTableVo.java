package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxIncomeDetailTableVo extends BaseVo {
    private static final long serialVersionUID = -6141688113899655722L;

    private String name;

    private String code;

    private String datekey;

    private BigDecimal taxIncome;

    private BigDecimal streetTaxIncome;

    private BigDecimal zb;

    private BigDecimal taxIncomeZf;

    private BigDecimal streetTaxIncomeZf;

    private BigDecimal totalTaxIncome;

    private BigDecimal totalTaxIncomeZf;

    private BigDecimal totalStreetTaxIncome;

    private BigDecimal totalStreetTaxIncomeZf;

    private Boolean hasChildren;

    private Integer compareType;


}
