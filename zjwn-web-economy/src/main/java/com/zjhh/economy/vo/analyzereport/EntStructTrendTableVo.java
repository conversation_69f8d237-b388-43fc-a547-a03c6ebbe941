package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 企业结构趋势表格
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntStructTrendTableVo extends BaseVo {
    private static final long serialVersionUID = 489044576067130484L;

    @Schema(description = "显示名称")
    private String name;

    private String code;

    private List<EntStructTrendTableVo> children;

    private Boolean hasChildren;

    @Schema(description = "属地企业个数")
    private Integer legalLocaledCount;

    @Schema(description = "属地个人个数")
    private Integer individualLocaledCount;

    @Schema(description = "属地产地单位")
    private Integer industryLocaledCount;

    @Schema(description = "非属地企业个数")
    private Integer legalNotLocaledCount;

    @Schema(description = "非属地个人个数")
    private Integer individualNotLocaledCount;

    @Schema(description = "非属地产地单位")
    private Integer industryNotLocaledCount;

    @Schema(description = "非属地个数")
    private Integer notLocaledCount;

    @Schema(description = "属地个数")
    private Integer localedCount;

    @Schema(description = "合计")
    private Integer totalCount;

    private Integer compareType;

}
