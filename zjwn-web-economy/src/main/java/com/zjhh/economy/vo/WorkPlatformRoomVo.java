package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 工作台房源管理
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkPlatformRoomVo extends BaseVo {
    private static final long serialVersionUID = -7558824943235224758L;

    @Schema(description = "总商务面积")
    private BigDecimal totalBusinessArea;

    @Schema(description = "总空置面积")
    private BigDecimal totalEmptyArea;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;


}
