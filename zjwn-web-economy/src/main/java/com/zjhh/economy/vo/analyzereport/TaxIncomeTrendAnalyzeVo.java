package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 税收收入趋势分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaxIncomeTrendAnalyzeVo extends BaseVo {
    private static final long serialVersionUID = 4358611212025899593L;

    @Schema(description = "显示名称")
    private String name;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "税收")
    private BigDecimal taxIncome;

}
