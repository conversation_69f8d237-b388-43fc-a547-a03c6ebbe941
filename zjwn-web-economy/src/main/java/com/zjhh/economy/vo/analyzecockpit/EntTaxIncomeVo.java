package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业纳税情况
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntTaxIncomeVo extends BaseVo {

    @Schema(description = "纳税情况")
    private String level;

    @Schema(description = "数量")
    private String entCount;

}
