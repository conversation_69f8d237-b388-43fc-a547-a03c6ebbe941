package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 *  经济指标趋势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EconomicIndicatorVo extends BaseVo {

    private static final long serialVersionUID = 8296746705311912609L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "税收收入")
    private BigDecimal taxIncome;

    @Schema(description = "税收收入除去房地产")
    private BigDecimal taxIncomeWithout;

    @Schema(description = "税收收入增幅")
    private BigDecimal zf;

    @Schema(description = "税收收入增幅除房地产")
    private BigDecimal withoutZf;

}
