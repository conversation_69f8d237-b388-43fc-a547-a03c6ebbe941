package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 企业标签
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseLabelVo extends BaseVo {

    private static final long serialVersionUID = 309870526412833918L;

    @Schema(description = "标签Id")
    private String id;

    @Schema(description = "标签编码")
    private String labelCode;

    @Schema(description = "标签名称")
    private String labelName;
}
