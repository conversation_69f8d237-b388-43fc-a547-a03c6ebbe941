package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MobileCompareVo extends BaseVo {
    @Serial
    private static final long serialVersionUID = 2449127065179237215L;

    @Schema(description = "最大数量")
    private String maxCount;

    @Schema(description = "对比项类型")
    private String itemType;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "默认选项")
    private List<String> codes;

}
