package com.zjhh.economy.vo.report;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警报告列表
 *
 * <AUTHOR>
 * @date 2022-05-23 10:11 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportListVo extends BaseVo {

    private static final long serialVersionUID = 5828981974956327449L;

    @Schema(description = "序号")
    private String guid;

    @Schema(description = "报告名称")
    private String fileName;

    @Schema(description = "报告日期")
    private String datekey;

    @Schema(description = "责任部门")
    private String unitName;

    @Schema(description = "责任部门Code")
    private String unitCode;

    @Schema(description = "文件Guido")
    private String fileGuid;

    @JsonIgnore
    private String fileDir;

    @Schema(description = "文件后缀")
    private String suffix;

    public String getSuffix() {
        return FileUtil.getSuffix(fileDir);
    }

    public String getDatekey() {
        if (StrUtil.isBlank(datekey)) {
            return datekey;
        } else if (datekey.length() == 4) {
            return datekey.substring(0, 4) + "年";
        } else if (datekey.length() == 6) {
            return datekey.substring(0, 4) + "年" + datekey.substring(4, 6) + "月";
        } else if (datekey.length() == 8) {
            return datekey.substring(0, 4) + "年" + datekey.substring(4, 6) + "月" + datekey.substring(6) + "日";
        } else {
            return datekey;
        }
    }
}
