package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单位产出走势对比
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UnitIncomeTrendVo extends BaseVo {
    private static final long serialVersionUID = 3018412173519305308L;

    @Schema(description = "单位产出")
    private String unitIncome;

    @Schema(description = "除房地产单位产出")
    private String unitIncomeWithoutFdc;

    @Schema(description = "日期")
    private String datekey;

    public String getDatekey() {
        String quarter = "";
        if (datekey.length() == 5 && !datekey.contains("-")) {
            quarter = datekey.substring(4,5);
        }
        if (quarter.equals("1") && datekey.length() == 5) {
            return datekey.substring(0,4) + "年" + "第一季度";
        }else if (quarter.equals("2")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第二季度";
        } else if (quarter.equals("3")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第三季度";

        }else if (quarter.equals("4")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第四季度";
        }else {
            return datekey;
        }
    }
}
