package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.comm.vo.TreeSelectVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndustrySettledAreaAssembleVo extends BaseVo {
    private static final long serialVersionUID = 6780605807463110353L;

    private List<TreeSelectVo> industries;

    private List<IndustrySettledAreaVo> list;


}
