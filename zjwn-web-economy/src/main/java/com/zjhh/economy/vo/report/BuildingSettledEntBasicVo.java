package com.zjhh.economy.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@ContentRowHeight(20)
@HeadRowHeight(25)
public class BuildingSettledEntBasicVo extends BaseVo {
    private static final long serialVersionUID = -8999568392829228876L;
    /**
     * 楼宇名称
     */
    @ExcelProperty(value = "楼宇名称", index = 0)
    @ColumnWidth(20)
    @Schema(description = "楼宇名称")
    private String projectName;

    /**
     * 单位详细名称
     */
    @ExcelProperty(value = "单位详细名称", index = 1)
    @ColumnWidth(30)
    @Schema(description = "单位详细名称")
    private String enterpriseName;

    /**
     * 机构类型（法人/分支/个体）
     */
    @ExcelProperty(value = "机构类型", index = 2)
    @ColumnWidth(15)
    @Schema(description = "机构类型（法人/分支/个体）")
    private String institutionalName;

    /**
     * 单位注册地址
     */
    @ExcelProperty(value = "单位注册地址", index = 3)
    @ColumnWidth(40)
    @Schema(description = "单位注册地址")
    private String registeredAddress;

    /**
     * 注册地行政区划代码
     */
    @ExcelProperty(value = "注册地行政区划代码", index = 4)
    @ColumnWidth(20)
    @Schema(description = "注册地行政区划代码")
    private String addressCode;

    /**
     * 主要业务活动
     */
    @ExcelProperty(value = "主要业务活动", index = 5)
    @ColumnWidth(40)
    @Schema(description = "主要业务活动")
    private String businessScope;

    /**
     * 行业代码
     */
    @ExcelProperty(value = "行业代码", index = 6)
    @ColumnWidth(15)
    @Schema(description = "行业代码")
    private String industryCode;

    /**
     * 法定代表人(单位负责人)
     */
    @ExcelProperty(value = "法定代表人", index = 7)
    @ColumnWidth(15)
    @Schema(description = "法定代表人(单位负责人)")
    private String legalPerson;

    /**
     * 固定电话
     */
    @ExcelProperty(value = "固定电话", index = 8)
    @ColumnWidth(15)
    @Schema(description = "固定电话")
    private String telephone;

    /**
     * 移动电话
     */
    @ExcelProperty(value = "移动电话", index = 9)
    @ColumnWidth(15)
    @Schema(description = "移动电话")
    private String mobilephone;

    /**
     * 开业年月
     */
    @ExcelProperty(value = "开业年月", index = 10)
    @ColumnWidth(15)
    @Schema(description = "开业年月")
    private String foundDate;

    /**
     * 入驻年月
     */
    @ExcelProperty(value = "入驻年月", index = 11)
    @ColumnWidth(15)
    @Schema(description = "入驻年月")
    private String settledDate;

    /**
     * 工商注册号
     */
    @ExcelProperty(value = "工商注册号", index = 12)
    @ColumnWidth(20)
    @Schema(description = "工商注册号")
    private String businessRegistrationNo;

    /**
     * 注册资金（万元）
     */
    @ExcelProperty(value = "注册资金（万元）", index = 13)
    @ColumnWidth(15)
    @Schema(description = "注册资金（万元）")
    private BigDecimal registeredCapital;

    /**
     * 地税注册号
     */
    @ExcelProperty(value = "地税注册号", index = 14)
    @ColumnWidth(20)
    @Schema(description = "地税注册号")
    private String taxRegistrationNo;

    /**
     * 企业出资来源地
     */
    @ExcelProperty(value = "企业出资来源地", index = 15)
    @ColumnWidth(20)
    @Schema(description = "企业出资来源地")
    private String investmentSource;

    /**
     * 企业营业状态
     */
    @ExcelProperty(value = "企业营业状态", index = 16)
    @ColumnWidth(15)
    @Schema(description = "企业营业状态")
    private String businessStatus;

    /**
     * 商务商业用房总面积
     */
    @ExcelProperty(value = "商务商业用房总面积", index = 17)
    @ColumnWidth(20)
    @Schema(description = "商务商业用房总面积")
    private BigDecimal businessArea;

    /**
     * 实用面积
     */
    @ExcelProperty(value = "实用面积", index = 18)
    @ColumnWidth(15)
    @Schema(description = "实用面积")
    private BigDecimal settledArea;

    /**
     * 期末从业人数
     */
    @ExcelProperty(value = "期末从业人数", index = 19)
    @ColumnWidth(15)
    @Schema(description = "期末从业人数")
    private Integer engagedNum;

    /**
     * 营业收入（千元）
     */
    @ExcelProperty(value = "营业收入（千元）", index = 20)
    @ColumnWidth(20)
    @Schema(description = "营业收入（千元）")
    private BigDecimal revenue;

    /**
     * 国税小计
     */
    @ExcelProperty(value = "国税小计", index = 21)
    @ColumnWidth(15)
    @Schema(description = "国税小计")
    private BigDecimal nationalTax;

    /**
     * 地税小计
     */
    @ExcelProperty(value = "地税小计", index = 22)
    @ColumnWidth(15)
    @Schema(description = "地税小计")
    private BigDecimal localTax;

    /**
     * 变更情况（新增、退出）
     */
    @ExcelProperty(value = "变更情况", index = 23)
    @ColumnWidth(15)
    @Schema(description = "变更情况（新增、退出）")
    private String changeSituation;

    /**
     * 同期数
     */
    @ExcelProperty(value = "同期数", index = 24)
    @ColumnWidth(15)
    @Schema(description = "同期数")
    private BigDecimal sqAmt;
}
