package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/3/11 15:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomDetailEnterpriseHistoryVo extends BaseVo {

    private static final long serialVersionUID = -3517668000159631362L;

    @Schema(description = "企业id")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "入驻开始时间")
    private LocalDate checkInDate;

    @Schema(description = "搬离时间")
    private LocalDate realityMoveOutDate;

    @Schema(description = "入驻面积")
    private BigDecimal area;
}
