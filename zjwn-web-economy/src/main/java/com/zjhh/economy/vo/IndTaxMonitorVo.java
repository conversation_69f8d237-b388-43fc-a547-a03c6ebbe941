package com.zjhh.economy.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 产业税收监测Vo
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class IndTaxMonitorVo extends BaseVo {

    private static final long serialVersionUID = 7125615756697887886L;
    @Schema(description = "id")
    private String id;

    @Schema(description = "日期")
    private String rq;

    @Schema(description = "监测预警")
    private String warningMonitor;

    @Schema(description = "预警详情")
    private String warningDetail;

    @Schema(description = "行业")
    private String industryName;

    @Schema(description = "统计范围")
    private String monitorRange;

    @Schema(description = "预警日期")
    private String warningDate;

    @Schema(description = "是否处理")
    private Boolean handled;

    @Schema(hidden = true, description = "监测周期")
    @JsonIgnore
    private Integer monitorRulePeriod;

    @Schema(hidden = true, description = "1同比2环比")
    private Integer monitorRuleCompare;

    @Schema(hidden = true, description = "1上升2下降")
    private Integer monitorRuleChange;

    @Schema(description = "本月税收")
    private BigDecimal bqAmt;

    @Schema(description = "同期税收")
    private BigDecimal sqAmt;

    @Schema(description = "本月环比税收")
    private BigDecimal bqljAmt;

    @Schema(description = "上月税收")
    private BigDecimal sqljAmt;

    @Schema(description = "同比增幅")
    private BigDecimal tbZf;

    @Schema(description = "环比增幅")
    private BigDecimal hbZf;

    public String getWarningMonitor() {
        return generateWarning();
    }

    public String getWarningDetail() {
        return generateDetail();
    }

    private String generateWarning() {
        String dateStr = null;
        switch (getMonitorRulePeriod()) {
            case 1: {
                String year = rq.substring(0, 4);
                String month = rq.substring(4, 6);
                dateStr = year + "年" + month + "月" + "税收";
                if (monitorRuleCompare == 1) {
                    dateStr = dateStr + "同比上年同期，增长率已超过 " ;
                } else {
                    dateStr = !month.equals("1") ? dateStr + "环比上月，增长率已超过 "  : "";
                }
                break;
            }
            case 2: {
                String year = rq.substring(0, 4);
                String quarter = rq.substring(4);
                dateStr = year + "年" + quarter + "季度" + "税收";
                if (monitorRuleCompare == 1) {
                    dateStr = dateStr + "同比上年同期，增长率已超过 " ;
                } else {
                    dateStr = !quarter.equals("1") ? dateStr + "环比上季度，增长率已超过 "  : "";
                }
                break;
            }
            default:
                String year = rq.substring(0, 4);
                dateStr = year + "年累计税收";

                if (monitorRuleCompare == 1) {
                    dateStr = dateStr + "同比上年，增长率已超过 " ;
                } else {
                    dateStr = dateStr + "环比上年，增长率已超过 " ;
                }
        }

        return dateStr;
    }

    private String generateDetail() {
        String detailStr = null;
        String hbStr = hbZf.toPlainString();
        String tbStr = tbZf.toPlainString();
        switch (getMonitorRulePeriod()) {
            case 1: {
                String currentYear = rq.substring(0, 4);
                String currentMonth = rq.substring(4, 6);
                Integer lastYear = Integer.valueOf(currentYear) - 1;
                BigDecimal currentAmt = monitorRuleCompare == 1 ? bqAmt : bqljAmt;
                BigDecimal lastAmt = monitorRuleCompare == 1 ? sqAmt : sqljAmt;
                detailStr = currentYear + "年" + currentMonth  + "月" + "税收: " + currentAmt.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_DOWN) + "万元 <br> "+
                        lastYear + "年" + currentMonth + "月" + "税收: " + lastAmt.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_DOWN) + "万元 <br> ";
                if (currentMonth.equals("1") && monitorRuleCompare.equals(2)) {
                    return "";
                }
                break;
            }
            case 2: {
                String currentYear = rq.substring(0, 4);
                String quarter = rq.substring(4);
                Integer lastYear = Integer.valueOf(currentYear) - 1;
                Integer lastQuarter = Integer.valueOf(quarter) - 1;
                BigDecimal currentAmt = monitorRuleCompare == 1 ? bqAmt : bqljAmt;
                BigDecimal lastAmt = monitorRuleCompare == 1 ? sqAmt : sqljAmt;
                detailStr = currentYear + "年" + lastQuarter  + "季度" + "税收: " + currentAmt.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_DOWN) + "万元 <br> "+
                        (monitorRuleCompare == 1 ? lastYear : currentYear) + "年" + (monitorRuleCompare == 1 ? lastQuarter : quarter) + "季度" + "税收: " + lastAmt.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_DOWN) + "万元 <br> ";
                if (quarter.equals("1") && monitorRuleCompare.equals(2)) {
                    return "";
                }
                break;
            }
            default:
                String currentYear = rq.substring(0, 4);
                Integer lastYear = Integer.valueOf(currentYear) - 1;
                BigDecimal currentAmt = monitorRuleCompare == 1 ? bqAmt : bqljAmt;
                BigDecimal lastAmt = monitorRuleCompare == 1 ? sqAmt : sqljAmt;
                detailStr = currentYear + "年"   + "累计" + "税收: " + currentAmt.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_DOWN) + "万元 <br> "+
                        lastYear + "年"  + "累计" + "税收: " + lastAmt.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_DOWN) + "万元 <br>";

        }

        return detailStr;
    }

}
