package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 空置周期分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyPeriodSummaryVo extends BaseVo {
    private static final long serialVersionUID = -7803570565879079487L;

    @Schema(description = "房源总数")
    private Integer totalCount;

    @Schema(description = "空置周期")
    private List<RoomPeriodVo> rooms;


}
