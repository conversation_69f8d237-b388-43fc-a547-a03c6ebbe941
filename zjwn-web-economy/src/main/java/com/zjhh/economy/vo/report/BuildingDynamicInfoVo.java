package com.zjhh.economy.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingDynamicInfoVo extends BaseVo {
    private static final long serialVersionUID = -6318054330105155327L;


    /**
     * 序号
     */
    @Schema(description = "序号")
    @ExcelProperty(index = 0,value = {"序号"})
    private String serialNo;

    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    @ExcelProperty(index = 1,value = {"楼宇名称"})
    private String projectName;

    /**
     * 投入使用日期
     */
    @Schema(description = "投入使用日期")
    @ExcelProperty(index = 2,value = {"投入使用日期"})
    private String operationTime;

    /**
     * 楼宇面积(m²)
     */
    @Schema(description = "楼宇面积(m²)")
    @ExcelProperty(index = 3,value = {"楼宇面积(m²)"})
    private BigDecimal businessArea;

    /**
     * 已用面积(m²)
     */
    @Schema(description = "已用面积(m²)")
    @ExcelProperty(index = 4,value = {"入住情况","已用面积(m²)"})
    private BigDecimal area;

    /**
     * 入驻率
     */
    @Schema(description = "入驻率")
    @ExcelProperty(index = 5,value = {"入住情况","入驻率"})
    private BigDecimal settledRate;

    /**
     * 法人及产业/个体(家)
     */
    @Schema(description = "法人及产业/个体(家)")
    @ExcelProperty(index = 6,value = {"注册情况","法人及产业/个体(家)"})
    private String frcygt;

    /**
     * 注册数
     */
    @Schema(description = "注册数")
    @ExcelProperty(index = 7,value = {"注册情况","注册数"})
    private Integer zcs;

    /**
     * 注册率(%)
     */
    @Schema(description = "注册率(%)")
    @ExcelProperty(index = 8,value = {"注册情况","注册率(%)"})
    private BigDecimal zcsRate;

    /**
     * 税收(万元)
     */
    @Schema(description = "税收(万元)")
    @ExcelProperty(index = 9,value = {"财税情况","税收(万元)"})
    private BigDecimal ss;

    /**
     * 单位税收(元/平米)
     */
    @Schema(description = "单位税收(元/平米)")
    @ExcelProperty(index = 10,value = {"财税情况","单位税收(元/平米)"})
    private BigDecimal ssUnit;

    /**
     * 除房地产税收(万元)
     */
    @Schema(description = "除房地产税收(万元)")
    @ExcelProperty(index = 11,value = {"财税情况","除房地产税收(万元)"})
    private BigDecimal cfdcss;

    /**
     * 除房地产单位税收(元/平米)
     */
    @Schema(description = "除房地产单位税收(元/平米)")
    @ExcelProperty(index = 12,value = {"财税情况","除房地产单位税收(元/平米)"})
    private BigDecimal cfdcssUnit;
}
