package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.enume.WarningRemindEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 工作台预警提醒
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkPlatformWarningRemindVo extends BaseVo {
    private static final long serialVersionUID = 4708015151248271105L;

    @Schema(description = "预警类型")
    private Integer warningType;

    @Schema(description = "预警类型转义")
    private String warningTypeStr;


    @Schema(description = "预警描述")
    private String remindDesc;

    @Schema(description = "预警日期")
    private LocalDate remindDate;

    public String getWarningTypeStr() {
        if (warningType.equals(WarningRemindEnum.ENT_MOVE.value())) {
            return "租赁即将到期";
        } else if (warningType.equals(WarningRemindEnum.ENT_MOVE_CONFIRM.value())) {
            return "租赁到期确认";
        } else if (warningType.equals(WarningRemindEnum.ROOM_EMPTY.value())) {
            return "房源空置提醒";
        }else {
            return "企业信息更新提醒";
        }
    }
}
