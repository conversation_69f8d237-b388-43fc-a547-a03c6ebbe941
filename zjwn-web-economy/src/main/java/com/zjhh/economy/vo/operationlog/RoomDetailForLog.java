package com.zjhh.economy.vo.operationlog;

import cn.hutool.core.util.BooleanUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomDetailForLog extends BaseVo {

    private static final long serialVersionUID = -5212498253712688939L;

    @Schema(description = "房间id")
    private String id;

    @Schema(description = "所属项目")
    private String projectName;

    @Schema(description = "所属楼宇")
    private String buildingName;

    @Schema(description = "所属楼层")
    private String floorName;

    @Schema(description = "房号")
    private String roomNo;

    @Schema(description = "建筑面积（㎡）")
    private BigDecimal buildingArea;

    @Schema(description = "商务面积（㎡）")
    private BigDecimal businessArea;

    private Boolean openHired;

    @Schema(description = "是否开放出租")
    private String openHiredName;

    private String renovationCode;

    @Schema(description = "装修状态")
    private String renovationName;

    private String roomTypeCode;

    @Schema(description = "房间类型")
    private String roomTypeName;

    @Schema(description = "房间照片")
    private List<String> roomImgs;

    @Schema(description = "房间标签")
    private List<String> roomLabels;

    private BigDecimal rendUnitPrice;

    private Integer rendUnit;

    @Schema(description = "租金单价")
    private String rendUnitName;

    private BigDecimal propertyFeesUnitPrice;

    private Integer propertyFeesUnit;

    @Schema(description = "物业费单价")
    private String propFeesUnitName;

    @Schema(description = "水费单价（元/m³）")
    private BigDecimal waterFeesUnitPrice;

    @Schema(description = "电费单价（元/KW/h）")
    private BigDecimal electricityFeesUnitPrice;

    private Integer houseOrientation;

    @Schema(description = "房屋朝向")
    private String houseOrientName;

    private String ownershipCode;

    @Schema(description = "权属性质")
    private String ownershipName;

    private String propertyCertificateCode;

    @Schema(description = "产证状态")
    private String propCertName;

    @Schema(description = "权属人")
    private List<String> personList;

    public String getOpenHiredName() {
        return BooleanUtil.isTrue(openHired) ? "是" : "否";
    }

    public String getRendUnitName() {
        return getPriceName(rendUnitPrice, rendUnit);
    }

    public String getPropFeesUnitName() {
        return getPriceName(propertyFeesUnitPrice, propertyFeesUnit);
    }

    public String getHouseOrientName() {
        if (houseOrientation == null) {
            return null;
        }
        switch (houseOrientation) {
            case 1:
                return "东";
            case 2:
                return "南";
            case 3:
                return "西";
            case 4:
                return "北";
            case 5:
                return "东南";
            case 6:
                return "西南";
            case 7:
                return "东北";
            case 8:
                return "西北";
            case 9:
                return "南北";
            case 10:
                return "东西";
        }
        return null;
    }

    private String getPriceName(BigDecimal price, Integer unit) {
        StringBuilder builder = new StringBuilder();
        if (price != null) {
            builder.append(price);
        }
        String unitName = getUnitName(unit);
        if (unitName != null) {
            builder.append(" ")
                    .append(unitName);
        }
        return builder.toString();
    }

    private String getUnitName(Integer unit) {
        if (unit == null) {
            return null;
        }
        if (unit == 1) {
            return "元/㎡/天";
        }
        if (unit == 2) {
            return "元/㎡/月";
        }
        if (unit == 3) {
            return "元/月";
        }
        return null;
    }
}
