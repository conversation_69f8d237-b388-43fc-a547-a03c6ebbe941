package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 导入入驻Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImportSettleVo extends BaseVo {
    private static final long serialVersionUID = -345982283856780781L;


    @Schema(description = "企业名称")
    @ExcelProperty(value = "企业名称", index = 0)
    private String enterpriseName;

    @Schema(description = "统一社会信用代码")
    @ExcelProperty(value = "统一社会信用代码", index = 1)
    private String uscc;

    @Schema(description = "法定代表人")
    @ExcelProperty(value = "法定代表人", index = 2)
    private String legalPerson;

    @Schema(description = "电话")
    @ExcelProperty(value = "电话", index = 3)
    private String phone;

    @Schema(description = "企业联系人")
    @ExcelProperty(value = "企业联系人", index = 4)
    private String entContactPerson;

    @Schema(description = "联系电话")
    @ExcelProperty(value = "联系电话", index = 5)
    private String entPhone;

    @Schema(description = "项目名称")
    @ExcelProperty(value = "项目", index = 6)
    private String projectName;

    @Schema(description = "楼宇名称")
    @ExcelProperty(value = "楼宇", index = 7)
    private String buildingName;

    @Schema(description = "楼层")
    @ExcelProperty(value = "楼层", index = 8)
    private String floorNo;

    @Schema(description = "房间号")
    @ExcelProperty(value = "房号", index = 9)
    private String roomNo;

    @Schema(description = "入驻开始时间")
    @ExcelProperty(value = "入驻开始时间", index = 10)
    private String checkInDate;

    @Schema(description = "预计退房时间")
    @ExcelProperty(value = "租赁到期时间", index = 11)
    private String expectMoveOutDate;

    @Schema(description = "面积")
    @ExcelProperty(value = "入驻面积(㎡)", index = 12)
    private String area;

    @Schema(description = "装修开始时间")
    @ExcelProperty(value = "装修开始日期", index = 13)
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "日期格式必须是 yyyy-MM-dd")
    private String renovationStartDate;

    @Schema(description = "装修结束时间")
    @ExcelProperty(value = "装修结束日期", index = 14)
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "日期格式必须是 yyyy-MM-dd")
    private String renovationEndDate;

    @Schema(description = "备注")
    @ExcelProperty(value = "备注", index = 15)
    private String remark;

    @Schema(description = "匹配结果")
    @ExcelProperty(value = "匹配结果", index = 16)
    private String matchResult;
}
