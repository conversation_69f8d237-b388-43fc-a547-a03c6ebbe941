package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettleInfoAnalyseVo extends BaseVo {
    private static final long serialVersionUID = 722179329141553305L;

    @Schema(description = "当前入驻房间数")
    private String roomCount;

    @Schema(description = "当前入驻面积")
    private BigDecimal area;

    @Schema(description = "租用趋势")
    private List<SettleTrendVo> trends;

    @Schema(description = "入驻明细")
    private List<SettleInfoListVo> settleInfos;


}
