package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端动态经济Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppDynamicEcoVo extends BaseVo {
    private static final long serialVersionUID = 2583130883146944718L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "图片id")
    private String documentId;

}
