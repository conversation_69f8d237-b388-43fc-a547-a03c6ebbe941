package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产业结构
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryStructVo extends BaseVo {
    private static final long serialVersionUID = -609724042738751135L;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "行业编码")
    private String industryCode;

    @Schema(description = "税收总额")
    private BigDecimal totalTaxIncome;

    @Schema(description = "占比")
    private BigDecimal zb;

}
