package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class ImportRoomVo extends BaseVo {
    private static final long serialVersionUID = -9084857584255568411L;

    private String projectId;

    private String projectName;

    private String buildingId;

    private String buildingName;

    private String floorId;

    private String floorNo;

    private String roomId;

    private String roomNo;

    private BigDecimal businessArea;
}
