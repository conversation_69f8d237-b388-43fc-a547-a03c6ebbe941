package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产业聚焦分析
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryFocusAnalyzeVo extends BaseVo {
    private static final long serialVersionUID = 983034275517935749L;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "行业Code")
    private String industryCode;

    @Schema(description = "纳税规模")
    private String taxScaled;

    @Schema(description = "企业数量")
    private String entCount;

    @Schema(description = "入驻面积")
    private String settledArea;

    @Schema(description = "税收占比")
    private BigDecimal taxScaledZb;

    @Schema(description = "企业占比")
    private BigDecimal entCountZb;


    @Schema(description = "入驻面积占比")
    private BigDecimal settledAreaZb;


}
