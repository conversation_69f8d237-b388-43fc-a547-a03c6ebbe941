package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.enume.EnterpriseChangeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseChangeListVo extends BaseVo {
    private static final long serialVersionUID = -4177807132681199710L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "变更日期")
    private LocalDate changeDate;

    @Schema(description = "变更类型code")
    private String changeType;

    @Schema(description = "变更类型转义")
    private String changeTypeValue;

    @Schema(description = "备注")
    private String remark;

    public String getChangeTypeValue() {
        return EnterpriseChangeTypeEnum.getNameByCode(changeType);
    }
}
