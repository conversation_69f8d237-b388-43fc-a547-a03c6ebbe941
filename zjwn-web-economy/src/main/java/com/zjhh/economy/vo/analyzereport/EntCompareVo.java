package com.zjhh.economy.vo.analyzereport;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 企业对比分析Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntCompareVo extends BaseVo {
    private static final long serialVersionUID = 2469145022548881762L;


    @Schema(description = "企业名称")
    @ExcelProperty(index = 0,value = "企业名称")
    private String enterpriseName;

    @Schema(hidden = true)
    @ExcelProperty(index = 1,value = "企业概览")
    @JsonIgnore
    private String label1;

    @Schema(description = "企业性质")
    @ExcelIgnore
    private List<String> enterprisePropertyList;

    @Schema(hidden = true)
    @ExcelProperty(index = 2,value = "企业属性")
    private String enterpriseProperty;

    @Schema(hidden = true)
    @ExcelProperty(index = 3,value = "企业标签")
    private String enterpriseLabel;

    @Schema(description = "行业")
    @ExcelProperty(index = 4,value = "所属行业")
    private String industryName;

    @Schema(description = "电话")
    @ExcelProperty(index = 5,value = "电话")
    private String phone;

    @Schema(description = "入驻信息")
    @ExcelProperty(index = 6,value = "入驻信息")
    private String settleInfo;

    @Schema(description = "搬离信息")
    @ExcelProperty(index = 7,value = "搬离信息")
    private String moveOutInfo;

    @Schema(description = "税收总量")
    @ExcelProperty(index = 8,value = "税收总量(万元)")
    private BigDecimal totalTax;

    @Schema(description = "入驻面积")
    @ExcelProperty(index = 9,value = "入驻面积(㎡)")
    private BigDecimal settleArea;

    @Schema(description = "企业单位产出")
    @ExcelProperty(index = 10,value = "企业单位产出(本年) (元/㎡)")
    private BigDecimal output;

    @Schema(description = "入驻房间数")
    @ExcelProperty(index = 11,value = "入驻房间数")
    private Integer settleRoomCount;

    @Schema(description = "入驻时长")
    @ExcelProperty(index = 12,value = "入驻时长(年)")
    private String settledTime;

    @Schema(hidden = true)
    @JsonIgnore
    @ExcelProperty(index = 13,value = "基本信息")
    private String label2;

    @Schema(hidden = true)
    @ExcelProperty(index = 14,value = "企业名称")
    private String enterpriseName2;

    /**
     * 社会信用代码
     */
    @Schema(description = "社会信用代码")
    @ExcelProperty(index = 15,value = "社会信用代码")
    private String uscc;

    /**
     * 曾用名
     */
    @Schema(description = "曾用名")
    @ExcelProperty(index = 16,value = "曾用名")
    private String oldName;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    @ExcelProperty(index = 17,value = "成立日期")
    private LocalDate foundDate;

    /**
     * 法人
     */
    @Schema(description = "法人")
    @ExcelProperty(index = 18,value = "法人或负责人")
    private String legalPerson;

    /**
     * 企业类型
     */
    @Schema(description = "企业类型名称")
    @ExcelProperty(index = 19,value = "企业类型")
    private String enterpriseTypeName;


    @Schema(description = "行业")
    @ExcelProperty(index = 20,value = "行业")
    private String industryName2;


    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    @ExcelProperty(index = 21,value = "注册资本(万)")
    private BigDecimal registeredCapital;


    /**
     * 住所
     */
    @Schema(description = "住所")
    @ExcelProperty(index = 22,value = "住所")
    private String residence;


    @Schema(hidden = true)
    @JsonIgnore
    @ExcelProperty(index = 23,value = "电话")
    private String phone2;

    @Schema(description = "是否规上企业")
    @ExcelProperty(index = 24,value = "是否规上企业")
    private String onScaledStr;


    @Schema(description = "是否属地企业")
    @ExcelProperty(index = 25,value = "是否属地企业")
    private String territorializedStr;

    @Schema(description = "营业期限")
    @ExcelProperty(index = 26,value = "营业期限")
    private String businessTerm;

    /**
     * 融资阶段
     */
    @Schema(description = "融资名称")
    @ExcelProperty(index = 27,value = "融资阶段")
    private String financingStageName;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    @ExcelProperty(index = 28,value = "注册地址")
    private String registeredAddress;

    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    @ExcelProperty(index = 29,value = "经营范围")
    private String businessScope;


    @Schema(description = "企业联系人")
    @ExcelProperty(index = 30,value = "企业联系人")
    private String entContactPerson;

    @Schema(description = "企业联系电话")
    @ExcelProperty(index = 31,value = "企业联系电话")
    private String entPhone;


    @Schema(hidden = true)
    @JsonIgnore
    @ExcelProperty(index = 32,value = "入驻信息")
    private String label3;

    @Schema(description = "项目名称")
    @ExcelProperty(index = 33,value = "项目名称")
    private String projectName;

    @Schema(description = "楼宇名称")
    @ExcelProperty(index = 34,value = "楼宇名称")
    private String buildingName;

    @Schema(description = "楼层名称")
    @ExcelProperty(index = 35,value = "楼层名称")
    private String floorName;

    @Schema(description = "房间号")
    @ExcelProperty(index = 36,value = "房间号")
    private String roomNo;

    @Schema(description = "入驻面积")
    @ExcelProperty(index = 37,value = "入驻面积(㎡)")
    private String settledArea;


    @Schema(hidden = true)
    @JsonIgnore
    @ExcelIgnore
    private Boolean onScaled;




    @Schema(hidden = true)
    @JsonIgnore
    @ExcelIgnore
    private Boolean territorialized;

    @Schema(description = "企业标签")
    @ExcelIgnore
    private List<String> enterpriseLabels;

    /**
     * 营业期限开始时间
     */
    @Schema(hidden = true)
    @JsonIgnore
    @ExcelIgnore
    private LocalDate businessTermStart;

    /**
     * 营业期限结束时间
     */
    @Schema(hidden = true)
    @JsonIgnore
    @ExcelIgnore
    private LocalDate businessTermEnd;

    public String getEntContactPerson() {
        return StrUtil.isNotBlank(entContactPerson) ? entContactPerson : "";
    }

    public String getEntPhone() {
        return StrUtil.isNotBlank(entPhone) ? entPhone : "";
    }

    public String getOnScaledStr() {
        return onScaled ? "是" : "否";
    }

    public String getTerritorializedStr() {
        return territorialized ? "是" : "否";
    }

    public String getEnterpriseProperty() {
        String scaledStr  = "";
        String localedStr  = "";
        if (onScaled) {
            scaledStr = "规上企业";
        }
        if (territorialized) {
            localedStr = "属地企业";
        }
        if (StrUtil.isNotBlank(scaledStr) && StrUtil.isNotBlank(localedStr)){
            return scaledStr + "/" + localedStr;
        }else {
            return scaledStr + localedStr;
        }
    }

    public String getEnterpriseLabel() {
        if (CollUtil.isNotEmpty(enterpriseLabels)) {
            return enterpriseLabel.join("/", enterpriseLabels);
        }else {
            return "";
        }
    }

    public String getBusinessTerm() {
      if (ObjectUtil.isNotNull(businessTermStart) && ObjectUtil.isNotNull(businessTermEnd)) {
          return DateUtil.format(businessTermStart.atStartOfDay(), "yyyy-MM-dd")
                  + "至" + DateUtil.format(businessTermEnd.atStartOfDay(), "yyyy-MM-dd");
      }else {
          return "";
      }
    }

    public String getEnterpriseName2() {
        return enterpriseName;
    }

    public String getPhone2() {
        return phone;
    }

    public List<String> getEnterprisePropertyList() {
        List<String> list = new ArrayList<>();
        if (onScaled) {
           list.add("规上企业") ;
        }
        if (territorialized) {
            list.add("属地企业") ;
        }
        return list;
    }

    public String getIndustryName2() {
        return industryName;
    }
}
