package com.zjhh.economy.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class WarningRuleVo extends BaseVo {
    private static final long serialVersionUID = 5332624766601163749L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "监测范围")
    private String monitorRange;

    @Schema(description = "监测规则描述")
    private String monitorDesc;

    @Schema(description = "监测时间维度")
    @JsonIgnore
    private Integer monitorRulePeriod;

    @Schema(description = "同比环比")
    @JsonIgnore
    private Integer monitorRuleCompare;

    @Schema(description = "上升下降")
    @JsonIgnore
    private Integer monitorRuleChange;

    @Schema(description = "增幅")
    @JsonIgnore
    private BigDecimal monitorRuleZf;

    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "是否启用")
    private Boolean enabled;

    private Integer monitorRuleType;


    public String getMonitorDesc() {
        String periodStr = monitorRulePeriod == 1 ? "月度税收" : (monitorRulePeriod == 2 ? "季度" : "年度");
        String compareStr = monitorRuleCompare == 1 ? "同比" : "环比";
        String changeStr = monitorRuleChange == 1 ? "上升" : "下降";
        return "当" + (monitorRuleType == 1 ? "企业" : "产业") + periodStr + compareStr + changeStr + "超" + monitorRuleZf + "%时，产生监测预警";
    }
}
