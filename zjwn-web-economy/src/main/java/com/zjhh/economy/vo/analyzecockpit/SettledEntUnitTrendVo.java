package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettledEntUnitTrendVo extends BaseVo {
    private static final long serialVersionUID = 6648146636579860583L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "企业总数量")
    private Integer entTotalCount;

    @Schema(description = "属地企业数量")
    private Integer localedCount;

    @Schema(description = "属地企业占比")
    private BigDecimal zb;

    public String getDatekey() {
        String quarter = "";
        if (datekey.length() == 5 && !datekey.contains("-")) {
            quarter = datekey.substring(4,5);
        }
        if (quarter.equals("1") && datekey.length() == 5) {
            return datekey.substring(0,4) + "年" + "第一季度";
        }else if (quarter.equals("2")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第二季度";
        } else if (quarter.equals("3")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第三季度";

        }else if (quarter.equals("4")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第四季度";
        }else {
            return datekey;
        }
    }

}
