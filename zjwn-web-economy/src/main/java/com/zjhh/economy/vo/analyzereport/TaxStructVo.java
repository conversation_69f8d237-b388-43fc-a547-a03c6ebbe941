package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 水周结构
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaxStructVo extends BaseVo {
    private static final long serialVersionUID = -7334521056745829192L;

    private String taxName;

    private String taxType;

    private String datekey;

    private BigDecimal ss;

    private BigDecimal ssZf;

    private BigDecimal jdsr;

    private BigDecimal jdsrZf;

    private BigDecimal totalSs;

    private BigDecimal totalSsZf;

    private BigDecimal totalJdsr;

    private BigDecimal totalJdsrZf;



}
