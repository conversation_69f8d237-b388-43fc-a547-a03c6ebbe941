package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 10:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomDetailOwnershipVo extends BaseVo {

    private static final long serialVersionUID = 3861720758831090243L;

    @Schema(description = "权属人")
    private String person;

    @Schema(description = "联系方式")
    private String phone;
}
