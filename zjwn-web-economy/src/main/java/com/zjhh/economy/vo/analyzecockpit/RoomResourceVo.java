package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 房源面积分布
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomResourceVo extends BaseVo {
    private static final long serialVersionUID = 1660903147322732332L;

    @Schema(description = "房间面积")
    private String roomArea;

    @Schema(description = "房间数量")
    private String roomCount;

    @Schema(description = "占比")
    private String zb;


}
