package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/8 14:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomDetailVo extends BaseVo {

    private static final long serialVersionUID = -1493195414150474219L;

    @Schema(description = "房间id")
    private String roomId;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "楼宇ID")
    private String buildingId;

    @Schema(description = "楼层ID")
    private String floorId;

    @Schema(description = "房间名称")
    private String roomName;

    @Schema(description = "是否开放出租")
    private Boolean openHired;

    @Schema(description = "房间状态：0-空置 1-已入驻 2-不可出租 3-新入驻装修中")
    private Integer roomStatusCode;

    @Schema(description = "房间状态")
    private String roomStatusName;

    @Schema(description = "面积")
    private BigDecimal businessArea;

    @Schema(description = "装修状态")
    private String renovationName;

    @Schema(description = "房间类型")
    private String roomTypeName;

    @Schema(description = "权属性质")
    private String ownershipName;

    @Schema(description = "产权状态")
    private String propertyCertificateName;

    @Schema(description = "房间图片id")
    private String roomImgId;

    @Schema(description = "房间标签")
    private List<String> labelList;

    @Schema(description = "历史入驻")
    private List<RoomDetailEnterpriseHistoryVo> enterpriseHistoryList;

    @Schema(description = "业权信息")
    private List<RoomDetailOwnershipVo> OwnershipList;

    @Schema(description = "入驻企业")
    private List<RoomDetailEnterpriseVo> enterpriseList;

    private BigDecimal buildingArea;

    @Schema(description = "备注")
    private String remark;

}
