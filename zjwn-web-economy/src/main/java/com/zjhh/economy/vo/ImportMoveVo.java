package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 导入搬离Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImportMoveVo extends BaseVo {
    private static final long serialVersionUID = -345982283856780781L;


    @Schema(description = "企业名称")
    @ExcelProperty(value = "企业名称", index = 0)
    private String enterpriseName;

    @Schema(description = "统一社会信用代码")
    @ExcelProperty(value = "统一社会信用代码", index = 1)
    private String uscc;

    @Schema(description = "项目名称")
    @ExcelProperty(value = "项目", index = 2)
    private String projectName;

    @Schema(description = "楼宇名称")
    @ExcelProperty(value = "楼宇", index = 3)
    private String buildingName;

    @Schema(description = "楼层")
    @ExcelProperty(value = "楼层", index = 4)
    private String floorNo;

    @Schema(description = "房间号")
    @ExcelProperty(value = "房号", index = 5)
    private String roomNo;

    @Schema(description = "实际搬离日期")
    @ExcelProperty(value = "实际搬离日期", index = 6)
    private LocalDate realityMoveOutDate;

    @Schema(description = "去向登记")
    @ExcelProperty(value = "去向登记", index = 7)
    private String destination;

    @Schema(description = "迁出原因")
    @ExcelProperty(value = "迁出原因", index = 8)
    private String moveOutReason;

    @Schema(description = "备注")
    @ExcelProperty(value = "备注", index = 9)
    private String remark;

    @Schema(description = "匹配结果")
    @ExcelProperty(value = "匹配结果", index = 10)
    private String matchResult;

}
