package com.zjhh.economy.vo.operationlog;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DemandHandleForLog extends BaseVo {
    private static final long serialVersionUID = 8413687227803324940L;

    private String id;

    @Schema(description = "处理结果")
    private String handleResult;

    @Schema(description = "处理说明")
    private String handleDesc;

    @Schema(description = "附件")
    private String documentName;
}
