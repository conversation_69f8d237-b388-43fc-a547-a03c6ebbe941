package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  楼宇概览
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingSummaryVo extends BaseVo {
    private static final long serialVersionUID = -362640714085146505L;

    @Schema(description = "楼宇总数")
    private Integer buildingCount;

    @Schema(description = "总建筑面积")
    private String totalArea;

    @Schema(description = "企业数量")
    private String entCount;

    @Schema(description = "属地注册率")
    private String localRegisterRate;

    @Schema(description = "税收总量")
    private String totalTaxIncome;

    @Schema(description = "入住率")
    private String settleRate;




}
