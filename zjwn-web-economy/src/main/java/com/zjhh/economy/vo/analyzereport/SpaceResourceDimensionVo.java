package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 空间资源多维度走势对比
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpaceResourceDimensionVo extends BaseVo {
    private static final long serialVersionUID = -3177815721711770754L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "展示名称")
    private String name;

    @Schema(description = "code")
    private String code;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "入驻率")
    private BigDecimal settledRate;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;


}
