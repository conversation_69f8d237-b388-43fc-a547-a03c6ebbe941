package com.zjhh.economy.vo.report;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022-05-25 3:01 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SendReportSubReq extends BaseReq {

    private static final long serialVersionUID = -4216040034109455812L;

    @Schema(description = "报告guid")
    private String guid;

    @Schema(description = "报告名称")
    private String reportName;
}
