package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业数量Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntCountVo extends BaseVo {
    private static final long serialVersionUID = -2694255032345570111L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "企业数量")
    private String entCount;
}
