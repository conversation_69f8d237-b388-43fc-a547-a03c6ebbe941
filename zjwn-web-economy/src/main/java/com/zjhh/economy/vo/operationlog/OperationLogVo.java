package com.zjhh.economy.vo.operationlog;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperationLogVo extends BaseVo {
    private static final long serialVersionUID = -1969062536168077283L;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"序号"}, index = 0)
    @Schema(description = "序号")
    private Integer xh;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = {"操作时间"}, index = 1)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "操作时间")
    private LocalDateTime createTime;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"操作ID"}, index = 2)
    @Schema(description = "操作ID")
    private String id;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"用户姓名"}, index = 3)
    @Schema(description = "用户姓名")
    private String username;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"用户账号"}, index = 4)
    @Schema(description = "用户账号")
    private String loginName;

    @ExcelIgnore
    @JsonIgnore
    @Schema(description = "操作模块")
    private String module;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"操作模块"}, index = 5)
    @Schema(description = "操作模块")
    private String moduleName;

    @ExcelIgnore
    @JsonIgnore
    @Schema(description = "操作类型")
    private String type;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"操作类型"}, index = 6)
    @Schema(description = "操作类型")
    private String typeName;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"操作对象"}, index = 7)
    @Schema(description = "操作对象")
    private String objectName;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = {"操作对象id"}, index = 8)
    @Schema(description = "操作对象id")
    private String objectId;

    @ExcelIgnore
    @JsonIgnore
    private String dataBefore;

    @ExcelIgnore
    @JsonIgnore
    private String dataAfter;

    @ExcelProperty(value = "操作前数据", index = 9)
    @JsonIgnore
    private String beforeForExcel;

    @ExcelProperty(value = "操作后数据", index = 10)
    @JsonIgnore
    private String afterForExcel;

    public String getModuleName() {
        return LogModuleEnum.getModuleName(module);
    }

    public String getTypeName() {
        return LogTypeEnum.getTypeName(type);
    }
}
