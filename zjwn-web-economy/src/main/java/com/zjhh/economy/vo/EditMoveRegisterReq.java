package com.zjhh.economy.vo;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class EditMoveRegisterReq extends BaseReq {
    private static final long serialVersionUID = -1181587488936733709L;

    @Schema(description = "入住ID")
    private String settledId;

    @Schema(description = "实际搬离日期")
    private LocalDate realMoveDate;

    @Schema(description = "去向登记-省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "迁出原因")
    private List<String> labels;

    @Schema(description = "搬离原因")
    private String moveReason;

}
