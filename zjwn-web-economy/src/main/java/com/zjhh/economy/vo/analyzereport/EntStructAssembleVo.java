package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业结构分析VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntStructAssembleVo extends BaseVo {
    private static final long serialVersionUID = -8776070611916683979L;

    @Schema(description = "显示名称")
    private String name;

    private String code;

    @Schema(description = "迁入数量")
    private Integer moveInCount;

    @Schema(description = "迁出数量")
    private Integer moveOutCount;

    @Schema(description = "属地数量")
    private Integer localedCount;

    @Schema(description = "非属地数量")
    private Integer notLocaledCount;


}
