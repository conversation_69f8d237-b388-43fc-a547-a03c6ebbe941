package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 楼层平面图配置
 *
 * <AUTHOR>
 * @date 2024/12/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FloorPlanConfigVo extends BaseVo {
    private static final long serialVersionUID = -5661863857124742975L;

    /**
     * 指向标配置类
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    private static class PointerConfig extends BaseVo {
        private static final long serialVersionUID = -2255602861723081796L;
        @Schema(description = "指向标-横坐标")
        private BigDecimal horizontalOrdinate;
        @Schema(description = "指向标-纵坐标")
        private BigDecimal verticalOrdinate;
        @Schema(description = "指向标-宽度")
        private BigDecimal width;
        @Schema(description = "指向标-高度")
        private BigDecimal height;
        @Schema(description = "指向标-旋转角度")
        private BigDecimal rotateAngle;
    }

    /**
     * 房源配置
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    private static class RoomConfig extends BaseVo {
        private static final long serialVersionUID = -6341006005169801584L;
        @Schema(description = "房间id")
        private String roomId;
        @Schema(description = "房号")
        private String roomNo;
        @Schema(description = "是否显示房号")
        private Boolean showRoomNo;
        @Schema(description = "是否显示房源面积")
        private Boolean showRoomArea;
        @Schema(description = "是否显示入住企业")
        private Boolean showEnt;
        @Schema(description = "是否显示空置状态")
        private Boolean showRoomStatus;
        @Schema(description = "显示方向。1-上，2-下，3-左，4-右")
        private String direction;

        @Schema(description = "指向标-横坐标")
        private BigDecimal horizontalOrdinate;
        @Schema(description = "指向标-纵坐标")
        private BigDecimal verticalOrdinate;
        @Schema(description = "指向标-宽度")
        private BigDecimal width;
        @Schema(description = "指向标-高度")
        private BigDecimal height;
    }

    @Schema(description = "楼层id")
    private String floorId;

    @Valid
    @Schema(description = "指向标配置")
    private PointerConfig pointerConfig;

    @Valid
    @Schema(description = "房源标记配置")
    private List<RoomConfig> roomConfigs;
}
