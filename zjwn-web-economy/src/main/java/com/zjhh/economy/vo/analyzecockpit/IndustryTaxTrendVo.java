package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 各产业纳税规模走势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryTaxTrendVo extends BaseVo {
    private static final long serialVersionUID = 6389166612189231968L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "纳税金额")
    private String taxIncome;

    @Schema(description = "占比")
    private String zb;

}
