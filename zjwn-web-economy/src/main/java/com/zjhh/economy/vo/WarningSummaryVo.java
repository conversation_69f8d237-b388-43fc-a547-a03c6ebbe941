package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class WarningSummaryVo extends BaseVo {
    private static final long serialVersionUID = 8303365660342816080L;

    @Schema(description = "本年预警数")
    private Integer bqWarningCount;

    @Schema(description = "本月预警数")
    private Integer byWarningCount;

    @Schema(description = "同比增幅")
    private BigDecimal tbZf;

    @Schema(description = "环比增幅")
    private BigDecimal hbZf;

}
