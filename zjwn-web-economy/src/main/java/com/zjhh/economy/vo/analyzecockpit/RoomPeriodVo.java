package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  空置周期环形图
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomPeriodVo extends BaseVo {
    private static final long serialVersionUID = -9011851997074399376L;

    @Schema(description = "房间数")
    private String roomCount;

    @Schema(description = "周期")
    private String period;

    @Schema(description = "占比")
    private String zb;

    public String getPeriod() {
        if (period.equals("1")) {
            return "0-30天";
        } else if (period.equals("2")) {
            return "31-60天";
        } else if (period.equals("3")) {
            return "61-120天";
        } else if (period.equals("4")) {
            return "121-240天";
        }else if (period.equals("5")){
            return "241-360天";
        }else {
            return "360天以上";
        }
    }
}
