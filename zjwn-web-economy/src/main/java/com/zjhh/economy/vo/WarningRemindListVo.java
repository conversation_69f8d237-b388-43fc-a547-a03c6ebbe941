package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.enume.WarningRemindEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 预警提醒列表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningRemindListVo extends BaseVo {


    private static final long serialVersionUID = 7271454592840895721L;

    @Schema(description = "主键")
    @ExcelIgnore
    private String id;

    @Schema(description = "预警类型")
    @ExcelIgnore
    private Integer warningType;

    @Schema(description = "预警类型转义")
    @ExcelProperty(index = 0,value = "提醒类型")
    private String warningTypeStr;

    @Schema(description = "提醒状态")
    @ExcelIgnore
    private Integer handleType;

    @Schema(description = "提醒状态转义")
    @ExcelProperty(index = 2,value = "提醒状态")
    private String handleTypeStr;

    @Schema(description = "是否显示操作")
    @ExcelIgnore
    private Boolean operated;


    @Schema(description = "提醒描述")
    @ExcelProperty(index = 1,value = "提醒详情")
    private String remindDesc;


    @Schema(description = "预警日期")
    @ExcelProperty(index = 3,value = "提醒时间")
    private LocalDate remindDate;

    public Boolean getOperated() {
        return handleType == 1;
    }

    public String getHandleTypeStr() {
        if (handleType.equals(1)) {
            return "待处理";
        } else if (handleType.equals(2)) {
            return "已处理";
        } else {
            return "已忽略";
        }
    }

    public String getWarningTypeStr() {
        if (warningType.equals(WarningRemindEnum.ENT_MOVE.value())) {
            return "租赁即将到期";
        } else if (warningType.equals(WarningRemindEnum.ENT_MOVE_CONFIRM.value())) {
            return "租赁到期确认";
        } else if (warningType.equals(WarningRemindEnum.ROOM_EMPTY.value())) {
            return "房源空置提醒";
        } else {
            return "企业信息更新提醒";
        }
    }
}
