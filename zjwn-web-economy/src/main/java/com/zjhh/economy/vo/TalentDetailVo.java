package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TalentDetailVo extends BaseVo {
    private static final long serialVersionUID = -8971711500429754341L;


    @Schema(description = "主键")
    private String id;
    /**
     * 企业id
     */
    @Schema(description = "企业ID")
    private String enterpriseId;

    /**
     * 员工数
     */
    @Schema(description = "员工数")
    private Integer employeeNum;

    /**
     * 博士数量
     */
    @Schema(description = "博士数量")
    private Integer drNum;

    /**
     * 硕士数
     */
    @Schema(description = "硕士数量")
    private Integer masterNum;

    /**
     * 本科数
     */
    @Schema(description = "本科数")
    private Integer bachelorNum;

    /**
     * 专科数
     */
    @Schema(description = "专科数")
    private Integer juniorNumber;

    /**
     * 中专数
     */
    @Schema(description = "中专数")
    private Integer polytechnicNum;

    /**
     * 其他学历数
     */
    @Schema(description = "其他学历")
    private Integer otherNum;
}
