package com.zjhh.economy.vo.operationlog;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FloorListForLog extends BaseVo {
    private static final long serialVersionUID = -544603159668844140L;

    @Schema(description = "序号")
    private Integer xh;

    /**
     * 楼层id
     */
    private String id;

    @Schema(description = "建筑楼层")
    private Integer floorNo;

    @Schema(description = "楼层名称")
    private String floorName;

    @Schema(description = "房间数量")
    private Integer roomSize;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "楼层平面图")
    private String documentName;
}
