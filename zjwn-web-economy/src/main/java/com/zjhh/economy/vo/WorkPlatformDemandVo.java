package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 工作台企业诉求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkPlatformDemandVo extends BaseVo {
    private static final long serialVersionUID = 8779401440931037219L;

    @Schema(description = "总诉求数")
    private Integer totalDemandCount;

    @Schema(description = "待处理数量")
    private Integer pendingDemandCount;

    @Schema(description = "完成率")
    private BigDecimal finishedRate;

    @Schema(description = "处理中数量")
    private Integer handlingCount;


}
