package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxCategoryVo extends BaseVo {
    private static final long serialVersionUID = -4992466546186765959L;

    @Schema(description = "税种名称")
    private String taxName;

    @Schema(description = "税收")
    private String taxIncome;

    @Schema(description = "占比")
    private BigDecimal zb;

}
