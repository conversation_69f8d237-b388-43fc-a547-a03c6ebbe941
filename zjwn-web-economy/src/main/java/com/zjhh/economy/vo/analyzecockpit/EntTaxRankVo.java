package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业税收 榜单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntTaxRankVo extends BaseVo {
    private static final long serialVersionUID = -5627384601676412735L;


    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String entName;

    @Schema(description = "税收")
    private String taxIncome;

    @Schema(description = "占比")
    private String zb;


}
