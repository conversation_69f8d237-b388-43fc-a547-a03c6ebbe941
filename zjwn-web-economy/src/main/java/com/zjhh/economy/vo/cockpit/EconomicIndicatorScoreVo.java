package com.zjhh.economy.vo.cockpit;

import cn.hutool.core.util.StrUtil;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 经济指数评分
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EconomicIndicatorScoreVo extends BaseVo {
    private static final long serialVersionUID = -4301008858484985743L;

    @Schema(description = "评分")
    private BigDecimal score;

    @Schema(description = "评价时间")
    private String datekey;

    @Schema(description = "税收收入分数")
    private BigDecimal taxIncomeScore;

    @Schema(description = "入驻率分数")
    private BigDecimal settledRateScore;

    @Schema(description = "企业培育的分数")
    private BigDecimal entCultivateScore;

    @Schema(description = "税收增长率")
    private BigDecimal taxRateScore;

    @Schema(description = "单位产出")
    private BigDecimal unitOutputScore;

    @Schema(description = "特色产业规模分数")
    private BigDecimal industryScaleScore;

    public String getDatekey() {
        if (StrUtil.isNotBlank(datekey)) {
            return datekey.substring(0,4) + "-" + datekey.substring(4,6);
        }
        return datekey;
    }
}
