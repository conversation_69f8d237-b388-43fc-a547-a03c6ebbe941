package com.zjhh.economy.vo.report;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 报告详情
 *
 * <AUTHOR>
 * @date 2022-05-25 2:45 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningReportDetailVo extends BaseVo {
    private static final long serialVersionUID = -8191707286761835960L;

    @Schema(description = "0-发送,1-反馈,2-评价,3-结束")
    private Integer phase;

    @Schema(description = "报告信息")
    private WarningReportListVo warningReportVo;

    @Schema(description = "部门反馈")
    private List<WarningReportFeedbackVo> feedbackVos;

    @Schema(description = "评价")
    private List<WarningReportCommentVo> commentVos;

}
