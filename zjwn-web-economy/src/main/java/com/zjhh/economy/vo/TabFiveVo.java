package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端限上批零、住餐企业销售额(营业额)Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TabFiveVo extends BaseVo {


    private static final long serialVersionUID = -6593701505962371768L;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "企业名称")
    private String qymc;

    @Schema(description = "销售额-累计完成")
    private String xseLjwc;

    @Schema(description = "销售额-去年同期")
    private String xseSqAmt;

    @Schema(description = "销售额-增长率")
    private String xseZzl;

    @Schema(description = "税收收入-累计完成")
    private String sssrLjwc;

    @Schema(description = "税收收入-去年同期")
    private String sssrSqAmt;

    @Schema(description = "税收收入-增长率")
    private String sssrZzl;




}
