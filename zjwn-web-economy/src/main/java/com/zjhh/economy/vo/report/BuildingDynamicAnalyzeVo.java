package com.zjhh.economy.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingDynamicAnalyzeVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = -302249242369729097L;


    /* 基础信息 */
    @Schema(description = "序号")
    @ExcelProperty(index = 0,value = {"序号"})
    private String serialNo;

    @Schema(description = "楼宇名称")
    @ExcelProperty(index = 1,value = {"楼宇名称"})
    private String projectName;

    @Schema(description = "投入使用日期")
    @ExcelProperty(index = 2,value = {"投入使用日期"})
    private String operationTime;

    /* 面积信息 */
    @Schema(description = "楼宇面积(m²)")
    @ExcelProperty(index = 3,value = {"楼宇面积(m²)"})
    private BigDecimal businessArea;

    /* 入驻情况 */
    @Schema(description = "已用面积(m²)")
    @ExcelProperty(index = 4,value = {"入驻情况","已用面积(m²)"})
    private BigDecimal area;

    @Schema(description = "入驻率")
    @ExcelProperty(index = 5,value = {"入驻情况","入驻率"})
    private BigDecimal settledRate;

    @Schema(description = "入驻率同期数")
    @ExcelProperty(index = 6,value = {"入驻情况","同期数"})
    private BigDecimal settledRateSq;

    @Schema(description = "入驻率增幅")
    @ExcelProperty(index = 7,value = {"入驻情况","增幅"})
    private BigDecimal settledRateZf;

    /* 注册情况 */
    @Schema(description = "法人及产业/个体(家)")
    @ExcelProperty(index = 8,value = {"注册情况","法人及产业/个体(家)"})
    private String frcygt;

    @Schema(description = "注册数")
    @ExcelProperty(index = 9,value = {"注册情况","注册数"})
    private Integer zcs;

    @Schema(description = "注册率(%)")
    @ExcelProperty(index = 10,value = {"注册情况","注册率(%)"})
    private BigDecimal zcsRate;

    @Schema(description = "注册率同期数")
    @ExcelProperty(index = 11,value = {"注册情况","同期数"})
    private BigDecimal zcsRateSq;


    /* 财税情况 */
    @Schema(description = "税收(万元)")
    @ExcelProperty(index = 12,value = {"财税情况","税收(万元)"})
    private BigDecimal ss;

    @Schema(description = "税收同期数(万元)")
    @ExcelProperty(index = 13,value = {"财税情况","同期数(万元)"})
    private BigDecimal ssSq;

    @Schema(description = "税收增幅")
    @ExcelProperty(index = 14,value = {"财税情况","增幅"})
    private BigDecimal ssZf;

    @Schema(description = "单位财税(元/平米)")
    @ExcelProperty(index = 15,value = {"财税情况","单位财税(元/平米)"})
    private BigDecimal ssUnit;

    @Schema(description = "单位税收同期数")
    @ExcelProperty(index = 16,value = {"财税情况","同期数"})
    private BigDecimal ssUnitSq;

    @Schema(description = "单位税收增幅")
    @ExcelProperty(index = 17,value = {"财税情况","增幅"})
    private BigDecimal ssUnitZf;

    /* 除房地产财税情况 */
    @Schema(description = "除房地产税收(万元)")
    @ExcelProperty(index = 18,value = {"除房地产财税情况(万元)","除房地产税收(万元)"})
    private BigDecimal cfdcss;

    @Schema(description = "除房地产税收同期数(万元)")
    @ExcelProperty(index = 19,value = {"除房地产财税情况(万元)","同期数(万元)"})
    private BigDecimal cfdcssSq;

    @Schema(description = "除房地产税收增幅")
    @ExcelProperty(index = 20,value = {"除房地产财税情况(万元)","增幅"})
    private BigDecimal cfdcssZf;

    @Schema(description = "除房地产单位税收(元/平米)")
    @ExcelProperty(index = 21,value = {"除房地产财税情况(万元)","单位税收(元/平米)"})
    private BigDecimal cfdcssUnit;

    @Schema(description = "除房地产单位税收同期数")
    @ExcelProperty(index = 22,value = {"除房地产财税情况(万元)","同期数(万元)"})
    private BigDecimal cfdcssUnitSq;

    @Schema(description = "除房地产单位税收增幅")
    @ExcelProperty(index = 23,value = {"除房地产财税情况(万元)","增幅"})
    private BigDecimal cfdcssUnitZf;
}
