package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 面积分析Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaAnalyzeVo extends BaseVo {
    private static final long serialVersionUID = -8285502405786876798L;

    @Schema(description = "总商务面积")
    private BigDecimal totalBusinessArea;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "入驻率")
    private BigDecimal settledRate;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;


}
