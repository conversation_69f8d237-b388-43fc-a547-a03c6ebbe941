package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingRankVo extends BaseVo {
    private static final long serialVersionUID = 6637360156398144663L;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "金额")
    private BigDecimal amt;


}
