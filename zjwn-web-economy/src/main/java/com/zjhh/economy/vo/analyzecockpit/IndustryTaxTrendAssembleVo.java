package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.comm.vo.TreeSelectVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryTaxTrendAssembleVo extends BaseVo {

    @Schema(description = "行业")
    List<TreeSelectVo> industries;

    @Schema(description = "数据")
    List<IndustryTaxTrendVo> list;

}
