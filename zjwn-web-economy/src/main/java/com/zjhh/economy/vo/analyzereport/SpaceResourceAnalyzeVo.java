package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 空间资源综合态势分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpaceResourceAnalyzeVo extends BaseVo {
    private static final long serialVersionUID = -1903355537130400574L;


    @Schema(description = "展示名称")
    private String name;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "入驻率")
    private BigDecimal settledRate;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;


}
