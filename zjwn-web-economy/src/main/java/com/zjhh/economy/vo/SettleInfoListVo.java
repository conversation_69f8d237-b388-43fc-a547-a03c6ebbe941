package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 入驻信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SettleInfoListVo extends BaseVo {
    private static final long serialVersionUID = -2986602427604051948L;

    @Schema(description = "主键")
    @ExcelIgnore
    @NotBlank(message = "入驻信息不能为空")
    private String id;

    @Schema(description = "项目名称")
    @ExcelProperty(index = 0, value = "项目")
    private String projectName;

    @Schema(description = "楼宇名称")
    @ExcelProperty(index = 1, value = "楼宇")
    private String buildingName;

    @Schema(description = "楼层")
    @ExcelProperty(index = 2, value = "楼层")
    private String floorName;

    @Schema(description = "房间号")
    @ExcelProperty(index = 3, value = "房号")
    private String roomNo;

    @Schema(description = "开始入驻日期")
    @ExcelProperty(index = 4, value = "开始入驻日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkInDate;

    @Schema(description = "预计搬离日期")
    @ExcelProperty(index = 5, value = "预计搬离日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectMoveOutDate;

    @Schema(description = "实际搬离日期")
    @ExcelProperty(index = 6, value = "实际搬离日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate realityMoveOutDate;

    @Schema(description = "入驻面积")
    @ExcelProperty(index = 7, value = "入驻面积（㎡）")
    private BigDecimal area;

    @Schema(description = "备注")
    @ExcelProperty(index = 8, value = "备注")
    private String remark;

    @Schema(description = "文件ID")
    @ExcelIgnore
    private String documentId;

    @Schema(description = "是否搬离")
    @ExcelIgnore
    private Boolean moved;

    @ExcelIgnore
    @Schema(description = "楼层id")
    private String floorId;

    @ExcelIgnore
    @Schema(description = "楼层平面图id")
    private String planeImgId;

    @ExcelIgnore
    @Schema(description = "楼层平面图配置id")
    private String planeConfigId;

}
