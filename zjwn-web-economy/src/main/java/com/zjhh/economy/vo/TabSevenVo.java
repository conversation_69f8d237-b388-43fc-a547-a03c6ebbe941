package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端纳税50万元以上企业Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TabSevenVo extends BaseVo {


    private static final long serialVersionUID = -6593701505962371768L;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "企业名称")
    private String qymc;

    @Schema(description = "财政总收入")
    private String czzsr;

    @Schema(description = "一般公共预算收入")
    private String ybggyssr;

}
