package com.zjhh.economy.vo.analyzereport;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 房源空置天数对比分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyRoomCompareVo extends BaseVo {
    private static final long serialVersionUID = 5576716867382691128L;

    @Schema(description = "显示名称")
    private String name;

    @Schema(description = "空置房源总数")
    private Integer totalCount;

    @Schema(description = "0-30天房间数")
    private Integer zeroCount;

    @Schema(description = "0-30天占比")
    private BigDecimal zeroZb;

    @Schema(description = "30-60天房间数")
    private Integer thirtyCount;

    @Schema(description = "30-60天占比")
    private BigDecimal thirtyZb;

    @Schema(description = "60-120天房间数")
    private Integer sixtyCount;

    @Schema(description = "60-120天占比")
    private BigDecimal sixtyZb;

    @Schema(description = "120-240天房间数")
    private Integer oneHundredTwentyCount;

    @Schema(description = "120-240天占比")
    private BigDecimal oneHundredTwentyZb;

    @Schema(description = "240-360天房间数")
    private Integer twoHundredFortyCount;

    @Schema(description = "240-360天占比")
    private BigDecimal twoHundredFortyZb;

    @Schema(description = "360天以上房间数")
    private Integer threeHundredSixtyCount;

    @Schema(description = "360天以上占比")
    private BigDecimal threeHundredSixtyZb;


}
