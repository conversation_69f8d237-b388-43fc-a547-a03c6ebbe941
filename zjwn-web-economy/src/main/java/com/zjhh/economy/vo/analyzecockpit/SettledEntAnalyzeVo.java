package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettledEntAnalyzeVo extends BaseVo {
    private static final long serialVersionUID = -649595102709249916L;

    @Schema(description = "总数")
    private Integer totalCount;

    @Schema(description = "注册数")
    private Integer registerCount;

    @Schema(description = "注册率")
    private BigDecimal registerRate;

}
