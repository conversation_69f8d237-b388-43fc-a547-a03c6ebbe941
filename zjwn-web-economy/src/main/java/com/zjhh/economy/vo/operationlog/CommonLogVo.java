package com.zjhh.economy.vo.operationlog;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommonLogVo extends BaseVo {
    private static final long serialVersionUID = -4177947804206302880L;

    @Schema(description = "数据")
    private TreeMap<String, Object> dataMap;

    @Schema(description = "字段颜色。0-无颜色标记，1-编辑，2-新增，3-删除")
    private TreeMap<String, Integer> contrastMap;

    public CommonLogVo() {
    }

    public CommonLogVo(TreeMap<String, Object> dataMap, TreeMap<String, Integer> contrastMap) {
        this.dataMap = dataMap;
        this.contrastMap = contrastMap;
    }
}
