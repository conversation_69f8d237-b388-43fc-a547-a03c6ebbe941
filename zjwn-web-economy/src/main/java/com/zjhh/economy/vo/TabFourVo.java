package com.zjhh.economy.vo;


import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端规上服务业主要经济指标完成情况Vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TabFourVo extends BaseVo {


    private static final long serialVersionUID = 7100842943040597012L;
    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "企业名称")
    private String qymc;

    @Schema(description = "营业收入-累计完成")
    private String yysrLjwc;

    @Schema(description = "营业收入-去年同期")
    private String yysrSqAmt;

    @Schema(description = "营业收入-增长率")
    private String yysrZzl;

    @Schema(description = "税收收入-本月")
    private String sssrLjwc;

    @Schema(description = "税收收入-去年同期")
    private String sssrSqAmt;

    @Schema(description = "税收收入-增长率")
    private String sssrZzl;




}
