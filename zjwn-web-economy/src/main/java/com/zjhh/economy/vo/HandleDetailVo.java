package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class HandleDetailVo extends BaseVo {
    private static final long serialVersionUID = -2986398852784321945L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "处理方式")
    private String handleType;

    @Schema(description = "处理说明")
    private String handleDesc;

    @Schema(description = "处理时间")
    private LocalDate createTime;

}
