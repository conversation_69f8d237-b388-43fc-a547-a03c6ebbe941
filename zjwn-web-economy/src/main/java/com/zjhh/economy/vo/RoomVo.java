package com.zjhh.economy.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.converter.FloorNoConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/3/8 11:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomVo extends BaseVo {

    private static final long serialVersionUID = 5691741751582342226L;

    @Schema(description = "房间id")
    @ExcelIgnore
    private String roomId;

    @Schema(description = "房号")
    @ExcelProperty(index = 0, value = "房号")
    private String roomNo;

    @Schema(description = "楼宇id")
    @ExcelIgnore
    private String buildingId;

    @Schema(description = "楼宇名称")
    @ExcelProperty(index = 2, value = "楼宇名称")
    private String buildingName;

    @Schema(description = "社区名称")
    @ExcelProperty(index = 3, value = "社区名称")
    private String communityName;

    @Schema(description = "项目名称")
    @ExcelProperty(index = 4, value = "项目名称")
    private String projectName;

    @Schema(description = "楼层")
    @ExcelProperty(index = 5, value = "楼层", converter = FloorNoConverter.class)
    private Integer floorNo;

    @Schema(description = "商务面积")
    @ExcelProperty(index = 6, value = "商务面积（㎡）")
    private BigDecimal businessArea;

    @Schema(description = "企业名称")
    @ExcelProperty(index = 7, value = "入驻企业")
    private String enterpriseName;

    @Schema(description = "房间状态：0-空置 1-已入驻 2-不可出租 3-新入驻装修中")
    @ExcelIgnore
    private Integer roomStatus;

    @Schema(hidden = true)
    @ExcelProperty(index = 1, value = "房间状态")
    private String roomStatusName;

    @ExcelIgnore
    @Schema(description = "楼层id")
    private String floorId;

    @ExcelIgnore
    @Schema(description = "楼层平面图id")
    private String planeImgId;

    @ExcelIgnore
    @Schema(description = "楼层平面图配置id")
    private String planeConfigId;

    public String getRoomStatusName() {
        switch (roomStatus) {
            case 1:
                return "已入驻";
            case 2:
                return "不可出租";
            case 3:
                return "新入驻装修中";
            case 0:
            default:
                return "空置";
        }
    }
}
