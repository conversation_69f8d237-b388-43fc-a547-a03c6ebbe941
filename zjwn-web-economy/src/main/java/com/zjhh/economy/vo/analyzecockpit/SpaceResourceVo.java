package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 空间资源分析
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpaceResourceVo extends BaseVo {
    private static final long serialVersionUID = -5374254215526782687L;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "入驻率")
    private BigDecimal settledRate;

    @Schema(description = "空置率")
    private BigDecimal emptyRate;

}
