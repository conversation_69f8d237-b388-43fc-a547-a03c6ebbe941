package com.zjhh.economy.vo.analyzereport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 房源空置明细
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyRoomDetailTableVo extends BaseVo {
    private static final long serialVersionUID = -328725337456776915L;

    @Schema(description = "显示名称")
    private String name;

    @Schema(description = "code")
    private String code;

    @Schema(description = "总房源数")
    private Integer totalCount;

    @Schema(description = "0-30天房间数")
    private Integer zeroCount;

    @Schema(description = "30-60天房间数")
    private Integer thirtyCount;

    @Schema(description = "60-120天房间数")
    private Integer sixtyCount;

    @Schema(description = "120-240天房间数")
    private Integer oneHundredTwentyCount;

    @Schema(description = "240-360天房间数")
    private Integer twoHundredFortyCount;

    @Schema(description = "360天以上房间数")
    private Integer threeHundredSixtyCount;

    private Integer compareType;

    private List<EmptyRoomDetailTableVo> children;

    private Boolean hasChildren;

    @JsonIgnore
    private String currentDate;


}
