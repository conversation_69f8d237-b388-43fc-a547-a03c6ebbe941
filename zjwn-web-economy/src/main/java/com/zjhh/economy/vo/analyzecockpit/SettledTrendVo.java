package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 入驻趋势
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SettledTrendVo extends BaseVo {
    private static final long serialVersionUID = -6079999388380266380L;

    @Schema(description = "日期")
    private String datekey;

    @Schema(description = "入驻面积")
    private BigDecimal settledArea;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "入驻率")
    private BigDecimal settledRate;

    public String getDatekey() {
        String quarter = "";
        if (datekey.length() == 5 && !datekey.contains("-")) {
            quarter = datekey.substring(4,5);
        }
        if (quarter.equals("1") && datekey.length() == 5) {
            return datekey.substring(0,4) + "年" + "第一季度";
        }else if (quarter.equals("2")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第二季度";
        } else if (quarter.equals("3")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第三季度";

        }else if (quarter.equals("4")  && datekey.length() == 5) {
            return datekey.substring(0,4) + "年"+"第四季度";
        }else {
            return datekey;
        }
    }
}
