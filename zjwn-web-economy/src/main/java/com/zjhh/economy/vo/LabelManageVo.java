package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.request.LabelReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class LabelManageVo extends BaseVo {
    private static final long serialVersionUID = 6761067361125227100L;

    @Schema(description = "标签")
    List<LabelReq> labels;
}
