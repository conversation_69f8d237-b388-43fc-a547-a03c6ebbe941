package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.request.ProjectImageVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 大屏项目总览
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CockpitProjectSummaryVo extends BaseVo {
    private static final long serialVersionUID = 3237999050550981164L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "项目地址")
    private String projectAddress;

    @Schema(description = "占地面积")
    private BigDecimal totalArea;

    @Schema(description = "物业公司")
    private String manageCompany;

    @Schema(description = "项目简介")
    private String projectIntro;

    @Schema(description = "项目照片")
    private List<ProjectImageVo> projectImages;

}
