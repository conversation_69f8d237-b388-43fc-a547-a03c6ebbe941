package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/3/8 10:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomStateRoomVo extends BaseVo {

    @Serial
    private static final long serialVersionUID = 6433688225444278639L;

    @Schema(description = "房间id")
    private String roomId;

    @Schema(description = "房间编号")
    private String roomNo;

    @Schema(description = "房间名称")
    private String roomName;

    @Schema(description = "是否开放出租")
    private Boolean openHired;

    @Schema(description = "房间状态：0-空置 1-已入驻 2-不可出租 3-新入驻装修中")
    private Integer roomStatusCode;

    @Schema(description = "房间状态名称")
    private String roomStatusName;

    @Schema(description = "商务面积")
    private BigDecimal businessArea;

    @Schema(description = "企业数量")
    private Integer enterpriseSize;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "装修中企业数量")
    private Integer renovationSize;

    public String getRoomName() {
        return roomNo + "室";
    }

    public Integer getRoomStatusCode() {
        if (!openHired) {
            return 2;
        } else if (renovationSize > 0) {
            return 3;
        } else if (enterpriseSize > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public String getRoomStatusName() {
        if (!openHired) {
            return "不可出租";
        } else if (renovationSize > 0) {
            return "新入驻装修中";
        } else if (enterpriseSize > 0) {
            return "已入驻";
        } else {
            return "空置";
        }
    }
}
