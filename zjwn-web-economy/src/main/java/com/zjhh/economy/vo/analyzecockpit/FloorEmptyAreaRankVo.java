package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class FloorEmptyAreaRankVo extends BaseVo {
    private static final long serialVersionUID = 3158205787133841046L;

    @Schema(description = "楼层")
    private String floorName;

    @Schema(description = "空置面积")
    private BigDecimal emptyArea;

    @Schema(description = "空置房间数")
    private Integer emptyCount;

}
