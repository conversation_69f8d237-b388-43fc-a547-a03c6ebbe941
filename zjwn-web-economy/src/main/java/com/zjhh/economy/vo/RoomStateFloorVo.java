package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/8 10:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomStateFloorVo extends BaseVo {

    private static final long serialVersionUID = 3746240596886608815L;

    @Schema(description = "楼层id")
    private String floorId;

    @Schema(description = "楼层名称")
    private String floorName;

    @Schema(description = "楼层面积")
    private BigDecimal businessArea;

    @Schema(description = "房间数")
    private Integer roomSize;

    @Schema(description = "房间列表")
    private List<RoomStateRoomVo> roomList;

    @Schema(description = "楼层平面图id")
    private String planeImgId;

    @Schema(description = "楼层平面图配置id")
    private String planeConfigId;
}
