package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/11 10:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentVo extends BaseVo {

    private static final long serialVersionUID = -7969983642681049124L;

    @Schema(description = "文件id")
    private String fileId;

}
