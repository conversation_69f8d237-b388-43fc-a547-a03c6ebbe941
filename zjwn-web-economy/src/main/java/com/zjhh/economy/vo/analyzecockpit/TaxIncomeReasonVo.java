package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxIncomeReasonVo extends BaseVo {
    private static final long serialVersionUID = -3806241665033485629L;

    @Schema(description = "税收收入")
    private BigDecimal taxIncome;

    @Schema(description = "增减额")
    private BigDecimal addTaxIncome;

    @Schema(description = "增幅")
    private BigDecimal zf;

    @Schema(description = "企业的税收引起的金额")
    private BigDecimal entTaxReasonAmt;

    @Schema(description = "注册/迁出引起的金额")
    private BigDecimal registerReasonAmt;

}
