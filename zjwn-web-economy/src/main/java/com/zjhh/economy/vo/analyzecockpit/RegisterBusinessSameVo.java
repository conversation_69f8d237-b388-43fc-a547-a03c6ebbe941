package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 注册地址和经营地址一致VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RegisterBusinessSameVo extends BaseVo {
    private static final long serialVersionUID = 1993579211342462075L;

    @Schema(description = "分布名称")
    private String disName;

    @Schema(description = "分布数量")
    private String entCount;

    @Schema(description = "占比")
    private String zb;
}
