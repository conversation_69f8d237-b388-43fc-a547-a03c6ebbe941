package com.zjhh.economy.vo.analyzecockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业入驻分析汇总
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntSettledSummaryVo extends BaseVo {
    private static final long serialVersionUID = 9093178677668621155L;

    @Schema(description = "入驻总数")
    private String settledTotalCount;

    @Schema(description = "入驻总数环比")
    private String settledTotalCountHb;

    @Schema(description = "入驻总数同比")
    private String settledTotalCountTb;

    @Schema(description = "注册数")
    private String registerCount;

    @Schema(description = "注册数环比")
    private String registerCountHb;

    @Schema(description = "注册数同比")
    private String registerCountTb;

    @Schema(description = "注册率")
    private String registerRate;

    @Schema(description = "注册率环比")
    private String registerRateHb;

    @Schema(description = "注册率同比")
    private String registerRateTb;

}
