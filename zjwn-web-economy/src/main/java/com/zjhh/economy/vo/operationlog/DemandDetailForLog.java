package com.zjhh.economy.vo.operationlog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DemandDetailForLog extends BaseVo {
    private static final long serialVersionUID = 617088645949748800L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "诉求描述")
    private String demandDesc;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "提交时间")
    private LocalDate submitDate;

    @Schema(description = "提交途径")
    private String submitSourceName;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "所属楼宇")
    private String buildingName;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "诉求类型")
    private String demandTypeName;

    @Schema(description = "照片")
    private List<String> photos;
}
