package com.zjhh.economy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import com.zjhh.economy.enume.ImportTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class ImportExternalDataVo extends BaseVo {
    private static final long serialVersionUID = 7931227655580701617L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "序号")
    private Integer xh;

    @Schema(description = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime uploadDate;

    @Schema(description = "上传类型")
    private String uploadType;

    @Schema(description = "上传类型转义")
    private String uploadTypeStr;

    @Schema(description = "上传状态")
    private Integer uploadStatus;

    @Schema(description = "上传状态转义")
    private String uploadStatusStr;


    public String getUploadTypeStr() {
        return ImportTypeEnum.getNameByCode(uploadType);
    }

    public String getUploadStatusStr() {
        return uploadStatus == 0 ? "失败" : "成功";
    }
}
