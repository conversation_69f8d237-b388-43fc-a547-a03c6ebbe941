package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoveRegisterDetailVo extends BaseVo {
    private static final long serialVersionUID = 1594934470302900486L;

    @Schema(description = "入驻id")
    private String settledId;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "搬离原因")
    private String moveReason;

    @Schema(description = "迁出原因")
    private List<MoveRegisterLabelVo> labels;

    @Schema(description = "实际搬离时间")
    private LocalDate realMoveDate;


}
