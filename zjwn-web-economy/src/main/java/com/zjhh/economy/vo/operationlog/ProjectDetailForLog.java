package com.zjhh.economy.vo.operationlog;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectDetailForLog extends BaseVo {
    private static final long serialVersionUID = -5186916850461529631L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "项目坐落")
    private String address;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String dimension;

    @Schema(description = "项目占地面积（㎡）")
    private BigDecimal projectArea;

    @Schema(description = "项目类型")
    private String projectTypeName;

    @Schema(description = "物业公司")
    private String manageCompany;

    @Schema(description = "物业负责人")
    private String contacts;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "项目简介")
    private String projectIntro;

    @Schema(description = "项目图片")
    private List<String> projectImages;
}
