package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.comm.vo.SingleSelectVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/12 19:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingDetailVo extends BaseVo {

    private static final long serialVersionUID = -9183251317898472481L;

    @Schema(description = "楼宇id")
    private String buildingId;

    @Schema(description = "楼宇编号")
    private String serialNo;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "所属社区编码")
    private String communityCode;

    @Schema(description = "所属社区")
    private String communityName;

    @Schema(description = "所属项目Id")
    private String projectId;

    @Schema(description = "所属项目")
    private String projectName;

    @Schema(description = "坐落位置")
    private String address;

    @Schema(description = "建筑面积")
    private BigDecimal buildingArea;

    @Schema(description = "商务面积")
    private BigDecimal businessArea;

    @Schema(description = "楼宇投入运行时间")
    private LocalDate operationTime;

    @Schema(description = "楼宇状态编码")
    private String buildingStatusCode;

    @Schema(description = "楼宇状态")
    private String buildingStatusName;

    @Schema(description = "楼宇编码")
    private String buildingTypeCode;

    @Schema(description = "楼宇类型")
    private String buildingTypeName;

    @Schema(description = "负责人")
    private String head;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "楼宇简介")
    private String introduce;

    @Schema(description = "楼宇外观图")
    private String outsideImgId;

    @Schema(description = "楼宇其他图片")
    private String otherImgId;

    @Schema(description = "楼宇标签")
    private List<SingleSelectVo> labelNames;

    @Schema(description = "楼宇定位")
    private List<SingleSelectVo> positionNames;

    @Schema(description = "地上停车位")
    private Integer landParkSpace;

    @Schema(description = "地下停车位")
    private Integer undergroundParkSpace;

    @Schema(description = "楼宇配套")
    private List<SingleSelectVo> configNames;

    @Schema(description = "备注")
    private String remark;
}
