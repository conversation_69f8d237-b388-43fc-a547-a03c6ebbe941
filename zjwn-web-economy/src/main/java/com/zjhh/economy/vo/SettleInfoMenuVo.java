package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettleInfoMenuVo extends BaseVo {
    private static final long serialVersionUID = -2539501904197967346L;

    @Schema(description = "入驻信息")
    private String settleInfo;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "入驻面积")
    private BigDecimal area;

    @Schema(description = "开始入驻日期")
    private LocalDate checkInDate;

    @Schema(description = "预计搬离日期")
    private LocalDate expectMoveOutDate;
}
