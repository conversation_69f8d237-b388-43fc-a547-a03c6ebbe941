package com.zjhh.economy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class DemandHandleVo extends BaseVo {

    private static final long serialVersionUID = -6572431481761679150L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "诉求ID")
    private String demandId;

    @Schema(description = "处理结果")
    private String handleTypeStr;

    @Schema(description = "处理描述")
    private String handleDesc;

    @Schema(description = "附件ID")
    private String documentId;

    @Schema(description = "附件名称")
    private String documentName;

    @Schema(description = "操作人")
    private String userName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime createTime;

    private String fileSize;

    @Schema(hidden = true)
    private long size;

    public String getFileSize() {
        BigDecimal fileSize = BigDecimal.valueOf(size);
        BigDecimal tmp = BigDecimal.valueOf(1024);
        if (fileSize.compareTo(tmp) > 0) {
            fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
            if (fileSize.compareTo(tmp) > 0) {
                fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                if (fileSize.compareTo(tmp) > 0) {
                    fileSize = fileSize.divide(tmp, 1, RoundingMode.HALF_UP);
                    return fileSize + "GB";
                }
                return fileSize + "MB";
            }
            return fileSize + "KB";
        }
        return fileSize + "B";
    }


}
