package com.zjhh.economy.vo.cockpit;

import com.zjhh.comm.vo.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 已投入使用建筑
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildingMapInfoUsedVo extends BaseVo {
    private static final long serialVersionUID = -6362417281223615254L;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目面积")
    private String buildingArea;

    @Schema(description = "已使用面积")
    private String usedArea;

    @Schema(description = "入驻率")
    private String settleRate;

    @Schema(description = "注册数")
    private String registerCount;

    @Schema(description = "注册率")
    private String registerRate;

    @Schema(description = "税收")
    private String taxIncome;

    @Schema(description = "单位税收")
    private String unitTaxIncome;

    @Schema(description = "除房地产税收")
    private String taxWithout;

    @Schema(description = "除房地产单位税收")
    private String unitTaxWithout;

    @Schema(description = "经度")
    private String latitude;

    @Schema(description = "纬度")
    private String longitude;

    @Schema(description = "x轴坐标")
    private String x;

    @Schema(description = "y轴坐标")
    private String y;

    @Schema(description = "显示位置")
    private String showPosition;

    @Schema(description = "建筑数量")
    private String buildingCount;



}
