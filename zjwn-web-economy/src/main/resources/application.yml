server:
    servlet:
        context-path: /building
spring:
    profiles:
        active: dev
    servlet:
        multipart:
            enabled: true
            file-size-threshold: 2KB
            max-file-size: 100MB
            max-request-size: 100MB
    jackson:
        default-property-inclusion: non_null
    mvc:
        static-path-pattern: /**
    web:
        resources:
            static-locations: classpath:/static/,classpath:/public/
    datasource:
        druid:
            stat-view-servlet:
                enabled: false
                allow:
            web-stat-filter:
                enabled: true
        dynamic:
            druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
                # 连接池的配置信息
                # 初始化大小，最小，最大
                initial-size: 5
                min-idle: 5
                maxActive: 20
                # 配置获取连接等待超时的时间
                maxWait: 60000
                # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                timeBetweenEvictionRunsMillis: 60000
                # 配置一个连接在池中最小生存的时间，单位是毫秒
                minEvictableIdleTimeMillis: 300000
                validationQuery: SELECT version()
                testWhileIdle: true
                testOnBorrow: false
                testOnReturn: false
                # 打开PSCache，并且指定每个连接上PSCache的大小
                poolPreparedStatements: true
                maxPoolPreparedStatementPerConnectionSize: 20
                # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
                filters: stat,slf4j
    data:
        redis:
            database: 0
            lettuce:
                pool:
                    max-wait: -1
                    max-idle: 8
                    min-idle: 0
    ai:
        model:
            chat: none
            embedding: none
            image: none
            audio:
                transcription: none
                speech: none
            moderation: none

sa-token:
    # token名称 (同时也是cookie名称)
    token-name: token
    # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
    is-concurrent: true
    # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
    is-share: false
    # token风格
    token-style: simple-uuid
    # 是否输出操作日志
    is-log: true

#mybatis plus 设置
mybatis-plus:
    mapper-locations: classpath*:/mapper/*Mapper.xml
    global-config:
        # 关闭MP3.0自带的banner
        banner: false
        db-config:
            #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
            id-type: ASSIGN_ID
            # 默认数据库表下划线命名
            table-underline: true
    configuration:
        # 返回类型为Map,显示null对应的字段
        call-setters-on-nulls: true
        map-underscore-to-camel-case: true
    type-handlers-package: com.zjhh.web.db.typehandler

springdoc:
    # get请求多参数时不需要添加额外的@ParameterObject和@Parameter注解
    default-flat-param-object: true
    swagger-ui:
        #自定义swagger前端请求路径，输入http：127.0.0.1:8080/swagger-ui.html会自动重定向到swagger页面
        path: /swagger-ui.html
        # 启用文档，默认开启
        enabled: true
        #    tags-sorter: alpha # 标签的排序方式 alpha:按照子母顺序排序（@ApiSupport注解排序不生效，因此需要设置）
        #    operations-sorter: alpha # 接口的排序方式 alpha:按照子母顺序排序（@ApiOperationSupport注解排序生效，因此这里不作设置）
        operations-sorter: order # 设置规则为order，该规则会使用Knife4j的增强排序扩展规则`x-order`
    api-docs:
        path: /v3/api-docs    #swagger后端请求地址
        enabled: true

# knife4j相关配置 可以不用改
knife4j:
    enable: false    #关闭knife4j增强功能，以解决与Spring Boot 3.4.3的兼容性问题
    setting:
        language: ZH_CN   # 中文:ZH_CN 英文:EN
        enable-swagger-models: true
        enable-dynamic-parameter: false
        footer-custom-content: "<strong>Copyright ©️ 2024 yejun. All Rights Reversed</strong>"
        enable-footer-custom: true
        enable-footer: true
        enable-document-manage: true
    documents: #文档补充说明
        -   name: MarkDown语法说明
            locations: classpath:static/markdown/grammar/*
            group: 01-系统接口 # 此处分组必须使用在Knife4jConfig已存在的分组名group，当存在displayName时，使用displayName名称
        -   name: 补充文档
            locations: classpath:static/markdown/others/*
            group: 01-系统接口 # 此处分组必须使用在Knife4jConfig已存在的分组名group，当存在displayName时，使用displayName名称


jetcache:
    statIntervalMinutes: 0
    areaInCacheName: false
    local:
        default:
            type: caffeine
            keyConvertor: fastjson
    remote:
        default:
            type: redis.lettuce
            keyConvertor: fastjson
            valueEncoder: kryo
            valueDecoder: kryo
flowable:
    triggerable: false
    process:
        definition-cache-limit: -1
    xml:
        encoding: UTF-8
    check-process-definitions: false
    database-schema-update: true
    history-level: full
    activityFontName: \u5B8B\u4F53
    labelFontName: \u5B8B\u4F53
    annotationFontName: \u5B8B\u4F53
    db-history-used: true
    async-executor-activate: false

jasypt:
    encryptor:
        algorithm: PBEWithMD5AndDES
        iv-generator-classname: org.jasypt.iv.NoIvGenerator

system:
    system: pc