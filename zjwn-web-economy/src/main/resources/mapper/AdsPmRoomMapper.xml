<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmRoomMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , floorId, room_no, building_area, business_area, open_hired, renovation_code, room_type_code, rend_unit_price, rend_unit, property_fees_unit_price, property_fees_unit, water_fees_unit_price, electricity_fees_unit_price, house_orientation, ownership_code, property_certificate_code, create_user, create_time, update_time
    </sql>

    <resultMap id="RoomStateFloor" type="com.zjhh.economy.vo.RoomStateFloorVo">
        <id column="floor_id" property="floorId"/>
        <result column="floor_name" property="floorName"/>
        <result column="business_area" property="businessArea"/>
        <result column="plane_img_id" property="planeImgId"/>
        <result column="plane_config_id" property="planeConfigId"/>
        <collection property="roomList" ofType="com.zjhh.economy.vo.RoomStateRoomVo">
            <id column="room_id" property="roomId"/>
            <result column="room_no" property="roomNo"/>
            <result column="open_hired" property="openHired"/>
            <result column="business_area" property="businessArea"/>
            <result column="enterprise_size" property="enterpriseSize"/>
            <result column="enterprise_name" property="enterpriseName"/>
            <result column="renovation_size" property="renovationSize"/>
        </collection>
    </resultMap>

    <select id="listFloor" resultMap="RoomStateFloor">
        select t1.id                   as floor_id,
               t1.floor_name,
               t2.id                   as room_id,
               t2.room_no,
               t2.business_area,
               t2.open_hired,
               t1.floor_no,
               t1.plane_img_id,
               t1.plane_config_id,
               count(t3.enterprise_id) as enterprise_size,
               max(t4.enterprise_name) as enterprise_name,
               coalesce((select count(t.id)
                         from ads_pm_room_enterprise t
                         where t.room_id = t2.id
                           and t.moved = false
                           and t.renovation_start_date &lt;= CURRENT_DATE
                           and t.renovation_end_date >= CURRENT_DATE), 0)
                                       as renovation_size
        from ads_pm_floor t1
                 left join ads_pm_room t2 on t1.id = t2.floor_id
                 left join ads_pm_room_enterprise t3 on t3.room_id = t2.id and t3.moved = false
                 left join ads_pm_enterprise t4 on t3.enterprise_id = t4.id
        where t1.building_id = #{buildingId}
        group by t1.id,
                 t1.floor_name,
                 t2.id,
                 t2.room_no,
                 t2.business_area,
                 t1.floor_no,
                 t2.open_hired, t1.plane_img_id,
                 t1.plane_config_id
        order by t1.floor_no,t2.room_no;
    </select>

    <select id="page" resultType="com.zjhh.economy.vo.RoomVo">
        WITH room_enterprise_status AS (
        SELECT
        apre.room_id,
        coalesce(string_agg(ape.enterprise_name, '；' order by apre.check_in_date desc,apre.id desc), '') as enterpriseName,
        COUNT(*) as enterprise_count,
        SUM(CASE
        WHEN current_date BETWEEN renovation_start_date AND renovation_end_date
        THEN 1
        ELSE 0
        END) as renovation_count
        FROM ads_pm_room_enterprise apre
        left join ads_pm_enterprise ape on apre.enterprise_id = ape.id
        WHERE apre.moved = false
        GROUP BY apre.room_id
        ),
        floor_areas AS (
        SELECT
        t.floor_id
        FROM ads_pm_room t
        LEFT JOIN room_enterprise_status res ON res.room_id = t.id
        WHERE t.open_hired = true
        AND (res.enterprise_count IS NULL OR res.enterprise_count &lt;= 0)
        GROUP BY t.floor_id
        HAVING
        <choose>
            <when test="req.minEmptyArea != null and req.maxEmptyArea != null">
                sum(t.business_area) >= #{req.minEmptyArea}::numeric
                AND sum(t.business_area) &lt; #{req.maxEmptyArea}::numeric
            </when>
            <when test="req.minEmptyArea != null">
                sum(t.business_area) >= #{req.minEmptyArea}::numeric
            </when>
            <when test="req.maxEmptyArea != null">
                sum(t.business_area) &lt; #{req.maxEmptyArea}::numeric
            </when>
            <otherwise>
                1=1
            </otherwise>
        </choose>
        )
        SELECT
        t1.id as room_id,
        t1.room_no,
        t3.id as building_id,
        t3.building_name,
        t5.name as communityName,
        t4.project_name,
        t2.floor_no,
        t2.plane_img_id,
        t2.plane_config_id,
        t1.business_area,
        res.enterpriseName,
        CASE
        WHEN t1.open_hired = false THEN 2
        WHEN COALESCE(res.renovation_count, 0) > 0 THEN 3
        WHEN COALESCE(res.enterprise_count, 0) > 0 THEN 1
        ELSE 0
        END as room_status
        FROM ads_pm_room t1
        LEFT JOIN ads_pm_floor t2 ON t1.floor_id = t2.id
        inner JOIN v_p_building t3 ON t2.building_id = t3.id
        LEFT JOIN ads_pm_project t4 ON t3.project_id = t4.id
        LEFT JOIN dm_pm t5 ON t4.community_code = t5.code AND t5.type = 'Community'
        LEFT JOIN room_enterprise_status res ON res.room_id = t1.id
        <where>
            <if test="req.buildingName != null and req.buildingName != ''">
                t3.building_name like concat('%', #{req.buildingName}, '%')
            </if>
            <if test="req.projectId != null and req.projectId != ''">
                AND t4.id = #{req.projectId}
            </if>
            <if test="req.communityCode != null and req.communityCode != ''">
                AND t5.code = #{req.communityCode}
            </if>
            <if test="req.roomNo != null and req.roomNo != ''">
                AND t1.room_no like concat('%', #{req.roomNo}, '%')
            </if>
            <if test="req.ownershipCode != null and req.ownershipCode != ''">
                AND t1.ownership_code = #{req.ownershipCode}
            </if>
            <if test="req.enterpriseName != null and req.enterpriseName != ''">
                AND EXISTS (
                SELECT 1
                FROM ads_pm_room_enterprise t
                LEFT JOIN ads_pm_enterprise tt ON t.enterprise_id = tt.id
                WHERE t.room_id = t1.id
                AND tt.enterprise_name LIKE concat('%', #{req.enterpriseName}, '%')
                )
            </if>
            <if test="req.roomStatus != null">
                AND (
                CASE
                WHEN t1.open_hired = false THEN 2
                WHEN COALESCE(res.renovation_count, 0) > 0 THEN 3
                WHEN COALESCE(res.enterprise_count, 0) > 0 THEN 1
                ELSE 0
                END
                ) = #{req.roomStatus}
            </if>
            <if test="req.minEmptyArea != null or req.maxEmptyArea != null">
                AND t1.open_hired = true
                AND t1.floor_id IN (SELECT floor_id FROM floor_areas)
                AND (res.enterprise_count IS NULL OR res.enterprise_count &lt;= 0)
            </if>
            <if test="req.minBusinessArea != null and req.maxBusinessArea != null">
                AND t1.business_area BETWEEN #{req.minBusinessArea}::numeric AND #{req.maxBusinessArea}::numeric
            </if>
            <if test="req.minBusinessArea != null and req.maxBusinessArea == null">
                AND t1.business_area >= #{req.minBusinessArea}::numeric
            </if>
            <if test="req.maxBusinessArea != null and req.minBusinessArea == null">
                AND t1.business_area &lt;= #{req.maxBusinessArea}::numeric
            </if>
        </where>
        ORDER BY t3.serial_no, t2.floor_no, t1.room_no
    </select>

    <select id="getAddRoom" resultType="com.zjhh.economy.vo.AddRoomVo">
        select t2.project_id, t1.building_id, t1.id as floor_id
        from ads_pm_floor t1
                 left join ads_pm_building t2 on t1.building_id = t2.id
        where t1.id = #{floorId}
    </select>

    <select id="getDetail" resultType="com.zjhh.economy.vo.RoomDetailVo">
        select t1.id                                                             as room_id,
               t3.id as buildingId,
               t3.project_id as projectId,
               t2.id as floorId,
               concat(t3.building_name, '-', t2.floor_no, '-', t1.room_no, '室') as room_name,
               t1.open_hired,
               t1.business_area,
               t1.building_area,
               t4.name                                                           as renovation_name,
               t5.name                                                           as room_type_name,
               t6.name                                                           as ownership_name,
               t7.name                                                           as property_certificate_name,
               t1.remark
        from ads_pm_room t1
                 left join ads_pm_floor t2 on t1.floor_id = t2.id
                 left join ads_pm_building t3 on t2.building_id = t3.id
                 left join dm_pm t4 on t4.code = t1.renovation_code and t4.type = 'Renovation'
                 left join dm_pm t5 on t5.code = t1.room_type_code and t5.type = 'RoomType'
                 left join dm_pm t6 on t6.code = t1.ownership_code and t6.type = 'Ownership'
                 left join dm_pm t7 on t7.code = t1.property_certificate_code and t7.type = 'PropertyCertificate'
        where t1.id = #{roomId}
    </select>

    <select id="listRoomLabel" resultType="java.lang.String">
        select t2.name
        from ads_pm_room_label t1
                 left join dm_pm t2 on t1.label_code = t2.code and t2.type = 'RoomLabel'
        where t1.room_id = #{roomId}
    </select>

    <select id="listEnterpriseHistory" resultType="com.zjhh.economy.vo.RoomDetailEnterpriseHistoryVo">
        select t1.enterprise_id,
               t2.enterprise_name,
               t1.check_in_date,
               t1.reality_move_out_date,
               t1.area
        from ads_pm_enterprise_settle t1
                 left join ads_pm_enterprise t2 on t1.enterprise_id = t2.id
        where t1.room_id = #{roomId}
        order by t1.check_in_date desc, t1.enterprise_id desc
    </select>

    <select id="listEnterprise" resultType="com.zjhh.economy.vo.RoomDetailEnterpriseVo">
        select
            t1.id as settledId,
            t1.enterprise_id,
               t2.enterprise_name,
               t2.legal_person,
               t2.phone,
               t2.registered_capital,
               t2.on_scaled,
               t1.area,
               t3.name as industry_name,
               t2.territorialized,
               t1.check_in_date,
               t1.expect_move_out_date,
               t1.renovation_start_date,
               t1.renovation_end_date,
               t2.logo_img_id
        from ads_pm_room_enterprise t1
                 left join ads_pm_enterprise t2 on t1.enterprise_id = t2.id
                 left join dm_gy_hy t3 on t3.code = t2.industry_code
        where t1.room_id = #{roomId}
          and t1.moved = false
        order by t1.check_in_date desc ,t1.id desc
    </select>

    <select id="getUpdateDetail" resultType="com.zjhh.economy.vo.RoomUpdateDetailVo">
        select t1.id as room_id,
               t3.project_id,
               t2.building_id,
               t1.floor_id,
               t1.room_no,
               t1.building_area,
               t1.business_area,
               t1.open_hired,
               t1.renovation_code,
               t1.room_type_code,
               t1.rend_unit_price,
               t1.rend_unit,
               t1.property_fees_unit_price,
               t1.property_fees_unit,
               t1.water_fees_unit_price,
               t1.electricity_fees_unit_price,
               t1.house_orientation,
               t1.ownership_code,
               t1.property_certificate_code,
               t1.remark
        from ads_pm_room t1
                 left join ads_pm_floor t2 on t1.floor_id = t2.id
                 left join ads_pm_building t3 on t2.building_id = t3.id
        where t1.id = #{roomId}
    </select>
    <select id="listRoom" resultType="com.zjhh.economy.vo.SettleRoomMenuVo">
        select id as roomId, room_no , business_area as area from ads_pm_room where floor_id = #{floorId} order by room_no
    </select>
    <select id="getRoomInfo" resultType="com.zjhh.economy.vo.FloorRoomInfoVo">
        select t2.id                                                 as floor_id,
               t1.id                                                 as room_id,
               t1.room_no,
               t1.business_area,
               coalesce(string_agg(t4.enterprise_name, '/' order by t4.id), '空置') as ent_name,
               count(t4.id) > 0 as room_status
        from ads_pm_room t1
                 inner join ads_pm_floor t2 on t1.floor_id = t2.id
                 left join ads_pm_room_enterprise t3 on t1.id = t3.room_id and t3.moved = false
                 left join ads_pm_enterprise t4 on t3.enterprise_id = t4.id
        where t1.id = #{roomId}
        group by t2.id, t1.id, t1.room_no, t1.business_area
    </select>
    <select id="listRoomInfo" resultType="com.zjhh.economy.vo.FloorRoomInfoVo">
        select t2.id                                                 as floor_id,
               t1.id                                                 as room_id,
               t1.room_no,
               t1.business_area,
               coalesce(string_agg(t4.enterprise_name, '/' order by t4.id), '空置') as ent_name,
               count(t4.id) > 0 as room_status
        from ads_pm_room t1
                 inner join ads_pm_floor t2 on t1.floor_id = t2.id
                 left join ads_pm_room_enterprise t3 on t1.id = t3.room_id and t3.moved = false
                 left join ads_pm_enterprise t4 on t3.enterprise_id = t4.id
        where t2.id = #{floorId}
        group by t2.id, t1.id, t1.room_no, t1.business_area
        order by t1.room_no
    </select>
    <select id="getImportRoom" resultType="com.zjhh.economy.vo.ImportRoomVo">
        select t4.id as projectId,t4.project_name,t3.id as buildingId,t3.building_name,t2.id as floorId,t2.floor_no,t1.id as roomId,t1.room_no, t1.business_area from ads_pm_room t1 left join ads_pm_floor t2 on t1.floor_id = t2.id
        left join ads_pm_building t3 on t2.building_id = t3.id
        left join ads_pm_project t4 on t3.project_id = t4.id
        where 1=1
            <if test="projectName != null and projectName != ''">
                and t4.project_name = #{projectName}
            </if>

        and t3.building_name = #{buildingName}
        <if test="floorNo != null and floorNo != ''">
            and t2.floor_no::varchar like concat('%',#{floorNo},'%')
        </if>
        and t1.room_no::varchar = #{roomNo}

    </select>

</mapper>
