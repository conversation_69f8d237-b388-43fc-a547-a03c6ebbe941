<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmFloorMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , building_id, floor_no, floor_name, remark, create_user, create_time, update_time
    </sql>

    <select id="list" resultType="com.zjhh.economy.vo.FloorVo">
        select t1.id                                                            as floor_id,
               t1.floor_no,
               t1.floor_name,
               (select count(t.id) from ads_pm_room t where t.floor_id = t1.id) as room_size,
               t1.remark,
               t1.plane_img_id,
               t1.plane_config_id
        from ads_pm_floor t1
        where t1.building_id = #{buildingId}
        order by t1.floor_no
    </select>

    <select id="countRoomSize" resultType="java.lang.Integer">
        select count(id)
        from ads_pm_room
        where floor_id in (select id
        from ads_pm_floor
        where building_id = #{buildingId}
        <if test="floorIds != null and floorIds.size > 0">
            and id not in
            <foreach collection="floorIds" item="floorId" open="(" close=")" separator=",">
                #{floorId}
            </foreach>
        </if>
        )
    </select>

</mapper>
