<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.MobileGovEconomicMapper">
    <resultMap id="dynamicEcoSummary" type="com.zjhh.economy.vo.AppDynamicSummaryVo">
        <result column="year" property="year"/>
        <collection property="list" select="listDynamicEco"
                    column="{year=year}">
        </collection>
    </resultMap>
    <select id="getTabOne" resultType="com.zjhh.economy.vo.TabOneVo">
        select id, title, content
        from ads_mobile_economic_generalization
        where dynamic_id = #{dynamicId}
    </select>
    <select id="listTabTwo" resultType="com.zjhh.economy.vo.TabTwoVo">
        select zb_mc as jjzb, unit as dw, by_amt, bq_amt, sq_amt, rate
        from ads_mobile_major_economic
        where dynamic_id = #{dynamicId}
        order by xh
    </select>
    <select id="listTabTree" resultType="com.zjhh.economy.vo.TabTreeVo">
        select xh, project_name as xmmc, enterprise_name as xmxxmc, by_invest as byTzs, bq_invest as bnTzs
        from ads_mobile_estate_investment
        where dynamic_id = #{dynamicId}
        order by xh
    </select>
    <select id="listTabFour" resultType="com.zjhh.economy.vo.TabFourVo">
        select xh,
               enterprise_name as qymc,
               yysr_lj         as yysrLjwc,
               yysr_sq         as yysrSqAmt,
               yysr_rate       as yysrZzl,
               amt_bq          as sssrLjwc,
               amt_sq          as sssrSqAmt,
               amt_rate        as sssrZzl
        from ads_mobile_scale_service
        where dynamic_id = #{dynamicId}
        order by xh
    </select>
    <select id="listTabFive" resultType="com.zjhh.economy.vo.TabFiveVo">
        select xh,
               enterprise_name as qymc,
               sales_lj        as xseLjwc,
               sales_sq        as xseSqAmt,
               sales_rate      as xseZzl,
               amt_lj          as sssrLjwc,
               amt_sq          as sssrSqAmt,
               amt_rate        as sssrZzl
        from ads_mobile_limit_sales
        where dynamic_id = #{dynamicId}
        order by xh
    </select>
    <select id="listTabSix" resultType="com.zjhh.economy.vo.TabSixVo">
        select xh,
               building_name     as lymc,
               building_area     as lymj,
               settled_area      as yymj,
               settled_rate      as rzl,
               registered_frcygt as sl,
               registered_zcs    as zcs,
               registered_rate   as zcl,
               tax_amt as ss,
               tax_rate as ssZf,
               tax_unit_output as dwss,
               tax_cfdc_amt as cfdcss,
               tax_cfdc_rate as cfdcssZf,
               tax_cfdcunit_output as cfdcssSs
        from ads_mobile_building_information
        where dynamic_id = #{dynamicId}
        order by xh
    </select>
    <select id="listTabSeven" resultType="com.zjhh.economy.vo.TabSevenVo">
        select xh, enterprise_name as qymc, total_amt as czzsr, public_budget as ybggyssr from ads_mobile_total_tax_enterprise
        where dynamic_id = #{dynamicId}
        order by xh
    </select>
    <select id="listTabEight" resultType="com.zjhh.economy.vo.TabEightVo">
        select tax_type as km ,public_budget_by as bysr,public_budget_bq as bqsr,central_amt as zyjsr,total_amt as czzsr, public_budget_rate as zb
         from ads_mobile_tax_type
        where dynamic_id = #{dynamicId}
        order by xh
    </select>
    <select id="listDynamicEcoSummary" resultMap="dynamicEcoSummary">
        select year from ads_mobile_dynamic_eco group by year
        order by year desc
    </select>
    <select id="listDynamicEco" resultType="com.zjhh.economy.vo.AppDynamicEcoVo">
        select id, title, document_id from ads_mobile_dynamic_eco where year = #{year}
        order by sort desc
    </select>
</mapper>