<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AnalyzeReportMapper">
    <sql id="settledArea">
        (select sum(settled_area) from v_enterprise where moved = false
        <if test="req.codes != null and req.codes.size > 0">
            and community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        )
    </sql>

    <resultMap id="emptyRoomCommunity" type="com.zjhh.economy.vo.analyzereport.EmptyRoomDetailTableVo">
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="totalCount" property="totalCount"/>
        <result column="zeroCount" property="zeroCount"/>
        <result column="thirtyCount" property="thirtyCount"/>
        <result column="sixtyCount" property="sixtyCount"/>
        <result column="oneHundredTwentyCount" property="oneHundredTwentyCount"/>
        <result column="twoHundredFortyCount" property="twoHundredFortyCount"/>
        <result column="threeHundredSixtyCount" property="threeHundredSixtyCount"/>
        <result column="compareType" property="compareType"/>
        <result column="hasChildren" property="hasChildren"/>
        <result column="currentDate" property="currentDate"/>
        <collection property="children" ofType="com.zjhh.economy.vo.analyzereport.EmptyRoomDetailTableVo"
                    select="listEmptyRoomDetailTableByProject"
                    column="{code=code,currentDate=currentDate}">
        </collection>
    </resultMap>

    <resultMap id="entStructTrend" type="com.zjhh.economy.vo.analyzereport.EntStructTrendTableVo">
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="totalCount" property="totalCount"/>
        <result column="legalLocaledCount" property="legalLocaledCount"/>
        <result column="individualLocaledCount" property="individualLocaledCount"/>
        <result column="compareType" property="compareType"/>
        <result column="industryLocaledCount" property="industryLocaledCount"/>
        <result column="legalNotLocaledCount" property="legalNotLocaledCount"/>
        <result column="individualNotLocaledCount" property="individualNotLocaledCount"/>
        <result column="industryNotLocaledCount" property="industryNotLocaledCount"/>
        <result column="localedCount" property="localedCount"/>
        <result column="hasChildren" property="hasChildren"/>
        <result column="notLocaledCount" property="notLocaledCount"/>
        <collection property="children" ofType="com.zjhh.economy.vo.analyzereport.EntStructTrendTableVo"
                    select="listEntStructTrendTableByProject"
                    column="{code=code}">
        </collection>
    </resultMap>


    <resultMap id="areaTrendVo" type="com.zjhh.economy.vo.analyzereport.SettledAreaTrendTableVo">
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="hasChildren" property="hasChildren"/>
        <result column="totalBusinessArea" property="totalBusinessArea"/>
        <result column="settledArea" property="settledArea"/>
        <result column="emptyArea" property="emptyArea"/>
        <result column="emptyRate" property="emptyRate"/>
        <result column="compareType" property="compareType"/>
        <result column="datekey" property="datekey"/>
        <collection property="children" ofType="com.zjhh.economy.vo.analyzereport.SettledAreaTrendTableVo"
                    select="listSettledAreaTrendTableByProject"
                    column="{code=code,datekey=datekey}">
        </collection>
    </resultMap>


    <select id="listSpaceResourceAnalyzeByCommunity"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceAnalyzeVo">

        select (select name from dm_pm where code = t1.code and type = 'Community') as name,
        coalesce(t1.businessArea,null) as businessArea, coalesce(t1.settledArea,null) as settledArea,
        case when coalesce(t1.businessArea,0) = 0 then null
        when coalesce(t1.settledArea,0) = 0 then null
        else round(coalesce(t1.settledArea,0) /
        coalesce(t1.businessArea,0) * 100, 2) end as
        settledRate,
        coalesce(coalesce(t1.businessArea,0) - coalesce(t1.settledArea,0),null) as emptyArea,
        case when coalesce(t1.businessArea,0) = 0 then null else round((coalesce(t1.businessArea,0) -
        coalesce(t1.settledArea,0)) /
        coalesce(t1.businessArea,0) * 100 , 2) end as emptyRate
        From (
        select code,(select coalesce(sum(business_area),0) from
        ads_pm_building where project_id in (select id from ads_pm_project where community_code = dp.code
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        )) as businessArea,(select coalesce(sum(settled_area)) from v_enterprise
        where moved = false and community_code = dp.code
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>) as settledArea from dm_pm dp
        <where>
            type = 'Community'
            <if test="req.codes != null and req.codes.size > 0">
                and code in
                <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
        </where>
        ) t1
        order by t1.code
    </select>
    <select id="listSpaceResourceAnalyzeByProject"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceAnalyzeVo">
        select t1.project_name as name,
        coalesce(t1.businessArea,null) as businessArea, coalesce(t1.settledArea,null) as settledArea,
        case when coalesce(t1.businessArea,0) = 0 then null
        when coalesce(t1.settledArea,0) = 0 then null
        else round(coalesce(t1.settledArea,0) /
        coalesce(t1.businessArea,0) * 100, 2) end as
        settledRate,
        coalesce(coalesce(t1.businessArea,0) - coalesce(t1.settledArea,0),null) as emptyArea,
        case when coalesce(t1.businessArea,0) = 0 then null else round((coalesce(t1.businessArea,0) -
        coalesce(t1.settledArea,0)) /
        coalesce(t1.businessArea,0) * 100 , 2) end as emptyRate
        From (
        select project_name,(select coalesce(sum(business_area),0) from
        ads_pm_building where project_id = app.id) as businessArea,(select coalesce(sum(settled_area)) from v_enterprise
        where moved = false and project_id = app.id) as settledArea from ads_pm_project app
        <where>
            <if test="req.projectType != null and req.projectType != ''">
                app.project_type = #{req.projectType}
            </if>
            <if test="req.codes != null and req.codes.size > 0">
                AND app.id in
                <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
        </where>
        ) t1

    </select>
    <select id="listSpaceResourceAnalyzeByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceAnalyzeVo">
        select t1.building_name as name,
        coalesce(t1.businessArea,null) as businessArea, coalesce(t1.settledArea,null) as settledArea,
        case when coalesce(t1.businessArea,0) = 0 then null
        when coalesce(t1.settledArea,0) = 0 then null
        else round(coalesce(t1.settledArea,0) /
        coalesce(t1.businessArea,0) * 100, 2) end as
        settledRate,
        coalesce(coalesce(t1.businessArea,0) - coalesce(t1.settledArea,0),null) as emptyArea,
        case when coalesce(t1.businessArea,0) = 0 then null else round((coalesce(t1.businessArea,0) -
        coalesce(t1.settledArea,0)) /
        coalesce(t1.businessArea,0) * 100 , 2) end as emptyRate
        From (
        select apb.building_name,apb.business_area as businessArea,(select coalesce(sum(settled_area)) from v_enterprise
        where moved = false and building_id = apb.id) as settledArea from ads_pm_building apb
        left join ads_pm_project app on apb.project_id = app.id
        <where>
            <if test="req.projectType != null and req.projectType != ''">
                app.project_type = #{req.projectType}
            </if>
            <if test="req.codes != null and req.codes.size > 0">
                AND apb.id in
                <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
        </where>
        ) t1
    </select>
    <select id="listSpaceResourceDimensionByCommunity"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select (select name from dm_pm where code = t1.community_code and type = 'Community') as name,t1.community_code
        as code ,concat(substr(t1.datekey,1,4),'-',substr(t1.datekey,5,6)) as datekey,
        coalesce(t1.businessArea,0) as businessArea, coalesce(t1.settledArea,0) as settledArea,
        coalesce(t1.businessArea,0) - coalesce(t1.settledArea,0) as emptyArea,
        case when coalesce(t1.businessArea, 0) = 0 then 0 else round(coalesce(t1.settledArea, 0) /
        coalesce(t1.businessArea,0) * 100 ,2) end as settledRate,
        case when coalesce(t1.businessArea, 0) = 0 then 0 else round((coalesce(t1.businessArea,0) -
        coalesce(t1.settledArea, 0)) / coalesce(t1.businessArea,0) * 100 ,2) end as emptyRate
        from (
        WITH data AS (
        SELECT community, datekey
        FROM (
        <foreach collection="req.codes" item="community" separator=" UNION ALL ">
            SELECT #{community} AS community
        </foreach>
        ) AS communites(community),
        (SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month
        )
        SELECT da.community as community_code, da.datekey, (select sum(business_area)
        from ads_pm_building
        where project_id in (select id from ads_pm_project where community_code = da.community  <if
            test="req.projectType != null and req.projectType != ''">
        and project_type = #{req.projectType}
    </if>)
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey) as businessArea,
        (select sum(settled_area) from v_enterprise where community_code = da.community and moved = false and
        TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey  <if test="req.projectType != null and req.projectType != ''">
        and project_type = #{req.projectType}
    </if>) as settledArea
        FROM data da
        ) t1
        order by t1.datekey
    </select>
    <select id="listSpaceResourceDimensionByProject"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select (select project_name from ads_pm_project where id = t1.project_id ) as name,t1.project_id as code,
        concat(substr(t1.datekey,1,4),'-',substr(t1.datekey,5,6)) as datekey,coalesce(t1.businessArea,0) as
        businessArea,
        coalesce(t1.settledArea,0) as settledArea,
        coalesce(t1.businessArea,0) - coalesce(t1.settledArea,0) as emptyArea,
        case when coalesce(t1.businessArea, 0) = 0 then 0 else round(coalesce(t1.settledArea, 0) /
        coalesce(t1.businessArea,0) * 100 ,2) end as settledRate,
        case when coalesce(t1.businessArea, 0) = 0 then 0 else round((coalesce(t1.businessArea,0) -
        coalesce(t1.settledArea, 0)) / coalesce(t1.businessArea,0) * 100 ,2) end as emptyRate
        from (
        WITH data AS (
        SELECT project, datekey
        FROM (
        <foreach collection="req.codes" item="projectId" separator=" UNION ALL ">
            SELECT #{projectId} AS project
        </foreach>
        ) AS projects(project),
        (SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month
        )
        SELECT da.project as project_id, da.datekey, (select sum(business_area)
        from ads_pm_building
        where project_id = da.project
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey) as businessArea,
        (select sum(settled_area) from v_enterprise where project_id = da.project and moved = false and
        TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey) as settledArea
        FROM data da
        ) t1
        order by t1.datekey
    </select>
    <select id="listSpaceResourceDimensionByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select (select building_name from ads_pm_building where id = t1.building_id) as name,t1.building_id as
        code,concat(substr(t1.datekey,1,4),'-',substr(t1.datekey,5,6)) as datekey,t1.businessArea,
        t1.settledArea,
        coalesce(t1.businessArea,0) - coalesce(t1.settledArea,0) as emptyArea,
        case when coalesce(t1.businessArea, 0) = 0 then 0 else round(coalesce(t1.settledArea, 0) /
        coalesce(t1.businessArea,0) * 100 ,2) end as settledRate,
        case when coalesce(t1.businessArea, 0) = 0 then 0 else round((coalesce(t1.businessArea,0) -
        coalesce(t1.settledArea, 0)) / coalesce(t1.businessArea,0) * 100 ,2) end as emptyRate
        from (
        WITH data AS (
        SELECT building, datekey
        FROM (
        <foreach collection="req.codes" item="buildingId" separator=" UNION ALL ">
            SELECT #{buildingId} AS building
        </foreach>
        ) AS buildings(building),
        (SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month
        )
        SELECT da.building as building_id, da.datekey, (select sum(business_area)
        from ads_pm_building
        where id = da.building
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey) as businessArea,
        (select sum(settled_area) from v_enterprise where building_id = da.building and moved = false and
        TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey) as settledArea
        FROM data da
        ) t1
        order by t1.datekey
    </select>
    <select id="listSettledAreaTrendTableByCommunity"
            resultMap="areaTrendVo">
        select '全街道'                                                                as name,
               '0'                                                                     as code,
               #{req.datekey}                                                          as datekey,
               sum(t1.businessArea)                                                    as totalBusinessArea,
               sum(t1.settledArea)                                                     as settledArea,
               case
                   when sum(t1.businessArea) = 0 then 0
                   else round(sum(t1.settledArea) / sum(t1.businessArea) * 100, 2) end as
                                                                                          settledRate,
               sum(t1.businessArea) - sum(t1.settledArea)                              as emptyArea,
               case
                   when sum(t1.businessArea) = 0 then 0
                   else round((sum(t1.businessArea) - sum(t1.settledArea)) /
                              sum(t1.businessArea) * 100,
                              2) end                                                   as emptyRate,
               2                                                                       as compareType,
               null                                                                    as hasChildren
        From (select code,
                     (select coalesce(sum(business_area), 0)
                      from ads_pm_building
                      where project_id in (select id from ads_pm_project where community_code = dp.code)
                        and TO_CHAR(operation_time, 'YYYYMM') &lt;= #{req.datekey}) as businessArea,
                     (select coalesce(sum(settled_area))
                      from v_enterprise
                      where moved = false
                        and community_code = dp.code
                        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{req.datekey})  as settledArea
              from dm_pm dp
              where type = 'Community') t1
        union all
        select (select name from dm_pm where code = t1.code and type = 'Community')                           as name,
               t1.code,
               #{req.datekey}                                                                                 as datekey,
               t1.businessArea                                                                                as totalBusinessArea,
               t1.settledArea                                                                                 as settledArea,
               case when t1.businessArea = 0 then 0 else round(t1.settledArea / t1.businessArea * 100, 2) end as
                                                                                                                 settledRate,
               t1.businessArea - t1.settledArea                                                               as emptyArea,
               case
                   when t1.businessArea = 0 then 0
                   else round((t1.businessArea - t1.settledArea) /
                              t1.businessArea * 100,
                              2) end                                                                          as emptyRate,
               2                                                                                              as compareType,
               null                                                                                           as hasChildren
        From (select code,
                     (select coalesce(sum(business_area), 0)
                      from ads_pm_building
                      where project_id in (select id from ads_pm_project where community_code = dp.code)
                        and TO_CHAR(operation_time, 'YYYYMM') &lt;= #{req.datekey}) as businessArea,
                     (select coalesce(sum(settled_area))
                      from v_enterprise
                      where moved = false
                        and community_code = dp.code
                        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{req.datekey})  as settledArea
              from dm_pm dp
              where type = 'Community') t1

    </select>
    <select id="listSettledAreaTrendTableByProject"
            resultType="com.zjhh.economy.vo.analyzereport.SettledAreaTrendTableVo">
        select t1.project_name                                                                                as name,
               t1.project_id                                                                                  as code,
               t1.serial_no,
               t1.businessArea                                                                                as totalBusinessArea,
               t1.settledArea                                                                                 as settledArea,
               case when t1.businessArea = 0 then 0 else round(t1.settledArea / t1.businessArea * 100, 2) end as
                                                                                                                 settledRate,
               t1.businessArea - t1.settledArea                                                               as emptyArea,
               case
                   when t1.businessArea = 0 then 0
                   else round((t1.businessArea - t1.settledArea) /
                              t1.businessArea * 100,
                              2) end                                                                          as emptyRate,
               3                                                                                              as compareType,
               case
                   when (select count(1) from ads_pm_building where project_id = t1.project_id) > 0 then true
                   else false end                                                                             as hasChildren
        From (select project_name,
                     id                                                         as project_id,
                     serial_no,
                     (select coalesce(sum(business_area), 0)
                      from ads_pm_building
                      where project_id = app.id
                        and TO_CHAR(operation_time, 'YYYYMM') &lt;= #{datekey}) as businessArea,
                     (select coalesce(sum(settled_area))
                      from v_enterprise
                      where moved = false
                        and project_id = app.id
                        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{datekey})  as settledArea
              from ads_pm_project app
              where app.community_code = #{code}) t1
        order by t1.serial_no
    </select>
    <select id="listSettledAreaTrendTableByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.SettledAreaTrendTableVo">
        select t1.building_name                                                                               as name,
               t1.building_id                                                                                 as code,
               t1.businessArea                                                                                as totalBusinessArea,
               t1.settledArea                                                                                 as settledArea,
               case when t1.businessArea = 0 then 0 else round(t1.settledArea / t1.businessArea * 100, 2) end as
                                                                                                                 settledRate,
               t1.businessArea - t1.settledArea                                                               as emptyArea,
               case
                   when t1.businessArea = 0 then 0
                   else round((t1.businessArea - t1.settledArea) /
                              t1.businessArea * 100,
                              2) end                                                                          as emptyRate
        From (select apb.building_name,
                     apb.id                                                        as building_id,
                     apb.business_area                                             as businessArea,
                     (select coalesce(sum(settled_area))
                      from v_enterprise
                      where moved = false
                        and building_id = apb.id
                        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{req.datekey}) as settledArea
              from ads_pm_building apb
              where apb.project_id = #{req.code}
                and TO_CHAR(operation_time, 'YYYYMM') &lt;= #{req.datekey}) t1
    </select>
    <select id="listSpaceResourceDimensionByCommunityByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select name, t3.community_code as code, quarter as datekey,
        sum(t3.businessArea) as businessArea,
        sum(t3.settledArea) as settledArea,
        sum(t3.emptyArea) as emptyArea,
        case
        when coalesce(sum(t3.businessArea), 0) = 0 then 0
        else round(coalesce(sum(t3.settledArea), 0) / coalesce(sum(t3.businessArea),0) * 100 ,2)
        end as settledRate,
        case
        when coalesce(sum(t3.businessArea), 0) = 0 then 0
        else round((coalesce(sum(t3.businessArea),0) - coalesce(sum(t3.settledArea), 0)) / coalesce(sum(t3.businessArea),0) * 100 ,2)
        end as emptyRate
        from (
        select name, community_code, quarter, businessArea, settledArea, emptyArea
        from (
        select
        (select name from dm_pm where code = t2.community_code and type = 'Community') as name,
        t2.community_code,
        t2.quarter,
        t2.businessArea,
        t2.settledArea,
        coalesce(t2.businessArea,0) - coalesce(t2.settledArea,0) as emptyArea,
        row_number() over (partition by t2.community_code, t2.quarter order by t2.datekey desc) as rn
        from (
        select
        t1.community_code,
        t1.datekey,
        case
        when substr(t1.datekey,5,2) in ('01', '02', '03') then concat(substr(t1.datekey,1,4),'Q1')
        when substr(t1.datekey,5,2) in ('04','05', '06') then concat(substr(t1.datekey,1,4),'Q2')
        when substr(t1.datekey,5,2) in ('07','08', '09') then concat(substr(t1.datekey,1,4),'Q3')
        when substr(t1.datekey,5,2) in ('10','11', '12') then concat(substr(t1.datekey,1,4),'Q4')
        else ''
        end as quarter,
        t1.businessArea,
        t1.settledArea
        from (
        WITH data AS (
        SELECT community, datekey
        FROM (
        <foreach collection="req.codes" item="community" separator=" UNION ALL ">
            SELECT #{community} AS community
        </foreach>
        ) AS communities(community),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey)
        )
        SELECT
        da.community as community_code,
        da.datekey,
        (select sum(business_area)
        from ads_pm_building
        where project_id in (
        select id from ads_pm_project
        where community_code = da.community
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        )
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey
        ) as businessArea,
        (select sum(settled_area)
        from v_enterprise
        where community_code = da.community
        and moved = false
        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        ) as settledArea
        FROM data da
        ) t1
        ) t2
        ) ranked_data
        where rn = 1
        ) t3
        group by t3.name, t3.quarter, t3.community_code
    </select>
    <select id="listSpaceResourceDimensionByProjectByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select name, t3.project_id as code, quarter as datekey,
        sum(t3.businessArea) as businessArea,
        sum(t3.settledArea) as settledArea,
        sum(t3.emptyArea) as emptyArea,
        case
        when coalesce(sum(t3.businessArea), 0) = 0 then 0
        else round(coalesce(sum(t3.settledArea), 0) / coalesce(sum(t3.businessArea),0) * 100 ,2)
        end as settledRate,
        case
        when coalesce(sum(t3.businessArea), 0) = 0 then 0
        else round((coalesce(sum(t3.businessArea),0) - coalesce(sum(t3.settledArea), 0)) / coalesce(sum(t3.businessArea),0) * 100 ,2)
        end as emptyRate
        from (
        select name, project_id, quarter, businessArea, settledArea, emptyArea
        from (
        select
        (select project_name from ads_pm_project where id = t2.project_id) as name,
        t2.project_id,
        t2.quarter,
        t2.businessArea,
        t2.settledArea,
        coalesce(t2.businessArea,0) - coalesce(t2.settledArea,0) as emptyArea,
        row_number() over (partition by t2.project_id, t2.quarter order by t2.datekey desc) as rn
        from (
        select
        t1.project_id,
        t1.datekey,
        case
        when substr(t1.datekey,5,2) in ('01', '02', '03') then concat(substr(t1.datekey,1,4),'Q1')
        when substr(t1.datekey,5,2) in ('04','05', '06') then concat(substr(t1.datekey,1,4),'Q2')
        when substr(t1.datekey,5,2) in ('07','08', '09') then concat(substr(t1.datekey,1,4),'Q3')
        when substr(t1.datekey,5,2) in ('10','11', '12') then concat(substr(t1.datekey,1,4),'Q4')
        else ''
        end as quarter,
        t1.businessArea,
        t1.settledArea
        from (
        WITH data AS (
        SELECT project, datekey
        FROM (
        <foreach collection="req.codes" item="projectId" separator=" UNION ALL ">
            SELECT #{projectId} AS project
        </foreach>
        ) AS projects(project),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey)
        )
        SELECT
        da.project as project_id,
        da.datekey,
        (select sum(business_area)
        from ads_pm_building
        where project_id = da.project
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey
        ) as businessArea,
        (select sum(settled_area)
        from v_enterprise
        where project_id = da.project
        and moved = false
        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        ) as settledArea
        FROM data da
        ) t1
        ) t2
        ) ranked_data
        where rn = 1
        ) t3
        group by t3.name, t3.quarter, t3.project_id
    </select>
    <select id="listSpaceResourceDimensionByBuildingByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select name, t3.building_id as code, quarter as datekey,
        sum(t3.businessArea) as businessArea,
        sum(t3.settledArea) as settledArea,
        sum(t3.emptyArea) as emptyArea,
        case
        when coalesce(sum(t3.businessArea), 0) = 0 then 0
        else round(coalesce(sum(t3.settledArea), 0) / coalesce(sum(t3.businessArea),0) * 100 ,2)
        end as settledRate,
        case
        when coalesce(sum(t3.businessArea), 0) = 0 then 0
        else round((coalesce(sum(t3.businessArea),0) - coalesce(sum(t3.settledArea), 0)) / coalesce(sum(t3.businessArea),0) * 100 ,2)
        end as emptyRate
        from (
        select name, building_id, quarter, businessArea, settledArea, emptyArea
        from (
        select
        (select building_name from ads_pm_building where id = t2.building_id) as name,
        t2.building_id,
        t2.quarter,
        t2.businessArea,
        t2.settledArea,
        coalesce(t2.businessArea,0) - coalesce(t2.settledArea,0) as emptyArea,
        row_number() over (partition by t2.building_id, t2.quarter order by t2.datekey desc) as rn
        from (
        select
        t1.building_id,
        t1.datekey,
        case
        when substr(t1.datekey,5,2) in ('01', '02', '03') then concat(substr(t1.datekey,1,4),'Q1')
        when substr(t1.datekey,5,2) in ('04','05', '06') then concat(substr(t1.datekey,1,4),'Q2')
        when substr(t1.datekey,5,2) in ('07','08', '09') then concat(substr(t1.datekey,1,4),'Q3')
        when substr(t1.datekey,5,2) in ('10','11', '12') then concat(substr(t1.datekey,1,4),'Q4')
        else ''
        end as quarter,
        t1.businessArea,
        t1.settledArea
        from (
        WITH data AS (
        SELECT building, datekey
        FROM (
        <foreach collection="req.codes" item="buildingId" separator=" UNION ALL ">
            SELECT #{buildingId} AS building
        </foreach>
        ) AS buildings(building),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey)
        )
        SELECT
        da.building as building_id,
        da.datekey,
        (select sum(business_area)
        from ads_pm_building
        where id = da.building
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey
        ) as businessArea,
        (select sum(settled_area)
        from v_enterprise
        where building_id = da.building
        and moved = false
        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        ) as settledArea
        FROM data da
        ) t1
        ) t2
        ) ranked_data
        where rn = 1
        ) t3
        group by t3.name, t3.quarter, t3.building_id
    </select>
    <select id="listSpaceResourceDimensionByCommunityByYear"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select name, t3.datekey, t3.community_code as code,
        t3.businessArea, t3.settledArea,
        coalesce(t3.businessArea,0) - coalesce(t3.settledArea,0) as emptyArea,
        case
        when coalesce(t3.businessArea, 0) = 0 then 0
        else round(coalesce(t3.settledArea, 0) / coalesce(t3.businessArea,0) * 100 ,2)
        end as settledRate,
        case
        when coalesce(t3.businessArea, 0) = 0 then 0
        else round((coalesce(t3.businessArea,0) - coalesce(t3.settledArea, 0)) / coalesce(t3.businessArea,0) * 100 ,2)
        end as emptyRate
        from (
        select name, community_code, datekey, businessArea, settledArea
        from (
        select
        (select name from dm_pm where code = t2.community_code and type = 'Community') as name,
        t2.community_code,
        t2.year as datekey,
        t2.businessArea,
        t2.settledArea,
        row_number() over (partition by t2.community_code, t2.year order by t2.month_datekey desc) as rn
        from (
        select
        t1.community_code,
        substr(t1.month_datekey, 1, 4) as year,
        t1.month_datekey,
        t1.businessArea,
        t1.settledArea
        from (
        WITH data AS (
        SELECT community, datekey
        FROM (
        <foreach collection="req.codes" item="community" separator=" UNION ALL ">
            SELECT #{community} AS community
        </foreach>
        ) AS communities(community),
        (
        -- 生成过去4年每个月的数据
        SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'), 'YYYYMM') AS datekey
        FROM generate_series(0, 47) n  -- 4年*12月=48个月
        UNION
        SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) - (n * INTERVAL '1 year'), 'YYYY') AS datekey
        FROM generate_series(0, 3) n   -- 保持原有的年份生成逻辑作为备用
        ) AS months(datekey)
        WHERE length(datekey) = 6  -- 只选择月份格式的数据 (YYYYMM)
        )
        SELECT
        da.community as community_code,
        da.datekey as month_datekey,
        (select sum(business_area)
        from ads_pm_building
        where project_id in (
        select id from ads_pm_project
        where community_code = da.community
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        )
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey
        ) as businessArea,
        (select sum(settled_area)
        from v_enterprise
        where community_code = da.community
        and moved = false
        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        ) as settledArea
        FROM data da
        ) t1
        ) t2
        ) ranked_data
        where rn = 1  -- 只取每年最后一个月的数据
        ) t3
        order by t3.datekey
    </select>
    <select id="listSpaceResourceDimensionByProjectByYear"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select name, t3.datekey, t3.businessArea, t3.settledArea, t3.project_id as code,
        coalesce(t3.businessArea,0) - coalesce(t3.settledArea,0) as emptyArea,
        case
        when coalesce(t3.businessArea, 0) = 0 then 0
        else round(coalesce(t3.settledArea, 0) / coalesce(t3.businessArea,0) * 100 ,2)
        end as settledRate,
        case
        when coalesce(t3.businessArea, 0) = 0 then 0
        else round((coalesce(t3.businessArea,0) - coalesce(t3.settledArea, 0)) / coalesce(t3.businessArea,0) * 100 ,2)
        end as emptyRate
        from (
        select name, project_id, datekey, businessArea, settledArea
        from (
        select
        (select project_name from ads_pm_project where id = t2.project_id) as name,
        t2.project_id,
        t2.year as datekey,
        t2.businessArea,
        t2.settledArea,
        row_number() over (partition by t2.project_id, t2.year order by t2.month_datekey desc) as rn
        from (
        select
        t1.project_id,
        substr(t1.month_datekey, 1, 4) as year,
        t1.month_datekey,
        t1.businessArea,
        t1.settledArea
        from (
        WITH data AS (
        SELECT project, datekey
        FROM (
        <foreach collection="req.codes" item="projectId" separator=" UNION ALL ">
            SELECT #{projectId} AS project
        </foreach>
        ) AS projects(project),
        (
        -- 生成过去4年每个月的数据
        SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'), 'YYYYMM') AS datekey
        FROM generate_series(0, 47) n  -- 4年*12月=48个月
        UNION
        SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) - (n * INTERVAL '1 year'), 'YYYY') AS datekey
        FROM generate_series(0, 3) n   -- 保持原有的年份生成逻辑作为备用
        ) AS months(datekey)
        WHERE length(datekey) = 6  -- 只选择月份格式的数据 (YYYYMM)
        )
        SELECT
        da.project as project_id,
        da.datekey as month_datekey,
        (select sum(business_area)
        from ads_pm_building
        where project_id = da.project
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey
        ) as businessArea,
        (select sum(settled_area)
        from v_enterprise
        where project_id = da.project
        and moved = false
        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        ) as settledArea
        FROM data da
        ) t1
        ) t2
        ) ranked_data
        where rn = 1  -- 只取每年最后一个月的数据
        ) t3
        order by t3.datekey
    </select>
    <select id="listSpaceResourceDimensionByBuildingByYear"
            resultType="com.zjhh.economy.vo.analyzereport.SpaceResourceDimensionVo">
        select name, t3.datekey, t3.businessArea, t3.settledArea, t3.building_id as code,
        coalesce(t3.businessArea,0) - coalesce(t3.settledArea,0) as emptyArea,
        case
        when coalesce(t3.businessArea, 0) = 0 then 0
        else round(coalesce(t3.settledArea, 0) / coalesce(t3.businessArea,0) * 100 ,2)
        end as settledRate,
        case
        when coalesce(t3.businessArea, 0) = 0 then 0
        else round((coalesce(t3.businessArea,0) - coalesce(t3.settledArea, 0)) / coalesce(t3.businessArea,0) * 100 ,2)
        end as emptyRate
        from (
        select name, building_id, datekey, businessArea, settledArea
        from (
        select
        (select building_name from ads_pm_building where id = t2.building_id) as name,
        t2.building_id,
        t2.year as datekey,
        t2.businessArea,
        t2.settledArea,
        row_number() over (partition by t2.building_id, t2.year order by t2.month_datekey desc) as rn
        from (
        select
        t1.building_id,
        substr(t1.month_datekey, 1, 4) as year,
        t1.month_datekey,
        t1.businessArea,
        t1.settledArea
        from (
        WITH data AS (
        SELECT building, datekey
        FROM (
        <foreach collection="req.codes" item="buildingId" separator=" UNION ALL ">
            SELECT #{buildingId} AS building
        </foreach>
        ) AS buildings(building),
        (
        -- 生成过去4年每个月的数据
        SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'), 'YYYYMM') AS datekey
        FROM generate_series(0, 47) n  -- 4年*12月=48个月
        UNION
        SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) - (n * INTERVAL '1 year'), 'YYYY') AS datekey
        FROM generate_series(0, 3) n   -- 保持原有的年份生成逻辑作为备用
        ) AS months(datekey)
        WHERE length(datekey) = 6  -- 只选择月份格式的数据 (YYYYMM)
        )
        SELECT
        da.building as building_id,
        da.datekey as month_datekey,
        (select sum(business_area)
        from ads_pm_building
        where id = da.building
        and TO_CHAR(operation_time, 'YYYYMM') &lt;= da.datekey
        ) as businessArea,
        (select sum(settled_area)
        from v_enterprise
        where building_id = da.building
        and moved = false
        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        ) as settledArea
        FROM data da
        ) t1
        ) t2
        ) ranked_data
        where rn = 1  -- 只取每年最后一个月的数据
        ) t3
        order by t3.datekey
    </select>
    <select id="listEmptyRoomCompareByCommunity"
            resultType="com.zjhh.economy.vo.analyzereport.EmptyRoomCompareVo">
        select (select name from dm_pm where type = 'Community' and code = t5.community_code) as name ,
        sum(totalCount) as totalCount,
        sum(zeroCount) as zeroCount,
        case when sum(totalCount) = 0 then 0 else round(sum(zeroCount) / sum(totalCount) * 100 , 2) end as zeroZb,
        sum(thirtyCount) as thirtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(thirtyCount) / sum(totalCount) * 100 , 2) end as thirtyZb,
        sum(sixtyCount) as sixtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(sixtyCount) / sum(totalCount) * 100 , 2) end as sixtyZb,
        sum(oneHundredTwentyCount) as oneHundredTwentyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(oneHundredTwentyCount) / sum(totalCount) * 100 , 2) end as
        oneHundredTwentyZb,
        sum(twoHundredFortyCount) as twoHundredFortyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(twoHundredFortyCount) / sum(totalCount) * 100 , 2) end as
        twoHundredFortyZb,
        sum(threeHundredSixtyCount) as threeHundredSixtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(threeHundredSixtyCount) / sum(totalCount) * 100 , 2) end as
        threeHundredSixtyZb
        from (select code as community_code,
        0 as totalCount,
        0 as zeroCount,
        0 as thirtyCount,
        0 as sixtyCount,
        0 as oneHundredTwentyCount,
        0 as twoHundredFortyCount,
        0 as threeHundredSixtyCount
        from dm_pm
        where type = 'Community'
        <if test="req.codes != null and req.codes.size > 0">
            and code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        union all
        select t4.community_code,
        sum(totalCount) as totalCount,
        sum(zeroCount) as zeroCount,
        sum(thirtyCount) as thirtyCount,
        sum(sixtyCount) as sixtyCount,
        sum(oneHundredTwentyCount) as oneHundredTwentyCount,
        sum(twoHundredFortyCount) as twoHundredFortyCount,
        sum(threeHundredSixtyCount) as threeHundredSixtyCount
        from (select t3.community_code,
        count(1) as totalCount,
        case when emptyPeriod = '1' then count(1) else 0 end as zeroCount,
        case when emptyPeriod = '2' then count(1) else 0 end as thirtyCount,
        case when emptyPeriod = '3' then count(1) else 0 end as sixtyCount,
        case when emptyPeriod = '4' then count(1) else 0 end as oneHundredTwentyCount,
        case when emptyPeriod = '5' then count(1) else 0 end as twoHundredFortyCount,
        case when emptyPeriod = '6' then count(1) else 0 end as threeHundredSixtyCount
        from (select t2.community_code,
        case
        when emptyDays >= 0 and emptyDays &lt;= 30 then '1'
        when emptyDays > 30 and emptyDays &lt;= 60 then '2'
        when emptyDays > 60 and emptyDays &lt;= 120 then '3'
        when emptyDays > 120 and emptyDays &lt;= 240 then '4'
        when emptyDays > 240 and emptyDays &lt;= 360 then '5'
        when emptyDays > 360 then '6'
        else '' end as emptyPeriod
        from (select t1.community_code, (#{req.currentDate}::date - realDate::date)::int as emptyDays
        from (select app.community_code,
        case
        when (select count(1)
        from ads_pm_room_enterprise
        where room_id = apr.id
        and apre.moved = false) =
        0
        then apr.create_time
        else (select max(reality_move_out_date)
        from ads_pm_room_enterprise
        where room_id = apr.id
        and moved = true) end as
        realDate
        from ads_pm_room apr
        left join ads_pm_floor apf on apr.floor_id = apf.id
        left join ads_pm_building apb on apb.id = apf.building_id
        left join ads_pm_project app on apb.project_id = app.id
        left join ads_pm_room_enterprise apre on apr.id = apre.room_id
        where community_code is not null
        <if test="req.codes != null and req.codes.size > 0">
            and community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and app.project_type = #{req.projectType}
        </if>
        and apr.id not in
        (select room_id
        from v_enterprise
        where moved = false
        and room_id is not null)) t1) t2) t3
        group by community_code, emptyPeriod) t4
        group by t4.community_code) t5
        group by t5.community_code
    </select>
    <select id="listEmptyRoomCompareByProject"
            resultType="com.zjhh.economy.vo.analyzereport.EmptyRoomCompareVo">
        select t4.project_id,t4.project_name as name,
        sum(totalCount) as totalCount,
        sum(zeroCount) as zeroCount,
        case when sum(totalCount) = 0 then 0 else round(sum(zeroCount) / sum(totalCount) * 100 , 2) end as zeroZb,
        sum(thirtyCount) as thirtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(thirtyCount) / sum(totalCount) * 100 , 2) end as thirtyZb,
        sum(sixtyCount) as sixtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(sixtyCount) / sum(totalCount) * 100 , 2) end as sixtyZb,
        sum(oneHundredTwentyCount) as oneHundredTwentyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(oneHundredTwentyCount) / sum(totalCount) * 100 , 2) end as
        oneHundredTwentyZb,
        sum(twoHundredFortyCount) as twoHundredFortyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(twoHundredFortyCount) / sum(totalCount) * 100 , 2) end as
        twoHundredFortyZb,
        sum(threeHundredSixtyCount) as threeHundredSixtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(threeHundredSixtyCount) / sum(totalCount) * 100 , 2) end as
        threeHundredSixtyZb
        from (select t3.project_id,t3.project_name,
        count(1) as totalCount,
        case when emptyPeriod = '1' then count(1) else 0 end as zeroCount,
        case when emptyPeriod = '2' then count(1) else 0 end as thirtyCount,
        case when emptyPeriod = '3' then count(1) else 0 end as sixtyCount,
        case when emptyPeriod = '4' then count(1) else 0 end as oneHundredTwentyCount,
        case when emptyPeriod = '5' then count(1) else 0 end as twoHundredFortyCount,
        case when emptyPeriod = '6' then count(1) else 0 end as threeHundredSixtyCount
        from (select t2.project_id, t2.project_name,
        case
        when emptyDays >= 0 and emptyDays &lt;= 30 then '1'
        when emptyDays > 30 and emptyDays &lt;= 60 then '2'
        when emptyDays > 60 and emptyDays &lt;= 120 then '3'
        when emptyDays > 120 and emptyDays &lt;= 240 then '4'
        when emptyDays > 240 and emptyDays &lt;= 360 then '5'
        when emptyDays > 360 then '6'
        else '' end as emptyPeriod
        from (select t1.project_id,t1.project_name, (#{req.currentDate}::date - realDate::date)::int as emptyDays
        from (select app.id as project_id,app.project_name,
        case
        when (select count(1)
        from ads_pm_room_enterprise
        where room_id = apr.id
        and apre.moved = false) =
        0
        then apr.create_time
        else (select max(reality_move_out_date)
        from ads_pm_room_enterprise
        where room_id = apr.id
        and moved = true) end as
        realDate
        from ads_pm_room apr
        left join ads_pm_floor apf on apr.floor_id = apf.id
        left join ads_pm_building apb on apb.id = apf.building_id
        left join ads_pm_project app on apb.project_id = app.id
        left join ads_pm_room_enterprise apre on apr.id = apre.room_id
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and app.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        and apr.id not in
        (select room_id
        from v_enterprise
        where moved = false
        and room_id is not null)) t1) t2) t3
        group by t3.project_id,t3.project_name, emptyPeriod) t4
        group by t4.project_id,t4.project_name

    </select>
    <select id="listEmptyRoomCompareByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.EmptyRoomCompareVo">
        select t4.building_id,t4.building_name as name,
        sum(totalCount) as totalCount,
        sum(zeroCount) as zeroCount,
        case when sum(totalCount) = 0 then 0 else round(sum(zeroCount) / sum(totalCount) * 100 , 2) end as zeroZb,
        sum(thirtyCount) as thirtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(thirtyCount) / sum(totalCount) * 100 , 2) end as thirtyZb,
        sum(sixtyCount) as sixtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(sixtyCount) / sum(totalCount) * 100 , 2) end as sixtyZb,
        sum(oneHundredTwentyCount) as oneHundredTwentyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(oneHundredTwentyCount) / sum(totalCount) * 100 , 2) end as
        oneHundredTwentyZb,
        sum(twoHundredFortyCount) as twoHundredFortyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(twoHundredFortyCount) / sum(totalCount) * 100 , 2) end as
        twoHundredFortyZb,
        sum(threeHundredSixtyCount) as threeHundredSixtyCount,
        case when sum(totalCount) = 0 then 0 else round(sum(threeHundredSixtyCount) / sum(totalCount) * 100 , 2) end as
        threeHundredSixtyZb
        from (select t3.building_id,t3.building_name,
        count(1) as totalCount,
        case when emptyPeriod = '1' then count(1) else 0 end as zeroCount,
        case when emptyPeriod = '2' then count(1) else 0 end as thirtyCount,
        case when emptyPeriod = '3' then count(1) else 0 end as sixtyCount,
        case when emptyPeriod = '4' then count(1) else 0 end as oneHundredTwentyCount,
        case when emptyPeriod = '5' then count(1) else 0 end as twoHundredFortyCount,
        case when emptyPeriod = '6' then count(1) else 0 end as threeHundredSixtyCount
        from (select t2.building_id, t2.building_name,
        case
        when emptyDays >= 0 and emptyDays &lt;= 30 then '1'
        when emptyDays > 30 and emptyDays &lt;= 60 then '2'
        when emptyDays > 60 and emptyDays &lt;= 120 then '3'
        when emptyDays > 120 and emptyDays &lt;= 240 then '4'
        when emptyDays > 240 and emptyDays &lt;= 360 then '5'
        when emptyDays > 360 then '6'
        else '' end as emptyPeriod
        from (select t1.building_id,t1.building_name, (#{req.currentDate}::date - realDate::date)::int as emptyDays
        from (select apb.id as building_id,apb.building_name,
        case
        when (select count(1)
        from ads_pm_room_enterprise
        where room_id = apr.id
        and apre.moved = false) =
        0
        then apr.create_time
        else (select max(reality_move_out_date)
        from ads_pm_room_enterprise
        where room_id = apr.id
        and moved = true) end as
        realDate
        from ads_pm_room apr
        left join ads_pm_floor apf on apr.floor_id = apf.id
        left join ads_pm_building apb on apb.id = apf.building_id
        left join ads_pm_project app on apb.project_id = app.id
        left join ads_pm_room_enterprise apre on apr.id = apre.room_id
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and apb.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        and apr.id not in
        (select room_id
        from v_enterprise
        where moved = false
        and room_id is not null)) t1) t2) t3
        group by t3.building_id,t3.building_name, emptyPeriod) t4
        group by t4.building_id,t4.building_name
    </select>
    <select id="listEmptyRoomDetailTableByCommunity"
            resultMap="emptyRoomCommunity">
        -- 使用第二个SQL的结构优化第一个SQL查询
        WITH current_date_param AS (
            SELECT #{req.currentDate}::date AS date_value
        ),

-- 直接计算每个房间的空置日期（按照第二个SQL的思路）
             room_data AS (
                 SELECT
                     a.id,
                     app.community_code,
                     MAX(
                             CASE
                                 WHEN b.room_id IS NULL THEN a.create_time
                                 WHEN b.room_id IS NOT NULL AND b.moved = TRUE THEN reality_move_out_date
                                 ELSE (SELECT date_value FROM current_date_param)
                                 END
                     ) AS realDate
                 FROM ads_pm_room a
                          LEFT JOIN ads_pm_floor t1 ON a.floor_id = t1.id
                          LEFT JOIN ads_pm_building t2 ON t1.building_id = t2.id
                          LEFT JOIN ads_pm_project app ON t2.project_id = app.id
                          LEFT JOIN ads_pm_room_enterprise b ON a.id = b.room_id AND b.moved = TRUE
                 WHERE a.id NOT IN (
                     SELECT room_id FROM v_enterprise
                     WHERE moved = FALSE AND room_id IS NOT NULL
                 )
                 GROUP BY a.id, app.community_code
             ),

-- 计算空置期
             empty_period_data AS (
                 SELECT
                     community_code,
                     CASE
                         WHEN ((SELECT date_value FROM current_date_param) - realDate::date) > 0 AND ((SELECT date_value FROM current_date_param) - realDate::date) &lt;= 30 THEN '1'
            WHEN ((SELECT date_value FROM current_date_param) - realDate::date) > 30 AND ((SELECT date_value FROM current_date_param) - realDate::date) &lt;= 60 THEN '2'
            WHEN ((SELECT date_value FROM current_date_param) - realDate::date) > 60 AND ((SELECT date_value FROM current_date_param) - realDate::date) &lt;= 120 THEN '3'
            WHEN ((SELECT date_value FROM current_date_param) - realDate::date) > 120 AND ((SELECT date_value FROM current_date_param) - realDate::date) &lt;= 240 THEN '4'
            WHEN ((SELECT date_value FROM current_date_param) - realDate::date) > 240 AND ((SELECT date_value FROM current_date_param) - realDate::date) &lt;= 360 THEN '5'
            WHEN ((SELECT date_value FROM current_date_param) - realDate::date) > 360 THEN '6'
            ELSE NULL
        END AS emptyPeriod
    FROM room_data
    WHERE ((SELECT date_value FROM current_date_param) - realDate::date) > 0
),

-- 按社区和空置期统计
community_period_stats AS (
    SELECT
        community_code,
        COUNT(*) AS totalCount,
        SUM(CASE WHEN emptyPeriod = '1' THEN 1 ELSE 0 END) AS zeroCount,
        SUM(CASE WHEN emptyPeriod = '2' THEN 1 ELSE 0 END) AS thirtyCount,
        SUM(CASE WHEN emptyPeriod = '3' THEN 1 ELSE 0 END) AS sixtyCount,
        SUM(CASE WHEN emptyPeriod = '4' THEN 1 ELSE 0 END) AS oneHundredTwentyCount,
        SUM(CASE WHEN emptyPeriod = '5' THEN 1 ELSE 0 END) AS twoHundredFortyCount,
        SUM(CASE WHEN emptyPeriod = '6' THEN 1 ELSE 0 END) AS threeHundredSixtyCount
    FROM empty_period_data
    GROUP BY community_code
)

-- 全街道汇总
        SELECT
            '全街道' AS name,
            SUM(totalCount) AS totalCount,
            SUM(zeroCount) AS zeroCount,
            SUM(thirtyCount) AS thirtyCount,
            SUM(sixtyCount) AS sixtyCount,
            SUM(oneHundredTwentyCount) AS oneHundredTwentyCount,
            SUM(twoHundredFortyCount) AS twoHundredFortyCount,
            SUM(threeHundredSixtyCount) AS threeHundredSixtyCount,
            FALSE AS isLeaf,
            1 AS compareType,
            '0' AS code,
            (SELECT date_value FROM current_date_param) AS currentDate
        FROM community_period_stats

        UNION ALL

-- 各社区数据
        SELECT
            dm.name,
            COALESCE(cps.totalCount, 0) AS totalCount,
            COALESCE(cps.zeroCount, 0) AS zeroCount,
            COALESCE(cps.thirtyCount, 0) AS thirtyCount,
            COALESCE(cps.sixtyCount, 0) AS sixtyCount,
            COALESCE(cps.oneHundredTwentyCount, 0) AS oneHundredTwentyCount,
            COALESCE(cps.twoHundredFortyCount, 0) AS twoHundredFortyCount,
            COALESCE(cps.threeHundredSixtyCount, 0) AS threeHundredSixtyCount,
            CASE WHEN COALESCE(cps.totalCount, 0) = 0 THEN FALSE ELSE TRUE END AS hasChildren,
            2 AS compareType,
            dm.code AS code,
            (SELECT date_value FROM current_date_param) AS currentDate
        FROM dm_pm dm
                 LEFT JOIN community_period_stats cps ON dm.code = cps.community_code
        WHERE dm.type = 'Community'
    </select>
    <select id="listEmptyRoomDetailTableByProject"
            resultType="com.zjhh.economy.vo.analyzereport.EmptyRoomDetailTableVo">
        WITH current_date_param AS (
            SELECT #{currentDate}::date AS date_value
        ),
-- 预先获取需要排除的房间ID
             excluded_rooms AS (
                 SELECT DISTINCT room_id
                 FROM v_enterprise
                 WHERE moved = false AND room_id IS NOT NULL
             ),
-- 预先获取每个房间的最大移出日期
             move_out_dates AS (
                 SELECT
                     room_id,
                     MAX(reality_move_out_date) AS max_move_out_date
                 FROM ads_pm_room_enterprise
                 WHERE moved = true
                 GROUP BY room_id
             ),
-- 计算房间的空置日期和空置区间
             room_empty_data AS (
                 SELECT
                     a.id AS room_id,
                     app.id AS project_id,
                     app.project_name,
                     app.serial_no,
                     ((SELECT date_value FROM current_date_param) -
                      CASE
                          WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                                           WHERE room_id = a.id AND moved = false)
                              THEN a.create_time
                          ELSE COALESCE(mod.max_move_out_date, a.create_time)
                          END::date
             )::INT AS emptyDays,
        CASE
            WHEN ((SELECT date_value FROM current_date_param) -
                  CASE
                      WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                                     WHERE room_id = a.id AND moved = false)
                          THEN a.create_time
                      ELSE COALESCE(mod.max_move_out_date, a.create_time)
                  END::date)::INT BETWEEN 1 AND 30 THEN '1'
            WHEN ((SELECT date_value FROM current_date_param) -
                  CASE
                      WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                                     WHERE room_id = a.id AND moved = false)
                          THEN a.create_time
                      ELSE COALESCE(mod.max_move_out_date, a.create_time)
                  END::date)::INT BETWEEN 31 AND 60 THEN '2'
            WHEN ((SELECT date_value FROM current_date_param) -
                  CASE
                      WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                                     WHERE room_id = a.id AND moved = false)
                          THEN a.create_time
                      ELSE COALESCE(mod.max_move_out_date, a.create_time)
                  END::date)::INT BETWEEN 61 AND 120 THEN '3'
            WHEN ((SELECT date_value FROM current_date_param) -
                  CASE
                      WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                                     WHERE room_id = a.id AND moved = false)
                          THEN a.create_time
                      ELSE COALESCE(mod.max_move_out_date, a.create_time)
                  END::date)::INT BETWEEN 121 AND 240 THEN '4'
            WHEN ((SELECT date_value FROM current_date_param) -
                  CASE
                      WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                                     WHERE room_id = a.id AND moved = false)
                          THEN a.create_time
                      ELSE COALESCE(mod.max_move_out_date, a.create_time)
                  END::date)::INT BETWEEN 241 AND 360 THEN '5'
            WHEN ((SELECT date_value FROM current_date_param) -
                  CASE
                      WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                                     WHERE room_id = a.id AND moved = false)
                          THEN a.create_time
                      ELSE COALESCE(mod.max_move_out_date, a.create_time)
                  END::date)::INT > 360 THEN '6'
            ELSE NULL
        END AS emptyPeriod
    FROM ads_pm_room a
    LEFT JOIN ads_pm_floor apf ON a.floor_id = apf.id
    LEFT JOIN ads_pm_building apb ON apb.id = apf.building_id
    LEFT JOIN ads_pm_project app ON apb.project_id = app.id
    LEFT JOIN move_out_dates mod ON a.id = mod.room_id
    WHERE app.community_code = #{code}
      AND NOT EXISTS (SELECT 1 FROM excluded_rooms er WHERE er.room_id = a.id)
      AND (
          (SELECT date_value FROM current_date_param) -
          CASE
              WHEN NOT EXISTS (SELECT 1 FROM ads_pm_room_enterprise
                             WHERE room_id = a.id AND moved = false)
                  THEN a.create_time
              ELSE COALESCE(mod.max_move_out_date, a.create_time)
          END::date
      )::INT > 0
),

-- 直接按项目、期间统计
project_stats AS (
    SELECT
        project_id,
        project_name,
        serial_no,
        COUNT(*) AS totalCount,
        SUM(CASE WHEN emptyPeriod = '1' THEN 1 ELSE 0 END) AS zeroCount,
        SUM(CASE WHEN emptyPeriod = '2' THEN 1 ELSE 0 END) AS thirtyCount,
        SUM(CASE WHEN emptyPeriod = '3' THEN 1 ELSE 0 END) AS sixtyCount,
        SUM(CASE WHEN emptyPeriod = '4' THEN 1 ELSE 0 END) AS oneHundredTwentyCount,
        SUM(CASE WHEN emptyPeriod = '5' THEN 1 ELSE 0 END) AS twoHundredFortyCount,
        SUM(CASE WHEN emptyPeriod = '6' THEN 1 ELSE 0 END) AS threeHundredSixtyCount
    FROM room_empty_data
    WHERE emptyPeriod IS NOT NULL
    GROUP BY project_id, project_name, serial_no
)

-- 最终结果
        SELECT
            project_id,
            project_name AS name,
            totalCount,
            zeroCount,
            thirtyCount,
            sixtyCount,
            oneHundredTwentyCount,
            twoHundredFortyCount,
            threeHundredSixtyCount,
            CASE WHEN totalCount = 0 THEN false ELSE true END AS hasChildren,
            3 AS compareType,
            project_id AS code
        FROM project_stats
        ORDER BY serial_no
    </select>
    <select id="listEmptyRoomDetailTableByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.EmptyRoomDetailTableVo">
        WITH current_date_param AS (
            SELECT #{req.currentDate}::date AS date_value
        ),
-- 预先获取排除的房间列表
             excluded_rooms AS (
                 SELECT DISTINCT room_id
                 FROM v_enterprise
                 WHERE moved = false AND room_id IS NOT NULL
             ),
-- 预先计算所有相关房间的最大移出日期
             room_move_dates AS (
                 SELECT
                     room_id,
                     MAX(reality_move_out_date) AS max_move_date
                 FROM ads_pm_room_enterprise
                 WHERE moved = true
                 GROUP BY room_id
             ),
-- 预先计算房间是否有在租
             room_occupancy AS (
                 SELECT
                     room_id,
                     COUNT(1) AS active_count
                 FROM ads_pm_room_enterprise
                 WHERE moved = false
                 GROUP BY room_id
             ),
-- 一次性计算所有房间的所有需要信息
             building_room_data AS (
                 SELECT
                     apb.id AS building_id,
                     apb.building_name,
                     CASE
                         WHEN COALESCE(ro.active_count, 0) = 0 THEN
                             CASE
                                 WHEN ((SELECT date_value FROM current_date_param) - a.create_time::date) BETWEEN 1 AND 30 THEN '1'
            WHEN ((SELECT date_value FROM current_date_param) - a.create_time::date) BETWEEN 31 AND 60 THEN '2'
            WHEN ((SELECT date_value FROM current_date_param) - a.create_time::date) BETWEEN 61 AND 120 THEN '3'
            WHEN ((SELECT date_value FROM current_date_param) - a.create_time::date) BETWEEN 121 AND 240 THEN '4'
            WHEN ((SELECT date_value FROM current_date_param) - a.create_time::date) BETWEEN 241 AND 360 THEN '5'
            WHEN ((SELECT date_value FROM current_date_param) - a.create_time::date) > 360 THEN '6'
            ELSE NULL
        END
        ELSE
                CASE
                    WHEN ((SELECT date_value FROM current_date_param) - COALESCE(rmd.max_move_date, a.create_time)::date) BETWEEN 1 AND 30 THEN '1'
                    WHEN ((SELECT date_value FROM current_date_param) - COALESCE(rmd.max_move_date, a.create_time)::date) BETWEEN 31 AND 60 THEN '2'
                    WHEN ((SELECT date_value FROM current_date_param) - COALESCE(rmd.max_move_date, a.create_time)::date) BETWEEN 61 AND 120 THEN '3'
                    WHEN ((SELECT date_value FROM current_date_param) - COALESCE(rmd.max_move_date, a.create_time)::date) BETWEEN 121 AND 240 THEN '4'
                    WHEN ((SELECT date_value FROM current_date_param) - COALESCE(rmd.max_move_date, a.create_time)::date) BETWEEN 241 AND 360 THEN '5'
                    WHEN ((SELECT date_value FROM current_date_param) - COALESCE(rmd.max_move_date, a.create_time)::date) > 360 THEN '6'
                    ELSE NULL
        END
        END AS emptyPeriod
    FROM ads_pm_room a
    JOIN ads_pm_floor apf ON a.floor_id = apf.id
    JOIN ads_pm_building apb ON apf.building_id = apb.id
    LEFT JOIN room_move_dates rmd ON a.id = rmd.room_id
    LEFT JOIN room_occupancy ro ON a.id = ro.room_id
    WHERE apb.project_id = #{req.code}
        AND NOT EXISTS (SELECT 1 FROM excluded_rooms er WHERE er.room_id = a.id)
        AND (
        CASE
        WHEN COALESCE(ro.active_count, 0) = 0 THEN
        ((SELECT date_value FROM current_date_param) - a.create_time::date)
        ELSE
        ((SELECT date_value FROM current_date_param) - COALESCE(rmd.max_move_date, a.create_time)::date)
        END > 0
        )
        ),
-- 直接统计每个楼栋中各空置期的房间数量
        building_stats AS (
        SELECT
        building_id,
        building_name,
        COUNT(*) AS totalCount,
        SUM(CASE WHEN emptyPeriod = '1' THEN 1 ELSE 0 END) AS zeroCount,
        SUM(CASE WHEN emptyPeriod = '2' THEN 1 ELSE 0 END) AS thirtyCount,
        SUM(CASE WHEN emptyPeriod = '3' THEN 1 ELSE 0 END) AS sixtyCount,
        SUM(CASE WHEN emptyPeriod = '4' THEN 1 ELSE 0 END) AS oneHundredTwentyCount,
        SUM(CASE WHEN emptyPeriod = '5' THEN 1 ELSE 0 END) AS twoHundredFortyCount,
        SUM(CASE WHEN emptyPeriod = '6' THEN 1 ELSE 0 END) AS threeHundredSixtyCount
        FROM building_room_data
        WHERE emptyPeriod IS NOT NULL
        GROUP BY building_id, building_name
        )

-- 最终结果
        SELECT
            building_id,
            building_name AS name,
            totalCount,
            zeroCount,
            thirtyCount,
            sixtyCount,
            oneHundredTwentyCount,
            twoHundredFortyCount,
            threeHundredSixtyCount
        FROM building_stats
    </select>
    <select id="listEmptyBuildingFloorTable"
            resultType="com.zjhh.economy.vo.analyzereport.EmptyBuildingFloorTableVo">
        select app.project_name,
        apb.id as buildingId,
        apb.building_name,
        apf.id as floorId,
        apf.floor_no,
        apf.floor_name,
        (select name from dm_pm where type = 'ProjectType' and code = app.project_type) as projectType,
        (select name from dm_pm where type = 'Community' and code = app.community_code) as communityName,
        (select count(1)
        from ads_pm_room
        where floor_id = apf.id) as roomNum,
        coalesce((select sum(apr.business_area)
        from ads_pm_room apr
        where apr.floor_id = apf.id), 0) as totalFloorArea,
        coalesce((select sum(apre.area)
        from ads_pm_room_enterprise apre
        join ads_pm_room apr on apre.room_id = apr.id
        where apr.floor_id = apf.id
        and apre.moved = false
        and apre.room_id is not null), 0) as settledArea,
        coalesce((select sum(apr.business_area)
        from ads_pm_room apr
        where apr.floor_id = apf.id), 0) -
        coalesce((select sum(apre.area)
        from ads_pm_room_enterprise apre
        join ads_pm_room apr on apre.room_id = apr.id
        where apr.floor_id = apf.id
        and apre.moved = false
        and apre.room_id is not null), 0) as emptyArea,
        (select count(1)
        from ads_pm_room apr
        where apr.floor_id = apf.id
        and apr.id not in (select room_id
        from ads_pm_room_enterprise
        where moved = false
        and room_id is not null
        and room_id = apr.id)) as emptyRoomNum
        from ads_pm_floor apf
        left join ads_pm_building apb on apf.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id
        where 1=1
        <if test="req.communities != null and req.communities.size > 0">
            and app.community_code in
            <foreach collection="req.communities" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projects != null and req.projects.size > 0">
            and app.id in
            <foreach collection="req.projects" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.buildings != null and req.buildings.size > 0">
            and apb.id in
            <foreach collection="req.buildings" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projectTypes != null and req.projectTypes.size > 0">
            and app.project_type in
            <foreach collection="req.projectTypes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        order by app.serial_no, apb.serial_no, apf.floor_no
    </select>
    <select id="listTaxIncomeCompareByCommunity"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeCompareVo">
        select (select name from dm_pm where type = 'Community' and code = t1.community_code) as name,
        sum(t1.taxIncome) as taxIncome,
        sum(streetTaxIncome) as streetTaxIncome,
        case when sum(sqTaxIncome) = 0 then 0 else round((sum(taxIncome) - sum(sqTaxIncome)) / sum(sqTaxIncome) * 100 ,
        2) end as zf
        from (
        SELECT community_code, 0 as taxIncome, 0 as streetTaxIncome, 0 as sqTaxIncome
        FROM (select code
        from dm_pm
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        ) AS communities(community_code)
        union all
        select t2.community_code, sum(t2.taxIncome) as taxIncome, sum(t2.streetTaxIncome) as streetTaxIncome,
        (select round(coalesce(sum(by_amt), 0), 2)
        from ads_pm_enterprise_tax_fkm
        where
        yskm_dm = 'ss'
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where community_code = t2.community_code))
        and substr(datekey, 1, 4) = #{req.taxLastYear}
        and substr(datekey,5,6) &lt;= substr(#{req.taxLastYearMonth},5,6)) as sqTaxIncome
        from (select app.community_code ,
        case when yskm_dm = 'ss' then round(coalesce(sum(by_amt), 0), 2) else 0 end as taxIncome,
        case when yskm_dm = 'jdsr' then round(coalesce(sum(by_amt), 0), 2) else 0 end as streetTaxIncome
        from ads_pm_building apb
        left join ads_pm_project app on apb.project_id = app.id
        left join ads_pm_enterprise_tax_fkm apet on apb.id = apet.building_id
        where substr(datekey, 1, 4) = #{req.taxCurrentYear}
        and substr(datekey,5,6) &lt;= substr(#{req.taxCurrentYearMonth},5,6)
        <if test="req.codes != null and req.codes.size > 0">
            and app.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        and app.community_code is not null
        group by app.community_code,apet.yskm_dm
        ) t2
        group by t2.community_code
        ) t1
        group by t1.community_code
        order by t1.community_code

    </select>
    <select id="listTaxIncomeCompareByProject"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeCompareVo">
        select t1.project_id, t1.project_name as name , sum(t1.taxIncome) as taxIncome, sum(t1.streetTaxIncome) as
        streetTaxIncome,
        case when sum(t1.sqTaxIncome) = 0 then 0 else round((sum(t1.taxIncome) - sum(t1.sqTaxIncome)) /
        sum(t1.sqTaxIncome) * 100,2) end as zf
        from (
        select project_id, project_name, 0 as taxIncome, 0 as streetTaxIncome, 0 as sqTaxIncome
        from (select id,project_name
        from ads_pm_project
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        ) AS projects(project_id,project_name)
        union all
        select t2.project_id,t2.project_name, sum(t2.taxIncome) as taxIncome, sum(t2.streetTaxIncome) as
        streetTaxIncome,
        (select round(coalesce(sum(by_amt), 0), 2)
        from ads_pm_enterprise_tax_fkm
        where
        yskm_dm = 'ss'
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where id = t2.project_id))
        and substr(datekey, 1, 4) = #{req.taxLastYear}
        and substr(datekey,5,6) &lt;= substr(#{req.taxLastYearMonth},5,6)) as sqTaxIncome
        from (select app.id as project_id,app.project_name ,
        case when yskm_dm = 'ss' then round(coalesce(sum(by_amt), 0), 2) else 0 end as taxIncome,
        case when yskm_dm = 'jdsr' then round(coalesce(sum(by_amt), 0), 2) else 0 end as streetTaxIncome
        from ads_pm_building apb
        left join ads_pm_project app on apb.project_id = app.id
        left join ads_pm_enterprise_tax_fkm apet on apb.id = apet.building_id
        where substr(datekey, 1, 4) = #{req.taxCurrentYear}
        and substr(datekey,5,6) &lt;= substr(#{req.taxCurrentYearMonth},5,6)
        <if test="req.codes != null and req.codes.size > 0">
            and app.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        and app.community_code is not null
        group by app.id,app.project_name,apet.yskm_dm
        ) t2
        group by t2.project_id,t2.project_name
        ) t1
        group by t1.project_id,t1.project_name
        order by t1.project_id


    </select>
    <select id="listTaxIncomeCompareByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeCompareVo">
        select t1.building_id,t1.building_name as name , sum(t1.taxIncome) as taxIncome, sum(t1.streetTaxIncome) as
        streetTaxIncome,
        case when sum(t1.sqTaxIncome) = 0 then 0 else round((sum(t1.taxIncome) - sum(t1.sqTaxIncome)) /
        sum(t1.sqTaxIncome) * 100 , 2) end as zf
        from (
        select building_id, building_name, 0 as taxIncome, 0 as streetTaxIncome, 0 as sqTaxIncome
        from (select id,building_name
        from ads_pm_building
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        ) AS buildings(building_id,building_name)
        union all
        select t2.building_id,t2.building_name, sum(t2.taxIncome) as taxIncome, sum(t2.streetTaxIncome) as
        streetTaxIncome,
        (select round(coalesce(sum(by_amt), 0), 2)
        from ads_pm_enterprise_tax_fkm
        where
        yskm_dm = 'ss'
        and building_id = t2.building_id
        and substr(datekey, 1, 4) = #{req.taxLastYear}
        and substr(datekey,5,6) &lt;= substr(#{req.taxLastYearMonth},5,6)) as sqTaxIncome
        from (select apb.id as building_id,apb.building_name ,
        case when yskm_dm = 'ss' then round(coalesce(sum(by_amt), 0), 2) else 0 end as taxIncome,
        case when yskm_dm = 'jdsr' then round(coalesce(sum(by_amt), 0), 2) else 0 end as streetTaxIncome
        from ads_pm_building apb
        left join ads_pm_project app on apb.project_id = app.id
        left join ads_pm_enterprise_tax_fkm apet on apb.id = apet.building_id
        where substr(datekey, 1, 4) = #{req.taxCurrentYear}
        and substr(datekey,5,6) &lt;= substr( #{req.taxCurrentYearMonth},5,6)
        and app.community_code is not null
        <if test="req.codes != null and req.codes.size > 0">
            and apb.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by apb.id,apb.building_name,apet.yskm_dm
        ) t2
        group by t2.building_id,t2.building_name
        ) t1
        group by t1.building_id,t1.building_name
        order by t1.building_id
    </select>
    <select id="listTaxIncomeTrendByCommunity"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        WITH data AS (SELECT community, datekey
        FROM (
        select code from dm_pm
        where code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and type = 'Community'
        ) AS communites(community),
        (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month)
        select da.community as community_code, concat(substr(da.datekey,1,4),'-',substr(da.datekey,5,6)) as datekey,
        (select name from dm_pm where type = 'Community' and code =
        da.community) as name,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                sum(bq_ss)
            </when>
            <otherwise>
                sum(bq_jdsr)
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select app.community_code,app.project_type,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt
        else 0 end as bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id
        group by app.community_code, datekey, apet.yskm_dm,apet.bq_amt,app.project_type
        ) t1
        on da.community = t1.community_code and da.datekey = t1.datekey
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.community in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and t1.project_type = #{req.projectType}
        </if>
        group by da.datekey, da.community
        order by datekey,community
    </select>
    <select id="listTaxIncomeTrendByProject"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        WITH data AS (SELECT project_id, project_name, datekey
        FROM (
        select id,project_name from ads_pm_project
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS projects(project_id),
        (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month)
        select da.project_id , concat(substr(da.datekey,1,4),'-',substr(da.datekey,5,6)) as datekey,da.project_name as
        name ,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                sum(bq_ss)
            </when>
            <otherwise>
                sum(bq_jdsr)
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select app.id as project_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.project_id = t1.project_id and da.datekey = t1.datekey
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>

        group by da.datekey, da.project_id, da.project_name
        order by datekey,project_id
    </select>
    <select id="listTaxIncomeTrendByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        WITH data AS (SELECT building_id, building_name ,datekey
        FROM ( select id,building_name from ads_pm_building
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>) AS buildings(building_id),
        (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month)
        select da.building_id , concat(substr(da.datekey,1,4),'-',substr(da.datekey,5,6)) as datekey,da.building_name as
        name ,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                sum(bq_ss)
            </when>
            <otherwise>
                sum(bq_jdsr)
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select apb.id as building_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.building_id = t1.building_id and da.datekey = t1.datekey
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.building_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>

        group by da.datekey, da.building_id, da.building_name
        order by datekey,building_id
    </select>
    <select id="listTaxIncomeTrendByCommunityByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        select t1.community_code,sum(t1.taxIncome) as taxIncome, t1.name,t1.month as datekey From (
        WITH data AS (
        SELECT community, datekey
        FROM (
        select code from dm_pm
        where code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and type = 'Community'
        ) AS communites(community),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey)
        )
        select da.community as community_code,
        case when substr(da.datekey,5,6) in ('01', '02',
        '03') then concat(substr(da.datekey,1,4),'Q1')
        when substr(da.datekey,5,6) in ('04','05',
        '06') then concat(substr(da.datekey,1,4),'Q2')
        when substr(da.datekey,5,6) in ('07','08',
        '09') then concat(substr(da.datekey,1,4),'Q3')
        when substr(da.datekey,5,6) in ('10','11',
        '12') then concat(substr(da.datekey,1,4),'Q4') else '' end as month
        , (select name from dm_pm where type = 'Community' and code =
        da.community) as name,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                t1.bq_ss)
            </when>
            <otherwise>
                t1.bq_jdsr
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select app.community_code,app.project_type,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt
        else 0 end as bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.community = t1.community_code and da.datekey = t1.datekey
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.community in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and t1.project_type = #{req.projectType}
        </if>
        ) t1
        group by t1.community_code, t1.name, t1.month
        order by t1.month,t1.community_code
    </select>
    <select id="listTaxIncomeTrendByProjectByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        select t1.project_id, t1.project_name as name, sum(t1.taxIncome) as taxIncome, t1.month as datekey from (
        WITH data AS (
        SELECT project_id, project_name, datekey
        FROM (
        select id,project_name from ads_pm_project
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS projects(project_id),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey)
        )
        select da.project_id ,
        case when substr(da.datekey,5,6) in ('01', '02',
        '03') then concat(substr(da.datekey,1,4),'Q1')
        when substr(da.datekey,5,6) in ('04','05',
        '06') then concat(substr(da.datekey,1,4),'Q2')
        when substr(da.datekey,5,6) in ('07','08',
        '09') then concat(substr(da.datekey,1,4),'Q3')
        when substr(da.datekey,5,6) in ('10','11',
        '12') then concat(substr(da.datekey,1,4),'Q4') else '' end as month
        ,
        da.project_name,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                bq_ss
            </when>
            <otherwise>
                bq_jdsr
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select app.id as project_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.project_id = t1.project_id and da.datekey = t1.datekey
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        ) t1
        group by t1.project_id, t1.project_name, t1.month
        order by t1.month,t1.project_id
    </select>
    <select id="listTaxIncomeTrendByBuildingByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        select t1.month as datekey, sum(t1.taxIncome) as taxIncome, t1.building_id, t1.building_name as name from (
        WITH data AS (
        SELECT building_id, building_name, datekey
        FROM (
        select id,building_name from ads_pm_building
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS buildings(building_id),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey)
        )
        select da.building_id ,
        case when substr(da.datekey,5,6) in ('01', '02',
        '03') then concat(substr(da.datekey,1,4),'Q1')
        when substr(da.datekey,5,6) in ('04','05',
        '06') then concat(substr(da.datekey,1,4),'Q2')
        when substr(da.datekey,5,6) in ('07','08',
        '09') then concat(substr(da.datekey,1,4),'Q3')
        when substr(da.datekey,5,6) in ('10','11',
        '12') then concat(substr(da.datekey,1,4),'Q4') else '' end as month
        ,
        da.building_name,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                bq_ss
            </when>
            <otherwise>
                bq_jdsr
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select apb.id as building_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.building_id = t1.building_id and da.datekey = t1.datekey
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.building_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>

        ) t1
        group by t1.month,t1.building_id, t1.building_name
        order by t1.month,t1.building_id
    </select>
    <select id="listTaxIncomeTrendByCommunityByYear"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        WITH data AS (SELECT community, datekey
        FROM (
        select code from dm_pm
        where code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and type = 'Community'
        ) AS communites(community),
        (SELECT TO_CHAR(DATE_TRUNC('year', #{req.currentDate}::date) - (n * INTERVAL '1 year'),
        'YYYY') ::varchar AS datekey
        FROM generate_series(0, 3) n) AS month)
        select da.community as community_code, da.datekey, (select name from dm_pm where type = 'Community' and code =
        da.community) as name,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                sum(bq_ss)
            </when>
            <otherwise>
                sum(bq_jdsr)
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select app.community_code,app.project_type,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt
        else 0 end as bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.community = t1.community_code and da.datekey = substr(t1.datekey,1,4)
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.community in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and t1.project_type = #{req.projectType}
        </if>
        group by da.datekey, da.community
        order by datekey,community
    </select>
    <select id="listTaxIncomeTrendByProjectByYear"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        WITH data AS (SELECT project_id, project_name, datekey
        FROM (
        select id,project_name from ads_pm_project
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS projects(project_id),
        (SELECT TO_CHAR(DATE_TRUNC('year', #{req.currentDate}::date) - (n * INTERVAL '1 year'),
        'YYYY') ::varchar AS datekey
        FROM generate_series(0, 3) n) AS month)
        select da.project_id , da.datekey,da.project_name as name ,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                sum(bq_ss)
            </when>
            <otherwise>
                sum(bq_jdsr)
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select app.id as project_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.project_id = t1.project_id and da.datekey = substr(t1.datekey,1,4)
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>

        group by da.datekey, da.project_id, da.project_name
        order by datekey,project_id
    </select>
    <select id="listTaxIncomeTrendByBuildingByYear"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeTrendAnalyzeVo">
        WITH data AS (SELECT building_id, building_name ,datekey
        FROM ( select id,building_name from ads_pm_building
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>) AS buildings(building_id),
        (SELECT TO_CHAR(DATE_TRUNC('year', #{req.currentDate}::date) - (n * INTERVAL '1 year'),
        'YYYY') ::varchar AS datekey
        FROM generate_series(0, 3) n) AS month)
        select da.building_id , da.datekey,da.building_name as name ,
        <choose>
            <when test="req.incomeType != null and req.incomeType == 1">
                sum(bq_ss)
            </when>
            <otherwise>
                sum(bq_jdsr)
            </otherwise>
        </choose>
        as taxIncome
        from data da
        left join (select apb.id as building_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.building_id = t1.building_id and da.datekey = substr(t1.datekey,1,4)
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and da.building_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>

        group by da.datekey, da.building_id, da.building_name
        order by datekey,building_id
    </select>
    <select id="listEntStructMoveByCommunity" resultType="com.zjhh.economy.vo.analyzereport.EntStructAssembleVo">
        select t2.community_code as code,(select name from dm_pm where type = 'Community' and code = t2.community_code)
        as
        name ,sum(t2.moveInCount) as moveInCount, sum(t2.moveOutCount) as moveOutCount from (
        select code as community_code, 0 as moveInCount, 0 as moveOutCount
        from dm_pm
        where type = 'Community'
        and code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        union all
        select t1.community_code, sum(moveInCount) as moveInCount, sum(moveOutCount) as moveOutCount
        From (select app.community_code,
        reality_move_out_date,
        check_in_date,
        case when reality_move_out_date is null then count(1) else 0 end as moveInCount,
        case when reality_move_out_date is not null then count(1) else 0 end as moveOutCount
        from ads_pm_room_enterprise apem
        left join ads_pm_building apb on apem.building_id = apb.id
        left join ads_pm_project app on app.id = apb.project_id
        where
        (to_char(apem.check_in_date, 'YYYY') &lt;= #{req.currentYear} or to_char(apem.reality_move_out_date, 'YYYY')
        &lt;= #{req.currentYear})
        <if test="req.codes != null and req.codes.size > 0">
            and app.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and app.project_type = #{req.projectType}
        </if>
        group by app.community_code, reality_move_out_date, check_in_date) t1
        group by t1.community_code
        ) t2
        group by t2.community_code
        order by t2.community_code
    </select>

    <select id="listEntStructMoveByProject" resultType="com.zjhh.economy.vo.analyzereport.EntStructAssembleVo">
        select t2.project_id as code,t2.project_name as name, sum(t2.moveInCount) as moveInCount, sum(t2.moveOutCount)
        as moveOutCount from (
        select id as project_id, project_name, 0 as moveInCount, 0 as moveOutCount
        from ads_pm_project
        where 1=1
        and id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        union all
        select t1.project_id,t1.project_name, sum(moveInCount) as moveInCount, sum(moveOutCount) as moveOutCount
        From (select app.id as project_id,app.project_name,
        reality_move_out_date,
        check_in_date,
        case when reality_move_out_date is null then count(1) else 0 end as moveInCount,
        case when reality_move_out_date is not null then count(1) else 0 end as moveOutCount
        from ads_pm_room_enterprise apem
        left join ads_pm_building apb on apem.building_id = apb.id
        left join ads_pm_project app on app.id = apb.project_id
        where
        (to_char(apem.check_in_date, 'YYYY') &lt; #{req.currentYear} or to_char(apem.reality_move_out_date, 'YYYY') &lt;=
        #{req.currentYear})
        <if test="req.codes != null and req.codes.size > 0">
            and app.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by app.id,app.project_name,check_in_date, reality_move_out_date) t1
        group by t1.project_id,t1.project_name
        ) t2
        group by t2.project_id,t2.project_name
        order by t2.project_id
    </select>
    <select id="listEntStructMoveByBuilding" resultType="com.zjhh.economy.vo.analyzereport.EntStructAssembleVo">
        select t2.building_id as code,t2.building_name as name, sum(t2.moveInCount) as moveInCount, sum(t2.moveOutCount)
        as moveOutCount from (
        select id as building_id, building_name, 0 as moveInCount, 0 as moveOutCount
        from ads_pm_building
        where 1=1
        and id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        union all
        select t1.building_id,t1.building_name, sum(moveInCount) as moveInCount, sum(moveOutCount) as moveOutCount
        From (select apb.id as building_id,apb.building_name,
        reality_move_out_date,
        check_in_date,
        case when reality_move_out_date is null then count(1) else 0 end as moveInCount,
        case when reality_move_out_date is not null then count(1) else 0 end as moveOutCount
        from ads_pm_room_enterprise apem
        left join ads_pm_building apb on apem.building_id = apb.id
        where
        (to_char(apem.check_in_date, 'YYYY') &lt;= #{req.currentYear} or to_char(apem.reality_move_out_date, 'YYYY')
        &lt;= #{req.currentYear})
        <if test="req.codes != null and req.codes.size > 0">
            and apb.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by apb.id,apb.building_name,reality_move_out_date, check_in_date) t1
        group by t1.building_id,t1.building_name
        ) t2
        group by t2.building_id,t2.building_name
        order by t2.building_id
    </select>

    <select id="listEntStructLocaledByCommunity" resultType="com.zjhh.economy.vo.analyzereport.EntStructAssembleVo">
        select (select name from dm_pm where type = 'Community' and code = t3.community_code) as name,
        sum(t3.localedCount) as localedCount, sum(t3.notLocaledCount) as notLocaledCount From (
        select code as community_code, 0 as localedCount, 0 as notLocaledCount
        from dm_pm
        where type = 'Community'
        and code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        union all
        select t2.community_code, sum(t2.localedCount) as localedCount, sum(t2.notLocaledCount) as notLocaledCount
        from (select app.community_code,
        case when ape.territorialized = true then count(1) else 0 end as localedCount,
        case when ape.territorialized = false then count(1) else 0 end as notLocaledCount
        from (select building_id, enterprise_id
        from ads_pm_room_enterprise apre
        left join ads_pm_enterprise ape on ape.id = apre.enterprise_id
        where apre.moved = false
        group by building_id, enterprise_id) t1
        left join ads_pm_building apb on apb.id = t1.building_id
        left join ads_pm_enterprise ape on ape.id = t1.enterprise_id
        left join ads_pm_project app on app.id = apb.project_id
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and app.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and app.project_type = #{req.projectType}
        </if>
        group by app.community_code, ape.territorialized) t2
        group by t2.community_code
        ) t3
        group by t3.community_code
    </select>

    <select id="listEntStructLocaledByProject"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructAssembleVo">
        select t3.project_id,t3.project_name as name, sum(t3.localedCount) as localedCount, sum(t3.notLocaledCount) as
        notLocaledCount From (
        select id as project_id,project_name, 0 as localedCount, 0 as notLocaledCount
        from ads_pm_project
        where
        id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        union all
        select t2.project_id,t2.project_name, sum(t2.localedCount) as localedCount, sum(t2.notLocaledCount) as
        notLocaledCount
        from (select app.id as project_id,app.project_name,
        case when ape.territorialized = true then count(1) else 0 end as localedCount,
        case when ape.territorialized = false then count(1) else 0 end as notLocaledCount
        from (select building_id, enterprise_id
        from ads_pm_room_enterprise apre
        left join ads_pm_enterprise ape on ape.id = apre.enterprise_id
        where apre.moved = false
        group by building_id, enterprise_id) t1
        left join ads_pm_building apb on apb.id = t1.building_id
        left join ads_pm_enterprise ape on ape.id = t1.enterprise_id
        left join ads_pm_project app on app.id = apb.project_id
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and app.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by app.id,app.project_name, ape.territorialized) t2
        group by t2.project_id,t2.project_name
        ) t3
        group by t3.project_id,t3.project_name
    </select>
    <select id="listEntStructLocaledByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructAssembleVo">
        select t3.building_id,t3.building_name as name, sum(t3.localedCount) as localedCount, sum(t3.notLocaledCount) as
        notLocaledCount From (
        select id as building_id,building_name, 0 as localedCount, 0 as notLocaledCount
        from ads_pm_building
        where
        id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        union all
        select t2.building_id,t2.building_name, sum(t2.localedCount) as localedCount, sum(t2.notLocaledCount) as
        notLocaledCount
        from (select apb.id as building_id,apb.building_name,
        case when ape.territorialized = true then count(1) else 0 end as localedCount,
        case when ape.territorialized = false then count(1) else 0 end as notLocaledCount
        from (select building_id, enterprise_id
        from ads_pm_room_enterprise apre
        left join ads_pm_enterprise ape on ape.id = apre.enterprise_id
        where apre.moved = false
        group by building_id, enterprise_id) t1
        left join ads_pm_building apb on apb.id = t1.building_id
        left join ads_pm_enterprise ape on ape.id = t1.enterprise_id
        left join ads_pm_project app on app.id = apb.project_id
        where 1=1
        <if test="req.codes != null and req.codes.size > 0">
            and apb.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by apb.id,apb.building_name, ape.territorialized) t2
        group by t2.building_id,t2.building_name
        ) t3
        group by t3.building_id,t3.building_name
    </select>
    <select id="listEntStructTrendByCommunity" resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select concat(substr(t1.datekey,1,4),'-',substr(t1.datekey,5,6)) as datekey,
        t1.community as code,
        (select name from dm_pm where code = t1.community and type = 'Community') as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT community, datekey
        FROM (select code
        from dm_pm
        where code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and type = 'Community') AS communites(community),
        (SELECT TO_CHAR(DATE_TRUNC('month', #{req.currentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month)
        select da.datekey,
        da.community,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and (TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey or reality_move_out_date is null)
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where
        1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and moved = true
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as entCount
        From data da) t1
        order by t1.datekey,t1.community
    </select>
    <select id="listEntStructTrendByProject" resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select concat(substr(t1.datekey,1,4),'-',substr(t1.datekey,5,6)) as datekey,
        t1.project_id as code,
        (select project_name from ads_pm_project where id = t1.project_id ) as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT project_id, datekey
        FROM (select id
        from ads_pm_project
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS projects(project_id),
        (SELECT TO_CHAR(DATE_TRUNC('month', #{req.currentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month)
        select da.datekey,
        da.project_id,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and (TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey or reality_move_out_date is null)
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and moved = true
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as entCount
        From data da) t1
        order by t1.datekey,t1.project_id
    </select>
    <select id="listEntStructTrendByBuilding" resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select concat(substr(t1.datekey,1,4),'-',substr(t1.datekey,5,6)) as datekey,
        t1.building_id as code,
        (select building_name from ads_pm_building where id = t1.building_id ) as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT building_id, datekey
        FROM (select id
        from ads_pm_building
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS buildings(building_id),
        (SELECT TO_CHAR(DATE_TRUNC('month', #{req.currentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n) AS month)
        select datekey,
        da.building_id,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and (TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey or reality_move_out_date is null)

        and building_id = da.building_id) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and moved = true
        ) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id = da.building_id) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id = da.building_id) as entCount
        From data da) t1
        order by t1.datekey,t1.building_id
    </select>
    <select id="listEntStructTrendByCommunityByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select t2.name, t2.code, t2.month as datekey, sum(t2.localedCount) as localedCount, sum(t2.moveOutCount) as
        moveOutCount, sum(t2.moveInCount) as moveInCount, sum(t2.notLocaledCount) as notLocaledCount from (
        select case when substr(t1.datekey,5,6) in ('01', '02',
        '03') then concat(substr(t1.datekey,1,4),'Q1')
        when substr(t1.datekey,5,6) in ('04','05',
        '06') then concat(substr(t1.datekey,1,4),'Q2')
        when substr(t1.datekey,5,6) in ('07','08',
        '09') then concat(substr(t1.datekey,1,4),'Q3')
        when substr(t1.datekey,5,6) in ('10','11',
        '12') then concat(substr(t1.datekey,1,4),'Q4') else '' end as month,
        t1.community as code,
        (select name from dm_pm where code = t1.community and type = 'Community') as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT community, datekey
        FROM (select code
        from dm_pm
        where code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and type = 'Community') AS communites(community),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey) )
        select datekey,
        da.community,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey
        and reality_move_out_date is null

        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where
        1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as entCount
        From data da) t1
        order by t1.community
        ) t2
        group by t2.code,t2.name,t2.month
        order by t2.month,t2.code
    </select>
    <select id="listEntStructTrendByProjectByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select t2.name, t2.code, t2.month as datekey, sum(t2.localedCount) as localedCount, sum(t2.moveOutCount) as
        moveOutCount, sum(t2.moveInCount) as moveInCount, sum(t2.notLocaledCount) as notLocaledCount from (
        select case when substr(t1.datekey,5,6) in ('01', '02',
        '03') then concat(substr(t1.datekey,1,4),'Q1')
        when substr(t1.datekey,5,6) in ('04','05',
        '06') then concat(substr(t1.datekey,1,4),'Q2')
        when substr(t1.datekey,5,6) in ('07','08',
        '09') then concat(substr(t1.datekey,1,4),'Q3')
        when substr(t1.datekey,5,6) in ('10','11',
        '12') then concat(substr(t1.datekey,1,4),'Q4') else '' end as month,
        t1.project_id as code,
        (select project_name from ads_pm_project where id = t1.project_id) as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT project_id, datekey
        FROM (select id
        from ads_pm_project
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS projects(project_id),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey) )
        select datekey,
        da.project_id,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey
        and reality_move_out_date is null
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as entCount
        From data da) t1
        order by t1.project_id
        ) t2
        group by t2.code,t2.name,t2.month
        order by t2.month,t2.code
    </select>
    <select id="listEntStructTrendByBuildingByQuarter"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select t2.name, t2.code, t2.month as datekey, sum(t2.localedCount) as localedCount, sum(t2.moveOutCount) as
        moveOutCount, sum(t2.moveInCount) as moveInCount, sum(t2.notLocaledCount) as notLocaledCount from (
        select case when substr(t1.datekey,5,6) in ('01', '02',
        '03') then concat(substr(t1.datekey,1,4),'Q1')
        when substr(t1.datekey,5,6) in ('04','05',
        '06') then concat(substr(t1.datekey,1,4),'Q2')
        when substr(t1.datekey,5,6) in ('07','08',
        '09') then concat(substr(t1.datekey,1,4),'Q3')
        when substr(t1.datekey,5,6) in ('10','11',
        '12') then concat(substr(t1.datekey,1,4),'Q4') else '' end as month,
        t1.building_id as code,
        (select building_name from ads_pm_building where id = t1.building_id) as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT building_id, datekey
        FROM (select id
        from ads_pm_building
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS buildings(building_id),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey) )
        select datekey,
        da.building_id,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey
        and reality_move_out_date is null
        and building_id = da.building_id) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and building_id = da.building_id) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id = da.building_id) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where datekey = da.datekey
        and building_id = da.building_id) as entCount
        From data da) t1
        order by t1.building_id
        ) t2
        group by t2.code,t2.name,t2.month
        order by t2.month,t2.code
    </select>
    <select id="listEntStructTrendByCommunityByYear"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select t1.datekey,
        t1.community as code,
        (select name from dm_pm where code = t1.community and type = 'Community') as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT community, datekey
        FROM (select code
        from dm_pm
        where code in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and type = 'Community') AS communites(community),
        (SELECT TO_CHAR(DATE_TRUNC('year', #{req.currentDate}::date) - (n * INTERVAL '1 year'),
        'YYYY') ::varchar AS datekey
        FROM generate_series(0, 3) n) AS month)
        select datekey,
        da.community,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey
        and reality_move_out_date is null
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where
        1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where substr(datekey,1,4) = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where substr(datekey,1,4) = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id in
        (select id from ads_pm_project where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        and community_code = da.community))) as entCount
        From data da) t1
        order by t1.datekey,t1.community
    </select>
    <select id="listEntStructTrendByProjectByYear"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select t1.datekey,
        t1.project_id as code,
        (select project_name from ads_pm_project where id = t1.project_id ) as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT project_id, datekey
        FROM (select id
        from ads_pm_project
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS projects(project_id),
        (SELECT TO_CHAR(DATE_TRUNC('year', #{req.currentDate}::date) - (n * INTERVAL '1 year'),
        'YYYY') ::varchar AS datekey
        FROM generate_series(0, 3) n) AS month)
        select da.datekey,
        da.project_id,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey
        and reality_move_out_date is null
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as moveInCount,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where substr(datekey,1,4) = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where substr(datekey,1,4) = da.datekey
        and building_id in (select id
        from ads_pm_building
        where project_id = da.project_id
        )) as entCount
        From data da) t1
        order by t1.datekey,t1.project_id
    </select>
    <select id="listEntStructTrendByBuildingByYear"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendVo">
        select t1.datekey,
        t1.building_id as code,
        (select building_name from ads_pm_building where id = t1.building_id ) as name,
        t1.localedCount,
        t1.moveOutCount,
        t1.entCount,
        t1.moveInCount,
        t1.entCount - t1.localedCount as notLocaledCount
        from (WITH data AS (SELECT building_id, datekey
        FROM (select id
        from ads_pm_building
        where id in
        <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        ) AS buildings(building_id),
        (SELECT TO_CHAR(DATE_TRUNC('year', #{req.currentDate}::date) - (n * INTERVAL '1 year'),
        'YYYY') ::varchar AS datekey
        FROM generate_series(0, 3) n) AS month)
        select da.datekey,
        da.building_id,
        (select count(1)
        from ads_pm_room_enterprise
        where TO_CHAR(check_in_date, 'YYYYMM') &lt;= da.datekey
        and TO_CHAR(reality_move_out_date, 'YYYYMM') != da.datekey
        and reality_move_out_date is null
        and building_id = da.building_id) as moveInCount,
        (select count(1)
        f from ads_pm_room_enterprise
        where TO_CHAR(reality_move_out_date, 'YYYYMM') &lt;= da.datekey
        and reality_move_out_date is not null
        and building_id = da.building_id) as moveOutCount,
        (select sum(territorialized_ent_count)
        from ads_pm_ent_territorialized
        where substr(datekey,1,4) = da.datekey
        and building_id = da.building_id) as localedCount,
        (select sum(ent_count)
        from ads_pm_ent_territorialized
        where substr(datekey,1,4) = da.datekey
        and building_id = da.building_id) as entCount
        From data da) t1
        order by t1.datekey,t1.building_id
    </select>
    <select id="listEntStructTrendTableByCommunity"
            resultMap="entStructTrend">
        select '0'                               as code,
               '全街道'                          as name,
               sum(t3.legalLocaledCount)         as legalLocaledCount,
               sum(t3.individualLocaledCount)    as individualLocaledCount,
               sum(t3.industryLocaledCount)      as industryLocaledCount,
               sum(t3.legalNotLocaledCount)      as legalNotLocaledCount,
               sum(t3.individualNotLocaledCount) as individualNotLocaledCount,
               sum(t3.industryNotLocaledCount)   as industryNotLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) +
               sum(t3.industryLocaledCount)      as localedCount,
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as notLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) + sum(t3.industryLocaledCount) +
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as totalCount
        From (select app.community_code,
                     t2.territorialized,
                     t2.unit_property,
                     case
                         when t2.territorialized = true and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryNotLocaledCount
              from (select *
                    from ads_pm_enterprise ape
                             left join (select building_id, enterprise_id
                                        from ads_pm_room_enterprise apre
                                        where moved = false
                                        group by building_id, enterprise_id) t1 on t1.enterprise_id = ape.id
                    where ape.id = t1.enterprise_id) t2
                       left join ads_pm_building apb on apb.id = t2.building_id
                       left join ads_pm_project app on app.id = apb.project_id
              group by app.community_code, t2.territorialized, t2.unit_property) t3

        union all
        select t3.community_code                 as code,
               (select name
                from dm_pm
                where type = 'Community'
                  and code = t3.community_code)  as name,
               sum(t3.legalLocaledCount)         as legalLocaledCount,
               sum(t3.individualLocaledCount)    as individualLocaledCount,
               sum(t3.industryLocaledCount)      as industryLocaledCount,
               sum(t3.legalNotLocaledCount)      as legalNotLocaledCount,
               sum(t3.individualNotLocaledCount) as individualNotLocaledCount,
               sum(t3.industryNotLocaledCount)   as industryNotLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) +
               sum(t3.industryLocaledCount)      as localedCount,
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as notLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) + sum(t3.industryLocaledCount) +
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as totalCount
        From (select app.community_code,
                     t2.territorialized,
                     t2.unit_property,
                     case
                         when t2.territorialized = true and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryNotLocaledCount
              from (select *
                    from ads_pm_enterprise ape
                             left join (select building_id, enterprise_id
                                        from ads_pm_room_enterprise apre
                                        where moved = false
                                        group by building_id, enterprise_id) t1 on t1.enterprise_id = ape.id
                    where ape.id = t1.enterprise_id) t2
                       left join ads_pm_building apb on apb.id = t2.building_id
                       left join ads_pm_project app on app.id = apb.project_id
              group by app.community_code, t2.territorialized, t2.unit_property) t3
        group by t3.community_code

    </select>
    <select id="listEntStructTrendTableByProject"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendTableVo">
        select t3.project_id                     as code,
               t3.project_name                   as name,
               sum(t3.legalLocaledCount)         as legalLocaledCount,
               sum(t3.individualLocaledCount)    as individualLocaledCount,
               sum(t3.industryLocaledCount)      as industryLocaledCount,
               sum(t3.legalNotLocaledCount)      as legalNotLocaledCount,
               sum(t3.individualNotLocaledCount) as individualNotLocaledCount,
               sum(t3.industryNotLocaledCount)   as industryNotLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) +
               sum(t3.industryLocaledCount)      as localedCount,
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as notLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) + sum(t3.industryLocaledCount) +
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as totalCount,
               case
                   when (select count(1) from ads_pm_building where project_id = t3.project_id) > 0 then true
                   else false end                as hasChildren,
               3                                 as compareType
        From (select app.id         as project_id,
                     app.project_name,
                     app.serial_no,
                     t2.territorialized,
                     t2.unit_property,
                     case
                         when t2.territorialized = true and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryNotLocaledCount
              from (select *
                    from ads_pm_enterprise ape
                             left join (select building_id, enterprise_id
                                        from ads_pm_room_enterprise apre
                                        where moved = false
                                        group by building_id, enterprise_id) t1 on t1.enterprise_id = ape.id
                    where ape.id = t1.enterprise_id) t2
                       left join ads_pm_building apb on apb.id = t2.building_id
                       left join ads_pm_project app on app.id = apb.project_id
              where app.community_code = #{code}
              group by app.id, t2.territorialized, t2.unit_property, app.project_name, app.serial_no) t3
        group by t3.project_id, t3.project_name, t3.serial_no
        order by t3.serial_no
    </select>
    <select id="listEntStructTrendTableByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.EntStructTrendTableVo">
        select t3.building_id                    as code,
               t3.building_name                  as name,
               sum(t3.legalLocaledCount)         as legalLocaledCount,
               sum(t3.individualLocaledCount)    as individualLocaledCount,
               sum(t3.industryLocaledCount)      as industryLocaledCount,
               sum(t3.legalNotLocaledCount)      as legalNotLocaledCount,
               sum(t3.individualNotLocaledCount) as individualNotLocaledCount,
               sum(t3.industryNotLocaledCount)   as industryNotLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) +
               sum(t3.industryLocaledCount)      as localedCount,
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as notLocaledCount,
               sum(t3.legalLocaledCount) + sum(t3.individualLocaledCount) + sum(t3.industryLocaledCount) +
               sum(t3.legalNotLocaledCount) + sum(t3.individualNotLocaledCount) +
               sum(t3.industryNotLocaledCount)   as totalCount
        From (select apb.id         as building_id,
                     apb.building_name,
                     t2.territorialized,
                     t2.unit_property,
                     case
                         when t2.territorialized = true and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualLocaledCount,
                     case
                         when t2.territorialized = true and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 1 then coalesce(count(1), 0)
                         else 0 end as legalNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 2 then coalesce(count(1), 0)
                         else 0 end as individualNotLocaledCount,
                     case
                         when t2.territorialized = false and unit_property = 3 then coalesce(count(1), 0)
                         else 0 end as industryNotLocaledCount
              from (select *
                    from ads_pm_enterprise ape
                             left join (select building_id, enterprise_id
                                        from ads_pm_room_enterprise apre
                                        where moved = false
                                        group by building_id, enterprise_id) t1 on t1.enterprise_id = ape.id
                    where ape.id = t1.enterprise_id) t2
                       left join ads_pm_building apb on apb.id = t2.building_id
                       left join ads_pm_project app on app.id = apb.project_id
              where apb.project_id = #{req.code}
              group by apb.id, t2.territorialized, t2.unit_property, apb.building_name) t3
        group by t3.building_id, t3.building_name
    </select>
    <select id="listTaxIncomeDetailTableByCommunity"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeDetailTableVo">
        select '0' as code,
        '全街道' as name,
        t2.datekey,
        sum(t2.zb) as zb,
        sum(t2.taxIncome) as taxIncome,
        sum(t2.streetTaxIncome) as streetTaxIncome,
        sum(t2.totalTaxIncome) as totalTaxIncome,
        sum(t2.totalStreetTaxIncome) as totalStreetTaxIncome,
        case when sum(t2.tqTaxIncome) = 0 then 0 else round((sum(t2.taxIncome) - sum(t2.tqTaxIncome)) /
        sum(t2.tqTaxIncome) * 100 , 2) end as taxIncomeZf,
        case when sum(t2.tqStreetTaxIncome) = 0 then 0 else round((sum(t2.streetTaxIncome) - sum(t2.tqStreetTaxIncome))
        / sum(t2.tqStreetTaxIncome) * 100 , 2) end as streetTaxIncomeZf,
        case when sum(t2.tqTotalTaxIncome) = 0 then 0 else round((sum(t2.totalTaxIncome) - sum(t2.tqTotalTaxIncome)) /
        sum(t2.tqTotalTaxIncome) * 100 , 2) end as totalTaxIncomeZf,
        case when sum(t2.tqTotalStreetTaxIncome) = 0 then 0 else round((sum(t2.totalStreetTaxIncome) -
        sum(t2.tqTotalStreetTaxIncome)) / sum(t2.tqTotalStreetTaxIncome) * 100 , 2) end as totalStreetTaxIncomeZf
        from (select t1.*,
        sum(t1.taxIncome) over (partition by t1.community_code) as totalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.community_code) as totalStreetTaxIncome,

        case
        when sum(t1.streetTaxIncome) over (partition by t1.community_code) = 0 then 0
        else
        round(sum(t1.taxIncome) over (partition by t1.community_code) /
        sum(t1.streetTaxIncome) over (partition by t1.community_code) * 100, 2) end as zb,
        0 as tqTaxIncome,
        0 as tqStreetTaxIncome,
        0 as tqTotalTaxIncome,
        0 as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT community, datekey
        FROM (select code
        from dm_pm
        where 1 = 1
        and type = 'Community') AS communites(community),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.community as community_code,
        da.datekey,
        (select name from dm_pm where type = 'Community' and code = da.community) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select app.community_code,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.community = t1.community_code and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.community
        order by datekey) t1
        union all
        select t1.community_code,
        (t1.datekey::int + 100)::varchar as datekey,
        t1.name,
        0 as taxIncome,
        0 as streetTaxIncome,
        0 as totalTaxIncome,
        0 as totalStreetTaxIncome,
        0 as zb,
        t1.taxIncome as tqTaxIncome,
        t1.streetTaxIncome as tqStreetTaxIncome,
        sum(t1.taxIncome) over (partition by t1.community_code) as tqTotalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.community_code) as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT community, datekey
        FROM (select code
        from dm_pm
        where 1 = 1
        and type = 'Community') AS communites(community),
        (
        <foreach collection="req.tqMonths" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.community as community_code,
        da.datekey,
        (select name from dm_pm where type = 'Community' and code = da.community) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select app.community_code,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.community = t1.community_code and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.community
        order by datekey) t1) t2
        group by t2.datekey
        union all
        select t2.community_code as code,
        t2.name,
        t2.datekey,
        sum(t2.zb) as zb,
        sum(t2.taxIncome) as taxIncome,
        sum(t2.streetTaxIncome) as streetTaxIncome,
        sum(t2.totalTaxIncome) as totalTaxIncome,
        sum(t2.totalStreetTaxIncome) as totalStreetTaxIncome,
        case when sum(t2.tqTaxIncome) = 0 then 0 else round((sum(t2.taxIncome) - sum(t2.tqTaxIncome)) /
        sum(t2.tqTaxIncome) * 100 , 2) end as taxIncomeZf,
        case when sum(t2.tqStreetTaxIncome) = 0 then 0 else round((sum(t2.streetTaxIncome) - sum(t2.tqStreetTaxIncome))
        / sum(t2.tqStreetTaxIncome) * 100 , 2) end as streetTaxIncomeZf,
        case when sum(t2.tqTotalTaxIncome) = 0 then 0 else round((sum(t2.totalTaxIncome) - sum(t2.tqTotalTaxIncome)) /
        sum(t2.tqTotalTaxIncome) * 100 , 2) end as totalTaxIncomeZf,
        case when sum(t2.tqTotalStreetTaxIncome) = 0 then 0 else round((sum(t2.totalStreetTaxIncome) -
        sum(t2.tqTotalStreetTaxIncome)) / sum(t2.tqTotalStreetTaxIncome) * 100 , 2) end as totalStreetTaxIncomeZf
        from (select t1.*,
        sum(t1.taxIncome) over (partition by t1.community_code) as totalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.community_code) as totalStreetTaxIncome,

        case
        when sum(t1.streetTaxIncome) over (partition by t1.community_code) = 0 then 0
        else
        round(sum(t1.taxIncome) over (partition by t1.community_code) /
        sum(t1.streetTaxIncome) over (partition by t1.community_code) * 100, 2) end as zb,
        0 as tqTaxIncome,
        0 as tqStreetTaxIncome,
        0 as tqTotalTaxIncome,
        0 as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT community, datekey
        FROM (select code
        from dm_pm
        where 1 = 1
        and type = 'Community') AS communites(community),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.community as community_code,
        da.datekey,
        (select name from dm_pm where type = 'Community' and code = da.community) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select app.community_code,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.community = t1.community_code and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.community
        order by datekey) t1
        union all
        select t1.community_code,
        (t1.datekey::int + 100)::varchar as datekey,
        t1.name,
        0 as taxIncome,
        0 as streetTaxIncome,
        0 as totalTaxIncome,
        0 as totalStreetTaxIncome,
        0 as zb,
        t1.taxIncome as tqTaxIncome,
        t1.streetTaxIncome as tqStreetTaxIncome,
        sum(t1.taxIncome) over (partition by t1.community_code) as tqTotalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.community_code) as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT community, datekey
        FROM (select code
        from dm_pm
        where 1 = 1
        and type = 'Community') AS communites(community),
        (
        <foreach collection="req.tqMonths" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.community as community_code,
        da.datekey,
        (select name from dm_pm where type = 'Community' and code = da.community) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select app.community_code,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.community = t1.community_code and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.community
        order by datekey) t1) t2
        group by t2.community_code, t2.name, t2.datekey

    </select>
    <select id="listTaxIncomeDetailTableByProject"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeDetailTableVo">
        select * from (select t2.project_id as code,
        t2.name,
        t2.datekey,
        3 as compareType,
        sum(t2.zb) as zb,
        sum(t2.taxIncome) as taxIncome,
        sum(t2.streetTaxIncome) as streetTaxIncome,
        sum(t2.totalTaxIncome) as totalTaxIncome,
        sum(t2.totalStreetTaxIncome) as totalStreetTaxIncome,
        case when sum(t2.tqTaxIncome) = 0 then 0 else round((sum(t2.taxIncome) - sum(t2.tqTaxIncome)) /
        sum(t2.tqTaxIncome) * 100 , 2) end as taxIncomeZf,
        case when sum(t2.tqStreetTaxIncome) = 0 then 0 else round((sum(t2.streetTaxIncome) - sum(t2.tqStreetTaxIncome))
        / sum(t2.tqStreetTaxIncome) * 100 , 2) end as streetTaxIncomeZf,
        case when sum(t2.tqTotalTaxIncome) = 0 then 0 else round((sum(t2.totalTaxIncome) - sum(t2.tqTotalTaxIncome)) /
        sum(t2.tqTotalTaxIncome) * 100 , 2) end as totalTaxIncomeZf,
        case when sum(t2.tqTotalStreetTaxIncome) = 0 then 0 else round((sum(t2.totalStreetTaxIncome) -
        sum(t2.tqTotalStreetTaxIncome)) / sum(t2.tqTotalStreetTaxIncome) * 100 , 2) end as totalStreetTaxIncomeZf,
        case when (select count(1) from ads_pm_building where project_id = t2.project_id) > 0 then true else false end
        as hasChildren
        from (select t1.*,
        sum(t1.taxIncome) over (partition by t1.project_id) as totalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.project_id) as totalStreetTaxIncome,

        case
        when sum(t1.streetTaxIncome) over (partition by t1.project_id) = 0 then 0
        else
        round(sum(t1.taxIncome) over (partition by t1.project_id) /
        sum(t1.streetTaxIncome) over (partition by t1.project_id) * 100, 2) end as zb,
        0 as tqTaxIncome,
        0 as tqStreetTaxIncome,
        0 as tqTotalTaxIncome,
        0 as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT project_id, datekey
        FROM (select id
        from ads_pm_project
        where community_code = #{req.code}
        ) AS projects(project_id),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.project_id ,
        da.datekey,
        (select project_name from ads_pm_project where id = da.project_id) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select app.id as project_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.project_id = t1.project_id and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.project_id
        order by datekey) t1
        union all
        select t1.project_id,
        (t1.datekey::int + 100)::varchar as datekey,
        t1.name,
        0 as taxIncome,
        0 as streetTaxIncome,
        0 as totalTaxIncome,
        0 as totalStreetTaxIncome,
        0 as zb,
        t1.taxIncome as tqTaxIncome,
        t1.streetTaxIncome as tqStreetTaxIncome,
        sum(t1.taxIncome) over (partition by t1.project_id) as tqTotalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.project_id) as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT project_id, datekey
        FROM (select id
        from ads_pm_project
        where community_code = #{req.code}
        ) AS projects(project_id),
        (
        <foreach collection="req.tqMonths" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.project_id,
        da.datekey,
        (select project_name from ads_pm_project where id = da.project_id) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select app.id as project_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.project_id = t1.project_id and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.project_id
        order by datekey) t1) t2
        group by t2.project_id, t2.name, t2.datekey) t1 left join ads_pm_project t2 on t1.code = t2.id
        order by t2.serial_no
    </select>
    <select id="listTaxIncomeDetailTableByBuilding"
            resultType="com.zjhh.economy.vo.analyzereport.TaxIncomeDetailTableVo">
        select t2.building_id as code,
        t2.name,
        t2.datekey,
        sum(t2.zb) as zb,
        sum(t2.taxIncome) as taxIncome,
        sum(t2.streetTaxIncome) as streetTaxIncome,
        sum(t2.totalTaxIncome) as totalTaxIncome,
        sum(t2.totalStreetTaxIncome) as totalStreetTaxIncome,
        case when sum(t2.tqTaxIncome) = 0 then 0 else round((sum(t2.taxIncome) - sum(t2.tqTaxIncome)) /
        sum(t2.tqTaxIncome) * 100 , 2) end as taxIncomeZf,
        case when sum(t2.tqStreetTaxIncome) = 0 then 0 else round((sum(t2.streetTaxIncome) - sum(t2.tqStreetTaxIncome))
        / sum(t2.tqStreetTaxIncome) * 100 , 2) end as streetTaxIncomeZf,
        case when sum(t2.tqTotalTaxIncome) = 0 then 0 else round((sum(t2.totalTaxIncome) - sum(t2.tqTotalTaxIncome)) /
        sum(t2.tqTotalTaxIncome) * 100 , 2) end as totalTaxIncomeZf,
        case when sum(t2.tqTotalStreetTaxIncome) = 0 then 0 else round((sum(t2.totalStreetTaxIncome) -
        sum(t2.tqTotalStreetTaxIncome)) / sum(t2.tqTotalStreetTaxIncome) * 100 , 2) end as totalStreetTaxIncomeZf
        from (select t1.*,
        sum(t1.taxIncome) over (partition by t1.building_id) as totalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.building_id) as totalStreetTaxIncome,

        case
        when sum(t1.streetTaxIncome) over (partition by t1.building_id) = 0 then 0
        else
        round(sum(t1.taxIncome) over (partition by t1.building_id) /
        sum(t1.streetTaxIncome) over (partition by t1.building_id) * 100, 2) end as zb,
        0 as tqTaxIncome,
        0 as tqStreetTaxIncome,
        0 as tqTotalTaxIncome,
        0 as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT building_id, datekey
        FROM (select id
        from ads_pm_building
        where project_id = #{req.code}
        ) AS buildings(building_id),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.building_id ,
        da.datekey,
        (select building_name from ads_pm_building where id = da.building_id) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select apb.id as building_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.building_id = t1.building_id and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.building_id
        order by datekey) t1
        union all
        select t1.building_id,
        (t1.datekey::int + 100)::varchar as datekey,
        t1.name,
        0 as taxIncome,
        0 as streetTaxIncome,
        0 as totalTaxIncome,
        0 as totalStreetTaxIncome,
        0 as zb,
        t1.taxIncome as tqTaxIncome,
        t1.streetTaxIncome as tqStreetTaxIncome,
        sum(t1.taxIncome) over (partition by t1.building_id) as tqTotalTaxIncome,
        sum(t1.streetTaxIncome) over (partition by t1.building_id) as tqTotalStreetTaxIncome
        From (WITH data AS (SELECT building_id, datekey
        FROM (select id
        from ads_pm_building
        where project_id = #{req.code}
        ) AS buildings(building_id),
        (
        <foreach collection="req.tqMonths" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select da.building_id,
        da.datekey,
        (select building_name from ads_pm_building where id = da.building_id) as name,
        coalesce(sum(bq_ss), 0) as taxIncome,
        coalesce(sum(bq_jdsr), 0) as streetTaxIncome
        from data da
        left join (select apb.id as building_id,datekey,case when apet.yskm_dm = 'ss' then apet.bq_amt else 0 end as
        bq_ss,
        case when apet.yskm_dm = 'jdsr' then apet.bq_amt else 0 end as bq_jdsr
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_building apb on apet.building_id = apb.id
        left join ads_pm_project app on apb.project_id = app.id) t1
        on da.building_id = t1.building_id and da.datekey = t1.datekey
        where 1 = 1
        group by da.datekey, da.building_id
        order by datekey) t1) t2
        group by t2.building_id, t2.name, t2.datekey
    </select>
    <select id="listTaxStructVo" resultType="com.zjhh.economy.vo.analyzereport.TaxStructVo">
        select t4.tax_type,
        t4.tax_name,
        t4.datekey,
        t4.ss,
        t4.jdsr,
        t4.totalSs,
        t4.totalJdsr,
        t4.ssZf,
        t4.jdsrZf,
        case when tqTotalSs = 0 then 0 else round((totalSs - tqTotalSs) / tqTotalSs * 100, 2) end as totalSsZf,
        case when tqTotalJdsr = 0 then 0 else round((totalJdsr - tqTotalJdsr) / tqTotalJdsr * 100, 2) end as totalJdsrZf
        from (select '0' as tax_type,
        '合计' as tax_name,
        t3.datekey,
        sum(t3.ss) as ss,
        sum(t3.jdsr) as jdsr,
        sum(sum(t3.ss)) over (partition by 0) as totalSs,
        sum(sum(t3.jdsr)) over (partition by 0) as totalJdsr,
        sum(sum(t3.tqSs)) over (partition by 0) as tqTotalSs,
        sum(sum(t3.tqJdsr)) over (partition by 0) as tqTotalJdsr,
        case
        when sum(t3.tqSs) = 0 then 0
        else round((sum(t3.ss) - sum(t3.tqSs)) / sum(t3.tqSs) * 100, 2) end as ssZf,
        case
        when sum(t3.tqJdsr) = 0 then 0
        else round((sum(t3.jdsr) - sum(t3.tqJdsr)) / sum(t3.tqJdsr) * 100,
        2) end as jdsrZf
        from (WITH data AS (SELECT tax_type, tax_name, datekey
        FROM (select 'zzs' as tax_type, '增值税' as tax_name
        union all
        select 'grsds' as tax_type, '个人所得税' as tax_name
        union all
        select 'qysds' as tax_type, '企业所得税' as tax_name
        union all
        select 'cswhjss' as tax_type, '城建税' as tax_name
        union all
        select 'fcs' as tax_type, '房产税' as tax_name
        union all
        select 'cztdsys' as tax_type, '土地使用税' as tax_name
        union all
        select 'tdzzs' as tax_type, '土地增值税' as tax_name
        union all
        select 'yhs' as tax_type, '印花税' as tax_name
        union all
        select 'gdzys' as tax_type, '耕地占用税' as tax_name
        union all
        select 'xfs' as tax_type, '消费税' as tax_name
        union all
        select 'zys' as tax_type, '资源税' as tax_name
        union all
        select 'hjbhs' as tax_type, '环境保护税' as tax_name
        union all
        select 'gyzy' as tax_type, '国有资产有偿使用收入' as tax_name) AS taxs(tax_type),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select tax_type,
        tax_name,
        datekey,
        0 as ss,
        0 as jdsr,
        0 as tqSs,
        0 as tqJdsr,
        0 as totalSs,
        0 as totalJdsr,
        0 as tqTotalSs,
        0 as tqTotalJdsr
        from data
        union all
        select t2.tax_type,
        tax_name,
        datekey,
        ss,
        jdsr,
        tqSs,
        tqJdsr,
        sum(ss) over (partition by tax_type) as totalSs,
        sum(jdsr) over (partition by tax_type) as totalJdsr,
        sum(tqSs) over (partition by tax_type) as tqTotalSs,
        sum(tqJdsr) over (partition by tax_type) as tqTotalJdsr
        from (select t1.tax_type,
        t1.tax_name,
        t1.datekey,
        sum(t1.ss) as ss,
        sum(t1.jdsr) as jdsr,
        sum(t1.tqSs) as tqSs,
        sum(t1.tqJdsr) as tqJdsr
        from (select tax_type, tax_name, datekey, sum(ss) as ss, sum(jdsr) as jdsr, 0 as tqSs, 0 as tqJdsr
        from ads_pm_tax_struct_analyze
        where datekey in
        <foreach collection="req.months" item="month" open="(" close=")" separator=",">
            #{month}
        </foreach>
        group by tax_type, tax_name, datekey
        union all
        select tax_type,
        tax_name,
        datekey,
        0 as ss,
        0 as jdsr,
        (select coalesce(sum(ss), 0)
        from ads_pm_tax_struct_analyze
        where datekey = (aptsa.datekey::decimal - 100)::varchar
        and tax_type = aptsa.tax_type) as tqSs,
        (select coalesce(sum(jdsr), 0)
        from ads_pm_tax_struct_analyze
        where datekey = (aptsa.datekey::decimal - 100)::varchar
        and tax_type = aptsa.tax_type) as tqJdsr
        from ads_pm_tax_struct_analyze aptsa
        where datekey in
        <foreach collection="req.months" item="month" open="(" close=")" separator=",">
            #{month}
        </foreach>
        group by tax_type, tax_name, datekey) t1
        group by t1.tax_type, t1.tax_name, t1.datekey) t2) t3
        group by t3.datekey) t4
        union all
        select t5.tax_type,
        t5.tax_name,
        t5.datekey,
        t5.ss,
        t5.jdsr,
        t5.totalSs,
        t5.totalJdsr,
        t5.ssZf,
        t5.jdsrZf,
        case when tqTotalSs = 0 then 0 else round((totalSs - tqTotalSs) / tqTotalSs * 100, 2) end as totalSsZf,
        case when tqTotalJdsr = 0 then 0 else round((totalJdsr - tqTotalJdsr) / tqTotalJdsr * 100, 2) end as totalJdsrZf
        from (
        select t4.tax_type,
        tax_name,
        datekey,
        ss,
        jdsr,
        tqSs,
        tqJdsr,
        sum(ss) over (partition by tax_type) as totalSs,
        sum(jdsr) over (partition by tax_type) as totalJdsr,
        sum(tqSs) over (partition by tax_type) as tqTotalSs,
        sum(tqJdsr) over (partition by tax_type) as tqTotalJdsr ,
        case
        when sum(tqSs) = 0 then 0
        else round((sum(ss) - sum(tqSs)) / sum(tqSs) * 100, 2) end as ssZf,
        case
        when sum(tqJdsr) = 0 then 0
        else round((sum(jdsr) - sum(tqJdsr)) / sum(tqJdsr) * 100,
        2) end as jdsrZf
        from (
        select t3.tax_type,
        t3.tax_name,
        t3.datekey,
        sum(t3.ss) as ss,
        sum(t3.jdsr) as jdsr,
        sum(t3.tqSs) as tqSs,
        sum(t3.tqJdsr) as tqJdsr

        from (WITH data AS (SELECT tax_type, tax_name, datekey
        FROM (select 'zzs' as tax_type, '增值税' as tax_name
        union all
        select 'grsds' as tax_type, '个人所得税' as tax_name
        union all
        select 'qysds' as tax_type, '企业所得税' as tax_name
        union all
        select 'cswhjss' as tax_type, '城建税' as tax_name
        union all
        select 'fcs' as tax_type, '房产税' as tax_name
        union all
        select 'cztdsys' as tax_type, '土地使用税' as tax_name
        union all
        select 'tdzzs' as tax_type, '土地增值税' as tax_name
        union all
        select 'yhs' as tax_type, '印花税' as tax_name
        union all
        select 'gdzys' as tax_type, '耕地占用税' as tax_name
        union all
        select 'xfs' as tax_type, '消费税' as tax_name
        union all
        select 'zys' as tax_type, '资源税' as tax_name
        union all
        select 'hjbhs' as tax_type, '环境保护税' as tax_name
        union all
        select 'gyzy' as tax_type, '国有资产有偿使用收入' as tax_name) AS taxs(tax_type),
        (
        <foreach collection="req.months" item="month" separator=" UNION ALL ">
            SELECT #{month} AS datekey
        </foreach>
        ) AS months(datekey))
        select tax_type,
        tax_name,
        datekey,
        0 as ss,
        0 as jdsr,
        0 as tqSs,
        0 as tqJdsr
        from data
        union all
        select t2.tax_type,
        tax_name,
        datekey,
        ss,
        jdsr,
        tqSs,
        tqJdsr
        from (select t1.tax_type,
        t1.tax_name,
        t1.datekey,
        sum(t1.ss) as ss,
        sum(t1.jdsr) as jdsr,
        sum(t1.tqSs) as tqSs,
        sum(t1.tqJdsr) as tqJdsr
        from (select tax_type,
        tax_name,
        datekey,
        sum(ss) as ss,
        sum(jdsr) as jdsr,
        (select coalesce(sum(ss), 0)
        from ads_pm_tax_struct_analyze
        where datekey = (aptsa.datekey::decimal - 100)::varchar
        and tax_type = aptsa.tax_type) as tqSs,
        (select coalesce(sum(jdsr), 0)
        from ads_pm_tax_struct_analyze
        where datekey = (aptsa.datekey::decimal - 100)::varchar
        and tax_type = aptsa.tax_type) as tqJdsr
        from ads_pm_tax_struct_analyze aptsa
        where datekey in
        <foreach collection="req.months" item="month" open="(" close=")" separator=",">
            #{month}
        </foreach>
        group by tax_type, tax_name, datekey) t1
        group by t1.tax_type, t1.tax_name, t1.datekey) t2) t3
        group by t3.tax_type, t3.tax_name, t3.datekey
        ) t4
        group by t4.tax_type, tax_name, datekey, ss, jdsr, tqSs, tqJdsr
        ) t5


    </select>
    <select id="getTaxMaxDate" resultType="java.lang.String">
        select max_date
        from dm_gy_module_date
        where target_page = 'ENTERPRISE_TAX_MON'
    </select>
    <select id="listBuildingSettledEntBasicVo"
            resultType="com.zjhh.economy.vo.report.BuildingSettledEntBasicVo">
        select
        project_name,
        enterprise_name,
        (select zs_mc from dm_gy_page_style where type_dm = '11' and zs_dm = institutional_name) as institutional_name,
        registered_address,
        address_code,
        business_scope,
        industry_code,
        legal_person,
        telephone,
        mobilephone,
        found_date,
        settled_date,
        business_registration_no,
        registered_capital,
        tax_registration_no,
        investment_source,
        business_status,
        business_area,
        settled_area,
        engaged_num,
        revenue,
        national_tax,
        local_tax,
        change_situation,
        sq_amt
        from ads_pm_dr_glb a
        where a.datekey = #{req.endDate}
        <if test="req.project != null and req.project.size > 0">
            and a.project_code IN
            <foreach collection="req.project" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.institutional != null and req.institutional.size > 0">
            and a.institutional_name IN
            <foreach collection="req.institutional" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.businessScope != null and req.businessScope != ''">
            and a.business_scope like concat('%',#{req.businessScope},'%')
        </if>
        <if test="req.foundDateStart != null and req.foundDateStart != ''">
            and substr(a.found_date,1,6) >= #{req.foundDateStart}
        </if>
        <if test="req.foundDateEnd != null and req.foundDateEnd != ''">
            and substr(a.found_date,1,6) &lt;= #{req.foundDateEnd}
        </if>
        <if test="req.settledDateStart != null and req.settledDateStart != ''">
            and substr(a.settled_date,1,6) >= #{req.settledDateStart}
        </if>
        <if test="req.settledDateEnd != null and req.settledDateEnd != ''">
            and substr(a.settled_date,1,6) &lt;= #{req.settledDateEnd}
        </if>
        <if test="req.businessStatus != null and req.businessStatus.size > 0">
            and a.business_status IN
            <foreach collection="req.businessStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.changeSituation != null and req.changeSituation.size > 0">
            and a.change_situation IN
            <foreach collection="req.changeSituation" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.businessAreaMin != null ">
            and business_area >= #{req.businessAreaMin}
        </if>
        <if test="req.businessAreaMax != null ">
            and business_area &lt;= #{req.businessAreaMax}
        </if>
        <if test="req.settledAreaMin != null ">
            and settled_area >= #{req.settledAreaMin}
        </if>
        <if test="req.settledAreaMax != null ">
            and settled_area &lt;= #{req.settledAreaMax}
        </if>
        <if test="req.localTaxMin != null">
            and local_tax >= #{req.localTaxMin}
        </if>
        <if test="req.localTaxMax != null ">
            and local_tax &lt;= #{req.localTaxMax}
        </if>

        <if test="req.registeredCapitalMin != null ">
            and registered_capital >= #{req.registeredCapitalMin}
        </if>
        <if test="req.registeredCapitalMax != null ">
            and registered_capital &lt;= #{req.registeredCapitalMax}
        </if>
        order by a.xh

    </select>
    <select id="listBuildingInvestigationVo" resultType="com.zjhh.economy.vo.report.BuildingInvestigationVo">
        select datekey,
               project_code,
               project_name,
               project_xh,
               address,
               division_code,
               project_type,
               tenements,
               organization_code,
               tenements_registered_address,
               tenements_contact,
               tenements_phone,
               strata_fee_unit,
               operation_time,
               land_area,
               building_area,
               work_area,
               working_area,
               work_vacant_area,
               business_area,
               business_settled_area,
               business_vacant_area,
               underground_area,
               other_area,
               parking_number,
               charges,
               settled_total,
               settled__fr,
               settled_cy,
               settled_gt,
               registered_total,
               registered_fr,
               registered_cy,
               registered_gt,
               employee,
               operating_income,
               total_taxes,
               national_tax,
               local_taxes,
               street_contacts,
               jd_phone,
               community_contacts,
               sq_hone,
               building_contacts,
               ly_phone,
               changes,
               round(settled_rate::numeric, 2)    as settledRate,
               round(registered_rate::numeric, 2) as registeredRate
        from ads_pm_project_information a
        where a.datekey = #{req.endDate}
        order by project_xh
    </select>
    <select id="listBuildingDynamicInfoVo" resultType="com.zjhh.economy.vo.report.BuildingDynamicInfoVo">
        select datekey,
               serial_no,
               project_name,
               operation_time,
               business_area,
               area,
               round(settled_rate::numeric, 2) as settledRate,
               frcygt,
               round(zcs::numeric, 2)          as zcs,
               round(zcs_rate::numeric, 2)     as zcs_rate,
               round(ss::numeric, 2)           as ss,
               round(ss_unit::numeric, 2)      as ss_unit,
               round(cfdcss::numeric, 2)       as cfdcss,
               round(cfdcss_unit::numeric, 2)  as cfdcss_unit,
               project_code
        from ads_pm_project_dynami
        where datekey::varchar = #{req.endDate}
        order by length (serial_no) desc, serial_no
    </select>
    <select id="listBuildingDynamicAnalyzeVo" resultType="com.zjhh.economy.vo.report.BuildingDynamicAnalyzeVo">
        select datekey,
               serial_no,
               project_name,
               operation_time,
               business_area,
               area,
               round(settled_rate::numeric, 2)    as settled_rate,
               round(settled_rate_sq::numeric, 2) as settled_rate_sq,
               round(settled_rate_zf::numeric, 2) as settled_rate_zf,
               frcygt,
               round(zcs::numeric, 2)             as zcs,
               round(zcs_rate::numeric, 2)        as zcs_rate,
               round(zcs_rate_sq::numeric, 2)     as zcs_rate_sq,
               round(ss::numeric, 2)              as ss,
               round(ss_sq::numeric, 2)           as ss_sq,
               round(ss_zf::numeric, 2)           as ss_zf,
               round(ss_unit::numeric, 2)         as ss_unit,
               round(ss_unit_sq::numeric, 2)      as ss_unit_sq,
               round(ss_unit_zf::numeric, 2)      as ss_unit_zf,
               round(cfdcss::numeric, 2)          as cfdcss,
               round(cfdcss_sq::numeric, 2)       as cfdcss_sq,
               round(cfdcss_zf::numeric, 2)       as cfdcss_zf,
               round(cfdcss_unit::numeric, 2)     as cfdcss_unit,
               round(cfdcss_unit_sq::numeric, 2)  as cfdcss_unit_sq,
               round(cfdcss_unit_zf::numeric, 2)  as cfdcss_unit_zf
        from ads_pm_project_analys a
        where a.datekey::varchar = #{req.endDate}
        order by length (serial_no:: varchar) desc, serial_no:: varchar
    </select>
    <select id="listEntBusinessInfoVo" resultType="com.zjhh.economy.vo.report.EntBusinessInfoVo">

        select
        type,
        enterprise_id,
        enterprise_name,
        serial_no,
        uscc,
        legal_person,
        found_date,
        enterprise_type_code,
        enterprise_type_name,
        registered_capital,
        industry_code,
        industry_name,
        business_scope,
        residence,
        phone,
        datekey,
        update_time
        from ads_pm_registration_form a
        where 1=1
        <if test="req.foundDateStart != null and req.foundDateStart != ''">
            and substr(a.found_date,1,6) >= #{req.foundDateStart}
        </if>
        <if test="req.foundDateEnd != null and req.foundDateEnd != ''">
            and substr(a.found_date,1,6) &lt;= #{req.foundDateEnd}
        </if>
        <if test="req.enterpriseType != null and req.enterpriseType.size > 0">
            and a.enterprise_type_code IN
            <foreach collection="req.enterpriseType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.industry != null and req.industry.size > 0">
            and a.industry_code IN
            <foreach collection="req.industry" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.registeredCapitalMin != null and req.registeredCapitalMin != ''">
            and registered_capital >= #{req.registeredCapitalMin}
        </if>
        <if test="req.registeredCapitalMax != null and req.registeredCapitalMax != ''">
            and registered_capital &lt;= #{req.registeredCapitalMax}
        </if>
        <if test="req.businessScope != null and req.businessScope != ''">
            and a.business_scope like concat('%',#{req.businessScope},'%')
        </if>
        <if test="req.residence != null and req.residence != ''">
            and a.residence like concat('%',#{req.residence},'%')
        </if>
        order by datekey desc
    </select>

</mapper>