<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmImportRecordMapper">
    <select id="listImportSettle" resultType="com.zjhh.economy.vo.ImportSettleVo">
        select id,
               enterprise_name,
               uscc,
               legal_person,
               phone,
               ent_contact_person,
               ent_phone,
               project_name,
               building_name,
               floor_no,
               room_no,
               check_in_date,
               expect_move_out_date,
               area,
               renovation_start_date,
               renovation_end_date,
               remark,
               reality_move_out_date,
               destination,
               move_out_reason,
               match_result,
               import_id
        from ads_pm_import_record
        where import_id = #{importId}
    </select>
    <select id="listImportMove" resultType="com.zjhh.economy.vo.ImportMoveVo">
        select id,
               enterprise_name,
               uscc,
               legal_person,
               phone,
               ent_contact_person,
               ent_phone,
               project_name,
               building_name,
               floor_no,
               room_no,
               check_in_date,
               expect_move_out_date,
               area,
               renovation_start_date,
               renovation_end_date,
               remark,
               reality_move_out_date,
               destination,
               move_out_reason,
               match_result,
               import_id
        from ads_pm_import_record
        where import_id = #{importId}
    </select>
</mapper>