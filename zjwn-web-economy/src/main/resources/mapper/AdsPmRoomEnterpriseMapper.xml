<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmRoomEnterpriseMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , room_id, enterprise_id, check_in_date, expect_move_out_date, area, renovation_start_date, renovation_end_date, remark, reality_move_out_date, moved, create_user, create_time, update_time
    </sql>
    <select id="pageSettleInfo" resultType="com.zjhh.economy.vo.SettleInfoListVo">
        select apre.id,
               app.project_name,
               apb.building_name,
               apf.floor_name,
               apf.id             as floor_id,
               apf.plane_img_id,
               apf.plane_config_id,
               apr.room_no,
               apre.check_in_date,
               apre.expect_move_out_date,
               apre.reality_move_out_date,
               apre.area,
               apre.remark,
               apre.document_id,
               apre.moved
        from ads_pm_room_enterprise apre
                 left join ads_pm_project app on apre.project_id = app.id
                 left join ads_pm_building apb on apre.building_id = apb.id
                 left join ads_pm_floor apf on apre.floor_id = apf.id
                 left join ads_pm_room apr on apre.room_id = apr.id
        where enterprise_id = #{req.enterpriseId}
        order by apre.check_in_date
    </select>
    <select id="listSettleInfo" resultType="com.zjhh.economy.vo.SettleInfoMenuVo">
        select concat(app.project_name, apb.building_name, concat(apf.floor_name, 'F-'), apr.room_no) as settleInfo,
        apre.id,
        app.project_name,
        apb.building_name,
        apf.floor_name,
        apr.room_no,
        apre.check_in_date,
        apre.expect_move_out_date,
        apre.area,
        apre.remark,
        apre.document_id,
        apre.moved
        from ads_pm_room_enterprise apre
        left join ads_pm_project app on apre.project_id = app.id
        left join ads_pm_building apb on apre.building_id = apb.id
        left join ads_pm_floor apf on apre.floor_id = apf.id
        left join ads_pm_room apr on apre.room_id = apr.id
        where enterprise_id = #{enterpriseId}
        and moved = false
        <if test="ids != null and ids.size > 0">
            and apre.id not in
            <foreach collection="ids" item="settleId" open="(" close=")" separator=",">
                #{settleId}
            </foreach>
        </if>
    </select>

    <select id="listSettleInfoShow" resultType="com.zjhh.economy.vo.SettleInfoShowVo">
        select (select name from dm_pm where type = 'Community' and code = app.community_code) as communityName,app.project_name, apb.building_name, apf.floor_name, apr.room_no, sum(area) as area
        from ads_pm_room_enterprise ape
                 left join ads_pm_project app on app.id = ape.project_id
                 left join ads_pm_building apb on ape.building_id = apb.id
                 left join ads_pm_floor apf on ape.floor_id = apf.id
                 left join ads_pm_room apr on ape.room_id = apr.id
        where ape.enterprise_id = #{enterpriseId}
          and ape.moved = false
        group by app.community_code,app.project_name, apb.building_name, apf.floor_name, apr.room_no
    </select>
    <select id="getStartCheckInDate" resultType="java.time.LocalDate">
        select min(check_in_date)
        from ads_pm_room_enterprise
        where enterprise_id = #{enterpriseId}

    </select>
    <select id="getRentSummary" resultType="com.zjhh.economy.vo.SettleInfoAnalyseVo">
        select count(1) as roomCount, sum(area) as area
        from ads_pm_room_enterprise
        where moved = false
          and enterprise_id = #{enterpriseId}
    </select>
    <select id="listSettleTrendVo" resultType="com.zjhh.economy.vo.SettleTrendVo">
        --         select * from
    </select>
    <select id="calculateSize" resultType="java.math.BigDecimal">
        select coalesce(sum(area), 0)
        from ads_pm_room_enterprise
        where moved = false
          and enterprise_id = #{enterpriseId}
    </select>
    <select id="calculateRoom" resultType="java.lang.Integer">
        select count(1)
        from ads_pm_room_enterprise
        where moved = false
          and enterprise_id = #{enterpriseId}
    </select>
    <select id="getSettleDetailInfo" resultType="com.zjhh.economy.vo.SettleInfoDetailVo">
        select apre.id,
               apre.room_id,
               apr.room_no,
               apre.project_id,
               app.project_name,
               apre.building_id,
               apb.building_name,
               apre.floor_id,
               apf.floor_name,
               apre.enterprise_id,
               apre.check_in_date::date,
               apre.expect_move_out_date::date,
               apre.area,
               apre.renovation_start_date::date,
               apre.renovation_end_date::date,
               apre.remark,
               ad.id    as documentId,
               ad.title as documentName,
               ad.size as  size
        From ads_pm_room_enterprise apre
            left join ads_document ad
        on apre.document_id = ad.id
            left join ads_pm_project app on apre.project_id = app.id
            left join ads_pm_building apb on apre.building_id = apb.id
            left join ads_pm_floor apf on apre.floor_id = apf.id
            left join ads_pm_room apr on apre.room_id = apr.id
        where apre.id = #{id}
    </select>
    <select id="getWorkPlatformRoom" resultType="com.zjhh.economy.vo.WorkPlatformRoomVo">
        select sum(totalBusinessArea)                    as totalBusinessArea,
               sum(totalBusinessArea) - sum(settledArea) as totalEmptyArea,
               case
                   when sum(totalBusinessArea) = 0 then 0
                   else
                       round((sum(totalBusinessArea) - sum(settledArea)) / sum(totalBusinessArea) * 100,
                             2) end                      as emptyRate
        from (select sum(business_area) as totalBusinessArea, 0 as settledArea
              from ads_pm_building
              union all
              select 0 as totalBusinessArea, sum(area)
              from ads_pm_room_enterprise
              where moved = false) t1
    </select>
    <select id="getWorkPlatformEntManage" resultType="com.zjhh.economy.vo.WorkPlatformEntManageVo">
        select sum(t2.localCont) + sum(t2.notLocalCount) as totalEntCount,
               case
                   when sum(t2.localCont) + sum(t2.notLocalCount) = 0 then 0
                   else round(sum(t2.localCont) / (sum(t2.notLocalCount) + sum(t2.localCont)) * 100,
                              2) end                     as localRate,
               (select count(1)
                from (select distinct enterprise_id
                      from ads_pm_room_enterprise
                      where TO_CHAR(check_in_date, 'YYYYMM') = #{currentYearMonth}
                      group by enterprise_id) t1) as entMonthCount
        from (select case when t1.territorialized = false then count(1) else 0 end as notLocalCount,
                     case when t1.territorialized = true then count(1) else 0 end  as localCont
              from (select distinct apre.enterprise_id, ape.territorialized
                    from ads_pm_room_enterprise apre
                             left join ads_pm_enterprise ape on apre.enterprise_id = ape.id
                    where apre.moved = false
                    group by apre.enterprise_id, ape.territorialized) t1
              group by territorialized) t2
    </select>
    <select id="listEntMoveLessThanThirsty" resultType="java.util.Map">
        select apre.enterprise_id as enterpriseId,
               apre.building_id as buildingId,
               apb.building_name as buildingName,
               (select enterprise_name from ads_pm_enterprise where id = apre.enterprise_id) as enterpriseName,
               apre.room_id as roomId,
               apr.room_no as roomNo,
               ((apre.expect_move_out_date::date - #{currentDate}::date ) ::int) as days
        from ads_pm_room_enterprise apre
        left join ads_pm_building apb on apre.building_id = apb.id
        left join ads_pm_room apr on apre.room_id = apr.id
        where
            30
            > (apre.expect_move_out_date:: date - #{currentDate}:: date):: int
        AND apre.expect_move_out_date::date  > #{currentDate}::date
        and apre.expect_move_out_date is not null
    </select>
    <select id="listEntConfirmMove" resultType="java.util.Map">
        select t1.enterprise_id                                                            as enterpriseid,
               (select enterprise_name from ads_pm_enterprise where id = t1.enterprise_id) as enterprisename,
               t1.room_id                                                                  as roomid,
               t3.room_no                                                                  as roomno,
               t1.building_id     as buildingId,
               t2.building_name                                                            as buildingname
        from ads_pm_room_enterprise t1
                 left join ads_pm_building t2 on t1.building_id = t2.id
                 left join ads_pm_room t3 on t3.id = t1.room_id
        where #{days} > (#{currentDate}:: date - t1.expect_move_out_date:: date):: int
        and moved = false
        and t3.room_no is not null and t2.building_name is not null
    </select>
    <select id="listEmptyRoomMoreThanSixty" resultType="java.util.Map">

        select t1.id as roomId, t1.room_no as roomNo, t1.floor_name as floorName, t1.project_name as projectName, t1.building_name as buildingName
        from (select apr.id,
                     apr.room_no,
                     app.project_name,
                     apf.floor_name,
                     apb.building_name,
                     case
                         when (select count(1) from v_enterprise where room_id = apr.id) = 0 then apr.create_time
                         else (select max(reality_move_out_date)
                               from v_enterprise
                               where room_id = apr.id
                                 and moved = true) end as
                         realDate
              from ads_pm_room apr
                       left join ads_pm_floor apf on apr.floor_id = apf.id
                       left join ads_pm_building apb on apf.building_id = apb.id
                       left join ads_pm_project app on apb.project_id = app.id
              where apr.open_hired = true) t1
        where (#{currentDate}::date - t1.realDate::date) > 60


    </select>
    <select id="listNoInputTax" resultType="java.util.Map">
        select id               as enterpriseId,
               enterprise_name as enterpriseName,
               #{lastYearMonth} as datekey
        from ads_pm_enterprise
        where territorialized = true
          and id not in (Select distinct enterprise_id from ads_pm_enterprise_tax_fkm where datekey = #{lastYearMonth})
          and id in (select distinct enterprise_id from ads_pm_room_enterprise where moved = false)
    </select>
    <select id="getBriefRegisterByTotalCount" resultType="java.lang.Integer">
        select coalesce(count(1),0)
        from ads_pm_enterprise
        where create_time::date between #{startDate}::date and #{endDate}::date
    </select>
    <select id="getBriefRegisterBySettledCount" resultType="java.lang.Integer">
        select coalesce(count(1),0)
        from (select distinct enterprise_id
              from ads_pm_room_enterprise
              where enterprise_id in
                    (select id from ads_pm_enterprise where create_time::date between #{startDate}::date and #{endDate}::date)) t1
    </select>
    <select id="getBriefRegisterBySettleRoomCount" resultType="java.lang.Integer">
        select coalesce(count(1),0)
        from (select room_id
              from ads_pm_room_enterprise
              where enterprise_id in
                    (select id from ads_pm_enterprise where create_time::date between #{startDate}::date and #{endDate}::date)) t1
    </select>
    <select id="getBriefRegisterBySettleArea" resultType="java.math.BigDecimal">
        select coalesce(sum(area),0)
        from ads_pm_room_enterprise
        where enterprise_id in (select id from ads_pm_enterprise where create_time::date between #{startDate}::date and #{endDate}::date)
    </select>
    <select id="getBriefRegisterByMoveTotalCount" resultType="java.lang.Integer">
        select coalesce(count(1),0)
        from (select distinct enterprise_id
              from ads_pm_room_enterprise
              where reality_move_out_date::date between #{startDate}:: date
                and #{endDate}:: date) t1
    </select>
    <select id="getBriefRegisterByLocalCount" resultType="java.lang.Integer">
        select coalesce(count(1),0)
        from ads_pm_enterprise
        where id in (select distinct enterprise_id
                     from ads_pm_room_enterprise
                     where reality_move_out_date
            :: date between #{startDate}:: date
          and #{endDate}:: date
            )
          and territorialized = true
    </select>
    <select id="getBriefRegisterByNotLocalCount" resultType="java.lang.Integer">
        select coalesce(count(1),0)
        from ads_pm_enterprise
        where id in (select distinct enterprise_id
                     from ads_pm_room_enterprise
                     where reality_move_out_date
            :: date between #{startDate}:: date
          and #{endDate}:: date
            )
          and territorialized = false
    </select>
    <select id="getSettleDetailInfoByRoomDetail" resultType="com.zjhh.economy.vo.RoomSettleInfoDetailVo">
        select apre.id,
               apre.room_id,
               apre.project_id,
               apre.building_id,
               apre.floor_id,
               apre.enterprise_id,
               (select enterprise_name from ads_pm_enterprise where id = apre.enterprise_id) as enterpriseName,
               apre.check_in_date::date,
               apre.expect_move_out_date::date,
               apre.area,
               apre.renovation_start_date::date,
               apre.renovation_end_date::date,
               apre.remark,
               apre.reality_move_out_date::date,
               ad.id    as documentId,
               ad.title as documentName,
               ad.size as  size
        From ads_pm_room_enterprise apre
            left join ads_document ad
        on apre.document_id = ad.id
            left join ads_pm_project app on apre.project_id = app.id
            left join ads_pm_building apb on apre.building_id = apb.id
            left join ads_pm_floor apf on apre.floor_id = apf.id
            left join ads_pm_room apr on apre.room_id = apr.id
        where apre.enterprise_id = #{enterpriseId}
        and apre.room_id = #{roomId}
        and apre.moved = false
    </select>
    <select id="listEntMoveLessThanSixty" resultType="java.util.Map">
        select apre.enterprise_id as enterpriseId,
               apre.building_id as buildingId,
               apb.building_name as buildingName,
               (select enterprise_name from ads_pm_enterprise where id = apre.enterprise_id) as enterpriseName,
               apre.room_id as roomId,
               apr.room_no as roomNo
        from ads_pm_room_enterprise apre
            left join ads_pm_building apb on apre.building_id = apb.id
            left join ads_pm_room apr on apre.room_id = apr.id
        where
            60
            > (apre.expect_move_out_date:: date - #{currentDate}:: date):: int
          AND apre.expect_move_out_date::date  > #{currentDate}::date
        and apre.expect_move_out_date is not null
    </select>
    <select id="listEntMoveLessThanNinety" resultType="java.util.Map">
        select apre.enterprise_id as enterpriseId,
               apre.building_id as buildingId,
               apb.building_name as buildingName,
               (select enterprise_name from ads_pm_enterprise where id = apre.enterprise_id) as enterpriseName,
               apre.room_id as roomId,
               apr.room_no as roomNo
        from ads_pm_room_enterprise apre
            left join ads_pm_building apb on apre.building_id = apb.id
            left join ads_pm_room apr on apre.room_id = apr.id
        where
            90
            > (apre.expect_move_out_date:: date - #{currentDate}:: date):: int
          AND apre.expect_move_out_date::date  > #{currentDate}::date
        and apre.expect_move_out_date is not null
    </select>
    <select id="getCompareSettledInfo" resultType="com.zjhh.economy.vo.EntCompareSettledInfoVo">
        select
            string_agg(distinct app.project_name, '/') as projectName,
            string_agg(distinct apb.building_name, '/') as buildingName,
            string_agg( apf.floor_name, '/') as floorName,
            string_agg( apr.room_no, '/') as roomNo,
            string_agg( round(apre.area,2)::varchar, '/') as settledArea
        from ads_pm_room_enterprise apre
                 left join ads_pm_project app on apre.project_id = app.id
                 left join ads_pm_building apb on apre.building_id = apb.id
                 left join ads_pm_floor apf on apre.floor_id = apf.id
                 left join ads_pm_room apr on apre.room_id = apr.id
        where enterprise_id = #{enterpriseId}
        and apre.moved = false

    </select>
    <select id="checkEnterpriseIsMoved" resultType="java.lang.Boolean">
        select case when count(1) > 0 then false else true end from ads_pm_room_enterprise where enterprise_id = #{enterpriseId} and moved = false
    </select>
    <select id="getMovedDate" resultType="java.time.LocalDate">
        select max(reality_move_out_date)::date from ads_pm_room_enterprise where enterprise_id = #{enterpriseId} and moved = true
    </select>
    <select id="getWorkPlatformRoomByAuth" resultType="com.zjhh.economy.vo.WorkPlatformRoomVo">
        select sum(totalBusinessArea)                    as totalBusinessArea,
               sum(totalBusinessArea) - sum(settledArea) as totalEmptyArea,
               case
                   when sum(totalBusinessArea) = 0 then 0
                   else
                       round((sum(totalBusinessArea) - sum(settledArea)) / sum(totalBusinessArea) * 100,
                             2) end                      as emptyRate
        from (select sum(business_area) as totalBusinessArea, 0 as settledArea
              from v_p_building
              where (id = #{req.id} or project_id = #{req.id})
              union all
              select 0 as totalBusinessArea, sum(area)
              from ads_pm_room_enterprise
              where moved = false
              and building_id in (select id from v_p_building
                  where
                      (id = #{req.id} or project_id = #{req.id})
                                            )
              ) t1
    </select>
    <select id="getWorkPlatformEntManageByAuth" resultType="com.zjhh.economy.vo.WorkPlatformEntManageVo">
        select coalesce(sum(t2.localCont),0) + coalesce(sum(t2.notLocalCount),0) as totalEntCount,
               case
                   when coalesce(sum(t2.localCont),0) + coalesce(sum(t2.notLocalCount),0) = 0 then 0
                   else round(sum(t2.localCont) / (sum(t2.notLocalCount) + sum(t2.localCont)) * 100,
                              2) end                     as localRate,
               (select coalesce(count(1),0)
                from (select distinct enterprise_id
                      from ads_pm_room_enterprise
                      where TO_CHAR(check_in_date, 'YYYYMM') = #{currentYearMonth}
                      and building_id in (select id from v_p_building
                                                    where (id = #{req.id} or project_id = #{req.id}))
                      group by enterprise_id) t1) as entMonthCount
        from (select case when t1.territorialized = false then count(1) else 0 end as notLocalCount,
                     case when t1.territorialized = true then count(1) else 0 end  as localCont
              from (select distinct apre.enterprise_id, ape.territorialized
                    from ads_pm_room_enterprise apre
                             left join ads_pm_enterprise ape on apre.enterprise_id = ape.id
                    where apre.moved = false
                    and apre.building_id in (select id from v_p_building where (id = #{req.id} or project_id = #{req.id}))
                    group by apre.enterprise_id, ape.territorialized) t1
              group by territorialized) t2
    </select>
    <select id="listMoveOutInfoShow" resultType="com.zjhh.economy.vo.SettleInfoShowVo">
        select (select name from dm_pm where type = 'Community' and code = app.community_code) as communityName,app.project_name, apb.building_name, apf.floor_name, apr.room_no, sum(area) as area
        from ads_pm_room_enterprise ape
                 left join ads_pm_project app on app.id = ape.project_id
                 left join ads_pm_building apb on ape.building_id = apb.id
                 left join ads_pm_floor apf on ape.floor_id = apf.id
                 left join ads_pm_room apr on ape.room_id = apr.id
        where ape.enterprise_id = #{enterpriseId}
          and ape.moved = true
        group by app.community_code, app.project_name, apb.building_name, apf.floor_name, apr.room_no
    </select>
    <select id="getCompareMoveOutInfo" resultType="com.zjhh.economy.vo.EntCompareMoveOutInfoVo">
        select
            string_agg(distinct app.project_name, '/') as moveOutProjectName,
            string_agg(distinct apb.building_name, '/') as moveOutBuildingName,
            string_agg( apf.floor_name, '/') as moveOutFloorName,
            string_agg( apr.room_no, '/') as moveOutRoomNo,
            string_agg( round(apre.area,2)::varchar, '/') as moveOutArea
        from ads_pm_room_enterprise apre
                 left join ads_pm_project app on apre.project_id = app.id
                 left join ads_pm_building apb on apre.building_id = apb.id
                 left join ads_pm_floor apf on apre.floor_id = apf.id
                 left join ads_pm_room apr on apre.room_id = apr.id
        where enterprise_id = #{enterpriseId}
          and apre.moved = true
    </select>

</mapper>
