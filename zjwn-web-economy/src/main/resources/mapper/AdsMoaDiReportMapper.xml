<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsMoaDiReportMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, datekey, report_name, report_type_code, make_mode, file_guid, create_user, create_time, unit_code, unit_name, hold1, hold2, hold3, hold4, hold5, hold6, hold7, hold8, hold9, hold10, display_info, link_module, adm_div_code, adm_div_name
    </sql>

    <select id="listDiReportFileDir" resultType="java.lang.String">
        select t2.path
        from ads_moa_di_report t1
        left join ads_document t2 on t1.file_guid = t2.id
        where t1.report_type_code in
        <foreach collection="reportTypeCodes" item="reportTypeCode" separator="," open="(" close=")">
            #{reportTypeCode}
        </foreach>
        and t1.datekey = #{datekey}
    </select>

    <select id="pageWarningReportList" resultType="com.zjhh.economy.vo.report.WarningReportListVo">
        select amdr.id as guid, amdr.datekey, unit_name, report_name as fileName, unit_code, file_guid, adc.path as file_dir
        from ads_moa_di_report amdr
        left join ads_document adc on amdr.file_guid = adc.id
        where 1=1
        <if test="req.reportName != null and req.reportName != ''">
            and report_name like concat('%',#{req.reportName},'%')
        </if>
        order by amdr.id DESC
    </select>

</mapper>
