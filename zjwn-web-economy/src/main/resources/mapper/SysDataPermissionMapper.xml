<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.DataPermissionMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_code, code, type, create_user, create_time
    </sql>

    <select id="listBuildingTree" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select t.id as key, t.building_name as title, t.project_id as parent_key, 'BUILDING' as extra, 0 as sort
        from ads_pm_building t
        union all
        select t.id as key, t.project_name as title, t.community_code as parent_key, 'PROJECT' as extra, 0 as sort
        from ads_pm_project t
        union all
        select t.code as key, t.name as title, 'root' as parent_key, 'COMMUNITY' as extra, 0 as sort
        from dm_pm t
        where t.type = 'Community'
        union all
        select 'OTHER_BUILDING' as key, '其它楼宇', 'root' as parent_key, 'BUILDING' as extra, 1 as sort
        order by sort, key
    </select>

    <select id="test" resultType="com.zjhh.economy.dao.entity.AdsPmBuilding">
        select * from v_p_building
    </select>

</mapper>
