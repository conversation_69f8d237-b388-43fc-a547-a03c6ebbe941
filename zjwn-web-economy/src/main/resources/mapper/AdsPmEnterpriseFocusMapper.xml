<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseFocusMapper">

    <select id="checkFocus" resultType="java.lang.Boolean">
        select case when count(1) > 0 then true else false
        end
        from ads_pm_enterprise_focus where enterprise_id =
        #{enterpriseId}
        and
        user_code
        =
        #{userCode}
    </select>
</mapper>
