<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.WorkPlatformWarningMapper">
    <select id="checkWorkPlatformWarning" resultType="com.zjhh.economy.dao.entity.WorkPlatformWarning">
        select *
        from work_platform_warning
        where business_id = #{businessId}
          and warning_type = #{warningType}
    </select>
    <select id="listWorkPlatformWarningRemind" resultType="com.zjhh.economy.vo.WorkPlatformWarningRemindVo">
        select id, warning_type, remind_desc, remind_date
        from work_platform_warning
        where handle_type = 1
        order by id asc,remind_date desc
         limit 5
    </select>
    <select id="pageWarningRemindList" resultType="com.zjhh.economy.vo.WarningRemindListVo">
        select id, warning_type, handle_type, remind_desc, remind_date
        from work_platform_warning
        where 1=1
        <if test="req.searchWord != null and req.searchWord != ''">
            and remind_desc like concat('%',#{req.searchWord},'%')
        </if>
          <choose>
              <when test="req.remindType != null and req.remindType.size > 0 ">
                  and warning_type = #{req.warningType}
              </when>
          <otherwise>
              and warning_type in (1,2,3)
          </otherwise>
          </choose>
        <if test="req.startDate != null and req.startDate != ''">
            and remind_date::date between #{req.startDate}::date and #{req.endDate}::date
        </if>

        order by handle_type ASC, remind_date DESC
    </select>
    <select id="listWorkPlatformWarningRemindByAuth"
            resultType="com.zjhh.economy.vo.WorkPlatformWarningRemindVo">
        select id, warning_type, remind_desc, remind_date
        from work_platform_warning t1
        where handle_type = 1
        and exists(select 1 from v_p_building t2 where t1.business_id like concat('%',t2.id,'%') and (t2.id = #{req.id} or t2.project_id = #{req.id}))
        order by id asc,remind_date desc
            limit 5
    </select>
    <select id="pageWarningRemindListByAuth" resultType="com.zjhh.economy.vo.WarningRemindListVo">
        select id, warning_type, handle_type, remind_desc, remind_date
        from work_platform_warning t1
        where 1=1
          and warning_type in (1,2,3)
          and case when warning_type = 1 or warning_type = 2 then exists(select 1 from v_p_building t2 where t1.business_id like concat('%',t2.id,'%')  and (t2.id = #{req.id} or t2.project_id = #{req.id})) else
              business_id in (select t2.id from ads_pm_room t2 left join  ads_pm_floor t3 on t2.floor_id = t3.id where t3.building_id in (select id from v_p_building where  (id = #{req.id} or project_id = #{req.id})))
              end
        order by handle_type ASC, remind_date DESC
    </select>
</mapper>