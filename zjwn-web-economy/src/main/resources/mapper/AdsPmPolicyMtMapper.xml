<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmPolicyMtMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, policy_name, policy_no, publish_agency, publish_date, written_date, policy_detail, create_time, create_user, update_time, update_user
    </sql>


    <resultMap id="policyManagementMap" type="com.zjhh.economy.vo.policymanagement.PolicyManagementVo">
        <result column="id" property="id"/>
        <result column="policy_name" property="policyName"/>
        <result column="policy_no" property="policyNo"/>
        <result column="publish_agency" property="publishAgency"/>
        <result column="publish_date" property="publishDate"/>
        <result column="written_date" property="writtenDate"/>
        <result column="policy_detail" property="policyDetail"/>
        <collection property="files" javaType="java.util.ArrayList"
                    ofType="com.zjhh.economy.vo.DocumentDetailVo">
            <id column="file_id" property="id"/>
            <result column="title" property="title"/>
            <result column="path" property="path"/>
            <result column="size" property="size"/>
        </collection>
        <collection property="cashList" javaType="java.util.ArrayList"
                    ofType="com.zjhh.economy.vo.policymanagement.PolicyManagementCashVo">
            <id column="cash_id" property="id"/>
            <result column="ent_id" property="entId"/>
            <result column="ent_name" property="entName"/>
            <result column="cash_amount" property="cashAmount"/>
            <result column="cash_date" property="cashDate"/>
            <result column="remark" property="remark"/>
        </collection>
    </resultMap>

    <select id="getPolicyManagement" resultMap="policyManagementMap">
        select t1.id,
               t1.policy_name,
               t1.policy_no,
               t1.publish_agency,
               t1.publish_date,
               t1.written_date,
               t1.policy_detail,
               t2.id as cash_id,
               t2.cash_amount,
               t2.cash_date,
               t2.remark,
               t4.id as file_id,
               t4.title,
               t4.path,
               t4.size,
               t2.ent_id,
               t5.enterprise_name as ent_name
        from ads_pm_policy_mt t1 left join ads_pm_policy_mt_cash t2 on t1.id = t2.policy_id
        left join ads_business_document t3 on t1.id = t3.business_id and document_type = 'policyManagementFile'
        left join ads_document t4 on t3.document_id = t4.id
        left join ads_pm_enterprise t5 on t2.ent_id = t5.id
        where t1.id = #{policyId}
        order by t2.id, t4.id
    </select>
    <select id="pagePolicyManagement" resultType="com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo">
        select t1.id,
        row_number() over(order by t1.publish_date desc, t1.id desc) as xh,
        t1.policy_name,
        t1.policy_no,
        t1.publish_agency,
        t1.publish_date,
        t1.policy_detail
        from ads_pm_policy_mt t1
        <where>
            <if test="req.policyName != null and req.policyName != ''.toString()">
                and t1.policy_name like concat('%', #{req.policyName}, '%')
            </if>
            <if test="req.startDate != null">
                and t1.publish_date >= #{req.startDate}
            </if>
            <if test="req.endDate != null">
                and t1.publish_date &lt;= #{req.endDate}
            </if>
        </where>
    </select>
    <select id="listPolicyManagementCash"
            resultType="com.zjhh.economy.vo.policymanagement.PolicyManagementCashVo">
        select t1.id,
               t1.ent_id,
               t2.enterprise_name as ent_name,
               t1.cash_amount,
               t1.cash_date,
               t1.remark
        from ads_pm_policy_mt_cash t1
                 inner join ads_pm_enterprise t2 on t1.ent_id = t2.id
        where t1.policy_id = #{policyId}
        order by id
    </select>
    <select id="listPolicyManagementByWorkPlatform"
            resultType="com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo">
        select t1.id,
        row_number() over(order by t1.publish_date desc, t1.id desc) as xh,
        t1.policy_name,
        t1.policy_no,
        t1.publish_agency,
        t1.publish_date,
        t1.policy_detail
        from ads_pm_policy_mt t1
        order by publish_date desc
        limit 5
    </select>
    <select id="listPolicyByCockpit" resultType="com.zjhh.economy.vo.policymanagement.PolicyManagementPageVo">
        select t1.id,
                t1.policy_name,
               t1.publish_date
        from ads_pm_policy_mt t1
        order by t1.publish_date desc
        limit 5
    </select>

</mapper>
