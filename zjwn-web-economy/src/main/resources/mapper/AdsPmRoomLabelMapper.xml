<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmRoomLabelMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, room_id, label_code
    </sql>
    <delete id="delRoomLabel">
        delete from ads_pm_room_label aprl where 1=1
        <if test="ids != null and ids.size > 0">
            and label_code not in (select code from dm_pm where id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </delete>

    <select id="listRoomLabels" resultType="com.zjhh.comm.vo.SingleSelectVo">
        select t2.code, t2.name as title, t2.code as value from ads_pm_room_label t1
            left join dm_pm t2 on t1.label_code = t2.code and t2.type = 'RoomLabel'
        where t1.room_id = #{roomId}
        order by t1.id
    </select>

</mapper>
