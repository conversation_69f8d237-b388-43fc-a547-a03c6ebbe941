<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmCommentScoreMapper">
    <select id="pageBuildingCommentDetail" resultType="com.zjhh.economy.vo.BuildingCommentDetailVo">
        select t1.id,
               dbci.xh,
               #{req.buildingId}             as buildingId,
               #{req.datekey}                as datekey,
               dbci.indicator_one,
               dbci.indicator_two,
               dbci.comment_demand,
               dbci.score,
               dbci.comment_method,
               dbci.comment_type,
               dbci.id                       as commentIndicatorId,
               #{req.buildingId}             as buildingId,
               (select building_name from ads_pm_building where id = #{req.buildingId}) as buildingName,
               (select project_name from ads_pm_project where id = (select project_id from ads_pm_building where id = #{req.buildingId})) as projectName,
               coalesce(t1.comment_score, 0) as commentScore,
               coalesce(t1.edited, true)     as edited
        from dm_building_comment_indicator dbci
                 left join (select *
                            from ads_pm_comment_score apcs
                            where building_id = #{req.buildingId}
                              and datekey = #{req.datekey}) t1 on dbci.id = t1.comment_indicator_id
        where dbci.comment_type = #{req.commentType}
        order by dbci.xh

    </select>
    <select id="pageBuildingCommentVo" resultType="com.zjhh.economy.vo.BuildingCommentVo">
        select t1.*,
               rank() over(order by
               <choose>
                   <when test="req.orderType == 1">
                       t1.totalScore
                   </when>
                   <when test="req.orderType == 2">
                       t1.buildingScore
                   </when>
                   <when test="req.orderType == 3">
                       t1.serviceScore
                   </when>
                   <when test="req.orderType == 4">
                       t1.contributeScore
                   </when>
               </choose>
                desc) as rank,
               case
                   when t1.totalScore >= 108 then 4
                   when t1.totalScore &lt;= 108 and t1.totalScore >= 96 then 3
                   when t1.totalScore &lt;= 96 and t1.totalScore >= 84 then 2
                   else 1
                   end as expectStar
        from (select apb.id                                                                          as buildingId,
                     apbc.current_star,
                     apb.building_name,
                     app.project_name,
                     (select name from dm_pm where code = app.community_code and type = 'Community') as communityName,
                     (select coalesce(sum(comment_score), 0)
                      from ads_pm_comment_score
                      where building_id = apbc.building_id
                        and datekey = apbc.datekey
                        and comment_indicator_id in
                            (select id from dm_building_comment_indicator where comment_type = 1))   as buildingScore,
        (select sum(score) from dm_building_comment_indicator where comment_type = 1) as totalBuildingScore,
                     (select coalesce(sum(comment_score), 0)
                      from ads_pm_comment_score
                      where building_id = apbc.building_id
                        and datekey = apbc.datekey
                        and comment_indicator_id in
                            (select id from dm_building_comment_indicator where comment_type = 2))   as serviceScore,
        (select sum(score) from dm_building_comment_indicator where comment_type = 2) as totalServiceScore,
                     (select coalesce(sum(comment_score), 0)
                      from ads_pm_comment_score
                      where building_id = apbc.building_id
                        and datekey = apbc.datekey
                        and comment_indicator_id in
                            (select id from dm_building_comment_indicator where comment_type = 3))   as contributeScore,
        (select sum(score) from dm_building_comment_indicator where comment_type = 3) as totalContributeScore,
                     (select coalesce( sum(comment_score),0)
                      from ads_pm_comment_score
                      where building_id = apbc.building_id
                        and datekey = apbc.datekey)                                                  as totalScore
              from ads_pm_building_comment apbc
                       left join ads_pm_building apb on apb.id = apbc.building_id
                       left join ads_pm_project app on app.id = apb.project_id
              where datekey =
                    #{req.datekey}) t1
    </select>
</mapper>