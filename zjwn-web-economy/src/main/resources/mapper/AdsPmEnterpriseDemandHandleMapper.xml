<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseDemandHandleMapper">
    <select id="listHandles" resultType="com.zjhh.economy.vo.DemandHandleVo">
        select apedh.id,
               demand_id,
              case when handle_type = 1 then '处理中'
                  when handle_type = 2 then '已处理'
                      when handle_type = 3 then '已忽略'
                          else '待处理' end as handleTypeStr,
               handle_desc,
               document_id,
               ad.title as documentName,
               ad.size as size,
               (select username from sys_user where code = apedh.user_code) as userName,
               apedh.create_time
        from ads_pm_enterprise_demand_handle apedh
        left join ads_document ad on ad.id = apedh.document_id
        where apedh.demand_id = #{demandId}
        order by apedh.handle_type ASC ,apedh.create_time DESC
    </select>
</mapper>