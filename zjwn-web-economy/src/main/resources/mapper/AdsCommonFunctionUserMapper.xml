<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsCommonFunctionUserMapper">
    <select id="listCommonFunction" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, name as title , id as value ,parent_code as parentKey from dm_common_function
    </select>
    <select id="listCommonFunctionUser" resultType="com.zjhh.economy.vo.CommonFunctionVo">
        select acfu.function_id as id,dcf.name,dcf.menu_path,dcf.query from ads_common_function_user acfu left join dm_common_function dcf on acfu.function_id = dcf.id
        left join sys_user su on su.code = acfu.user_code
        where su.code = #{userCode}

    </select>
</mapper>