<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmBuildingMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , serial_no, building_name, project_id, building_area, business_area, operation_time, building_status_code, building_type_code, head, phone, introduce, land_park_space, underground_park_space, xh, create_user, create_time, update_time
    </sql>

    <select id="findMaxXh" resultType="java.lang.Integer">
        select coalesce(max(xh), 0)
        from ads_pm_building
        where project_id = (select id from ads_pm_project where ads_pm_project.serial_no = #{projectSerialNo});
    </select>

    <select id="page" resultType="com.zjhh.economy.vo.BuildingVo">
        SELECT t1.id AS building_id,
        t1.serial_no,
        t1.building_name,
        t3.name AS community_name,
        t2.project_name,
        round(t1.building_area,2) as building_area,
        round(t1.business_area,2) as business_area,
        t1.head,
        t1.phone,
        (SELECT count(t.id) FROM ads_pm_floor t WHERE t.building_id = t1.id) AS floor_size,
        (SELECT count(t.id)
        FROM ads_pm_room t
        WHERE t.floor_id IN (SELECT tt.id FROM ads_pm_floor tt WHERE tt.building_id = t1.id)) AS room_size,
        coalesce((SELECT count((apr.id))
        FROM ads_pm_room apr
        LEFT JOIN ads_pm_room_enterprise apre ON apr.id = apre.room_id AND apre.moved = false
        WHERE apr.floor_id IN (SELECT tt.id FROM ads_pm_floor tt WHERE tt.building_id = t1.id)
        and apre.id is null), 0) AS vacant_room_size,
        round(coalesce(t1.business_area,0) - coalesce((select sum(area) from ads_pm_room_enterprise where building_id = t1.id and moved =
        false), 0),2) AS vacant_area,
        coalesce((select count(distinct apm.id)
        from ads_pm_enterprise apm
        left join ads_pm_room_enterprise apre on apm.id = apre.enterprise_id
        where apre.moved = false
        and apre.building_id in (select id from ads_pm_building)
        and apre.building_id = t1.id),
        0) as check_in_enterprise_size,
        coalesce((select count(distinct ape.id)
        from ads_pm_enterprise ape
        left join ads_pm_room_enterprise apre on ape.id = apre.enterprise_id
        where apre.moved = false
        and ape.on_scaled = true
        and apre.building_id in (select id from ads_pm_building)
        and apre.building_id = t1.id),
        0) as on_scaled_enterprise_size
        FROM v_p_building t1
        LEFT JOIN ads_pm_project t2 ON t1.project_id = t2.id
        LEFT JOIN dm_pm t3 ON t3.code = t2.community_code AND t3.type = 'Community'
        <where>
            <if test="req.buildingName != null and req.buildingName != ''">
                t1.building_name like concat('%', #{req.buildingName}, '%')
            </if>
            <if test="req.projectId != null and req.projectId != ''">
                and t1.project_id = #{req.projectId}
            </if>
            <if test="req.communityCode != null and req.communityCode != ''">
                and t2.community_code = #{req.communityCode}
            </if>
           and t1.id != 'OTHER_BUILDING'
        </where>
        ORDER BY t1.serial_no
    </select>

    <select id="listBuildingProject" resultType="com.zjhh.economy.vo.BuildingProjectVo">
        select t1.id   as project_id,
               t1.project_name,
               t2.name as community_name,
               t1.address
        from ads_pm_project t1
                 left join dm_pm t2 on t1.community_code = t2.code and t2.type = 'Community'
        order by t1.serial_no
    </select>

    <select id="countRoomSize" resultType="java.lang.Integer">
        select count(id)
        from ads_pm_room
        where floor_id in (select id from ads_pm_floor where building_id = #{buildingId})
    </select>

    <select id="getDetail" resultType="com.zjhh.economy.vo.BuildingDetailVo">
        select t1.id                                  as building_id,
               t1.serial_no,
               t1.building_name,
               t2.community_code,
               t3.name                                as community_name,
               t1.project_id,
               t2.project_name,
               t2.address,
               t1.business_area,
               t1.building_area,
               t1.operation_time,
               t1.building_status_code,
               t4.name                                as building_status_name,
               t1.building_type_code,
               t5.name                                as building_type_name,
               t1.head,
               t1.phone,
               t1.introduce,
               t1.outside_img_id,
               t1.other_img_id,
               coalesce(t1.land_park_space, 0)        as land_park_space,
               coalesce(t1.underground_park_space, 0) as underground_park_space,
               t1.remark
        from ads_pm_building t1
                 left join ads_pm_project t2 on t1.project_id = t2.id
                 left join dm_pm t3 on t3.code = t2.community_code and t3.type = 'Community'
                 left join dm_pm t4 on t1.building_status_code = t4.code and t4.type = 'BuildingStatus'
                 left join dm_pm t5 on t1.building_type_code = t5.code and t5.type = 'BuildingType'
        where t1.id = #{buildingId}
    </select>
    <select id="listBuilding" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as code, building_name as title, id as value
        from ads_pm_building
        order by serial_no
    </select>
    <select id="listBuildingSearch" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as code, building_name as title, id as value from ads_pm_building
        where 1=1
        <if test="keyword != null and keyword != ''">
            and building_name like concat('%',#{keyword},'%')
        </if>
        order by serial_no
        limit 10
    </select>
    <select id="listBuildingByAuth" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as key, project_name as title, id as value, 'root' as parentKey
        from ads_pm_project
        where id in (select project_id from v_p_building)
        union all
        select id as key, building_name as title, id as value, project_id as parentKey
        from v_p_building
    </select>


</mapper>
