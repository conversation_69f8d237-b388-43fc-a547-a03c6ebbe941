<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsReportColumnRelationMapper">
    <select id="listExportColumn" resultType="com.zjhh.comm.vo.ColumnVO">
        select arc.field as dataIndex,arc.field_name as title from ads_report_column_relation arcr left join ads_report_column arc on arcr.field_id = arc.id
        and arcr.report_type = #{reportType} and type = 2 and user_code = #{userCode}
        where arc.field is not null and arc.field_name is not null
        order by arcr.sort
    </select>
</mapper>