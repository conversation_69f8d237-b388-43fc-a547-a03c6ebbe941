<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.IndustryTaxMonitorMapper">
    <select id="pageIndTaxMonitor" resultType="com.zjhh.economy.vo.IndTaxMonitorVo">
        select aetm.id,
        rq,
        coalesce(bq_amt,0) as bqAmt,
        coalesce(sq_amt,0) as sqAmt,
        coalesce(bqlj_amt,0) as bqljAmt,
        coalesce(sqlj_amt,0) as sqljAmt,
        coalesce(tb_zf,0) as tbZf,
        coalesce(hb_zf,0) as hbZf,
        aetm.create_time::date as warningDate,
        awr.monitor_rule_period,
        awr.monitor_rule_compare,
        awr.monitor_rule_change,
        case when (select count(1) from ads_handle_warning ahw where ahw.warning_id = aetm.id) > 0 then true else false end as
        handled,
        (select name from dm_gy_hy where code = aetm.industry_code ) as industryName,
        (select building_name from ads_pm_building where id = aetm.building_id) as monitorRange
        from ads_industry_tax_monitor aetm
        left join ads_warning_rule awr on aetm.rule_id = awr.id
        left join ads_handle_warning ahw on aetm.id = ahw.warning_id
        where 1=1
        <if test="req.indCode != null and req.indCode != ''">
            and aetm.industry_code = #{req.indCode}
        </if>
        <if test="req.project != null and req.project != ''">
            and aetm.building_id in (select id from ads_pm_building where project_id = #{projectId})
        </if>
        <if test="req.buildingId != null and req.buildingId != ''">
            and aetm.building_id = #{buildingId}
        </if>
        <if test="req.indCode != null and req.indCode != ''">
            and aetm.industry_code = #{req.indCode}
        </if>

        <if test="req.startDate != null and req.startDate != ''">
            and aetm.create_time::date >= #{req.startDate}::date
        </if>
        <if test="req.endDate != null and req.endDate != ''">
            and aetm.create_time::date &lt;= #{req.endDate}::date
        </if>
        <choose>
            <when test="req.handleType != null and req.handleType != ''">
                and ahw.handle_type = #{req.handleType}
            </when>
            <otherwise>
                and ahw.handle_type is null
            </otherwise>
        </choose>
        order by aetm.create_time desc

    </select>
    <select id="getWarningSummaryByInd" resultType="com.zjhh.economy.vo.WarningSummaryVo">
        select sum(bqWarningCount)                                                                            as bqWarningCount,
               sum(sqWarningCount)                                                                            as sqWanringCount,
               sum(byWarningCount)                                                                            as byWarningCount,
               sum(syWarnginCount)                                                                            as syWarningCount,
               case
                   when sum(sqWarningCount) = 0 then 0
                   else round((sum(bqWarningCount) - sum(sqWarningCount)) / sum(sqWarningCount) * 100, 2) end as tbZf,
               case
                   when sum(syWarnginCount) = 0 then 0
                   else round((sum(byWarningCount) - sum(syWarnginCount)) / sum(syWarnginCount) * 100, 2) end as hbZf
        from (select count(1) as bqWarningCount, 0 as sqWarningCount, 0 as byWarningCount, 0 as syWarnginCount
              from ads_industry_tax_monitor
              where to_char(create_time, 'YYYY') = #{year}
              union all
              select 0 as bqWarningCount, count(1) as sqWarningCount, 0 as byWarningCount, 0 as syWarnginCount
              from ads_industry_tax_monitor
              where to_char(create_time, 'YYYY') = #{lastYear}
              union all
              select 0 as bqWarningCount, 0 as sqWarningCount, count(1) as byWarningCount, 0 as syWarnginCount
              from ads_industry_tax_monitor
              where to_char(create_time, 'YYYY') = #{year}
                and to_char(create_time, 'MM') = #{month}
              union all
              select 0 as bqWarningCount, 0 as sqWarningCount, 0 as byWarningCount, count(1) as syWarnginCount
              from ads_industry_tax_monitor
              where to_char(create_time, 'YYYY') = #{year}
                and to_char(create_time, 'MM') = #{lastMonth}) t1
    </select>
</mapper>