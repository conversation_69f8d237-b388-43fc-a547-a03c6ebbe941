<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsReportColumnMapper">

    <resultMap id="summaryVo" type="com.zjhh.economy.vo.ReportSummaryVo">
        <result column="name" property="name"></result>
        <result column="field" property="field"></result>
        <result column="userCode" property="userCode"></result>
        <result column="queryType" property="queryType"></result>
        <result column="parentField" property="parentField"></result>
        <collection property="columns" select="listColumns"
                    column="{field=field,userCode=userCode,queryType=queryType,parentField=parentField}">
        </collection>
    </resultMap>

    <select id="listReportSummaryColumn" resultMap="summaryVo">
        select field_name as name, field, #{userCode} as userCode, #{queryType} as queryType, case when parent_field =
        'condition' then 1 else 2 end as parentField From ads_report_column
        where 1=1
        <if test="type != null and type != ''">
            AND parent_field = #{type}
        </if>
        order by sort
    </select>
    <select id="listColumns" resultType="com.zjhh.economy.vo.ReportColumnVo">
        select id ,field, field_name,deleted
        <choose>
            <when test="userCode != null and userCode != ''">
                ,case when id in (select field_id from ads_report_column_relation where user_code = #{userCode} and type
                = #{queryType} and report_type = #{parentField}) then true else false end as selected,
            </when>
            <otherwise>
                false as selected,
            </otherwise>
        </choose>
        (select sort from ads_report_column_relation where field_id = arc.id and user_code = #{userCode} and type =
        #{queryType}
        <choose>
            <when test="parentField == archive">
                and report_type = 2
            </when>
            <otherwise>
                and report_type = 1
            </otherwise>
        </choose>
        ) as selectedSort
        from ads_report_column arc
        where 1=1
        and parent_field = #{field}
        order by sort


    </select>
    <select id="listSelectedColumnByArchive" resultType="com.zjhh.economy.vo.ReportColumnVo">
        select arc.id, arc.field, arc.field_name, arcr.sort
        from ads_report_column arc
                 left join ads_report_column_relation arcr on arc.id = arcr.field_id
        where 1 = 1
          and arcr.user_code is not null
          and arcr.type = #{type}
          and arcr.report_type = 2
          and arcr.user_code = #{userCode}
        order by arcr.sort
    </select>

    <select id="listSelectedColumnByCondition" resultType="com.zjhh.economy.vo.ReportColumnVo">
        select arc.id, arc.field, arc.field_name
        from ads_report_column arc
                 left join ads_report_column_relation arcr on arc.id = arcr.field_id
        where 1 = 1
          and arcr.user_code is not null
          and arcr.type = #{type}
          and arcr.report_type = 1
          and arcr.user_code = #{userCode}
        order by arcr.sort
    </select>
    <select id="listAllColumn" resultType="com.zjhh.economy.vo.ReportColumnVo">
        select id,field,field_name from ads_report_column where
        1=1
        <choose>
            <when test="type == 1">
                and (parent_field = 'basic' or parent_field = 'settledInfo' or parent_field = '经济统计信息')
            </when>
            <otherwise>
                and (parent_field = 'basicInfo' or parent_field = 'economicInfo')
            </otherwise>
        </choose>


        order by sort
    </select>
</mapper>