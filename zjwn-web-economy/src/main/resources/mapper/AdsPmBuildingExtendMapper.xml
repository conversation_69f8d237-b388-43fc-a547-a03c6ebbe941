<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmBuildingExtendMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, building_id, extend_code, type
    </sql>
    <delete id="delBuildingExtendByLabel">
        delete from ads_pm_building_extend apbe where type = 'label'
        <choose>
            <when test="ids != null and ids.size > 0">
               and extend_code not in (select code from dm_pm where id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                )
            </when>
        <otherwise>
            and 1=1
        </otherwise>
        </choose>
    </delete>

    <select id="listBuildingExtend" resultType="com.zjhh.economy.vo.BuildingExtendVo">
        select t1.extend_code,
               t2.name as label_name,
               t3.name as config_name,
               t4.name as position_name,
               t1.type
        from ads_pm_building_extend t1
                 left join dm_pm t2 on t1.extend_code = t2.code and t2.type = 'BuildingLabel' and t1.type = 'label'
                 left join dm_pm t3 on t1.extend_code = t3.code and t3.type = 'BuildingConfig' and t1.type = 'config'
                 left join dm_gy_hy t4 on t1.extend_code = t4.code and t1.type = 'position'
        where t1.building_id = #{buildingId}
        order by t1.extend_code
    </select>

</mapper>
