<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AnalyzeCockpitMapper">

    <sql id="totalTax">
        select sum(by_amt)
        from ads_pm_enterprise_tax_fkm a
        where substr(datekey,1,4) = #{req.taxCurrentYear}
        and yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and a.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and a.building_id = #{req.code}
            </when>
        </choose>
    </sql>
    <sql id="totalStreetTax">
        select sum(by_amt)
        from ads_pm_enterprise_tax_fkm a
        and yskm_dm = 'jdsr'
        where substr(datekey,1,4) = #{req.taxCurrentYear}
        <if test="req.projectType != null and req.projectType != ''">
            and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and a.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and a.building_id = #{req.code}
            </when>
        </choose>
    </sql>
    <sql id="businessArea">
        (select coalesce(sum(business_area), 0)
        from ads_pm_building apb
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and apb.id in (select id from ads_pm_building where project_id in (select id from ads_pm_project where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and apb.id in (select id from ads_pm_building where project_id in (select id from ads_pm_project where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and apb.id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and apb.id = #{req.code}
            </when>
        </choose>
        )
    </sql>
    <sql id="roomTotalArea">
        (select coalesce(sum(building_area), 0)
        from ads_pm_building apr
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in(select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id = #{req.code}
            </when>
        </choose>
        )
    </sql>
    <resultMap id="cockpitProjectSummary" type="com.zjhh.economy.vo.analyzecockpit.CockpitProjectSummaryVo">
        <result column="id" property="id"></result>
        <result column="project_name" property="projectName"></result>
        <result column="community_name" property="communityName"></result>
        <result column="address" property="projectAddress"></result>
        <result column="projectType" property="projectType"></result>
        <result column="project_area" property="totalArea"></result>
        <result column="manage_company" property="manageCompany"></result>
        <result column="project_intro" property="projectIntro"></result>
        <collection property="projectImages" select="listProjectImages"
                    column="{businessId=id}">
        </collection>
    </resultMap>

    <resultMap id="compareVo" type="com.zjhh.economy.vo.MobileCompareVo">
        <result column="maxCount" property="maxCount"></result>
        <result column="item_type" property="itemType"></result>
        <result column="projectId" property="projectId"></result>
        <collection property="codes" select="listDefaultItem"
                    column="{itemType=item_type,projectId= projectId}">
        </collection>
    </resultMap>

    <resultMap id="cockpitBuildingSummary" type="com.zjhh.economy.vo.analyzecockpit.CockpitBuildingSummaryVo">
        <result column="id" property="id"></result>
        <result column="building_name" property="buildingName"></result>
        <result column="opening_date" property="openingDate"></result>
        <result column="address" property="buildingAddress"></result>
        <result column="floor" property="floor"></result>
        <result column="roomArea" property="roomArea"></result>
        <result column="manager" property="manager"></result>
        <result column="phone" property="phone"></result>
        <result column="buildingIntro" property="buildingIntro"></result>
        <result column="buildingSupporting" property="buildingSupporting"></result>
        <collection property="buildingLocation" select="listBuildingLocation"
                    column="{businessId=id}">
        </collection>
        <collection property="buildingTags" select="listBuildingTags"
                    column="{businessId=id}">
        </collection>
    </resultMap>

    <select id="getAreaAnalyze" resultType="com.zjhh.economy.vo.analyzecockpit.AreaAnalyzeVo">
        select round(sum(businessArea), 2) as totalBusinessArea,
        round(sum(settledArea), 2) as settledArea,
        case when sum(businessArea) = 0 then 0
        else round(sum(settledArea) / sum(businessArea) * 100, 2) end as settledRate,
        round(sum(businessArea) - sum(settledArea), 2) as emptyArea,
        case when sum(businessArea) - sum(settledArea) = 0 then 0
        else round((sum(businessArea) - sum(settledArea)) / sum(businessArea) * 100, 2) end as emptyRate
        from(
        select cast(hold2 as numeric) businessarea,cast(hold3 as numeric) as settledarea
        from ads_bas_screen_view
        where screen_module = 'LYMJ_DP'
        and datekey = #{req.taxCurrentYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>

        ) a

    </select>
    <select id="listSettledTrendByMonth" resultType="com.zjhh.economy.vo.analyzecockpit.SettledTrendVo">
        select concat(substr(datekey,3,2),'-',substr(datekey,5,6)) as datekey,round(sum(settledArea), 2) as settledArea,
        case when sum(businessArea) = 0 then 0
        else round(sum(settledArea) / sum(businessArea) * 100, 2) end as settledRate,
        round(sum(businessArea) - sum(settledArea), 2) as emptyArea
        from(
        select datekey,cast(hold2 as numeric) businessarea,cast(hold3 as numeric) as settledarea
        from ads_bas_screen_view
        where screen_module = 'LYMJ_DP'
        and datekey between #{req.taxLastYearMonth} and #{req.taxCurrentYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        ) a
        group by datekey
        order by datekey
    </select>
    <select id="listSettledTrendByQuarter" resultType="com.zjhh.economy.vo.analyzecockpit.SettledTrendVo">

        select
        datekey,
        round(settledArea, 2) as settledArea,
        case when businessArea = 0 then 0
        else round(settledArea / businessArea * 100, 2) end as settledRate,
        round(businessArea - settledArea, 2) as emptyArea
        from (
        select
        quarter_key as datekey,
        settledArea,
        businessArea
        from (
        select
        quarter_key,
        settledArea,
        businessArea,
        row_number() over(partition by quarter_key order by month_key desc) as rn
        from (
        select
        datekey as month_key,
        case when substr(datekey,5,6) in ('01', '02', '03') then concat(substr(datekey,1,4),'1')
        when substr(datekey,5,6) in ('04','05', '06') then concat(substr(datekey,1,4),'2')
        when substr(datekey,5,6) in ('07','08', '09') then concat(substr(datekey,1,4),'3')
        when substr(datekey,5,6) in ('10','11', '12') then concat(substr(datekey,1,4),'4')
        else '' end as quarter_key,
        sum(cast(hold3 as numeric)) as settledArea,
        sum(cast(hold2 as numeric)) as businessArea
        from ads_bas_screen_view
        where screen_module = 'LYMJ_DP'
        and datekey between #{req.lastYearMonth} and #{req.currentYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        group by datekey
        ) monthly_data
        where quarter_key != ''
        ) ranked_data
        where rn = 1
        ) final_data
        order by datekey


    </select>
    <select id="listSettledTrendByYear" resultType="com.zjhh.economy.vo.analyzecockpit.SettledTrendVo">

        select
        datekey,
        round(settledArea, 2) as settledArea,
        case when businessArea = 0 then 0
        else round(settledArea / businessArea * 100, 2) end as settledRate,
        round(businessArea - settledArea, 2) as emptyArea
        from (
        select
        year_key as datekey,
        settledArea,
        businessArea
        from (
        select
        year_key,
        settledArea,
        businessArea,
        row_number() over(partition by year_key order by month_key desc) as rn
        from (
        select
        datekey as month_key,
        substr(datekey,1,4) as year_key,
        sum(cast(hold2 as numeric)) as businessArea,
        sum(cast(hold3 as numeric)) as settledArea
        from ads_bas_screen_view
        where screen_module = 'LYMJ_DP'
        and substr(datekey,1,4) between (#{req.currentYear}::int - 5)::varchar and #{req.currentYear}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        group by datekey
        ) monthly_data
        ) ranked_data
        where rn = 1
        ) final_data
        order by datekey
    </select>
    <select id="getTaxAnalyze" resultType="com.zjhh.economy.vo.analyzecockpit.TaxAnalyzeVo">
        select round(sum(ss) / 10000, 2) as taxIncome, round(sum(jdsr) / 10000 ,2) as streetIncome, round(sum(fdcss) /
        10000,2) as cfdcss, round(sum(swzmj) / 10000,2) as swzmj,
        round((sum(ss) - sum(fdcss)) / 10000 ,2) as taxIncomeWithoutFdc, case when sum(swzmj) = 0 then 0 else
        round(sum(ss) /
        sum(swzmj),2) end
        as unitIncome,
        case when sum(swzmj) = 0 then 0 else round((sum(ss) - sum(fdcss)) / sum(swzmj),2)end as taxIncomeWithoutUnitFdc
        from (
        select sum(ss) as ss,sum(jdsr) as jdsr, 0 as fdcss,
        0 as swzmj from (select case when yskm_dm = 'ss' then coalesce(sum(bq_amt), 0) else 0 end as ss,
        case when yskm_dm = 'jdsr' then coalesce(sum(bq_amt), 0) else 0 end as jdsr

        from ads_pm_enterprise_tax_fkm apet
        where datekey = #{req.taxCurrentYearMonth}
        and (yskm_dm = 'ss' or yskm_dm = 'jdsr')
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by yskm_dm
        ) t1
        union all
        select 0 as ss,
        0 as jdsr,
        coalesce(sum(bq_amt), 0) fdcss,
        0 as swzmj
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        where ape.industry_code in ('701','7010')
        and datekey = #{req.taxCurrentYearMonth}
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        union all
        select 0 as ss,
        0 as jdsr,
        0 as fdcss,
        coalesce(sum(business_area), 0) as swzmj
        from ads_pm_building ve
        where 1=1
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="(req.projectType == null or req.projectType == '') or req.projectType == 'ly'">
                    and id in (select id from ads_pm_building where project_id in ( select project_id from
                    ads_empty_area_rank_project where defaulted = true))
                </if>
                <if test="(req.projectType != null and req.projectType != '') and req.projectType != 'ly'">
                    and id in (select id from ads_pm_building where project_id in (select id from ads_pm_project where
                    project_type = #{req.projectType}))

                </if>
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and id in (select id from ads_pm_building where project_id in (select id from ads_pm_project where
                    project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where
                        community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.id = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>

        ) t1


    </select>
    <select id="listTaxTrendByMonth" resultType="com.zjhh.economy.vo.analyzecockpit.TaxTrendVo">
        select concat(substr(datekey,3,2),'-',substr(datekey,5,6)) as datekey,
        round(sum(ss) / 10000,2) as taxIncome,
        round(sum(tqss) / 10000, 2) as sqTaxIncome,
        case when sum(tqss) = 0 then 0 else round((sum(ss) - sum(tqss)) / sum(tqss) * 100, 2)
        end
        as zf
        from (WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1
        month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n)
        select datekey, 0 as ss, 0 as tqss
        from all_months
        union all
        (select apet.datekey,
        sum(by_amt) as ss,
        (select sum(by_amt)
        from ads_pm_enterprise_tax_fkm
        where datekey = (apet.datekey::decimal - 100) ::varchar
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        )
        as tqss
        from ads_pm_enterprise_tax_fkm apet
        where 1 = 1
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        and apet.datekey in (select datekey from all_months)
        group by apet.datekey)) t1
        group by datekey

    </select>
    <select id="listTaxTrendByQuarter" resultType="com.zjhh.economy.vo.analyzecockpit.TaxTrendVo">
        select case
        when substr(month,5,6) in ('01','02','03') then concat(substr(month,1,4),'1')
        when substr(month,5,6) in ('04','05','06') then concat(substr(month,1,4),'2')
        when substr(month,5,6) in ('07','08','09') then concat(substr(month,1,4),'3')
        when substr(month,5,6) in ('10','11','12') then concat(substr(month,1,4),'4') end as datekey,
        round(sum(ss) / 10000,2) as taxIncome,
        round(sum(tqss) / 10000, 2) as sqTaxIncome,
        case when sum(tqss) = 0 then 0 else round((sum(ss) - sum(tqss)) / sum(tqss) * 100, 2)
        end
        as zf
        from (WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1
        month'),
        'YYYYMM') ::varchar AS month
        FROM generate_series(0, 11) n)
        select month, 0 as ss, 0 as tqss
        from all_months
        union all
        (select apet.datekey as month,
        sum(by_amt) as ss,
        (select sum(by_amt)
        from ads_pm_enterprise_tax_fkm
        where datekey = (apet.datekey::decimal - 100) ::varchar
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        )
        as tqss
        from ads_pm_enterprise_tax_fkm apet
        where 1 = 1
        and apet.datekey in (select month from all_months)
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by apet.datekey)) t1
        group by datekey
    </select>
    <select id="listTaxTrendByYear" resultType="com.zjhh.economy.vo.analyzecockpit.TaxTrendVo">

        with data1 as (
        select a.*, to_char(add_months(to_date(a.datekey,'yyyymm'),-12),'yyyymm') as sq_date from (
        select substr(datekey,1,4) as year,max(datekey) as datekey
        from ads_pm_enterprise_tax_fkm a
        where yskm_dm = 'ss'

        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by substr(datekey,1,4)
        ) a
        ),data2 as (
        select datekey,sum(bq_amt) as bq_amt
        from ads_pm_enterprise_tax_fkm a where yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey
        )
        select concat(a.year, '年') as datekey,
        round(sum(b.bq_amt) / 10000,2) as taxIncome ,
        round(sum(c.bq_amt) / 10000, 2) as sqTaxIncome,
        case when sum(c.bq_amt) = 0 then 0 else round((sum(b.bq_amt)/sum(c.bq_amt)-1)*100,1) end as zf
        from data1 a
        inner join data2 b on a.datekey = b.datekey
        left join data2 c on a.sq_date = c.datekey
        group by concat(a.year, '年')
        order by concat(a.year, '年')
    </select>
    <select id="getSettledEntAnalyze" resultType="com.zjhh.economy.vo.analyzecockpit.SettledEntAnalyzeVo">
        select sum(ent_count) as totalCount, sum(territorialized_ent_count) as registerCount,
        case when sum(ent_count) = 0 then 0 else round(sum(territorialized_ent_count)::decimal / sum(ent_count)::decimal
        * 100 , 2) end as registerRate
        from ads_pm_ent_territorialized t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and t3.community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and t3.id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and t1.building_id = #{req.code}
            </when>
        </choose>

    </select>
    <select id="listSettledEntUnitTrendByMonth"
            resultType="com.zjhh.economy.vo.analyzecockpit.SettledEntUnitTrendVo">
        WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'),
        'YYYYMM')::varchar AS datekey
        FROM generate_series(0, 11) n),
        enterprise_cumulative_counts AS (SELECT am.datekey,
        COUNT(DISTINCT ve.enterprise_id) AS entTotalCount,
        COUNT(DISTINCT CASE
        WHEN ve.territorialized = true
        THEN ve.enterprise_id END) AS localedCount
        FROM all_months am
        CROSS JOIN v_enterprise ve
        WHERE to_char(ve.check_in_date, 'YYYYMM') &lt;= am.datekey
        and (to_char(reality_move_out_date, 'YYYYMM') > am.datekey or reality_move_out_date is null)
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        GROUP BY am.datekey)

        SELECT CONCAT(SUBSTR(datekey, 3, 2), '-', SUBSTR(datekey, 5, 6)) AS datekey,
        entTotalCount,
        localedCount,
        CASE
        WHEN entTotalCount = 0 THEN 0
        ELSE ROUND(localedCount * 100.0 / entTotalCount, 2)
        END AS zb
        FROM enterprise_cumulative_counts
        ORDER BY datekey

    </select>
    <select id="listSettledEntUnitTrendByQuarter"
            resultType="com.zjhh.economy.vo.analyzecockpit.SettledEntUnitTrendVo">

        WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', CURRENT_DATE) - (n * INTERVAL '1 month'),
        'YYYYMM')::varchar AS datekey
        FROM generate_series(0, 11) n),
        enterprise_cumulative_counts AS (SELECT am.datekey,
        COUNT(DISTINCT ve.enterprise_id) AS entTotalCount,
        COUNT(DISTINCT CASE
        WHEN ve.territorialized = true
        THEN ve.enterprise_id END) AS localedCount
        FROM all_months am
        CROSS JOIN v_enterprise ve
        WHERE to_char(ve.check_in_date, 'YYYYMM') &lt;= am.datekey
        and (to_char(reality_move_out_date, 'YYYYMM') > am.datekey or reality_move_out_date is null)
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        GROUP BY am.datekey)

        SELECT case when substr(datekey,5,6) in ('01','02','03') then concat(substr(datekey,1,4),'1')
        when substr(datekey,5,6) in ('04','05','06') then concat(substr(datekey,1,4),'2')
        when substr(datekey,5,6) in ('07','08','09') then concat(substr(datekey,1,4),'3')
        when substr(datekey,5,6) in ('10','11','12') then concat(substr(datekey,1,4),'4') end AS datekey,
        entTotalCount,
        localedCount,
        CASE
        WHEN entTotalCount = 0 THEN 0
        ELSE ROUND(localedCount * 100.0 / entTotalCount, 2)
        END AS zb
        FROM enterprise_cumulative_counts
        where substr(datekey,5,6) in ('03','06','09','12')
        ORDER BY datekey


    </select>
    <select id="listSettledEntUnitTrendByYear"
            resultType="com.zjhh.economy.vo.analyzecockpit.SettledEntUnitTrendVo">
        WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) - (n * INTERVAL '1 year'),
        'YYYY')::varchar AS datekey
        FROM generate_series(0, 5) n),
        enterprise_cumulative_counts AS (SELECT am.datekey,
        COUNT(DISTINCT ve.enterprise_id) AS entTotalCount,
        COUNT(DISTINCT CASE
        WHEN ve.territorialized = true
        THEN ve.enterprise_id END) AS localedCount
        FROM all_months am
        CROSS JOIN v_enterprise ve
        WHERE to_char(ve.check_in_date, 'YYYY') &lt;= am.datekey
        and (to_char(reality_move_out_date, 'YYYY') > am.datekey or reality_move_out_date is null)
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        GROUP BY am.datekey)

        SELECT datekey,
        entTotalCount,
        localedCount,
        CASE
        WHEN entTotalCount = 0 THEN 0
        ELSE ROUND(localedCount * 100.0 / entTotalCount, 2)
        END AS zb
        FROM enterprise_cumulative_counts
        ORDER BY datekey
    </select>
    <select id="getCockpitProjectSummary"
            resultMap="cockpitProjectSummary">
        Select id,
               project_name,
               (select name from dm_pm where type = 'Community' and code = app.community_code) as community_name,
               project_area,
               (select name from dm_pm where type = 'ProjectType' and code = app.project_type) as projectType,
               manage_company,
               project_intro,
               address
        from ads_pm_project app
        where app.id = #{req.code}
    </select>

    <select id="listProjectImages" resultType="com.zjhh.economy.request.ProjectImageVo">
        select ad.id as documentId, ad.title as documentName, ad.path as documentPath, ad.size
        from ads_business_document abd
                 left join ads_document ad on abd.document_id = ad.id
        where abd.business_id = #{businessId}
    </select>
    <select id="getCockpitBuildingSummary" resultMap="cockpitBuildingSummary">
        select concat(t1.parkCount, '、', t1.buildingSupport) as buildingSupporting,
               t1.id,
               building_name,
               address,
               buildingintro,
               phone,
               manager,
               outside_img_id,
               other_img_id,
               opening_date,
               roomArea,
               floor
        from (select apb.id,
                     (select concat(min(floor_no), 'F~', max(floor_no), 'F')
                      from ads_pm_floor
                      where building_id = apb.id)                                                 as floor,
                     (select concat(round(coalesce(min(building_area), 0), 0), '㎡~',
                                    round(coalesce(max(building_area), 0), 0), '㎡')
                      from ads_pm_room
                      where floor_id in (select id from ads_pm_floor where building_id = apb.id)) as roomArea,
                     building_name,
                     app.address,
                     apb.introduce                                                                as buildingIntro,
                     apb.phone,
                     apb.head                                                                     as manager,
                     apb.outside_img_id,
                     apb.other_img_id,
                     apb.operation_time                                                           as opening_date,
                     concat('停车位', '(地上停车位', apb.land_park_space, ',', '地下停车位', apb.underground_park_space,
                            '个)')                                                                as parkCount,
                     (select string_agg(name, '、')
                      from dm_pm
                      where code in
                            (select extend_code
                             from ads_pm_building_extend
                             where building_id = apb.id
                               and type = 'config')
                        and type = 'BuildingConfig')                                              as buildingSupport
              from ads_pm_building apb
                       left join ads_pm_project app on apb.project_id = app.id
              where apb.id = #{req.code}) t1

    </select>

    <select id="listBuildingLocation" resultType="java.lang.String">
        select name
        from dm_gy_hy
        where code in
              (select extend_code from ads_pm_building_extend where building_id = #{businessId} and type = 'position')
    </select>

    <select id="listBuildingTags" resultType="java.lang.String">
        select name
        from dm_pm
        where code in
              (select extend_code from ads_pm_building_extend where building_id = #{businessId} and type = 'label')
          and type = 'BuildingLabel'
    </select>
    <select id="listIndustryFocusAnalyzeTaxRank"
            resultType="com.zjhh.economy.vo.analyzecockpit.IndustryFocusAnalyzeVo">
        select t1.industryCode,t1.industryName,round(t1.taxScaled / 10000,2) as taxScaled,case when sum(t1.taxScaled)
        over () = 0 then 0
        else round(t1.taxScaled /
        sum(t1.taxScaled) over() * 100,2) end as taxScaledZb from (
        select t1.industryCode,(select name from dm_gy_hy where code = t1.industryCode) as industryName,
        t1.ss as taxScaled
        from (select substr(ape.industry_code, 1, ${req.industryLength}) as industryCode, coalesce(sum(by_amt), 0) as ss
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on ape.id = apet.enterprise_id
        where substr(datekey, 1, 4) = #{req.taxCurrentYear}
        and substr(datekey, 5,6) &lt;= substr(#{req.taxCurrentYearMonth},5,6)
        and yskm_dm = 'ss'
        and ape.id is not null
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                and substr(industry_code
                , 1
                , ${req.industryLength}) in (select code from dm_gy_hy where length (code) = ${req.industryLength})
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>

        group by substr(ape.industry_code, 1, ${req.industryLength})) t1
        ) t1
        order by t1.taxScaled DESC
        limit 10

    </select>
    <select id="listIndustryFocusAnalyzeEntCountRank"
            resultType="com.zjhh.economy.vo.analyzecockpit.IndustryFocusAnalyzeVo">
        select t1.industryCode,(select name from dm_gy_hy where code = t1.industryCode) as industryName, entCount,
        case when t1.totalCount = 0 then 0 else round(t1.entCount::decimal / t1.totalCount::decimal * 100 , 2) end as
        entCountZb
        from (select substr(industry_code, 1, ${req.industryLength}) as industryCode, count(1) as entCount,(select
        count(1)
        from ads_pm_enterprise
        where id in (select distinct enterprise_id
        from v_enterprise ve
        where moved = false
        and check_in_date is not null
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        )
        and substr(industry_code
        , 1
        , ${req.industryLength}) in (select code from dm_gy_hy where length (code) = ${req.industryLength})) as
        totalCount
        from ads_pm_enterprise
        where id in (select distinct enterprise_id
        from v_enterprise ve
        where moved = false
        and check_in_date is not null
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        )
        and substr(industry_code
        , 1
        , ${req.industryLength}) in (select code from dm_gy_hy where length (code) = ${req.industryLength})
        group by substr(industry_code, 1, ${req.industryLength})) t1
        order by t1.entCount DESC limit 10
    </select>
    <select id="listIndustryFocusAnalyzeTaxSettledAreaRank"
            resultType="com.zjhh.economy.vo.analyzecockpit.IndustryFocusAnalyzeVo">
        select t1.industryCode,(select name from dm_gy_hy where code = t1.industryCode) as industryName,
        round(t1.settledArea,2) as
        settledArea,
        case when t1.totalArea = 0 then 0 else round(t1.settledArea / t1.totalArea * 100,2) end as settledAreaZb
        from (select substr(industry_code, 1, ${req.industryLength}) as industryCode, coalesce(sum(settled_area),0) as
        settledArea,
        (select sum(settled_area)
        from v_enterprise ve
        where moved = false
        and check_in_date is not null
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        and industry_code in (select hy_dm from dm_ly_hy_zq where length(sjhy_dm) = ${req.industryLength})) as totalArea
        from v_enterprise ve
        where moved = false
        and check_in_date is not null
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        and industry_code in (select hy_dm from dm_ly_hy_zq where length(sjhy_dm) = ${req.industryLength})
        group by substr(industry_code, 1, ${req.industryLength})
        order by settledArea desc) t1
        limit 10
    </select>
    <select id="getSpaceAnalyzeSummary" resultType="com.zjhh.economy.vo.analyzecockpit.SpaceAnalyzeSummaryVo">
        WITH current_data AS (SELECT datekey,
        sum(cast(hold2 as numeric)) as businessArea,
        sum(cast(hold3 as numeric)) as settledArea
        FROM ads_bas_screen_view
        WHERE screen_module = 'LYMJ_DP'
        AND datekey = #{req.taxCurrentYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '' ) and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        GROUP BY datekey),
        last_month AS (SELECT datekey,
        sum(cast(hold2 as numeric)) as businessArea,
        sum(cast(hold3 as numeric)) as settledArea
        FROM ads_bas_screen_view
        WHERE screen_module = 'LYMJ_DP'
        and datekey = #{req.taxLastMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        GROUP BY datekey),
        last_year AS (SELECT datekey,
        sum(cast(hold2 as numeric)) as businessArea,
        sum(cast(hold3 as numeric)) as settledArea
        FROM ads_bas_screen_view
        WHERE screen_module = 'LYMJ_DP'
        AND datekey = #{req.taxLastYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        GROUP BY datekey)
        SELECT c.datekey,

        round(c.businessArea, 2) as totalBusinessArea,
        round(c.settledArea, 2) as settledArea,
        CASE
        WHEN c.businessArea = 0 THEN 0
        ELSE round(c.settledArea / c.businessArea * 100, 2)
        END as settledRate,
        round(c.businessArea - c.settledArea, 2) as emptyArea,
        CASE
        WHEN c.businessArea = 0 THEN 0
        ELSE round((c.businessArea - c.settledArea) / c.businessArea * 100, 2)
        END as emptyRate,

        case when coalesce(lm.businessArea, 0) = 0 then 0 else
        round((c.businessArea - lm.businessArea) / coalesce(lm.businessArea, 0) * 100, 2) end as totalBusinessAreaHb,
        case when coalesce(lm.settledArea, 0) = 0 then 0 else
        round((c.settledArea - lm.settledArea) / coalesce(lm.settledArea, 0) * 100, 2) end as settledAreaHb,
        case when coalesce(lm.businessArea - lm.settledArea, 0) = 0 then 0 else
        round((c.businessArea - c.settledArea - (lm.businessArea - lm.settledArea)) /
        coalesce(lm.businessArea - lm.settledArea, 0) * 100, 2) end as emptyRate ,

        round(
        case when c.businessArea = 0 then 0 else
        (c.settledArea / coalesce(c.businessArea, 0) * 100) end -
        case when lm.businessArea = 0 then 0 else
        (lm.settledArea / coalesce(lm.businessArea, 0) * 100) end ,
        2) as settledRateHb,
        round(
        case when c.businessArea = 0 then 0 else
        ((c.businessArea - c.settledArea) / coalesce(c.businessArea, 0) * 100) end -
        case when lm.businessArea = 0 then 0 else
        ((lm.businessArea - lm.settledArea) / coalesce(lm.businessArea, 0) * 100) end ,
        2) as emptyRateHb,
        case when ly.businessArea = 0 then 0 else
        round((c.businessArea - ly.businessArea) / coalesce(ly.businessArea, 0) * 100, 2) end as totalBusinessAreaTb,
        case when ly.settledArea = 0 then 0 else
        round((c.settledArea - ly.settledArea) / coalesce(ly.settledArea, 0) * 100, 2) end as settledAreaTb,
        case when ly.businessArea - ly.settledArea = 0 then 0 else
        round((c.businessArea - c.settledArea - (ly.businessArea - ly.settledArea)) /
        coalesce(ly.businessArea - ly.settledArea, 0) * 100, 2) end as emptyAreaTb,

        case when lm.businessArea - lm.settledArea = 0 then 0 else
        round((c.businessArea - c.settledArea - (lm.businessArea - lm.settledArea)) /
        coalesce(lm.businessArea - lm.settledArea, 0) * 100, 2) end as emptyAreaHb,
        round(
        case when c.businessArea = 0 then 0 else
        (c.settledArea / coalesce(c.businessArea, 0) * 100) end -
        case when ly.businessArea = 0 then 0 else
        (ly.settledArea / coalesce(ly.businessArea, 0) * 100) end ,
        2) as settledRateTb,

        round(
        case when c.businessArea = 0 then 0 else
        ((c.businessArea - c.settledArea) / coalesce(c.businessArea, 0) * 100) end -
        case when ly.businessArea = 0 then 0 else
        ((ly.businessArea - ly.settledArea) / coalesce(ly.businessArea, 0) * 100) end ,
        2) as emptyRateTb
        FROM current_data c
        LEFT JOIN last_month lm ON 1 = 1
        LEFT JOIN last_year ly ON 1 = 1

    </select>
    <select id="listSpaceResourceByProject" resultType="com.zjhh.economy.vo.analyzecockpit.SpaceResourceVo">
        select t3.project_name as buildingName,
        sum(cast(hold2 as numeric)) as businessArea,
        sum(cast(hold3 as numeric)) as settledArea,
        sum(cast(hold2 as numeric)) - sum(cast(hold3 as numeric)) as emptyArea,
        CASE
        WHEN sum(cast(hold2 as numeric)) = 0 THEN 0
        ELSE round(sum(cast(hold3 as numeric)) / sum(cast(hold2 as numeric)) * 100, 2)
        END as settledRate,
        CASE
        WHEN sum(cast(hold2 as numeric)) = 0 THEN 0
        ELSE round((sum(cast(hold2 as numeric)) - sum(cast(hold3 as numeric))) / sum(cast(hold2 as numeric)) * 100, 2)
        END as emptyRate
        from ads_bas_screen_view t1
        left join ads_pm_building t2 on t1.hold1 = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where screen_module = 'LYMJ_DP'
        and t1.datekey = #{req.taxCurrentYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        group by t3.id,t3.project_name
    </select>
    <select id="listSpaceResourceByCommunity" resultType="com.zjhh.economy.vo.analyzecockpit.SpaceResourceVo">
        select (select name from dm_pm where code = t3.community_code and type = 'Community') as buildingName,
        sum(cast(hold2 as numeric)) as businessArea,
        sum(cast(hold3 as numeric)) as settledArea,
        sum(cast(hold2 as numeric)) - sum(cast(hold3 as numeric)) as emptyArea,
        CASE
        WHEN sum(cast(hold2 as numeric)) = 0 THEN 0
        ELSE round(sum(cast(hold3 as numeric)) / sum(cast(hold2 as numeric)) * 100, 2)
        END as settledRate,
        CASE
        WHEN sum(cast(hold2 as numeric)) = 0 THEN 0
        ELSE round((sum(cast(hold2 as numeric)) - sum(cast(hold3 as numeric))) / sum(cast(hold2 as numeric)) * 100, 2)
        END as emptyRate
        from ads_bas_screen_view t1
        left join ads_pm_building t2 on t1.hold1 = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where t1.screen_module = 'LYMJ_DP'
        and t1.datekey = #{req.taxCurrentYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        group by t3.community_code
    </select>
    <select id="listSpaceResourceByBuilding" resultType="com.zjhh.economy.vo.analyzecockpit.SpaceResourceVo">
        select t2.building_name as buildingName,
        sum(cast(hold2 as numeric)) as businessArea,
        sum(cast(hold3 as numeric)) as settledArea,
        sum(cast(hold2 as numeric)) - sum(cast(hold3 as numeric)) as emptyArea,
        CASE
        WHEN sum(cast(hold2 as numeric)) = 0 THEN 0
        ELSE round(sum(cast(hold3 as numeric)) / sum(cast(hold2 as numeric)) * 100, 2)
        END as settledRate,
        CASE
        WHEN sum(cast(hold2 as numeric)) = 0 THEN 0
        ELSE round((sum(cast(hold2 as numeric)) - sum(cast(hold3 as numeric))) / sum(cast(hold2 as numeric)) * 100, 2)
        END as emptyRate
        from ads_bas_screen_view t1
        left join ads_pm_building t2 on t1.hold1 = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where screen_module = 'LYMJ_DP'
        and t1.datekey = #{req.taxCurrentYearMonth}
        <choose>
            <when test="(req.projectType == null or req.projectType == 'ly' or req.projectType == '') and req.type == 1">
                and hold4 = 'ly'
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                    where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and hold1 in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and hold1 in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and hold1 = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        group by t2.id,t2.building_name
    </select>
    <select id="listRoomResource" resultType="com.zjhh.economy.vo.analyzecockpit.RoomResourceVo">
        select t3.roomArea,sum(t3.roomCount) as roomCount,sum(t3.zb) as zb from (
        select '0-100㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '100-200㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '200-300㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '300-500㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '500㎡以上' as roomArea, 0 roomCount, 0 zb
        union all
        select t2.roomArea, t2.roomCount, round(t2.roomCount::decimal / t2.totalCount::decimal * 100 ,2) as zb from (
        select t1.roomArea,count(1) as roomCount,(select count(1) from ads_pm_room t1
        left join ads_pm_floor t2 on t1.floor_id = t2.id
        inner join ads_pm_building t3 on t2.building_id = t3.id
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType})))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code})))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building where
                project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and floor_id in (select id from ads_pm_floor where building_id = #{req.code})
            </when>
        </choose>
        ) as totalCount from (
        select case when t1.business_area >= 0 and t1.business_area
        &lt;=
        100 then '0-100㎡'
        when t1.business_area > 100 and t1.business_area
        &lt;=
        200 then '100-200㎡'
        when t1.business_area > 200 and t1.business_area
        &lt;=
        300 then '200-300㎡'
        when t1.business_area > 300 and t1.business_area
        &lt;=
        500 then '300-500㎡'
        else '500㎡以上' end as roomArea
        from ads_pm_room t1
        left join ads_pm_floor t2 on t1.floor_id = t2.id
        inner join ads_pm_building t3 on t2.building_id = t3.id
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t1.floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType})))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and t1.floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building
                where
                project_id in (select id from ads_pm_project where community_code = #{req.code})))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and t1.floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building
                where
                project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and floor_id in (select id from ads_pm_floor where building_id = #{req.code})
            </when>
        </choose>
        ) t1
        group by t1.roomArea
        ) t2
        ) t3
        group by t3.roomArea
        order by t3.roomArea


    </select>
    <select id="listEntRentRoomResource" resultType="com.zjhh.economy.vo.analyzecockpit.RoomResourceVo">
        select t3.roomArea, sum(t3.roomCount) as roomCount, sum(t3.zb) as zb from (
        select '0-100㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '100-200㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '200-300㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '300-500㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '500㎡以上' as roomArea, 0 roomCount, 0 zb
        union all
        select t2.roomArea,t2.roomCount, case when sum(t2.roomCount) over() = 0 then 0 else round(t2.roomCount /
        sum(t2.roomCount) over() * 100,2) end as zb from (
        select t1.roomArea,count(1) as roomCount from (
        select case when building_area >= 0 and building_area
        &lt;=
        100 then '0-100㎡'
        when building_area > 100 and building_area
        &lt;=
        200 then '100-200㎡'
        when building_area > 200 and building_area
        &lt;=
        300 then '200-300㎡'
        when building_area > 300 and building_area
        &lt;=
        500 then '300-500㎡'
        else '500㎡以上' end as roomArea from ads_pm_room where id in (select distinct room_id from v_enterprise ve
        where moved = false
        and check_in_date is not null

        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2 and req.code != '' ">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3 and req.code != '' ">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4 and req.code != ''">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        )
        ) t1
        group by t1.roomArea
        ) t2

        ) t3
        group by t3.roomArea
        order by t3.roomArea
    </select>
    <select id="listEmptyRoomResource" resultType="com.zjhh.economy.vo.analyzecockpit.RoomResourceVo">
        select t3.roomArea, sum(t3.roomCount) as roomCount, sum(t3.zb) as zb from (
        select '0-100㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '100-200㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '200-300㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '300-500㎡' as roomArea, 0 roomCount, 0 zb
        union all
        select '500㎡以上' as roomArea, 0 roomCount, 0 zb
        union all
        select t2.roomArea,t2.roomCount, case when sum(t2.roomCount) over() = 0 then 0 else round(t2.roomCount /
        sum(t2.roomCount) over() * 100,2) end as zb from (
        select t1.roomArea,count(1) as roomCount from (
        select case when business_area >= 0 and business_area
        &lt;=
        100 then '0-100㎡'
        when business_area > 100 and business_area
        &lt;=
        200 then '100-200㎡'
        when business_area > 200 and business_area
        &lt;=
        300 then '200-300㎡'
        when business_area > 300 and business_area
        &lt;=
        500 then '300-500㎡'
        else '500㎡以上' end as roomArea from ads_pm_room where id not in (select distinct room_id from v_enterprise ve
        where moved = false
        and room_id is not null
        and check_in_date is not null
        <if test="req.projectType != null and req.projectType != ''">
            and ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and ve.building_id = #{req.code}
            </when>
        </choose>
        )
        <if test="req.projectType != null and req.projectType != ''">
            and floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType})))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code})))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and floor_id in (select id from ads_pm_floor where building_id in (select id from ads_pm_building where
                project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and floor_id in (select id from ads_pm_floor where building_id = #{req.code})
            </when>
        </choose>
        ) t1
        group by t1.roomArea
        ) t2

        ) t3
        group by t3.roomArea
        order by t3.roomArea
    </select>
    <select id="listEmptyPeriod" resultType="com.zjhh.economy.vo.analyzecockpit.RoomPeriodVo">
        select period,
        t1.roomCount,
        case when sum(t1.roomCount) over () = 0 then 0
        else round(t1.roomCount / sum(t1.roomCount) over () * 100, 2) end as zb
        from (
        select period, sum(roomCount) as roomCount
        from (
        select '1' as period, 0 as roomCount
        union all
        select '2' as period, 0 as roomCount
        union all
        select '3' as period, 0 as roomCount
        union all
        select '4' as period, 0 as roomCount
        union all
        select '5' as period, 0 as roomCount
        union all
        select '6' as period, 0 as roomCount
        union all
        select t3.emptyPeriod as period, count(1) as roomCount
        from (
        select case when emptyDays > 0 and emptyDays &lt;= 30 then '1'
        when emptyDays > 30 and emptyDays &lt;= 60 then '2'
        when emptyDays > 60 and emptyDays &lt;= 120 then '3'
        when emptyDays > 120 and emptyDays &lt;= 240 then '4'
        when emptyDays > 240 and emptyDays &lt;= 360 then '5'
        when emptyDays > 360 then '6'
        else '' end as emptyPeriod
        from (
        select (#{req.currentDate}::date - realDate::date)::int as emptyDays
        From (
        select a.id,max(case when b.room_id is null then a.create_time
        when b.room_id is not null and b.moved = true then reality_move_out_date
        else #{req.currentDate}::date end ) as realDate
        from ads_pm_room a
        left join ads_pm_floor t1 on a.floor_id = t1.id
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        left join v_enterprise b on a.id = b.room_id
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and t3.community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and t2.project_id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and t1.building_id = #{req.code}
            </when>
        </choose>
        and a.id not in (select room_id from v_enterprise
        where 1=1
        and moved = false
        and room_id is not null
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and project_id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
        )
        group by a.id
        ) t1
        ) t2 where emptyDays > 0
        ) t3 group by period
        ) t4
        group by t4.period
        order by t4.period) t1
    </select>
    <select id="getEmptyPeriodCount" resultType="java.lang.Integer">
        select coalesce(sum(t1.roomCount),0) from (
        select t3.emptyPeriod as period, count(1) as roomCount
        from (
        select case when emptyDays > 0 and emptyDays &lt;= 30 then '1'
        when emptyDays > 30 and emptyDays &lt;= 60 then '2'
        when emptyDays > 60 and emptyDays &lt;= 120 then '3'
        when emptyDays > 120 and emptyDays &lt;= 240 then '4'
        when emptyDays > 240 and emptyDays &lt;= 360 then '5'
        when emptyDays > 360 then '6'
        else '' end as emptyPeriod
        from (
        select (#{req.currentDate}::date - realDate::date)::int as emptyDays
        From (
        select a.id,max(case when b.room_id is null then a.create_time
        when b.room_id is not null and b.moved = true then reality_move_out_date
        else #{req.currentDate}::date end ) as realDate
        from ads_pm_room a
        left join ads_pm_floor t1 on a.floor_id = t1.id
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        left join v_enterprise b on a.id = b.room_id
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and t3.community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and t2.project_id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and t1.building_id = #{req.code}
            </when>
        </choose>
        and a.id not in (select room_id from v_enterprise
        where 1=1
        and moved = false
        and room_id is not null
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and project_id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
        )
        group by a.id
        ) t1
        ) t2 where emptyDays > 0
        ) t3 group by period
        ) t1

    </select>
    <select id="listFloorEmptyAreaRank" resultType="com.zjhh.economy.vo.analyzecockpit.FloorEmptyAreaRankVo">
        select concat(t1.buildingName, '-', t1.floorName) as floorName, coalesce(emptyArea,0) as emptyArea,
        coalesce(roomCount,0)
        as emptyCount
        from (select (select floor_name from ads_pm_floor where id = apf.id) as floorName,
        (select building_name from ads_pm_building where id = apf.building_id) as buildingName,
        (select sum(building_area)
        from ads_pm_room
        where floor_id = apf.id
        and id not in (select room_id
        from v_enterprise
        where floor_id = apf.id and moved = false and room_id is not null)) as emptyArea,
        (select count(1)
        from ads_pm_room
        where floor_id = apf.id
        and id not in (select room_id
        from v_enterprise
        where floor_id = apf.id and moved = false and room_id is not null)) as roomCount
        from ads_pm_floor apf
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and apf.building_id in ((select id from ads_pm_building where
                project_id in (select project_id from ads_empty_area_rank_project where community_code = #{req.code} and
                defaulted = true)))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and apf.building_id in ((select id from ads_pm_building where
                project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and apf.building_id = #{req.code}
            </when>
            <otherwise>
                and apf.building_id in (select id from ads_pm_building where project_id in (select project_id from
                ads_empty_area_rank_project where defaulted = true ))
            </otherwise>
        </choose>
        group by id, building_id) t1
        order by emptyArea desc
        limit 10
    </select>
    <select id="listIndustryFocusAnalyze"
            resultType="com.zjhh.economy.vo.analyzecockpit.IndustryFocusAnalyzeVo">
        SELECT
        t2.industryCode,
        (SELECT NAME FROM dm_gy_hy WHERE code = t2.industryCode) AS industryName,
        SUM(t2.taxScaled) AS taxScaled,
        SUM(t2.taxScaledZb) AS taxScaledZb,
        SUM(t2.entCount) AS entCount,
        SUM(t2.entCountZb) AS entCountZb,
        round(SUM(t2.settledArea), 2) AS settledArea,
        SUM(t2.settledAreaZb) AS settledAreaZb
        FROM
        (
        WITH data3 AS (
        WITH data1 AS (
        SELECT enterprise_id, SUM(bq_amt) AS bq_amt
        FROM ads_pm_enterprise_tax_fkm ape
        WHERE datekey = #{req.taxCurrentYearMonth} AND yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    AND ape.building_id IN (SELECT id FROM ads_pm_building WHERE project_id IN
                    (SELECT id FROM ads_pm_project WHERE project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                AND ape.building_id IN (
                SELECT DISTINCT building_id
                FROM v_enterprise ve
                WHERE 1=1 AND moved = false AND check_in_date IS NOT NULL
                AND substr(industry_code, 1, ${req.industryLength}) IN
                (SELECT code FROM dm_gy_hy WHERE length(code) = ${req.industryLength})
                <if test="req.projectType != null and req.projectType != ''">
                    AND ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        AND ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        AND ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        AND ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        GROUP BY enterprise_id
        ),
        data2 AS (
        SELECT * FROM ads_pm_enterprise ape
        WHERE substr(ape.industry_code, 1, ${req.industryLength}) IN
        (SELECT code FROM dm_gy_hy WHERE LENGTH(code) = ${req.industryLength})
        )
        SELECT
        substr(ape.industry_code, 1, ${req.industryLength}) AS industryCode,
        COALESCE(SUM(bq_amt), 0) AS ss
        FROM data1 apet
        INNER JOIN data2 ape ON ape.ID = apet.enterprise_id

        GROUP BY substr(ape.industry_code, 1, ${req.industryLength})
        )
        SELECT
        t1.industryCode,
        round(t1.ss / 10000, 2) AS taxScaled,
        CASE
        WHEN sum(t1.ss)over() = 0 THEN 0
        ELSE round(t1.ss/sum(t1.ss)over() * 100, 2)
        END AS taxScaledZb,
        0 AS entCount,
        0 AS entCountZb,
        0 AS settledArea,
        0 AS settledAreaZb
        FROM data3 t1

        UNION ALL

        SELECT
        t1.industryCode,
        0 AS taxScaled,
        0 AS taxScaledZb,
        entCount,
        CASE
        WHEN t1.totalCount = 0 THEN 0
        ELSE round(t1.entCount::DECIMAL / t1.totalCount::DECIMAL * 100, 1)
        END AS entCountZb,
        0 AS settledArea,
        0 AS settledAreaZb
        FROM
        (
        SELECT
        substr(industry_code, 1, ${req.industryLength}) AS industryCode,
        COUNT(1) AS entCount,
        (
        SELECT COUNT(1)
        FROM ads_pm_enterprise
        WHERE
        ID IN (
        SELECT DISTINCT enterprise_id
        FROM v_enterprise ve
        WHERE moved = FALSE AND check_in_date IS NOT NULL
        <if test="req.projectType != null and req.projectType != ''">
            AND ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                AND ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                AND ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                AND ve.building_id = #{req.code}
            </when>
        </choose>
        )
        AND substr(industry_code, 1, ${req.industryLength}) IN
        (SELECT code FROM dm_gy_hy WHERE LENGTH(code) = ${req.industryLength})
        ) AS totalCount
        FROM
        ads_pm_enterprise
        WHERE
        ID IN (
        SELECT DISTINCT enterprise_id
        FROM v_enterprise ve
        WHERE moved = FALSE AND check_in_date IS NOT NULL
        <if test="req.projectType != null and req.projectType != ''">
            AND ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                AND ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                AND ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                AND ve.building_id = #{req.code}
            </when>
        </choose>
        )
        AND substr(industry_code, 1, ${req.industryLength}) IN
        (SELECT code FROM dm_gy_hy WHERE LENGTH(code) = ${req.industryLength})
        GROUP BY
        substr(industry_code, 1, ${req.industryLength})
        ) t1

        UNION ALL

        SELECT
        t1.industryCode,
        0 AS taxScaled,
        0 AS taxScaledZb,
        0 AS entCount,
        0 AS entCountZb,
        t1.settledArea,
        CASE
        WHEN t1.totalArea = 0 THEN 0
        ELSE round(t1.settledArea / t1.totalArea * 100, 2)
        END AS settledAreaZb
        FROM
        (
        SELECT
        substr(industry_code, 1, ${req.industryLength}) AS industryCode,
        SUM(settled_area) AS settledArea,
        (
        SELECT SUM(settled_area)
        FROM v_enterprise ve
        WHERE
        moved = FALSE
        AND check_in_date IS NOT NULL
        <if test="req.projectType != null and req.projectType != ''">
            AND ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                AND ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                AND ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                AND ve.building_id = #{req.code}
            </when>
        </choose>
        AND substr(industry_code, 1, ${req.industryLength}) IN
        (SELECT code FROM dm_gy_hy WHERE length(code) = ${req.industryLength})
        ) AS totalArea
        FROM
        v_enterprise ve
        WHERE
        moved = FALSE
        AND check_in_date IS NOT NULL
        <if test="req.projectType != null and req.projectType != ''">
            AND ve.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                AND ve.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                AND ve.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                AND ve.building_id = #{req.code}
            </when>
        </choose>
        AND substr(industry_code, 1, ${req.industryLength}) IN
        (SELECT code FROM dm_gy_hy WHERE length(code) = ${req.industryLength})
        GROUP BY
        substr(industry_code, 1, ${req.industryLength})
        ) t1
        ) t2
        GROUP BY
        t2.industryCode
        <choose>
            <when test="req.orderType == 1">
                ORDER BY sum(t2.taxScaled) DESC
            </when>
            <when test="req.orderType == 2">
                ORDER BY sum(t2.entCount) DESC
            </when>
            <otherwise>
                ORDER BY sum(t2.settledArea) DESC
            </otherwise>
        </choose>
        LIMIT 10
    </select>
    <select id="getEntSettledSummary" resultType="com.zjhh.economy.vo.analyzecockpit.EntSettledSummaryVo">
        with data1 as (select 'summary' as code,sum(ent_count) as entCount, sum(territorialized_ent_count) as
        localedEntCount,
        case when sum(ent_count) = 0 then 0 else round(sum(territorialized_ent_count)::decimal / sum(ent_count)::decimal
        * 100 , 2) end as zb
        from ads_pm_ent_territorialized t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and t3.community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and t3.id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and t1.building_id = #{req.code}
            </when>
        </choose>
        ),
        data2 as (select 'summary' as code,sum(ent_count) as lastMonthEntCount, sum(territorialized_ent_count) as
        lastMonthLocaledEntCount,
        case when sum(ent_count) = 0 then 0 else round(sum(territorialized_ent_count)::decimal / sum(ent_count)::decimal
        * 100 , 2) end as lastMonthZb
        from ads_pm_ent_territorialized t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where datekey = #{req.taxLastMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and t3.community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and t3.id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and t1.building_id = #{req.code}
            </when>
        </choose>
        )
        , data3 as (
        select 'summary' as code, sum(ent_count) as lastYearEntCount, sum(territorialized_ent_count) as
        lastYearLocaledEntCount,
        case when sum(ent_count) = 0 then 0 else round(sum(territorialized_ent_count)::decimal / sum(ent_count)::decimal
        * 100 , 2) end as lastYearZb
        from ads_pm_ent_territorialized t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where datekey = #{req.taxLastYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and t3.community_code = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and t3.id = #{req.code}
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and t1.building_id = #{req.code}
            </when>
        </choose>
        )
        select t1.entCount as settledTotalCount, t1.localedEntCount as registerCount,t1.zb as registerRate,case when
        t3.lastYearEntCount = 0 then 0 else round((t1.entCount - t3.lastYearEntCount)::decimal /
        t3.lastYearEntCount::decimal * 100 ,2) end as settledTotalCountTb
        ,case when t2.lastMonthEntCount = 0 then 0 else round((t1.entCount - t2.lastMonthEntCount)::decimal /
        t2.lastMonthEntCount::decimal * 100 ,2) end as settledTotalCountHb,
        case when t3.lastYearLocaledEntCount = 0 then 0 else round((t1.localedEntCount -
        t3.lastYearLocaledEntCount)::decimal / t3.lastYearLocaledEntCount::decimal * 100 ,2) end as registerCountTb
        ,case when t2.lastMonthLocaledEntCount = 0 then 0 else round((t1.localedEntCount -
        t2.lastMonthLocaledEntCount)::decimal / t2.lastMonthLocaledEntCount::decimal * 100 ,2) end as registerCountHb,
        case when t3.lastYearZb = 0 then 0 else round((t1.zb - t3.lastYearZb)::decimal / t3.lastYearZb::decimal * 100
        ,2) end as registerRateTb
        ,case when t2.lastMonthZb = 0 then 0 else round((t1.zb - t2.lastMonthZb)::decimal / t2.lastMonthZb::decimal *
        100 ,2) end as registerRateHb
        from data1 t1 left join data2 t2 on t1.code = t2.code
        left join data3 t3 on t1.code = t3.code

    </select>
    <select id="listEntTaxRank" resultType="com.zjhh.economy.vo.analyzecockpit.EntTaxRankVo">
        select t1.enterprise_id, apb.enterprise_name as entName, round(t1.taxIncome / 10000,2) as taxIncome,
        case when t1.totalTaxIncome = 0 then 0 else round(t1.taxIncome / t1.totalTaxIncome * 100, 2) end as zb
        from (select enterprise_id, sum(by_amt) as taxIncome,(select sum(by_amt) from ads_pm_enterprise_tax_fkm where
        substr(datekey,1,4) = #{req.taxCurrentYear}
        and substr(datekey,5,6) &lt;= substr(#{req.taxCurrentYearMonth},5,6)
        and yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and building_id in (select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
        ) as
        totalTaxIncome
        from ads_pm_enterprise_tax_fkm apet
        where substr(apet.datekey, 1, 4) = #{req.taxCurrentYear}
        and substr(datekey,5,6) &lt;= substr(#{req.taxCurrentYearMonth},5,6)
        and yskm_dm = 'ss'
        and length(apet.enterprise_id) >= 9
        <if test="req.projectType != null and req.projectType != ''">
            and apet.building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and apet.building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and apet.building_id in (select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and apet.building_id = #{req.code}
            </when>
        </choose>
        group by enterprise_id) t1
        left join ads_pm_enterprise apb on t1.enterprise_id = apb.id
        order by taxIncome DESC
        limit 10
    </select>
    <select id="listEntSettledAreaRank" resultType="com.zjhh.economy.vo.analyzecockpit.EntSettledAreaVo">
        select t1.enterprise_id,
        apb.enterprise_name as entName,
        round(coalesce(t1.settledArea,0), 2) as settledArea,
        t1.totalArea,
        case when t1.totalArea = 0 then 0 else round(t1.settledArea / t1.totalArea * 100 , 2) end as zb

        From (
        select enterprise_id, sum(area) as settledArea,
        (select sum(area) from ads_pm_room_enterprise where moved = false
        <if test="req.projectType != null and req.projectType != ''">
            and building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and building_id in (select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
        and check_in_date is not null) as totalArea
        from ads_pm_room_enterprise
        where moved = false

        <if test="req.projectType != null and req.projectType != ''">
            and building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and building_id in (select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
        and check_in_date is not null
        group by enterprise_id
        ) t1 left join ads_pm_enterprise apb on t1.enterprise_id = apb.id
        order by coalesce(t1.settledArea,0) DESC
        limit 10
    </select>
    <select id="listEntStableRank" resultType="com.zjhh.economy.vo.analyzecockpit.EntStableRankVo">
        select * from (
        select ape.id as enterpriseId,ape.enterprise_name as entName, date_part('day',
        cast(max(expect_move_out_date) as timestamp) - cast(now() as timestamp) )
        ::integer as settledTime
        from ads_pm_enterprise ape
        left join ads_pm_room_enterprise apem
        on apem.enterprise_id = ape.id
        where apem.moved = false
        and apem.reality_move_out_date is null
        <if test="req.projectType != null and req.projectType != ''">
            and apem.enterprise_id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and apem.enterprise_id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and apem.enterprise_id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and apem.enterprise_id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        group by ape.id,ape.enterprise_name
        ) t1
        where (t1.settledTime is not null or t1.settledTime != 0 )
        order by t1.settledTime asc
        limit 10

    </select>
    <select id="listEntLocaledDis" resultType="com.zjhh.economy.vo.analyzecockpit.EntLocaledDisVo">
        select '本地注册企业' as disName, sum(t1.territorialized_ent_count) as entCount,
        case when sum(t1.ent_count) = 0 then 0 else round(sum(t1.territorialized_ent_count)::decimal /
        sum(t1.ent_count)::decimal * 100,2) end as zb from ads_pm_ent_territorialized t1
        where datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and building_id in (select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
        union all
        select '异地注册企业' as disName, (sum(t1.ent_count) - sum(t1.territorialized_ent_count)) as entCount,case when
        sum(t1.ent_count) = 0 then 0 else round((sum(t1.ent_count) - sum(t1.territorialized_ent_count))::decimal /
        sum(t1.ent_count)::decimal * 100,2) end as zb from ads_pm_ent_territorialized t1
        where datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and building_id in (select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
    </select>
    <select id="listUnitPropertyDis" resultType="com.zjhh.economy.vo.analyzecockpit.UnitPropertyDisVo">
        select t2.unitPropertyName,t2.entCount as unitCount,
        case when t2.totalCount = 0 then 0 else round(t2.entCount::decimal / t2.totalCount::decimal * 100 , 2) end as zb
        from (select case
        when t1.unit_property = 1 then '法人单位'
        when t1.unit_property = 2 then '个体经营户'
        when t1.unit_property = 3 then '产业活动单位'
        else '' end as unitPropertyName,
        t1.entCount,
        (select count(1) from ads_pm_enterprise
        where 1=1
        and id in (select distinct enterprise_id from v_enterprise where moved = false)
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        ) as totalCount
        from (Select unit_property, count(1) as entCount from ads_pm_enterprise
        where 1=1
        and id in (select distinct enterprise_id from v_enterprise where moved = false)
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        group by unit_property) t1) t2
    </select>
    <select id="listEntTaxIncome" resultType="com.zjhh.economy.vo.analyzecockpit.EntTaxIncomeVo">

        select t2.level,sum(t2.entCount) as entCount from (
        select '1' as level, 0 as entCount
        union all
        select '2' as level, 0 as entCount
        union all
        select '3' as level, 0 as entCount
        union all
        select '4' as level, 0 as entCount
        union all
        select '5' as level, 0 as entCount
        union all
        select level, count(1) as entCount from (
        select enterprise_id, case when sum(bq_amt) > 50000000 then '1'
        when sum(bq_amt) > 20000000 and sum(bq_amt)&lt;= 50000000 then '2'
        when sum(bq_amt) > 10000000 and sum(bq_amt)&lt;= 20000000 then '3'
        when sum(bq_amt) > 5000000 and sum(bq_amt)&lt;= 10000000 then '4'
        when sum(bq_amt) > 1000000 and sum(bq_amt) &lt;= 5000000 then '5'
        else null
        end as level
        from ads_pm_enterprise_tax_fkm
        where 1=1
        and yskm_dm = 'ss'
        and substr(datekey,1,4) = #{req.taxCurrentYear}
        and enterprise_id not in ('1','2')
        <if test="req.projectType != null and req.projectType != ''">
            and building_id in (select id from ads_pm_building where
            project_id in (select id from ads_pm_project where project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and building_id in (select id from ads_pm_building where
                project_id in (select id from ads_pm_project where community_code = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and building_id in (select id from ads_pm_building where
                project_id = #{req.code})
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and building_id = #{req.code}
            </when>
        </choose>
        group by enterprise_id

        ) t1
        group by t1.level
        ) t2
        group by t2.level

    </select>
    <select id="listRegisterBusinessSame"
            resultType="com.zjhh.economy.vo.analyzecockpit.RegisterBusinessSameVo">
        select t1.title as disName,
        t1.entCount,
        case when t1.totalCount = 0 then 0 else round(t1.entCount::decimal / t1.totalCount::decimal * 100, 2) end as zb
        from (select '注经一致' as title, count(1) as entCount, (select count(1) from ads_pm_enterprise
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        ) as totalCount
        from ads_pm_enterprise
        where registered_address = residence
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        union all
        select '注经不一致' as title, count(1) as entCount, (select count(1) from ads_pm_enterprise
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        ) as totalCount
        from ads_pm_enterprise
        where registered_address != residence
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        ) t1
    </select>
    <select id="listRegisterType" resultType="com.zjhh.economy.vo.analyzecockpit.RegisterTypeVo">

        WITH RECURSIVE tree AS (
        -- 基础查询：选择根节点
        SELECT code, parent_code, code AS root_code
        FROM dm_pm
        WHERE parent_code = 'root'

        UNION ALL

        -- 递归查询：选择子节点
        SELECT t.code, t.parent_code, tree.root_code
        FROM dm_pm t
        JOIN tree ON t.parent_code = tree.code)
        select (select name From dm_pm where type = 'EnterpriseType' and code = t2.enterprise_type_code) as
        registerTypeName,
        t2.entCount, round(t2.entCount / sum(t2.entCount) over() * 100, 2) as zb

        from (select t1.enterprise_type_code, t1.entCount
        from (Select tr.root_code as enterprise_type_code,
        count(1) as entCount
        from ads_pm_enterprise ape
        left join tree tr on ape.enterprise_type_code = tr.code
        where 1 = 1
        <if test="req.projectType != null and req.projectType != ''">
            and id in (select enterprise_id from v_enterprise where moved = false
            and project_id in (select id from ads_pm_project where project_type = #{req.projectType}) )
        </if>
        <choose>
            <when test="(req.type != null and req.type != '') and req.type == 2">
                and id in (select enterprise_id from v_enterprise where moved = false
                and project_id in (select id from ads_pm_project where community_code = #{req.code}) )
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 3">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id in (select id from ads_pm_building where project_id = #{req.code}))
            </when>
            <when test="(req.type != null and req.type != '') and req.type == 4">
                and id in (select enterprise_id from v_enterprise where moved = false
                and building_id = #{req.code}
                )
            </when>
        </choose>
        and enterprise_type_code in (select code From dm_pm where type = 'EnterpriseType')
        group by tr.root_code ) t1) t2
        order by t2.entCount DESC

    </select>
    <select id="getEconomicSummary" resultType="com.zjhh.economy.vo.analyzecockpit.EconomicSummaryVo">
        WITH data1 AS (
        SELECT
        t1.building_id,
        SUM ( t1.bq_amt ) AS bq_amt,
        SUM ( t1.sq_amt ) AS sq_amt,
        SUM ( t1.sy_amt ) AS sy_amt
        FROM
        (
        SELECT
        building_id,
        SUM ( bq_amt ) AS bq_amt,
        0 sq_amt,
        0 sy_amt
        FROM
        ads_pm_enterprise_tax_fkm
        WHERE datekey = #{req.taxCurrentYearMonth}
        AND yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id
        UNION ALL
        SELECT
        building_id,
        0 AS bq_amt,
        SUM ( bq_amt ) AS sq_amt,
        0 AS sy_amt
        FROM
        ads_pm_enterprise_tax_fkm
        WHERE
        datekey = #{req.taxLastYearMonth}
        AND yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id UNION ALL
        SELECT
        building_id,
        0 AS bq_amt,
        0 AS sq_amt,
        SUM ( bq_amt ) AS sy_amt
        FROM
        ads_pm_enterprise_tax_fkm
        WHERE
        datekey = #{req.taxLastMonth}
        AND yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id
        ) t1
        GROUP BY
        building_id
        ),
        data2 AS (
        SELECT
        t1.building_id,
        SUM ( t1.bq_amt ) AS bq_amt,
        SUM ( t1.sq_amt ) AS sq_amt,
        SUM ( t1.sy_amt ) AS sy_amt
        FROM
        (
        SELECT
        building_id,
        SUM ( bq_amt ) AS bq_amt,
        0 sq_amt,
        0 sy_amt
        FROM
        ads_pm_enterprise_tax_fkm
        WHERE
        datekey = #{req.taxCurrentYearMonth}
        AND yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY building_id
        UNION ALL
        SELECT
        building_id,
        0 AS bq_amt,
        SUM ( bq_amt ) AS sq_amt,
        0 AS sy_amt
        FROM
        ads_pm_enterprise_tax_fkm
        WHERE datekey = #{req.taxLastYearMonth}
        AND yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id
        UNION ALL
        SELECT
        building_id,
        0 AS bq_amt,
        0 AS sq_amt,
        SUM ( bq_amt ) AS sy_amt
        FROM
        ads_pm_enterprise_tax_fkm
        WHERE
        datekey = #{req.taxLastMonth}
        AND yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id
        ) t1
        GROUP BY
        building_id
        ),
        data3 AS (
        SELECT
        t1.building_id,
        SUM ( t1.bq_amt ) AS bq_amt,
        SUM ( t1.sq_amt ) AS sq_amt,
        SUM ( t1.sy_amt ) AS sy_amt
        FROM
        (
        SELECT
        building_id,
        SUM ( bq_amt ) AS bq_amt,
        0 AS sq_amt,
        0 AS sy_amt
        FROM
        ads_pm_enterprise_tax_fkm apetk
        LEFT JOIN ads_pm_enterprise ape ON apetk.enterprise_id = ape.ID
        WHERE
        datekey = #{req.taxCurrentYearMonth}
        AND yskm_dm = 'ss'
        AND ape.industry_code IN ( '701', '7010' )
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id UNION ALL
        SELECT
        building_id,
        0 AS bq_amt,
        SUM ( bq_amt ) AS sq_amt,
        0 AS sy_amt
        FROM
        ads_pm_enterprise_tax_fkm apetk
        LEFT JOIN ads_pm_enterprise ape ON apetk.enterprise_id = ape.ID
        WHERE
        datekey = #{req.taxLastYearMonth}
        AND yskm_dm = 'ss'
        AND ape.industry_code IN ( '701', '7010' )
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id UNION ALL
        SELECT
        building_id,
        0 AS bq_amt,
        0 AS sq_amt,
        SUM ( bq_amt ) AS sy_amt
        FROM
        ads_pm_enterprise_tax_fkm apetk
        LEFT JOIN ads_pm_enterprise ape ON apetk.enterprise_id = ape.ID
        WHERE
        datekey = #{req.taxLastMonth}
        AND yskm_dm = 'ss'
        AND ape.industry_code IN ( '701', '7010' )
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>

                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and building_id in (select id from ads_pm_building where project_id in (select id from
                        ads_pm_project where community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and building_id in (select id from ads_pm_building where project_id = #{req.code})
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and building_id = #{req.code}
                    </when>
                </choose>

            </otherwise>
        </choose>
        GROUP BY
        building_id
        ) t1
        GROUP BY
        building_id
        ) SELECT
        round( SUM ( t1.bq_amt ) / 10000, 2 ) AS taxIncome,
        CASE

        WHEN SUM ( t1.sq_amt ) = 0 THEN
        0 ELSE round( ( SUM ( t1.bq_amt ) - SUM ( t1.sq_amt ) ) / SUM ( t1.sq_amt ) * 100, 2 )
        END taxIncomeTb,
        CASE

        WHEN SUM ( t1.sy_amt ) = 0 THEN
        0 ELSE round( ( SUM ( t1.bq_amt ) - SUM ( t1.sy_amt ) ) / SUM ( t1.sy_amt ) * 100, 2 )
        END taxIncomeHb,
        round( SUM ( t2.bq_amt ) / 10000, 2 ) AS streetTaxIncome,
        CASE

        WHEN SUM ( t2.sq_amt ) = 0 THEN
        0 ELSE round( ( SUM ( t2.bq_amt ) - SUM ( t2.sq_amt ) ) / SUM ( t2.sq_amt ) * 100, 2 )
        END streetTaxIncomeTb,
        CASE

        WHEN SUM ( t2.sy_amt ) = 0 THEN
        0 ELSE round( ( SUM ( t2.bq_amt ) - SUM ( t2.sy_amt ) ) / SUM ( t2.sy_amt ) * 100, 2 )
        END streetTaxIncomeHb,
        round( ( SUM ( t1.bq_amt ) - COALESCE ( SUM ( t3.bq_amt ), 0 ) ) / 10000, 2 ) AS taxIncomeWithoutFdc,
        CASE

        WHEN ( SUM ( t1.sq_amt ) - COALESCE ( SUM ( t3.sq_amt ), 0 ) ) = 0 THEN
        0 ELSE round(
        (
        ( SUM ( t1.bq_amt ) - COALESCE ( SUM ( t3.bq_amt ), 0 ) ) - ( SUM ( t1.sq_amt ) - COALESCE ( SUM ( t3.sq_amt ),
        0 ) )
        ) / ( SUM ( t1.sq_amt ) - COALESCE ( SUM ( t3.sq_amt ), 0 ) ) * 100,
        2
        )
        END taxIncomeWithoutFdcTb,
        CASE

        WHEN ( SUM ( t1.sy_amt ) - COALESCE ( SUM ( t3.sy_amt ), 0 ) ) = 0 THEN
        0 ELSE round(
        (
        ( SUM ( t1.bq_amt ) - COALESCE ( SUM ( t3.bq_amt ), 0 ) ) - ( SUM ( t1.sy_amt ) - COALESCE ( SUM ( t3.sy_amt ),
        0 ) )
        ) / ( SUM ( t1.sy_amt ) - COALESCE ( SUM ( t3.sy_amt ), 0 ) ) * 100,
        2
        )
        END taxIncomeWithoutFdcHb,
        CASE

        WHEN SUM ( COALESCE ( business_area, 0 ) ) = 0 THEN
        0 ELSE round( SUM ( t1.bq_amt ) / SUM ( COALESCE ( business_area, 0 ) ), 2 )
        END unitTaxIncome,
        CASE

        WHEN SUM ( t1.sq_amt ) = 0
        OR SUM ( COALESCE ( business_area, 0 ) ) = 0 THEN
        0 ELSE round(
        (
        SUM ( t1.bq_amt ) / COALESCE ( SUM ( business_area ), 0 ) / ( SUM ( t1.sq_amt ) / COALESCE ( SUM ( business_area
        ), 0 ) ) - 1
        ) * 100,
        2
        )
        END AS unitTaxIncomeTb,
        CASE

        WHEN SUM ( t1.sy_amt ) = 0
        OR SUM ( COALESCE ( business_area, 0 ) ) = 0 THEN
        0 ELSE round(
        (
        SUM ( t1.bq_amt ) / COALESCE ( SUM ( business_area ), 0 ) / ( SUM ( t1.sy_amt ) / COALESCE ( SUM ( business_area
        ), 0 ) ) - 1
        ) * 100,
        2
        )
        END AS unitTaxIncomeHb,
        round(
        ( SUM ( t1.bq_amt ) - COALESCE ( SUM ( t3.bq_amt ), 0 ) ) / COALESCE ( SUM ( business_area ), 0 ),
        2
        ) unitTaxIncomeWithoutFdc,
        CASE

        WHEN SUM ( t1.sq_amt ) - COALESCE ( SUM ( t3.sq_amt ), 0 ) = 0
        OR COALESCE ( SUM ( business_area ), 0 ) = 0 THEN
        0 ELSE round(
        (
        (
        ( SUM ( t1.bq_amt ) - COALESCE ( SUM ( t3.bq_amt ), 0 ) ) / COALESCE ( SUM ( business_area ), 0 )
        ) / (
        SUM ( t1.sq_amt ) - COALESCE ( SUM ( t3.sq_amt ), 0 ) / COALESCE ( SUM ( business_area ), 0 )
        ) - 1
        ) * 100,
        2
        )
        END AS unitTaxIncomeWithoutFdcTb,
        CASE

        WHEN SUM ( t1.sy_amt ) - COALESCE ( SUM ( t3.sy_amt ), 0 ) = 0
        OR COALESCE ( SUM ( business_area ), 0 ) = 0 THEN
        0 ELSE round(
        (
        (
        ( SUM ( t1.bq_amt ) - COALESCE ( SUM ( t3.bq_amt ), 0 ) ) / COALESCE ( SUM ( business_area ), 0 )
        ) / (
        SUM ( t1.sy_amt ) - COALESCE ( SUM ( t3.sy_amt ), 0 ) / COALESCE ( SUM ( business_area ), 0 )
        ) - 1
        ) * 100,
        2
        )
        END AS unitTaxIncomeWithoutFdcHb
        FROM
        data1 t1
        LEFT JOIN data2 t2 ON t1.building_id = t2.building_id
        LEFT JOIN data3 t3 ON t1.building_id = t3.building_id
        FULL JOIN ( SELECT ID, SUM ( COALESCE ( business_area, 0 ) ) business_area FROM ads_pm_building ve
        where 1=1
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="(req.projectType == null or req.projectType == '') or req.projectType == 'ly'">
                    and id in (select id from ads_pm_building where project_id in ( select project_id from
                    ads_empty_area_rank_project where defaulted = true))
                </if>
                <if test="(req.projectType != null and req.projectType != '') and req.projectType != 'ly'">
                    and id in (select id from ads_pm_building where project_id in (select id from ads_pm_project where
                    project_type = #{req.projectType}))

                </if>
            </when>
            <otherwise>
                <if test="req.projectType != null and req.projectType != ''">
                    and id in (select id from ads_pm_building where project_id in (select id from ads_pm_project where
                    project_type = #{req.projectType}))
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                        where
                        community_code = #{req.code}))
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.id = #{req.code}
                    </when>
                </choose>
            </otherwise>
        </choose>
        GROUP BY ID ) t4 ON t1.building_id = t4.ID
        WHERE
        1 = 1


    </select>
    <select id="getTaxIncomeReasonByTax" resultType="com.zjhh.economy.vo.analyzecockpit.TaxIncomeReasonVo">
        select sum(t1.ss) / 10000 as taxIncome,
        sum(t1.tqSs) / 10000 as tqSs,
        ( sum(t1.ss) - sum(t1.tqSs)) / 10000 as addTaxIncome,
        case
        when sum(t1.tqSs) = 0 then 0
        else round((sum(t1.ss) - sum(t1.tqSs)) / sum(t1.tqSs) * 100, 2) end as zf

        from (select coalesce(sum(by_amt), 0) as ss, 0 as tqSs

        from ads_pm_enterprise_tax_fkm apet

        where substr(datekey, 1, 4) = #{req.taxCurrentYear}
        and substr(datekey, 5,6) &lt;= substr(#{req.taxCurrentYearMonth},5,6)
        and apet.yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        union all
        select 0 as ss, coalesce(sum(by_amt), 0) as tqSs

        from ads_pm_enterprise_tax_fkm apet
        where substr(datekey, 1, 4) = #{req.taxLastYear}
        and substr(datekey, 5,6) &lt;= substr(#{req.taxLastYearMonth},5,6)
        and apet.yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        ) t1
    </select>
    <select id="getTaxIncomeReasonByEntTax" resultType="java.math.BigDecimal">
        select coalesce(bq_ss - sq_ss,0) / 10000
        from (with data1 as (select enterprise_id, sum(bq_amt) as bq_ss
        from ads_pm_enterprise_tax_fkm
        where datekey = #{req.taxCurrentYearMonth}
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by enterprise_id),
        data2 as (select enterprise_id, sum(bq_amt) as bq_ss
        from ads_pm_enterprise_tax_fkm
        where datekey = #{req.taxLastYearMonth}
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by enterprise_id)
        select sum(a.bq_ss) as bq_ss, sum(b.bq_ss) as sq_ss
        from data1 a
        inner join data2 b on a.enterprise_id = b.enterprise_id) a
    </select>
    <select id="getTaxIncomeReasonByMove" resultType="java.math.BigDecimal">
        select coalesce(sum(bq_ss) - sum(sq_ss) - (sum(bq_clss) - sum(sq_clss)), 0) / 10000
        from (with data1 as (select enterprise_id, sum(bq_amt) as bq_ss
        from ads_pm_enterprise_tax_fkm
        where datekey = #{req.taxCurrentYearMonth}
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by enterprise_id),
        data2 as (select enterprise_id, sum(bq_amt) as bq_ss
        from ads_pm_enterprise_tax_fkm
        where datekey = #{req.taxLastYearMonth}
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by enterprise_id)
        select sum(a.bq_ss) as bq_clss, sum(b.bq_ss) as sq_clss, 0 as bq_ss, 0 as sq_ss
        from data1 a
        inner join data2 b on a.enterprise_id = b.enterprise_id
        union all
        select 0 as bq_clss, 0 as sq_clss, sum(bq_Ss) as bq_ss, 0 as sq_ss
        from data1 a
        union all
        select 0 as bq_clss, 0 as sq_clss, 0 as bq_ss, sum(bq_Ss) as sq_ss
        from data2 a) a
    </select>
    <select id="listStreetTaxTrendByMonth" resultType="com.zjhh.economy.vo.analyzecockpit.TaxTrendVo">
        select concat(substr(datekey,3,2),'-',substr(datekey,5,6)) as datekey,
        round(sum(ss)/ 10000, 2) as taxIncome,
        round(sum(tqss) / 10000, 2) as sqTaxIncome,
        case when sum(tqss) = 0 then 0 else round((sum(ss) - sum(tqss)) / sum(tqss) * 100, 2)
        end
        as zf
        from (WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1
        month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n)
        select datekey, 0 as ss, 0 as tqss
        from all_months
        union all
        (select apet.datekey,
        sum(by_amt) as ss,
        (select sum(by_amt)
        from ads_pm_enterprise_tax_fkm
        where datekey = (apet.datekey::decimal - 100) ::varchar
        and yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        )
        as tqss
        from ads_pm_enterprise_tax_fkm apet
        where 1 = 1
        and yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        and apet.datekey in (select datekey from all_months)
        group by apet.datekey)) t1
        group by datekey
    </select>
    <select id="listStreetTaxTrendByQuarter" resultType="com.zjhh.economy.vo.analyzecockpit.TaxTrendVo">
        select case
        when substr(month,5,6) in ('01','02','03') then concat(substr(month,1,4),'1')
        when substr(month,5,6) in ('04','05','06') then concat(substr(month,1,4),'2')
        when substr(month,5,6) in ('07','08','09') then concat(substr(month,1,4),'3')
        when substr(month,5,6) in ('10','11','12') then concat(substr(month,1,4),'4') end as datekey,
        round(sum(ss)/10000 ,2) as taxIncome,
        round( sum(tqss) / 10000 ,2) as sqTaxIncome,
        case when sum(tqss) = 0 then 0 else round((sum(ss) - sum(tqss)) / sum(tqss) * 100, 2)
        end
        as zf
        from (WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1
        month'),
        'YYYYMM') ::varchar AS month
        FROM generate_series(0, 11) n)
        select month, 0 as ss, 0 as tqss
        from all_months
        union all
        (select apet.datekey as month,
        sum(by_amt) as ss,
        (select sum(by_amt)
        from ads_pm_enterprise_tax_fkm
        where datekey = (apet.datekey::decimal - 100) ::varchar
        and yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        )
        as tqss
        from ads_pm_enterprise_tax_fkm apet
        where 1 = 1
        and apet.datekey in (select month from all_months)
        and apet.yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by apet.datekey)) t1
        group by datekey
    </select>
    <select id="listStreetTaxTrendByYear" resultType="com.zjhh.economy.vo.analyzecockpit.TaxTrendVo">
        with data1 as (
        select a.*, to_char(add_months(to_date(a.datekey,'yyyymm'),-12),'yyyymm') as sq_date from (
        select substr(datekey,1,4) as year,max(datekey) as datekey
        from ads_pm_enterprise_tax_fkm a
        where yskm_dm = 'jdsr'

        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by substr(datekey,1,4)
        ) a
        ),data2 as (
        select datekey,sum(bq_amt) as bq_amt
        from ads_pm_enterprise_tax_fkm a where yskm_dm = 'jdsr'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey
        )
        select concat(a.year, '年') as datekey,
        round(sum(b.bq_amt) / 10000,2) as taxIncome ,
        round(sum(c.bq_amt) / 10000, 2) as sqTaxIncome,
        case when sum(c.bq_amt) = 0 then 0 else round((sum(b.bq_amt)/sum(c.bq_amt)-1)*100,1) end as zf
        from data1 a
        inner join data2 b on a.datekey = b.datekey
        left join data2 c on a.sq_date = c.datekey
        group by concat(a.year, '年')
    </select>
    <select id="listUnitIncomeTrendByMonth" resultType="com.zjhh.economy.vo.analyzecockpit.UnitIncomeTrendVo">
        select concat(substr(datekey,3,2),'-',substr(datekey,5,6)) as datekey,

        case when <include refid="businessArea"></include> = 0 then 0 else round(sum(ss) / <include
            refid="businessArea"></include>, 2) end as unitIncome,
        case
        when <include refid="businessArea"></include> = 0 then 0
        else round((sum(ss) - sum(fdcss)) / <include refid="businessArea"></include>, 2) end as unitIncomeWithoutFdc

        from (WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1
        month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n)
        select datekey, 0 as ss, 0 as jdsr, 0 as fdcss
        from all_months
        union all
        select t1.datekey,sum(t1.ss) as ss, sum(t1.jdsr) as jdsr, 0 as fdcss from (
        select datekey, case when yskm_dm = 'ss' then coalesce(sum(by_amt), 0) else 0 end as ss, case when yskm_dm =
        'jdsr' then coalesce(sum(by_amt), 0) else 0 end as jdsr
        from ads_pm_enterprise_tax_fkm apet
        where datekey in (select datekey from all_months)
        and (yskm_dm = 'ss' or yskm_dm = 'jdsr')
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey,yskm_dm
        ) t1
        group by t1.datekey
        union all
        select datekey, 0 as ss, 0 as jdsr, coalesce(sum(by_amt), 0) fdcss
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        where ape.industry_code in ('701','7010')
        and datekey in (select datekey from all_months)
        and yskm_dm = 'ss'
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey) t1
        group by datekey
    </select>
    <select id="listUnitIncomeByQuarter" resultType="com.zjhh.economy.vo.analyzecockpit.UnitIncomeTrendVo">
        select
        case when substr(month,5,6) in ('01', '02',
        '03') then concat(substr(month,1,4),'1')
        when substr(month,5,6) in ('04','05',
        '06') then concat(substr(month,1,4),'2')
        when substr(month,5,6) in ('07','08',
        '09') then concat(substr(month,1,4),'3')
        when substr(month,5,6) in ('10','11',
        '12') then concat(substr(month,1,4),'4') else '' end as datekey,
        case when <include refid="businessArea"></include> = 0 then 0 else round(sum(ss) / <include
            refid="businessArea"></include>, 2) end as unitIncome,
        case
        when <include refid="businessArea"></include> = 0 then 0
        else round((sum(ss) - sum(fdcss)) / <include refid="businessArea"></include>, 2) end as unitIncomeWithoutFdc

        from (WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1
        month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n)
        select datekey as month, 0 as ss, 0 as jdsr, 0 as fdcss
        from all_months
        union all

        select t1.datekey,sum(t1.ss) as ss, sum(t1.jdsr) as jdsr, 0 as fdcss from (
        select datekey, case when yskm_dm = 'ss' then coalesce(sum(by_amt), 0) else 0 end as ss, case when yskm_dm =
        'jdsr' then coalesce(sum(by_amt), 0) else 0 end as jdsr
        from ads_pm_enterprise_tax_fkm apet
        where datekey in (select datekey from all_months)
        and (yskm_dm = 'ss' or yskm_dm = 'jdsr')
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey,yskm_dm
        ) t1
        group by t1.datekey
        union all
        select datekey as month, 0 as ss, 0 as jdsr, coalesce(sum(by_amt), 0) fdcss
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        where ape.industry_code in ('701','7010')
        and apet.yskm_dm = 'ss'
        and datekey in (select datekey from all_months)
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey) t1
        group by datekey
    </select>
    <select id="listUnitIncomeTrendByYear" resultType="com.zjhh.economy.vo.analyzecockpit.UnitIncomeTrendVo">
        select
        substr(datekey,1,4) as datekey,
        case when <include refid="businessArea"></include> = 0 then 0 else round(sum(ss) / <include
            refid="businessArea"></include>, 2) end as unitIncome,
        case
        when <include refid="businessArea"></include> = 0 then 0
        else round((sum(ss) - sum(fdcss)) / <include refid="businessArea"></include>, 2) end as unitIncomeWithoutFdc

        from (
        select t1.datekey,sum(t1.ss) as ss, sum(t1.jdsr) as jdsr,0 as fdcss from (
        select datekey, case when yskm_dm = 'ss' then coalesce(sum(by_amt), 0) else 0 end as ss , case when yskm_dm =
        'jdsr' then coalesce(sum(by_amt), 0) else 0 end as jdsr
        from ads_pm_enterprise_tax_fkm apet
        where
        substr(datekey,1,4)::decimal >= (#{req.taxCurrentYear}::decimal - 5) and substr(datekey,1,4)::decimal &lt;=
        #{req.taxCurrentYear}::decimal
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey,yskm_dm
        ) t1
        group by t1.datekey
        union all
        select datekey, 0 as ss, 0 as jdsr, coalesce(sum(by_amt), 0) fdcss
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        where ape.industry_code in ('701','7010')
        and apet.yskm_dm = 'ss'
        and substr(datekey,1,4)::decimal >= (#{req.taxCurrentYear}::decimal - 5) and substr(datekey,1,4)::decimal &lt;=
        #{req.taxCurrentYear}::decimal
        <choose>
            <when test="req.type == '' or req.type == 1">
                <if test="req.projectType != null and req.projectType != ''">
                    and building_id in (select id from ads_pm_building where project_id in (select id from
                    ads_pm_project where project_type = #{req.projectType}))
                </if>
            </when>
            <otherwise>
                and building_id in ( select distinct building_id
                from v_enterprise ve
                where 1=1 and moved = false
                and check_in_date is not null
                <if test="req.projectType != null and req.projectType != ''">
                    and ve.project_type = #{req.projectType}
                </if>
                <choose>
                    <when test="req.type != null and req.type == 2">
                        and ve.community_code = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 3">
                        and ve.project_id = #{req.code}
                    </when>
                    <when test="req.type != null and req.type == 4">
                        and ve.building_id = #{req.code}
                    </when>
                </choose>
                )
            </otherwise>
        </choose>
        group by datekey) t1
        group by substr(datekey, 1, 4)
    </select>
    <select id="listIndustryTaxTrendByMonth"
            resultType="com.zjhh.economy.vo.analyzecockpit.IndustryTaxTrendVo">
        select t2.datekey,t2.sjhy_dm,t2.industryName, round(taxIncome / 10000 ,2) as taxIncome, case when
        sum(t2.taxIncome) over(partition by
        t2.datekey) = 0 then 0 else round(t2.taxIncome/sum(t2.taxIncome) over (partition by
        t2.datekey) * 100 ,2) end as zb from (
        select concat(substr(a.datekey,3,2),'-',substr(a.datekey,5,6)) as datekey, a.sjhy_dm, a.sjhy_mc as
        industryName, sum(bq_jdsr) as taxIncome from (
        with data1 as (select a.*
        from (select a.*, row_number() over (order by bq_jdsr desc) as rn
        from (with data1 as (select enterprise_id, sum(by_amt) bq_jdsr
        from ads_pm_enterprise_tax_fkm b
        where datekey between #{req.taxLastYearMonth} and #{req.taxCurrentYearMonth}
        and b.yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and b.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and b.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and b.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and b.building_id = #{req.code}
            </when>
        </choose>
        group by enterprise_id),
        data2 as (select *
        from dm_ly_hy_zq a
        where 1=1
        <choose>
            <when test="req.industryCodes != null and req.industryCodes.size > 0">
                and sjhy_dm in
                <foreach collection="req.industryCodes" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
                and length(sjhy_dm) = 2
            </otherwise>
        </choose>
        )
        select c.sjhy_dm, c.sjhy_mc, sum(b.bq_jdsr) as bq_jdsr
        from ads_pm_enterprise a
        inner join data1 b on a.id = b.enterprise_id
        inner join data2 c on a.industry_code = c.hy_dm
        group by c.sjhy_dm, c.sjhy_mc) a) a
        where a.rn &lt;= 5),
        data2 as (select b.datekey, a.industry_code, sum(by_amt) bq_jdsr
        from ads_pm_enterprise a
        inner join ads_pm_enterprise_tax_fkm b on a.id = b.enterprise_id
        AND b.yskm_dm = 'ss'
        where datekey between #{req.taxLastYearMonth} and #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and b.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and b.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and b.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and b.building_id = #{req.code}
            </when>
        </choose>
        group by b.datekey, a.industry_code)
        select a.datekey, b.sjhy_dm, b.sjhy_mc, sum(bq_jdsr) bq_jdsr
        from data2 a
        inner join (select * from dm_ly_hy_zq a where sjhy_dm in (select sjhy_dm from data1)) b
        on a.industry_code = b.hy_dm
        group by a.datekey, b.sjhy_dm, b.sjhy_mc
        union all
        select distinct a.mon as datekey , b.sjhy_dm, b.sjhy_mc, 0 bq_jdsr from dm_rq a, (select * from dm_ly_hy_zq a
        where sjhy_dm in (select sjhy_dm from data1)) b
        where mon between #{req.taxLastYearMonth} and #{req.taxCurrentYearMonth}
        ) a group by a.datekey, a.sjhy_dm, a.sjhy_mc
        order by a.datekey, a.sjhy_dm
        ) t2
    </select>
    <select id="listIndustryEntCountTrendByMonth"
            resultType="com.zjhh.economy.vo.analyzecockpit.IndustryContributeVo">

        select t2.sjhy_mc as industryName,t2.sjhy_dm,concat(substr(t2.datekey,1,4),'-',substr(t2.datekey,5,6)) as
        datekey,t2.entCount,case when sum(t2.entCount) over (partition by
        t2.datekey ) = 0 then 0 else round(t2.entCount / sum(t2.entCount) over (partition by
        t2.datekey ) * 100, 2) end as zb from (select t1.sjhy_dm,
        t1.sjhy_mc,
        t1.datekey,
        (select count(1) from (select apre.enterprise_id, dlhz.sjhy_dm
        from v_enterprise apre
        left join (select hy_dm, sjhy_dm from dm_ly_hy_zq ) dlhz
        on apre.industry_code = dlhz.hy_dm
        where to_char(check_in_date, 'YYYYMM') &lt;= t1.datekey
        and (to_char(reality_move_out_date, 'YYYYMM') > t1.datekey or moved = false)
        and dlhz.sjhy_dm = t1.sjhy_dm
        <if test="req.projectType != null and req.projectType != ''">
            and apre.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and apre.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and apre.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and apre.building_id = #{req.code}
            </when>
        </choose>
        group by dlhz.sjhy_dm, apre.enterprise_id, dlhz.sjhy_dm
        ) t2
        ) as entCount
        From (select distinct a.mon as datekey, b.sjhy_dm, b.sjhy_mc
        from dm_rq a, (select * from dm_ly_hy_zq a where
        1=1
        <choose>
            <when test="req.industryCodes != null and req.industryCodes.size > 0">
                and sjhy_dm in
                <foreach collection="req.industryCodes" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </when>
        </choose>
        ) b
        where mon between #{req.taxLastYearMonth} AND #{req.taxCurrentYearMonth} ) t1) t2
        order by t2.datekey, t2.sjhy_dm

    </select>


    <select id="listIndustrySettledAreaTrendByMonth"
            resultType="com.zjhh.economy.vo.analyzecockpit.IndustrySettledAreaVo">

        select t2.sjhy_mc as industryName,
        t2.sjhy_dm,
        concat(substr(t2.datekey, 1, 4), '-', substr(t2.datekey, 5, 6)) as datekey,
        round(t2.settledArea, 2) as settledArea,
        case
        when sum(t2.settledArea) over (partition by t2.datekey ) = 0 then 0
        else round(t2.settledArea / sum(t2.settledArea) over (partition by t2.datekey ) * 100, 2) end as zb
        from (
        with data1 as(
        select distinct a.mon as datekey, b.sjhy_dm, b.sjhy_mc
        from dm_rq a, (select * from dm_ly_hy_zq a where 1 = 1
        <choose>
            <when test="req.industryCodes != null and req.industryCodes.size > 0">
                and sjhy_dm in
                <foreach collection="req.industryCodes" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </when>
        </choose>
        ) b
        where mon between #{req.taxLastYearMonth} AND #{req.taxCurrentYearMonth}
        ), data2 as (
        select to_char(check_in_date, 'YYYYMM') as check_in_date,
        to_char(reality_move_out_date, 'YYYYMM') as reality_move_out_date,
        moved,dlhz.sjhy_dm,
        coalesce(sum(settled_area), 0) as settled_area
        from v_enterprise apre
        left join (select hy_dm, sjhy_dm from dm_ly_hy_zq) dlhz on apre.industry_code = dlhz.hy_dm
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and apre.project_type = #{req.projectType}
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and apre.community_code = #{req.code}
            </when>
            <when test="req.type != null and req.type == 3">
                and apre.project_id = #{req.code}
            </when>
            <when test="req.type != null and req.type == 4">
                and apre.building_id = #{req.code}
            </when>
        </choose>
        group by to_char(check_in_date, 'YYYYMM') ,
        to_char(reality_move_out_date, 'YYYYMM') ,
        moved,dlhz.sjhy_dm
        )
        select t1.sjhy_dm, t1.sjhy_mc,t1.datekey,
        coalesce(sum(settled_area), 0) as settledArea
        From data1 t1,data2 t2
        where check_in_date &lt;= t1.datekey
        and (reality_move_out_date > t1.datekey or moved = false)
        and t2.sjhy_dm = t1.sjhy_dm
        group by t1.sjhy_dm, t1.sjhy_mc,t1.datekey
        ) t2
        order by t2.sjhy_dm,datekey

    </select>

    <select id="listLocaledRegisterTrendByMonth"
            resultType="com.zjhh.economy.vo.analyzecockpit.LocaledRegisterTrendVo">
        select concat(substr(datekey,3,2),'-',substr(datekey,5,6)) as datekey, sum(t1.ent_count) as entTotalCount,
        sum(t1.territorialized_ent_count) as localedEntCount,
        case when sum(t1.ent_count) = 0 then 0 else round(sum(t1.territorialized_ent_count)::decimal /
        sum(t1.ent_count)::decimal * 100,2) end as localedZb
        from (
        WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n)
        select datekey, 0 as ent_count, 0 as territorialized_ent_count
        from all_months
        union all
        select datekey, ent_count, territorialized_ent_count
        from ads_pm_ent_territorialized a
        where datekey in (select datekey from all_months)
        <if test="req.projectType != null and req.projectType != ''">
            and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and a.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and a.building_id = #{req.code}
            </when>
        </choose>
        ) t1
        group by t1.datekey
        order by t1.datekey
    </select>
    <select id="listLocaledRegisterTrendByQuarter"
            resultType="com.zjhh.economy.vo.analyzecockpit.LocaledRegisterTrendVo">
        select datekey, sum(ent_count) as entTotalCount, sum(territorialized_ent_count) as localedEntCount, case when
        sum(ent_count) = 0 then 0 else round(sum(territorialized_ent_count)::decimal / sum(ent_count) * 100 ,
        2)::decimal end as localedZb from (
        WITH all_months AS (SELECT TO_CHAR(DATE_TRUNC('month', #{req.taxCurrentDate}::date) - (n * INTERVAL '1 month'),
        'YYYYMM') ::varchar AS datekey
        FROM generate_series(0, 11) n)
        select case when substr(datekey,5,6) in ('01', '02',
        '03') then concat(substr(datekey,1,4),'1')
        when substr(datekey,5,6) in ('04','05',
        '06') then concat(substr(datekey,1,4),'2')
        when substr(datekey,5,6) in ('07','08',
        '09') then concat(substr(datekey,1,4),'3')
        when substr(datekey,5,6) in ('10','11',
        '12') then concat(substr(datekey,1,4),'4') else '' end as datekey, 0 as ent_count, 0 as
        territorialized_ent_count
        from all_months
        union all
        select case when substr(datekey,5,6) in ('01', '02',
        '03') then concat(substr(datekey,1,4),'1')
        when substr(datekey,5,6) in ('04','05',
        '06') then concat(substr(datekey,1,4),'2')
        when substr(datekey,5,6) in ('07','08',
        '09') then concat(substr(datekey,1,4),'3')
        when substr(datekey,5,6) in ('10','11',
        '12') then concat(substr(datekey,1,4),'4') else '' end as datekey, ent_count, territorialized_ent_count
        from ads_pm_ent_territorialized a
        where datekey in (select datekey from all_months)
        <if test="req.projectType != null and req.projectType != ''">
            and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and a.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and a.building_id = #{req.code}
            </when>
        </choose>
        ) t1
        group by t1.datekey
    </select>
    <select id="listLocaledRegisterTrendByYear"
            resultType="com.zjhh.economy.vo.analyzecockpit.LocaledRegisterTrendVo">
        select datekey, sum(ent_count) as entTotalCount, sum(territorialized_ent_count) as localedEntCount,case when
        sum(ent_count) = 0 then 0 else round(sum(territorialized_ent_count)::decimal / sum(ent_count)::decimal * 100 ,
        2) end as localedZb from (
        select substr(datekey,1,4) as datekey, ent_count, territorialized_ent_count
        from ads_pm_ent_territorialized a
        where substr(datekey,1,4) between (#{req.taxCurrentYear}::decimal - 5)::varchar and #{req.currentYear}
        <if test="req.projectType != null and req.projectType != ''">
            and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and a.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and a.building_id = #{req.code}
            </when>
        </choose>
        ) t1
        group by t1.datekey
    </select>
    <select id="listTaxCategoryBySs" resultType="com.zjhh.economy.vo.analyzecockpit.TaxCategoryVo">
        select t1.yskm_dm,
        t1.yskm_mc as taxName,
        round(t1.taxIncome / 10000,0) as taxIncome,
        case
        when sum(t1.taxIncome) over ( ) = 0 then 0
        else round(t1.taxIncome / sum(t1.taxIncome) over ( ) * 100, 2) end as zb
        from (select tax_type yskm_dm,
        tax_name yskm_mc,
        sum(ss) as taxIncome
        from ads_pm_tax_struct_analyze a
        where a.datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and a.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and a.building_id = #{req.code}
            </when>
        </choose>
        group by tax_type, tax_name) t1
        order by t1.taxIncome DESC


    </select>
    <select id="listTaxCategoryByStreet" resultType="com.zjhh.economy.vo.analyzecockpit.TaxCategoryVo">

        select t1.yskm_dm,
        t1.yskm_mc as taxName,
        round(t1.taxIncome / 10000,0) as taxIncome,
        case
        when sum(t1.taxIncome) over ( ) = 0 then 0
        else round(t1.taxIncome / sum(t1.taxIncome) over ( ) * 100, 2) end as zb
        from (select tax_type yskm_dm,
        tax_name yskm_mc,
        sum(jdsr) as taxIncome
        from ads_pm_tax_struct_analyze a
        where a.datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and a.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and a.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and a.building_id = #{req.code}
            </when>
        </choose>
        group by tax_type, tax_name) t1
        order by t1.taxIncome DESC


    </select>
    <select id="listIndustryTop5" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select a.sjhy_dm as key, a.sjhy_mc as title, a.sjhy_dm as value
        from (select a.*, row_number() over (order by bq_jdsr desc) as rn
        from (with data1 as (select enterprise_id, sum(by_amt) bq_jdsr
        from ads_pm_enterprise_tax_fkm b
        where datekey between #{req.taxLastYearMonth} and #{req.taxCurrentYearMonth}

        and yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and b.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
            where
            project_type = #{req.projectType}))
        </if>
        <choose>
            <when test="req.type != null and req.type == 2">
                and b.building_id in (select id from ads_pm_building where project_id in (select id from ads_pm_project
                where
                community_code = #{req.code}))
            </when>
            <when test="req.type != null and req.type == 3">
                and b.building_id in (select id from ads_pm_building where project_id = #{req.code})
            </when>
            <when test="req.type != null and req.type == 4">
                and b.building_id = #{req.code}
            </when>
        </choose>
        group by enterprise_id),
        data2 as (select *
        from dm_ly_hy_zq a
        where 1=1
        and length(sjhy_dm) = 2
        )
        select c.sjhy_dm, c.sjhy_mc, sum(b.bq_jdsr) as bq_jdsr
        from ads_pm_enterprise a
        inner join data1 b on a.id = b.enterprise_id
        inner join data2 c on a.industry_code = c.hy_dm
        group by c.sjhy_dm, c.sjhy_mc) a) a
        where a.rn &lt;= 5
    </select>
    <select id="listAppEconomicByCommunity" resultType="com.zjhh.economy.vo.analyzecockpit.AppEconomicVo">
        with data1 as (select community_code,
        sum(ss)                                                                 as taxIncome,
        sum(fdcss)                                                              as fdcss,
        sum(ss) - sum(fdcss)                                                    as cfdcss,
        case when sum(swzmj) = 0 then 0 else round(sum(ss) / sum(swzmj), 2) end as unitIncome
        from (select t3.community_code, sum(bq_amt) as ss, 0 as fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where t1.datekey = #{req.taxCurrentYearMonth}
        and t1.yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t3.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t3.community_code
        union all
        select t3.community_code, 0 as ss, coalesce(sum(bq_amt), 0) fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        left join ads_pm_building t2 on apet.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where ape.industry_code in ('701', '7010')
        and apet.yskm_dm = 'ss'
        and apet.datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t3.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t3.community_code
        union all
        select t2.community_code, 0 as ss, 0 as fdcss, sum(business_area) as swzmj
        from ads_pm_building t1
        left join ads_pm_project t2 on t1.project_id = t2.id
                                           where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t2.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t2.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t2.community_code) t1
        group by community_code),
        data2 as (select community_code,
        sum(ss)                                                                 as taxIncome,
        sum(fdcss)                                                              as fdcss,
        sum(ss) - sum(fdcss)                                                    as cfdcss,
        case when sum(swzmj) = 0 then 0 else round(sum(ss) / sum(swzmj), 2) end as unitIncome
        from (select t3.community_code, sum(bq_amt) as ss, 0 as fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where t1.datekey = #{req.taxLastYearMonth}
        and t1.yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t3.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t3.community_code
        union all
        select t3.community_code, 0 as ss, coalesce(sum(bq_amt), 0) fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        left join ads_pm_building t2 on apet.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where ape.industry_code in ('701', '7010')
        and apet.yskm_dm = 'ss'
        and apet.datekey = #{req.taxLastYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t3.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t3.community_code
        union all
        select t2.community_code, 0 as ss, 0 as fdcss, sum(business_area) as swzmj
        from ads_pm_building t1
        left join ads_pm_project t2 on t1.project_id = t2.id
                                           where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t2.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t2.community_code in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t2.community_code) t1
        group by community_code)
        select (select name from dm_pm where type = 'Community' and code = t1.community_code) as name,
        <choose>
            <when test="req.queryType == 1">
                round(t1.taxIncome / 10000,2) as amount1,
                case
                when t2.taxIncome = 0 then 0
                else round((t1.taxIncome - t2.taxIncome) / t2.taxIncome * 100, 2) end as amount2
            </when>
            <when test="req.queryType == 2">
                round(t1.cfdcss / 10000,2) as amount1,
                case
                when t2.cfdcss = 0 then 0
                else round((t1.cfdcss - t2.cfdcss) / t2.cfdcss * 100, 2) end as amount2
            </when>
            <otherwise>
                round(t1.unitIncome,2) as amount1,
                case
                when t2.unitIncome = 0 then 0
                else round((t1.unitIncome - t2.unitIncome) / t2.unitIncome * 100, 2) end as amount2
            </otherwise>
        </choose>

        from data1 t1
        left join data2 t2 on t1.community_code = t2.community_code
        where t1.community_code is not null

    </select>
    <select id="listAppEconomicByProject" resultType="com.zjhh.economy.vo.analyzecockpit.AppEconomicVo">
        with data1 as (select project_id,
        sum(ss)                                                                 as taxIncome,
        sum(fdcss)                                                              as fdcss,
        sum(ss) - sum(fdcss)                                                    as cfdcss,
        case when sum(swzmj) = 0 then 0 else round(sum(ss) / sum(swzmj), 2) end as unitIncome
        from (select t2.project_id, sum(bq_amt) as ss, 0 as fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where t1.datekey = #{req.taxCurrentYearMonth}
        and t1.yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t2.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t2.project_id
        union all
        select t2.project_id, 0 as ss, coalesce(sum(bq_amt), 0) fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        left join ads_pm_building t2 on apet.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where ape.industry_code in ('701', '7010')
        and apet.yskm_dm = 'ss'
        and apet.datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t2.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t2.project_id
        union all
        select t1.project_id, 0 as ss, 0 as fdcss, sum(business_area) as swzmj
        from ads_pm_building t1
        left join ads_pm_project t2 on t1.project_id = t2.id
                                           where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t2.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t1.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t1.project_id) t1
        group by project_id),
        data2 as (select project_id,
        sum(ss)                                                                 as taxIncome,
        sum(fdcss)                                                              as fdcss,
        sum(ss) - sum(fdcss)                                                    as cfdcss,
        case when sum(swzmj) = 0 then 0 else round(sum(ss) / sum(swzmj), 2) end as unitIncome
        from (select t2.project_id, sum(bq_amt) as ss, 0 as fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where t1.datekey = #{req.taxLastYearMonth}
        and t1.yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t2.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t2.project_id
        union all
        select t2.project_id, 0 as ss, coalesce(sum(bq_amt), 0) fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        left join ads_pm_building t2 on apet.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where ape.industry_code in ('701', '7010')
        and apet.yskm_dm = 'ss'
        and apet.datekey = #{req.taxLastYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t2.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t2.project_id
        union all
        select t1.project_id, 0 as ss, 0 as fdcss, sum(business_area) as swzmj
        from ads_pm_building t1
        left join ads_pm_project t2 on t1.project_id = t2.id
                                           where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t2.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t1.project_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t1.project_id) t1
        group by project_id)
        select (select project_name from ads_pm_project where id = t1.project_id) as name,
        <choose>
            <when test="req.queryType == 1">
                round(t1.taxIncome / 10000,2) as amount1,
                case
                when t2.taxIncome = 0 then 0
                else round((t1.taxIncome - t2.taxIncome) / t2.taxIncome * 100, 2) end as amount2
            </when>
            <when test="req.queryType == 2">
                round(t1.cfdcss / 10000,2) as amount1,
                case
                when t2.cfdcss = 0 then 0
                else round((t1.cfdcss - t2.cfdcss) / t2.cfdcss * 100, 2) end as amount2
            </when>
            <otherwise>
                round(t1.unitIncome,2) as amount1,
                case
                when t2.unitIncome = 0 then 0
                else round((t1.unitIncome - t2.unitIncome) / t2.unitIncome * 100, 2) end as amount2
            </otherwise>
        </choose>

        from data1 t1
        left join data2 t2 on t1.project_id = t2.project_id
        where t1.project_id is not null
    </select>
    <select id="listAppEconomicByBuilding" resultType="com.zjhh.economy.vo.analyzecockpit.AppEconomicVo">
        with data1 as (select building_id,
        sum(ss)                                                                 as taxIncome,
        sum(fdcss)                                                              as fdcss,
        sum(ss) - sum(fdcss)                                                    as cfdcss,
        case when sum(swzmj) = 0 then 0 else round(sum(ss) / sum(swzmj), 2) end as unitIncome
        from (select t1.building_id, sum(bq_amt) as ss, 0 as fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where t1.datekey = #{req.taxCurrentYearMonth}
        and t1.yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t1.building_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t1.building_id
        union all
        select apet.building_id, 0 as ss, coalesce(sum(bq_amt), 0) fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        left join ads_pm_building t2 on apet.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where ape.industry_code in ('701', '7010')
        and apet.yskm_dm = 'ss'
        and apet.datekey = #{req.taxCurrentYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and apet.building_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by apet.building_id
        union all
        select t1.id as building_id, 0 as ss, 0 as fdcss, sum(business_area) as swzmj
        from ads_pm_building t1
        left join ads_pm_project t2 on t1.project_id = t2.id
                                           where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t2.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t1.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t1.id) t1
        group by building_id),
        data2 as (select building_id,
        sum(ss)                                                                 as taxIncome,
        sum(fdcss)                                                              as fdcss,
        sum(ss) - sum(fdcss)                                                    as cfdcss,
        case when sum(swzmj) = 0 then 0 else round(sum(ss) / sum(swzmj), 2) end as unitIncome
        from (select t1.building_id, sum(bq_amt) as ss, 0 as fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm t1
        left join ads_pm_building t2 on t1.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where t1.datekey = #{req.taxLastYearMonth}
        and t1.yskm_dm = 'ss'
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t1.building_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t1.building_id
        union all
        select apet.building_id, 0 as ss, coalesce(sum(bq_amt), 0) fdcss, 0 as swzmj
        from ads_pm_enterprise_tax_fkm apet
        left join ads_pm_enterprise ape on apet.enterprise_id = ape.id
        left join ads_pm_building t2 on apet.building_id = t2.id
        left join ads_pm_project t3 on t2.project_id = t3.id
        where ape.industry_code in ('701', '7010')
        and apet.yskm_dm = 'ss'
        and apet.datekey = #{req.taxLastYearMonth}
        <if test="req.projectType != null and req.projectType != ''">
            and t3.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and apet.building_id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by apet.building_id
        union all
        select t1.id as building_id, 0 as ss, 0 as fdcss, sum(business_area) as swzmj
        from ads_pm_building t1
        left join ads_pm_project t2 on t1.project_id = t2.id
                                           where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and t2.project_type = #{req.projectType}
        </if>
        <if test="req.codes != null and req.codes.size > 0">
            and t1.id in
            <foreach collection="req.codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        group by t1.id) t1
        group by building_id)
        select (select building_name from ads_pm_building where id = t1.building_id) as name,
        <choose>
            <when test="req.queryType == 1">
                round(t1.taxIncome / 10000,2) as amount1,
                case
                when t2.taxIncome = 0 then 0
                else round((t1.taxIncome - t2.taxIncome) / t2.taxIncome * 100, 2) end as amount2
            </when>
            <when test="req.queryType == 2">
                round(t1.cfdcss / 10000,2) as amount1,
                case
                when t2.cfdcss = 0 then 0
                else round((t1.cfdcss - t2.cfdcss) / t2.cfdcss * 100, 2) end as amount2
            </when>
            <otherwise>
                round(t1.unitIncome,2) as amount1,
                case
                when t2.unitIncome = 0 then 0
                else round((t1.unitIncome - t2.unitIncome) / t2.unitIncome * 100, 2) end as amount2
            </otherwise>
        </choose>

        from data1 t1
        left join data2 t2 on t1.building_id = t2.building_id
        where t1.building_id is not null
    </select>

    <select id="getTaxByBuilding" resultType="java.math.BigDecimal">
        select coalesce(sum(by_amt), 0)
        from ads_pm_enterprise_tax_fkm
        where building_id = #{buildingId}
          and yskm_dm = 'ss'
          and datekey = #{queryDate}
    </select>
    <select id="getTop3IndustryTaxByBuilding" resultType="java.math.BigDecimal">
        select sum(settledArea)
        from (select t1.building_id,
                     (select coalesce(sum(area), 0)
                      from ads_pm_room_enterprise apre
                               left join ads_pm_enterprise ape on apre.enterprise_id = ape.id
                      where apre.building_id = t1.building_id
                        and ape.industry_code in (select hy_dm from dm_ly_hy_zq where sjhy_dm = t1.sjhy_dm)
                        and moved = false) settledArea
              from (WITH data1 AS (SELECT building_id, enterprise_id, SUM(bq_amt) AS bq_ss
                                   FROM ads_pm_enterprise_tax_fkm b
                                   WHERE datekey = #{queryDate}
                                     AND yskm_dm = 'ss'
                                     AND building_id = #{buildingId}
                                   GROUP BY enterprise_id, building_id),
                         data2 AS (SELECT *
                                   FROM dm_ly_hy_zq a
                                   WHERE 1 = 1),
                         aggregated_data AS (SELECT b.building_id,
                                                    c.sjhy_dm,
                                                    c.sjhy_mc,
                                                    SUM(b.bq_ss) AS bq_ss
                                             FROM ads_pm_enterprise a
                                                      INNER JOIN data1 b ON a.id = b.enterprise_id
                                                      INNER JOIN data2 c ON a.industry_code = c.hy_dm AND LENGTH(c.sjhy_dm) = 1
                                             GROUP BY c.sjhy_dm, c.sjhy_mc, b.building_id),
                         ranked_data AS (SELECT *,
                                                ROW_NUMBER() OVER (PARTITION BY building_id ORDER BY bq_ss DESC) AS rn
                                         FROM aggregated_data)
                    SELECT *
                    FROM ranked_data
                    WHERE rn &lt;= 3
                    ORDER BY building_id, bq_ss DESC) t1) t2
        group by t2.building_id

    </select>
    <select id="getSettledRateByBuilding" resultType="java.math.BigDecimal">
        select case
                   when coalesce(t1.businessArea, 0) = 0 then 0
                   when coalesce(t1.settledArea, 0) = 0 then 0
                   else round(coalesce(t1.settledArea, 0) /
                              coalesce(t1.businessArea, 0) * 100, 2) end as
                   settledRate
        From (select apb.building_name,
                     apb.business_area                                           as businessArea,
                     (select coalesce(sum(settled_area), 0)
                      from v_enterprise
                      where moved = false
                        and building_id = apb.id
                        and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{queryDate}) as settledArea
              from ads_pm_building apb
                       left join ads_pm_project app on apb.project_id = app.id
              where apb.id = #{buildingId}) t1
    </select>
    <select id="getLocalRateByBuilding" resultType="java.math.BigDecimal">
        select case
                   when coalesce(sum(totalCount), 0) = 0 then 0
                   else round(coalesce(sum(localedCount), 0) / coalesce(sum(totalCount), 0) * 100, 2) end as localedRate
        from (select coalesce(count(1), 0) as totalCount, 0 as localedCount
              from ads_pm_enterprise
              where id in (select distinct enterprise_id
                           from ads_pm_room_enterprise
                           where moved = false
                             and building_id = #{buildingId}
                             and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{queryDate})
              union all
              select 0 as totalCount, coalesce(count(1), 0) as localedCount
              from ads_pm_enterprise
              where id in (select distinct enterprise_id
                           from ads_pm_room_enterprise
                           where moved = false
                             and building_id = #{buildingId}
                             and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{queryDate})
                and territorialized = true) t1

    </select>
    <select id="getUnitOutputByBuilding" resultType="java.math.BigDecimal">
        select case when coalesce(swzmj, 0) = 0 then 0 else round(taxIncome / swzmj * 100, 2) end as unitOutput
        from (select coalesce(sum(bq_amt), 0)                                             as taxIncome,
                     (select business_area from ads_pm_building where id = #{buildingId}) as swzmj
              from ads_pm_enterprise_tax_fkm
              where building_id = #{buildingId}
                and yskm_dm = 'ss'
                and enterprise_id in (select enterprise_id from ads_pm_room_enterprise where moved = false)
                and datekey = #{queryDate}) t1
    </select>
    <select id="getTaxRateByBuilding" resultType="java.math.BigDecimal">
        select case
                   when coalesce(sum(t1.tqTaxIncome), 0) = 0 then 0
                   else round((coalesce(sum(t1.taxIncome), 0) - coalesce(sum(t1.tqTaxIncome), 0)) /
                              coalesce(sum(t1.tqTaxIncome), 0) * 100, 2) end as zf
        from (select coalesce(sum(bq_amt), 0) as taxIncome, 0 as tqTaxIncome
              from ads_pm_enterprise_tax_fkm
              where building_id = #{buildingId}
                and yskm_dm = 'ss'
                and datekey = #{queryDate}
              union all
              select 0 as taxIncome, coalesce(sum(bq_amt), 0) as tqTaxIncome
              from ads_pm_enterprise_tax_fkm
              where building_id = #{buildingId}
                and yskm_dm = 'ss'
                and datekey = #{lastQueryDate}) t1
    </select>
    <select id="getTaxByMaxMin" resultType="java.util.Map">
        select coalesce(max(taxIncome), 0) as maxTaxIncome, coalesce(min(taxIncome), 0) as minTaxIncome
        from (select building_id, sum(bq_amt) as taxIncome
              from ads_pm_enterprise_tax_fkm
              where datekey = #{queryDate}
                and yskm_dm = 'ss'
              group by building_id) t1
    </select>
    <select id="getSettledRateByMaxMin" resultType="java.util.Map">
        select coalesce(max(settledRate), 0) as maxSettledRate, coalesce(min(settledRate), 0) as minSettledRate
        from (select t1.building_id,
                     case
                         when coalesce(t1.businessArea, 0) = 0 then null
                         when coalesce(t1.settledArea, 0) = 0 then null
                         else round(coalesce(t1.settledArea, 0) /
                                    coalesce(t1.businessArea, 0) * 100, 2) end as
                         settledRate
              From (select apb.id                                                      as building_id,
                           apb.business_area                                           as businessArea,
                           (select coalesce(sum(settled_area))
                            from v_enterprise
                            where moved = false
                              and building_id = apb.id
                              and TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{queryDate}) as settledArea
                    from ads_pm_building apb
                             left join ads_pm_project app on apb.project_id = app.id) t1) t2
    </select>
    <select id="getLocalRateByMaxMin" resultType="java.util.Map">
        select coalesce(max(localedRate), 0) as maxLocaledRate, coalesce(min((localedRate)), 0) as minLocaledRate
        from (select building_id,
                     case
                         when sum(totalCount) = 0 then 0
                         else round(sum(localedCount) / sum(totalCount) * 100, 2) end as localedRate
              from (select building_id, count(1) as totalCount, 0 as localedCount
                    from (select building_id, enterprise_id
                          from ads_pm_room_enterprise apre
                                   left join ads_pm_enterprise ape
                                             on apre.enterprise_id = ape.id
                          where TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{queryDate}) t1
                    group by building_id
                    union all
                    select building_id, 0 as totalCount, count(1) as localedCount
                    from (select building_id, enterprise_id
                          from ads_pm_room_enterprise apre
                                   left join ads_pm_enterprise ape
                                             on apre.enterprise_id = ape.id
                          where TO_CHAR(check_in_date, 'YYYYMM') &lt;= #{queryDate}
                            and ape.territorialized = true) t2
                    group by building_id) t2
              group by building_id) t3
    </select>
    <select id="getUnitOutputByMaxMin" resultType="java.util.Map">
        select coalesce(max(t2.unitOutput), 0) as maxUnitOutput, coalesce(min(t2.unitOutput), 0) as minUnitOutput
        From (select building_id,
                     case when swzmj = 0 then 0 else coalesce(round(taxIncome / swzmj * 100, 2), 0) end as unitOutput
              from (select building_id,
                           sum(bq_amt)                                                             as taxIncome,
                           (select business_area from ads_pm_building where id = apet.building_id) as swzmj
                    from ads_pm_enterprise_tax_fkm apet
                    where enterprise_id in (select enterprise_id from ads_pm_room_enterprise where moved = false)
                      and yskm_dm = 'ss'
                      and datekey = #{queryDate}
                    group by building_id) t1) t2
    </select>
    <select id="getTaxRateByMaxMin" resultType="java.util.Map">
        select coalesce(max(zf), 0) as maxTaxRate, coalesce(min(zf), 0) as minTaxRate
        from (select building_id,
                     case
                         when sum(t1.tqTaxIncome) = 0 then 0
                         else round((sum(t1.taxIncome) - sum(t1.tqTaxIncome)) / sum(t1.tqTaxIncome) * 100, 2) end as zf
              from (select building_id, sum(bq_amt) as taxIncome, 0 as tqTaxIncome
                    from ads_pm_enterprise_tax_fkm
                    where datekey = #{queryDate}
                      and yskm_dm = 'ss'
                    group by building_id
                    union all
                    select building_id, 0 as taxIncome, sum(bq_amt) as tqTaxIncome
                    from ads_pm_enterprise_tax_fkm
                    where datekey = #{lastQueryDate}
                      and yskm_dm = 'ss'
                    group by building_id) t1
              group by building_id) t2
    </select>
    <select id="getTop3IndustryTaxByMaxMin" resultType="java.util.Map">
        select coalesce(max(settledArea), 0) maxAreaTop3, coalesce(min(settledArea), 0) minAreaTop3
        from (select t2.building_id,
                     sum(settledArea) as settledArea
              from (select t1.building_id,
                           (select coalesce(sum(area), 0)
                            from ads_pm_room_enterprise apre
                                     left join ads_pm_enterprise ape on apre.enterprise_id = ape.id
                            where apre.building_id = t1.building_id
                              and ape.industry_code in (select hy_dm from dm_ly_hy_zq where sjhy_dm = t1.sjhy_dm)
                              and moved = false) settledArea
                    from (WITH data1 AS (SELECT building_id, enterprise_id, SUM(bq_amt) AS bq_ss
                                         FROM ads_pm_enterprise_tax_fkm b
                                         WHERE datekey = #{queryDate}
                                           AND yskm_dm = 'ss'
                                         GROUP BY enterprise_id, building_id),
                               data2 AS (SELECT *
                                         FROM dm_ly_hy_zq a
                                         WHERE 1 = 1),
                               aggregated_data AS (SELECT b.building_id,
                                                          c.sjhy_dm,
                                                          c.sjhy_mc,
                                                          SUM(b.bq_ss) AS bq_ss
                                                   FROM ads_pm_enterprise a
                                                            INNER JOIN data1 b ON a.id = b.enterprise_id
                                                            INNER JOIN data2 c ON a.industry_code = c.hy_dm AND LENGTH(c.sjhy_dm) = 1
                                                   GROUP BY c.sjhy_dm, c.sjhy_mc, b.building_id),
                               ranked_data AS (SELECT *,
                                                      ROW_NUMBER() OVER (PARTITION BY building_id ORDER BY bq_ss DESC) AS rn
                                               FROM aggregated_data)
                          SELECT *
                          FROM ranked_data
                          WHERE rn &lt;= 3
                          ORDER BY building_id, bq_ss DESC) t1) t2
              group by t2.building_id) t3

    </select>
    <select id="listBuildingIds" resultType="java.lang.String">
        select id
        from ads_pm_building
        where building_status_code = '3'
    </select>
    <select id="getTaxMaxDate" resultType="java.lang.String">
        select max_date
        from dm_gy_module_date
        where target_page = 'ENTERPRISE_TAX_MON'
    </select>
    <select id="getMobileCompare" resultMap="compareVo">
        select item_type, max_item_count as maxCount, #{req.projectId} as projectId
        from ads_mobile_compare_item
        where 1=1
          <choose>
              <when test="req.itemType != null and req.itemType != ''">
                  and item_type = #{req.itemType}
              </when>
        <otherwise>
            and defaulted = true
        </otherwise>
          </choose>
    </select>
    <select id="listDefaultItem" resultType="java.lang.String">
        select id from ads_pm_building where project_id = #{projectId}
    </select>
    <select id="getCompareItem" resultType="java.lang.String">
        select item_code
        from ads_mobile_compare_default_item
        where item_type = #{req.itemType}
          and defaulted = true
    </select>


</mapper>