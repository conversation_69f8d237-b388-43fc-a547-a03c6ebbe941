<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.HandleWarningMapper">
    <select id="getHandleDetail" resultType="com.zjhh.economy.vo.HandleDetailVo">
        select id, case when handle_type = 1 then '已处理' else '已忽略' end as handleType, handle_desc, create_time::date
        from ads_handle_warning ahw
        where warning_id = #{id}
    </select>
</mapper>