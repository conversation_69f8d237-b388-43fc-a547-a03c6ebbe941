<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseTaxMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , datekey, zzs, grsds, qysds, cswhjss, fcs, cztdsys, tdzzs, yhs, gdzys, xfs, zys, hjbhs, gyzy, create_user, create_time, update_time
    </sql>
    <select id="selectTotalTax" resultType="java.math.BigDecimal">
        select coalesce(sum(bq_amt), 0) / 10000
        from ads_pm_enterprise_tax_fkm
        where enterprise_id = #{enterpriseId}
          AND yskm_dm = 'ss'
          and datekey = #{datekey}
    </select>
    <select id="listTaxAnalyseChartByMonth" resultType="com.zjhh.economy.vo.TaxAnalyseChartVo">

        WITH data1 AS (SELECT A
                                  .*,
                              to_char(add_months(to_date(A.datekey, 'yyyymm'), - 12), 'yyyymm') AS sq_date
                       FROM (SELECT datekey AS MONTH,
   MAX ( datekey ) AS datekey
                             FROM
                                 ads_pm_enterprise_tax_fkm A
                             WHERE
                                 yskm_dm = 'ss'
                               AND substr( A.datekey
                                 , 1
                                 , 4 ) = #{req.datekey}
                               AND enterprise_id = #{req.enterpriseId}
                             GROUP BY
                                 datekey) A),
             data2 AS (SELECT datekey,
                              SUM(by_amt) AS bq_amt
                       FROM ads_pm_enterprise_tax_fkm A
                       WHERE yskm_dm = 'ss'
                         AND enterprise_id = #{req.enterpriseId}
                         AND substr(A.datekey, 1, 4) = #{req.datekey}
                       GROUP BY datekey),
             data3 AS (SELECT datekey,
                              SUM(by_amt) AS bq_amt
                       FROM ads_pm_enterprise_tax_fkm A
                       WHERE yskm_dm = 'ss'
                         AND enterprise_id = #{req.enterpriseId}
                         AND substr(A.datekey, 1, 4) = (#{req.datekey}::int - 1):: varchar
        GROUP BY
            datekey
            )
        SELECT concat(substr(MONTH, 5, 6) :: DECIMAL, '月') AS datekeyStr,
               round(SUM(b.bq_amt) / 10000, 2)              AS amount,
               CASE

                   WHEN COALESCE(SUM(C.bq_amt), 0) = 0 THEN
                       0
                   ELSE round((COALESCE(SUM(b.bq_amt), 0) / SUM(C.bq_amt) - 1) * 100, 1)
                   END                                      AS zf
        FROM data1 A
                 INNER JOIN data2 b ON A.datekey = b.datekey
                 LEFT JOIN data3 C ON A.sq_date = C.datekey
        GROUP BY
            MONTH

    </select>
    <select id="listTaxAnalyseChartByYear" resultType="com.zjhh.economy.vo.TaxAnalyseChartVo">
        with data1 as (select a.*, to_char(add_months(to_date(a.datekey, 'yyyymm'), -12), 'yyyymm') as sq_date
                       from (select substr(datekey, 1, 4) as year,max(datekey) as datekey
                             from ads_pm_enterprise_tax_fkm a
                             where yskm_dm = 'ss'
                               and enterprise_id = #{req.enterpriseId}
                             group by substr(datekey, 1, 4)) a),
             data2 as (select datekey, sum(bq_amt) as bq_amt
                       from ads_pm_enterprise_tax_fkm a
                       where yskm_dm = 'ss'
                         and enterprise_id = #{req.enterpriseId}
                       group by datekey)
        select concat(a.year, '年')                                                          as datekeyStr,
               round(sum(b.bq_amt) / 10000, 2)                                               as amount,
               case
                   when coalesce(sum(c.bq_amt), 0) = 0 then 0
                   else round((coalesce(sum(b.bq_amt), 0) / sum(c.bq_amt) - 1) * 100, 1) end as zf
        from data1 a
                 inner join data2 b on a.datekey = b.datekey
                 left join data2 c on a.sq_date = c.datekey
        group by concat(a.year, '年')
        order by datekeyStr
    </select>
    <select id="listTaxAnalyseTable" resultType="com.zjhh.economy.vo.TaxAnalyseTableVo">

        WITH data1 AS (select a.mon, a.datekey, b.xh, b.zs_dm yskm_dm, b.zs_mc yskm_mc, a.by_amt
                       from (select substr(datekey, 5, 2) as mon, datekey, yskm_dm, sum(by_amt) AS by_amt
                             FROM ads_pm_enterprise_tax_fkm a
                             WHERE substr(datekey, 1, 4) = substr(#{req.datekey}, 1, 4)
                               AND enterprise_id = #{req.enterpriseId}
                             group by datekey, yskm_dm, substr(datekey, 5, 2)) a
                                inner join (select * from dm_gy_page_style where type_dm = '16') b
                                           on a.yskm_dm = b.dmmx),
             data2 AS (select a.mon, b.xh, b.zs_dm yskm_dm, b.zs_mc yskm_mc, a.by_amt
                       from (select substr(datekey, 5, 2) as mon, yskm_dm, sum(by_amt) AS by_amt
                             FROM ads_pm_enterprise_tax_fkm a
                             WHERE substr(datekey, 1, 4) = #{req.lastDatekey}
                               AND enterprise_id = #{req.enterpriseId}
                             group by datekey, yskm_dm, substr(datekey, 5, 2)) a
                                inner join (select * from dm_gy_page_style where type_dm = '16') b
                                           on a.yskm_dm = b.dmmx)
        SELECT concat(t1.mon::decimal, '月')                                                     AS monthStr,
               t1.datekey,
               t1.yskm_dm,
               t1.yskm_mc,
               coalesce(t1.by_amt, 0)                                                            AS hj,
               case when A.by_amt = 0 then 0 else round((t1.by_amt / A.by_amt - 1) * 100, 1) end as zf
        FROM data1 t1
                 left JOIN data2 A ON t1.mon = A.mon and t1.yskm_dm = A.yskm_dm
        order by t1.datekey, t1.xh

    </select>
    <select id="getTaxSummaryByEnt" resultType="com.zjhh.economy.vo.TaxSummaryVo">
        select sum(bqlj)                                                                                       as bqljAmt,
               sum(sqlj)                                                                                       as sqljAmt,
               sum(byAmt)                                                                                      as byAmt,
               sum(syAmt)                                                                                      as syAmt,
               case when sum(sqlj) = 0 then 0 else round((sum(bqlj) - sum(sqlj)) / sum(sqlj) * 100, 2) end     as tbZf,
               case when sum(syAmt) = 0 then 0 else round((sum(byAmt) - sum(syAmt)) / sum(syAmt) * 100, 2) end as hbZf
        From (select (sum(bq_amt)) as bqlj,
                     0             as sqlj,
                     0             as byAmt,
                     0             as syAmt
              from ads_pm_enterprise_tax_fkm
              where substr(datekey, 1, 4) = #{year}
                and yskm_dm = 'ss'
                and datekey = (select max(datekey) from ads_pm_enterprise_tax_fkm where substr(datekey, 1, 4) = #{year})
              union all
              select 0             as bqlj,
                     (sum(bq_amt)) as sqlj,
                     0             as byAmt,
                     0             as syAmt
              from ads_pm_enterprise_tax_fkm
              where substr(datekey, 1, 4) = #{lastYear}
                and yskm_dm = 'ss'
                and datekey =
                    (select max(datekey) from ads_pm_enterprise_tax_fkm where substr(datekey, 1, 4) = #{lastYear})
              union all
              select 0             as bqlj,
                     0             as sqlj,
                     (sum(by_amt)) as byAmt,
                     0             as syAmt
              from ads_pm_enterprise_tax_fkm
              where substr(datekey, 1, 4) = #{year}
                and yskm_dm = 'ss'
                and substr(datekey, 5, 6) = #{month}
              union all
              select 0             as bqlj,
                     0             as sqlj,
                     0             as byAmt,
                     (sum(by_amt)) as syAmt
              from ads_pm_enterprise_tax_fkm
              where substr(datekey, 1, 4) = #{year}
                and yskm_dm = 'ss'
                and substr(datekey, 5, 6) = #{lastMonth}) t1
    </select>
    <select id="pageBuildingArchive" resultType="com.zjhh.economy.vo.BuildingArchiveVo">

        select *,
        (select string_agg(dp.name,',') from ads_pm_enterprise_label t1 left join dm_pm dp on t1.label_code = dp.code
        and dp.type = 'EnterpriseLabel'
        where t1.enterprise_id = t2.id) as label
        from (
        with data1 as (
        SELECT a.enterprise_id,
        coalesce(sum(case when yskm_dm = 'ss' then by_amt else 0 end), 0) as taxIncome,
        coalesce(sum(case when yskm_dm = 'jdsr' then by_amt else 0 end), 0) as streetIncome,
        coalesce(sum(case when yskm_dm = 'zzs' then by_amt else 0 end), 0) as zzs,
        coalesce(sum(case when yskm_dm = 'grsds' then by_amt else 0 end), 0) as grsds,
        coalesce(sum(case when yskm_dm = 'qysds' then by_amt else 0 end), 0) as qysds,
        coalesce(sum(case when yskm_dm = 'cswhjss' then by_amt else 0 end), 0) as cswhjss,
        coalesce(sum(case when yskm_dm = 'fcs' then by_amt else 0 end), 0) as fcs,
        coalesce(sum(case when yskm_dm = 'cztdsys' then by_amt else 0 end), 0) as cztdsys,
        coalesce(sum(case when yskm_dm = 'tdzzs' then by_amt else 0 end), 0) as tdzzs,
        coalesce(sum(case when yskm_dm = 'yhs' then by_amt else 0 end), 0) as yhs,
        coalesce(sum(case when yskm_dm = 'gdzys' then by_amt else 0 end), 0) as gdzys,
        coalesce(sum(case when yskm_dm = 'xfs' then by_amt else 0 end), 0) as xfs,
        coalesce(sum(case when yskm_dm = 'zys' then by_amt else 0 end), 0) as zys,
        coalesce(sum(case when yskm_dm = 'hjbhs' then by_amt else 0 end), 0) as hjbhs,
        coalesce(sum(case when yskm_dm = 'gyzy' then by_amt else 0 end), 0) as gyzy
        FROM ads_pm_enterprise_tax_fkm a
        WHERE a.datekey BETWEEN #{req.startDate} AND #{req.endDate}
        group by a.enterprise_id
        ),
        data2 as (
        SELECT a.enterprise_id,
        sum(case when yskm_dm = 'ss' then by_amt else 0 end) sqamt,sum(case when yskm_dm = 'jdsr' then by_amt else 0
        end) streetSqAmt
        FROM ads_pm_enterprise_tax_fkm a
        WHERE datekey BETWEEN #{req.lastStartDate} AND #{req.lastEndDate}
        group by a.enterprise_id
        ),
        data3 as (

        SELECT a.enterprise_id ,coalesce(sum(bq_ss),0) sqljamt FROM ads_pm_enterprise_tax a
        inner join (SELECT enterprise_id,max(datekey) as datekey FROM ads_pm_enterprise_tax WHERE substr(datekey, 1, 4)
        = #{req.accLastYear} group by enterprise_id) b
        on a.enterprise_id = b.enterprise_id and a.datekey = b.datekey
        group by a.enterprise_id
        ),
        data4 as (

        SELECT a.enterprise_id ,coalesce(sum(bq_ss),0) syljamt FROM ads_pm_enterprise_tax a
        inner join (SELECT enterprise_id,max(datekey) as datekey FROM ads_pm_enterprise_tax WHERE datekey =
        #{req.lastYearMonth} group by enterprise_id) b
        on a.enterprise_id = b.enterprise_id and a.datekey = b.datekey
        group by a.enterprise_id
        )
        select row_number() over (order by t2.projectserialno,t2.buildingserialno,t2.floorno ) as xh,
        entname,id,projectserialno as projectSerialNo,buildingserialno as buildingSerialNo,floorno as
        floorNo,communityname as communityName,buildingname as buildingName,projectname as projectName,projecttype as
        projectType,floorname as floorName,uscc,
        financialsplit as financialSplit
        ,registeraddress as registerAddress,
        businessaddress as businessAddress,
        businessscope as businessScope,
        unitproperty as unitProperty,
        unitpropertystr as unitPropertyStr,
        registeredcapital as registerCapital,
        currenttype as currentType,
        enterprisetype as enterpriseType,
        industry_code as industryCode,
        industryname as industryName,
        legalperson as legalPerson,
        phone,
        financialperson as financialPerson,
        financialpersonphone as financialPersonPhone,
        settledarea as settledArea,
        businessstatus as businessStatus,
        businessstatusstr as businessStatusStr,
        business_status_remark as businessStatusRemark,
        settledtime as settledTime,
        employeecount as employeeCount,
        recorded
        ,insystem as inSystem,
        territorialized,onscaled as onScaled,recordedstr as recordedStr,insystemstr as inSystemStr,territorializedstr as
        territorializedStr,onscaledstr as onScaledStr,localtaxstatus as localTaxStatus,localtaxstatusstr as
        localTaxStatusStr,
        coalesce(taxincome,0) as taxIncome,sqamt as sqAmt,streetincome as streetIncome,streetsqamt as
        streetSqAmt,zzs,qysds,grsds,cswhjss,fcs,cztdsys,tdzzs,yhs,gdzys,xfs,zys,hjbhs,gyzy,coalesce(sqljamt,0) as
        sqljAmt,coalesce(syljamt,0) as syljAmt
        ,t2.ent_contact_person,
        t2.ent_phone,
        t2.property_ownership,
        t2.settledinfo as settledInfo,
        t2.moveoutinfo as moveOutInfo
        from view_ads_enterprise t2
        left join data1 b on t2.id = b.enterprise_id
        left join data2 c on t2.id = c.enterprise_id
        left join data3 d on t2.id = d.enterprise_id
        left join data4 e on t2.id = e.enterprise_id
        ) t2
        where 1=1
        <if test="req.labels != null and req.labels.size > 0">
            and t2.id in (select enterprise_id from ads_pm_enterprise_label where label_code in
            <foreach collection="req.labels" item="label" open="(" separator="," close=")">
                #{label}
            </foreach>)
        </if>
        <if test="req.searchWord != null and req.searchWord != ''">
            and (t2.entName like concat('%',#{req.searchWord},'%') or t2.registerAddress like
            concat('%',#{req.searchWord},'%') or t2.businessAddress like concat('%',#{req.searchWord},'%')
            or t2.businessScope like concat('%',#{req.searchWord},'%')
            )
        </if>
        <if test="req.communityCodes != null and req.communityCodes.size > 0">
            and (t2.id in (select enterprise_id from ads_pm_room_enterprise t1
                                                inner join v_p_building t2 on t1.building_id = t2.id
                                                where
            t1.project_id in
            <foreach collection="req.communityCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
            and moved = false
            ) or t2.id in (select enterprise_id from ads_pm_room_enterprise
            t1
            inner join v_p_building t2 on t1.building_id = t2.id
                                                where
            t1.building_id in
            <foreach collection="req.communityCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
            and moved = false
            ) or t2.id in (select enterprise_id from ads_pm_room_enterprise
            t1
            inner join v_p_building t2 on t1.building_id = t2.id
                                                where
            t1.floor_id in
            <foreach collection="req.communityCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
            and moved = false
            ) or t2.id in (select enterprise_id from ads_pm_room_enterprise
            t1
            inner join v_p_building t2 on t1.building_id = t2.id
                                                where
            t1.project_id in
            (select id from ads_pm_project where community_code in
            <foreach collection="req.communityCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>)
            and moved = false
            ))
        </if>

        <if test="req.taxCommunityCodes != null and req.taxCommunityCodes.size > 0">
            and (t2.id in (select distinct enterprise_id from ads_pm_room_enterprise where
            building_id in
            <foreach collection="req.taxCommunityCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
            and moved = false
            ) or t2.id in (select distinct enterprise_id from ads_pm_room_enterprise where
            floor_id in
            <foreach collection="req.taxCommunityCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
            and moved = false
            ) or t2.id in (select distinct enterprise_id from ads_pm_room_enterprise where
            project_id in
            (select id from ads_pm_project where community_code in
            <foreach collection="req.taxCommunityCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>)
            and moved = false
            ))
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and t2.id in (select enterprise_id from ads_pm_room_enterprise where
            project_id in
            (select id from ads_pm_project where project_type = #{req.projectType})
            and moved = false
            )
        </if>
        <if test="req.territorialized != null">
            and t2.territorialized =#{req.territorialized}
        </if>
        <if test="req.onScaled != null">
            and t2.onScaled =#{req.onScaled}
        </if>

        <if test="req.industryCodes != null and req.industryCodes.size > 0">
            and t2.industryCode in
            (select hy_dm from dm_ly_hy_zq a where a.sjhy_dm in
            <foreach collection="req.industryCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
            )

        </if>
        <choose>
            <when test="req.taxRange != null and req.taxRange == 1">
                and t2.taxIncome = 0
            </when>
            <when test="req.taxRange != null and req.taxRange == 2">
                and t2.taxIncome between 1 and 10000
            </when>
            <when test="req.taxRange != null and req.taxRange == 3">
                and t2.taxIncome between 10000 and 50000
            </when>
            <when test="req.taxRange != null and req.taxRange == 4">
                and t2.taxIncome between 50000 and 100000
            </when>
            <when test="req.taxRange != null and req.taxRange == 5">
                and t2.taxIncome between 100000 and 5000000
            </when>
            <when test="req.taxRange != null and req.taxRange == 6">
                and t2.taxIncome > 5000000
            </when>
        </choose>
        <if test="req.legalPerson != null and req.legalPerson != ''">
            and t2.legalPerson like concat('%',#{req.legalPerson},'%')
        </if>
        <if test="req.unitProperty != null and req.unitProperty != ''">
            and t2.unitProperty = #{req.unitProperty}
        </if>
        <if test="req.businessStatus != null and req.businessStatus != ''">
            and t2.businessStatus = #{req.businessStatus}
        </if>
        <if test="req.localTaxStatus != null and req.localTaxStatus != ''">
            and t2.localTaxStatus = #{req.localTaxStatus}
        </if>
        <choose>
            <when test="req.consistent != null and req.consistent == true">
                and t2.businessAddress = t2.registerAddress
            </when>
            <when test="req.consistent != null and req.consistent == false">
                and t2.businessAddress != t2.registerAddress
            </when>
        </choose>
        order by t2.taxIncome desc
    </select>
    <select id="getTaxMaxDate" resultType="java.lang.String">
        select max_date
        from dm_gy_module_date
        where target_page = 'ENTERPRISE_TAX_MON'
    </select>
    <select id="listTaxAnalyseSummaryTable" resultType="com.zjhh.economy.vo.TaxAnalyseTableVo">
        WITH data1 AS (
            SELECT  'lj' as datekey,'ljhjs' as yskm_dm,'累计合计数' as yskm_mc, sum(by_amt) AS by_amt
            FROM ads_pm_enterprise_tax_fkm apetk
            WHERE yskm_dm = 'ss'
              AND substr( datekey, 1, 4 ) = substr( #{req.datekey}, 1, 4 )
              AND enterprise_id = #{req.enterpriseId}
        ),
             data2 AS (
                 SELECT  'lj' as datekey,'ljhjs' as yskm_dm,'累计合计数' as yskm_mc,sum(by_amt) AS by_amt
                 FROM ads_pm_enterprise_tax_fkm apetk
                 WHERE yskm_dm = 'ss'
                   AND substr( datekey, 1, 4 ) = substr( #{req.lastDatekey}, 1, 4 )
                   AND enterprise_id = #{req.enterpriseId}
             ) SELECT
                   t1.datekey,t1.yskm_dm,t1.yskm_mc,
                   coalesce(t1.by_amt,0)  AS hj,
                   case when A.by_amt=0 then 0 else round(((t1.by_amt - A.by_amt)/A.by_amt) *100,1) end as zf
        FROM data1 t1
                 left JOIN data2 A ON t1.datekey = A.datekey and t1.yskm_dm = A.yskm_dm

    </select>


</mapper>
