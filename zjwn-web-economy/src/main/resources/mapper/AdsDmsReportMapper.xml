<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsDmsReportMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        report_type_code, report_type_name, create_user, create_time, report_date, report_state, cron_expression, description, cron_state
    </sql>

    <resultMap id="AdsDmsReportVo" type="com.zjhh.economy.vo.earlywarning.AdsDmsReportVo">
        <id column="report_type_code" property="reportTypeCode"/>
        <result column="report_type_name" property="reportTypeName"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="report_date" property="reportDate"/>
        <result column="report_state" property="reportState"/>
        <result column="cron_expression" property="cronExpression"/>
        <result column="description" property="description"/>
        <result column="cron_state" property="cronState"/>
        <result column="doc_id" property="docId"/>
        <result column="auth_type" property="authType"/>
        <result column="preview" property="preview"/>
        <result column="doc_name" property="docName"/>
        <collection property="orgCodes" ofType="java.lang.String">
            <result column="org_code"/>
        </collection>
    </resultMap>

    <select id="listCreateReportDoc" resultType="com.zjhh.economy.vo.earlywarning.CreateReportDocVo">
        select t1.report_type_code, t1.report_type_name, t2.page_json
        from ads_dms_report t1
        left join ads_dms_report_page t2 on t1.report_type_code = t2.report_type_code
        where t1.report_type_code in
        <foreach collection="reportTypeCodes" item="reportTypeCode" open="(" close=")" separator=",">
            #{reportTypeCode}
        </foreach>
    </select>

    <select id="pageAnalysisReport" resultMap="AdsDmsReportVo">
        select t1.report_type_code,
        t1.report_type_name,
        t1.create_user,
        t1.create_time,
        t1.report_date,
        t1.report_state,
        t1.cron_expression,
        t1.description,
        t1.cron_state,
        t1.doc_id,
        t1.auth_type,
        t1.doc_id is not null as preview,
        t3.title as doc_name,
        t2.org_code
        from ads_dms_report t1
        left join ads_dms_report_auth t2 on t1.report_type_code = t2.report_type_code
        left join ads_document t3 on t1.doc_id = t3.id
        <where>
            <if test="req.reportTypeName != null and req.reportTypeName != ''">
                and t1.report_type_name like concat('%',#{req.reportTypeName},'%')
            </if>
            <if test="req.reportState != null and req.reportState != 99">
                and t1.report_state = #{req.reportState}
            </if>
        </where>
        order by t1.create_time desc
    </select>

    <select id="listSelectOrgCodes" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, parent_code as parent_key, name as title, code as value
        from v_organize order by code
    </select>
</mapper>
