<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.DmGyHyMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, name, parent_code, xh
    </sql>

    <select id="listHy" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, parent_code as parent_key, name as title, code as value
        from dm_gy_hy
        where length(code) &lt;= #{length}
        and parent_code is not null
        order by xh
    </select>

</mapper>
