<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsBasScreenViewMapper">

    <resultMap id="building3D" type="com.zjhh.economy.vo.cockpit.BuildingMapInfo3DVo">
        <result column="buildingId" property="buildingId"></result>
        <result column="buildingName" property="buildingName"></result>
        <result column="buildingArea" property="buildingArea"/>
        <result column="usedArea" property="usedArea"/>
        <result column="settleRate" property="settleRate"/>
        <result column="registerCount" property="registerCount"/>
        <result column="registerRate" property="registerRate"/>
        <result column="taxIncome" property="taxIncome"/>
        <result column="unitTaxIncome" property="unitTaxIncome"/>
        <result column="taxWithout" property="taxWithout"/>
        <result column="unitTaxWithout" property="unitTaxWithout"/>
        <result column="projectId" property="projectId"/>
        <result column="latitude" property="latitude"/>
        <result column="longitude" property="longitude"/>
        <result column="path" property="path"/>
        <collection property="buildingLabels" select="listBuildingLabels"
                    column="{buildingId=buildingId}">
        </collection>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        guid
        , datekey, screen_module, hold1, hold2, hold3, hold4, hold5, hold6, hold7, hold8, hold9, hold10, hold11, hold12, hold13, hold14, hold15, hold16, hold17, hold18, hold19, hold20
    </sql>
    <select id="listEconomicIndicator" resultType="com.zjhh.economy.vo.cockpit.EconomicIndicatorVo">
        select datekey, hold1 as taxIncome, hold2 as taxIncomeWithout, hold3 as zf, hold4 as withoutZf
        from ads_bas_screen_view
        where screen_module = 'JJZB'
          and datekey between #{preDatekey} and #{datekey}
    </select>
    <select id="listIndustryStruct" resultType="com.zjhh.economy.vo.cockpit.IndustryStructVo">
        select datekey, hold1 as industryName, hold2 as totalTaxIncome, hold3 as zb
        from ads_bas_screen_view
        where screen_module = 'CYJG_HY'
          and substr(datekey, 1, 4) = #{datekey}
    </select>
    <select id="listIndustryEntStruct" resultType="com.zjhh.economy.vo.cockpit.IndustryEntStructVo">
        select datekey, hold1 as industryName, hold2 as entCount, hold3 as zb
        from ads_bas_screen_view
        where screen_module = 'CYJG_QY'
          and substr(datekey, 1, 4) = #{datekey}
    </select>
    <select id="listEntTaxPaid" resultType="com.zjhh.economy.vo.cockpit.EntTaxPaidVo">
        select datekey, hold1 as level, hold2 as entCount
        from ads_bas_screen_view
        where screen_module = 'NSQK_NS'
          and substr(datekey, 1, 4) = #{datekey}
    </select>
    <select id="listEntTaxPaidRank" resultType="com.zjhh.economy.vo.cockpit.EntTaxPaidRankVo">
        select datekey, hold1 as entName, hold2 as paidAmount
        from ads_bas_screen_view
        where screen_module = 'NSQK_RANK'
          and substr(datekey, 1, 4) = #{datekey}
        order by hold2::decimal DESC
    </select>
    <select id="listBuildingWarning" resultType="com.zjhh.economy.vo.cockpit.BuildingWarningVo">
        select datekey, hold1 as warningCode, hold2 as warningCount
        from ads_bas_screen_view
        where screen_module = 'LYYJ'
          and substr(datekey, 1, 4) = #{datekey}
    </select>
    <select id="listEntRegisterCount" resultType="com.zjhh.economy.vo.cockpit.EntCountVo">
        select datekey, hold1 as entCount
        from ads_bas_screen_view
        where screen_module = 'QYSL'
          and datekey between #{preDatekey} and #{datekey}
        order by datekey::decimal asc
    </select>
    <select id="listBuildingRank" resultType="com.zjhh.economy.vo.cockpit.BuildingRankVo">
        select datekey,hold1 as buildingName, hold2 as amt
        from ads_bas_screen_view
        where screen_module = 'LYPH'
          and substr(datekey, 1, 4) = #{datekey}
        order by hold2::decimal DESC
    </select>
    <select id="getBuildingSummary" resultType="com.zjhh.economy.vo.cockpit.BuildingSummaryVo">
        select datekey,
               hold1 as buildingCount,
               hold2 as totalArea,
               hold3 as entCount,
               hold4 as localRegisterRate,
               hold5 as totalTaxIncome,
               hold6 as settleRate
        from ads_bas_screen_view
        where screen_module = 'LYZL'
          and substr(datekey, 1, 4) = #{datekey}
    </select>
    <select id="listBuildingMapInfoUsed" resultType="com.zjhh.economy.vo.cockpit.BuildingMapInfoUsedVo">
        select datekey,
               hold1 as projectId,
               hold2 as projectName,
               hold3 as buildingCount,
               hold4 as buildingArea,
               hold5 as usedArea,
               hold6 as settleRate,
               hold7 as registerCount,
               hold8 as registerRate,
               hold9 as taxIncome,
               hold10 as unitTaxIncome,
               hold11 as taxWithout,
               hold12 as unitTaxWithout,
               hold13 as x,
               hold14 as y,
                hold15 as latitude,
                hold16 as longitude,
                hold17 as showPosition
        from ads_bas_screen_view
        where screen_module = 'LYSY'
          and substr(datekey, 1, 4) = #{datekey}
    </select>
    <select id="listBuildingMapInfoUnused" resultType="com.zjhh.economy.vo.cockpit.BuildingMapInfoUnusedVo">
        select datekey,
               hold1 as projectId,
               hold2 as projectName,
               hold3 as latitude,
               hold4 as longitude,
            hold11 as x,
                hold12 as y,
               hold13 as showPosition
        from ads_bas_screen_view
        where screen_module = 'LYXJ'
          and substr(datekey, 1, 4) = #{datekey}

    </select>
    <select id="listBuildingMapInfo3D" resultMap="building3D">
        select datekey,
               hold1  as buildingId,
               hold2  as buildingName,
               hold3  as buildingArea,
               hold4  as usedArea,
               hold5  as settleRate,
               hold6  as registerCount,
               hold7  as registerRate,
               hold8  as taxIncome,
               hold9  as unitTaxIncome,
               hold10 as taxWithout,
               hold11 as unitTaxWithout,
               hold12 as latitude,
               hold13 as longitude,
               hold14 as path,
               hold15 as projectId
        from ads_bas_screen_view
        where screen_module = 'LY3D'
          and substr(datekey, 1, 4) = #{datekey}
    </select>

    <select id="listBuildingLabels" resultType="java.lang.String">
        select hold2 as labelName
        from ads_bas_screen_view
        where screen_module = 'LYBQ'
          and hold1 = #{buildingId}
    </select>
    <select id="listShareCockpit" resultType="com.zjhh.economy.vo.cockpit.ShareCockpitVo">
        select hold1 as name, hold2 as link, hold3 as publishDate  from ads_bas_screen_view
        where screen_module = 'GRGX'
        order by hold3 DESC
    </select>


</mapper>
