<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsBusinessDocumentMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , business_id, document_id, document_type
    </sql>
    <select id="listDocument" resultType="java.lang.String">
        select document_id from ads_business_document where business_id = #{businessId} and document_type = #{documentType}
    </select>
    <select id="getMobileContactPerson" resultType="com.zjhh.economy.vo.DocumentVo">
        select document_id as fileId from ads_business_document where business_id = 'mobile_contact_person' and document_type = 'mobileContactPerson'
    </select>


</mapper>
