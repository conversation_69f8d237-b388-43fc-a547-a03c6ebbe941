<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.ChangeInfoMapper">

    <resultMap id="ProjectDetailForLog" type="com.zjhh.economy.vo.operationlog.ProjectDetailForLog">
        <id column="id" property="id"/>
        <result column="project_name" property="projectName"/>
        <result column="community_name" property="communityName"/>
        <result column="address" property="address"/>
        <result column="longitude" property="longitude"/>
        <result column="dimension" property="dimension"/>
        <result column="project_area" property="projectArea"/>
        <result column="project_type_name" property="projectTypeName"/>
        <result column="manage_company" property="manageCompany"/>
        <result column="contacts" property="contacts"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="project_intro" property="projectIntro"/>
        <collection property="projectImages" javaType="java.util.ArrayList"
                    ofType="java.lang.String">
            <result column="document_name"/>
        </collection>
    </resultMap>
    <select id="getProjectDetailForLog" resultMap="ProjectDetailForLog">
        select t1.id,
               t1.project_name,
               t2.name  as community_name,
               t1.address,
               t1.longitude,
               t1.dimension,
               t1.project_area,
               t3.name  as project_type_name,
               t1.manage_company,
               t1.project_intro,
               t1.contacts,
               t1.contact_phone,
               t5.id    as document_id,
               t5.title as document_name
        from ads_pm_project t1
                 left join dm_pm t2 on t1.community_code = t2.code and t2.type = 'Community'
                 left join dm_pm t3 on t1.project_type = t3.code and t3.type = 'ProjectType'
                 left join ads_business_document t4 on t1.id = t4.business_id and t4.document_type = 'projectImg'
                 left join ads_document t5 on t4.document_id = t5.id
        where t1.id = #{projectId}
        order by t5.id
    </select>

    <select id="pageOperationLog" resultType="com.zjhh.economy.vo.operationlog.OperationLogVo">
        select t1.id,
        t1.create_time,
        row_number() over (order by create_time desc, t1.id desc) as xh,
        case when t1.create_user = 'qiye' then (select t3.contact_person from ads_pm_enterprise_demand t3 where t3.id = t1.object_id)
            else t2.username end as username,
        case when t1.create_user = 'qiye' then  t1.create_user else t2.login_name end as login_name,
        t1.module,
        t1.type,
        t1.object_id,
        t1.object_name,
        t1.data_before,
        t1.data_after
        from ads_operation_log t1 left join sys_user t2 on t1.create_user = t2.code
        <where>
            <if test="req.startDate != null and req.startDate != ''.toString()">
                and t1.create_time >= #{req.startDate}::timestamp
            </if>
            <if test="req.endDate != null and req.endDate != ''.toString()">
                and t1.create_time &lt; #{req.endDate}::timestamp + interval '1 day'
            </if>
            <if test="req.username != null and req.username != ''.toString()">
                and t2.username like concat('%', #{req.username}, '%')
            </if>
            <if test="req.modules != null and req.modules.size > 0">
                and t1.module in
                <foreach collection="req.modules" item="module" open="(" close=")" separator=",">
                    #{module}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getBuildingDetailForLog" resultType="com.zjhh.economy.vo.operationlog.BuildingDetailForLog">
        select t1.id,
               t1.serial_no,
               t1.building_name,
               t2.project_name,
               t3.name as community_name,
               t2.address,
               t1.business_area,
               t1.building_area,
               t1.operation_time,
               t4.name as building_status_name,
               t5.name as building_type_name,
               t1.head,
               t1.phone,
               t1.introduce,
               t6.title as outside_img,
               t7.title as other_img,
               coalesce(t1.land_park_space,0) as land_park_space,
               coalesce(t1.underground_park_space,0) as underground_park_space
        from ads_pm_building t1
                 left join ads_pm_project t2 on t1.project_id = t2.id
                 left join dm_pm t3 on t3.code = t2.community_code and t3.type = 'Community'
                 left join dm_pm t4 on t1.building_status_code = t4.code and t4.type = 'BuildingStatus'
                 left join dm_pm t5 on t1.building_type_code = t5.code and t5.type = 'BuildingType'
                 left join ads_document t6 on t1.outside_img_id = t6.id
                 left join ads_document t7 on t1.other_img_id = t7.id
        where t1.id = #{buildingId}
    </select>
    <select id="getDemandDetailForLog" resultType="com.zjhh.economy.vo.operationlog.DemandDetailForLog">
        select t1.id,
               t1.demand_desc,
               t1.submit_date,
               t4.name as submitSourceName,
               t2.enterprise_name,
               t5.building_name,
               t1.contact_person,
               t1.phone,
               t3.name as demandTypeName
        from ads_pm_enterprise_demand t1
                 left join ads_pm_enterprise t2 on t1.enterprise_id = t2.id
                left join dm_pm t3 on t1.demand_type = t3.code and t3.type = 'DemandType'
                left join dm_pm t4 on t1.submit_source = t4.code and t4.type = 'SubmitSource'
                left join ads_pm_building t5 on t1.building_id = t5.id
        where t1.id = #{demandId}
    </select>
    <select id="listDocTitles" resultType="java.lang.String">
        select t2.title
        from ads_business_document t1
                 left join ads_document t2 on t1.document_id = t2.id
        where t1.document_type = #{docType}
          and t1.business_id = #{bizId}
        order by t2.id
    </select>
    <select id="getDemandHandleForLog" resultType="com.zjhh.economy.vo.operationlog.DemandHandleForLog">
        select t1.id,
               case
                   when t1.handle_type = 1 then '处理中'
                   when t1.handle_type = 2 then '已处理'
                   when t1.handle_type = 3 then '已忽略'
                   else '待处理' end as handleResult,
               t1.handle_desc,
               t2.title              as documentName
        from ads_pm_enterprise_demand_handle t1
                 left join ads_document t2 on t1.document_id = t2.id
        where t1.id = #{handleId}
    </select>
    <select id="getRoomDetailForLog" resultType="com.zjhh.economy.vo.operationlog.RoomDetailForLog">
        select t1.id,
               t4.project_name,
               t3.building_name,
               t2.floor_name,
               t1.room_no,
               t1.building_area,
               t1.business_area,
               t1.open_hired,
               t1.renovation_code,
               t1.room_type_code,
               t1.rend_unit_price,
               t1.rend_unit,
               t1.property_fees_unit_price,
               t1.property_fees_unit,
               t1.water_fees_unit_price,
               t1.electricity_fees_unit_price,
               t1.house_orientation,
               t1.ownership_code,
               t1.property_certificate_code
        from ads_pm_room t1
                 left join ads_pm_floor t2 on t1.floor_id = t2.id
                 left join ads_pm_building t3 on t2.building_id = t3.id
                 left join ads_pm_project t4 on t3.project_id = t4.id
        where t1.id = #{roomId}
    </select>
    <select id="getDmPmName" resultType="java.lang.String">
        select name
        from dm_pm
        where type = #{type}
          and code = #{code}
    </select>
    <select id="getRoomSettleInfoForLog" resultType="com.zjhh.economy.vo.operationlog.RoomSettleInfoForLog">
        select t1.id,
               t3.enterprise_name,
               t1.check_in_date,
               t1.expect_move_out_date,
               t1.area,
               t1.renovation_start_date,
               t1.renovation_end_date,
               t1.remark,
               t2.title as document_name,
               t4.room_no,
               t5.building_name
        from ads_pm_room_enterprise t1
                 left join ads_document t2 on t1.document_id = t2.id
                 left join ads_pm_enterprise t3 on t1.enterprise_id = t3.id
                 left join ads_pm_room t4 on t1.room_id = t4.id
                 left join ads_pm_building t5 on t1.building_id = t5.id
        where t1.id = #{settleId}
    </select>
    <select id="listFloorListForLog" resultType="com.zjhh.economy.vo.operationlog.FloorListForLog">
        select t1.id,
               t1.floor_no,
               t1.floor_name,
               (select count(t.id) from ads_pm_room t where t.floor_id = t1.id) as room_size,
               t1.remark,
               t2.title                                                         as documentName,
               row_number() over (order by t1.floor_no, t1.id)                  as xh
        from ads_pm_floor t1
                 left join ads_document t2 on t1.plane_img_id = t2.id
        where t1.building_id = #{buildingId}
    </select>
</mapper>
