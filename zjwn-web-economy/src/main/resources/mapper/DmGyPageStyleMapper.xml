<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.DmGyPageStyleMapper">
    <select id="listBuildingMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        SELECT zs_dm as key, zs_dm as value,zs_mc as title
        FROM dm_gy_page_style
        where type_dm = '10'
        order by xh
    </select>
    <select id="listInstitutionMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        SELECT zs_dm as key, zs_dm as value,zs_mc as title
        FROM dm_gy_page_style
        where type_dm = '11'
        order by xh
    </select>
    <select id="listEntStatusMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        SELECT zs_dm as key, zs_dm as value,zs_mc as title
        FROM dm_gy_page_style
        where type_dm = '12'
        order by xh
    </select>
    <select id="listChangeSituationMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        SELECT zs_dm as key, zs_dm as value,zs_mc as title
        FROM dm_gy_page_style
        where type_dm = '13'
        order by xh
    </select>
    <select id="listTreeEntTypeMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select djzclx_dm as key, djzclx_dm as value, djzclx_mc as title,coalesce(sjdjzclx_dm,'root') as parentKey
        from dm_dj_djzclx a
        order by sjdjzclx_dm, djzclx_dm
    </select>
    <select id="listTreeIndustryMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, code as value, name as title,coalesce(parent_code,'root') as parentKey
        from dm_gy_hy a
        order by parent_code, code
    </select>
</mapper>