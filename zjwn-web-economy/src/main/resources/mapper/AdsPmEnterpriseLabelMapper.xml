<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseLabelMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , room_id, label_code
    </sql>
    <delete id="delLabel">
        delete from ads_pm_enterprise_label apel where 1=1
        <if test="ids != null and ids.size > 0">
            and label_code not in (select code from dm_pm where id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </delete>
    <select id="listEnterpriseLabels" resultType="java.lang.String">
        select dp.name
        from ads_pm_enterprise_label apel
                 left join dm_pm dp on apel.label_code = dp.code
        where enterprise_id = #{enterpriseId}
          and dp.type = 'EnterpriseLabel'
    </select>

    <select id="listEnterpriseLabelsCode" resultType="com.zjhh.economy.vo.EnterpriseLabelVo">
        select dp.code as labelCode,dp.name as labelName
        from ads_pm_enterprise_label apel
                 left join dm_pm dp on apel.label_code = dp.code
        where enterprise_id = #{enterpriseId}
          and dp.type = 'EnterpriseLabel'
    </select>

</mapper>
