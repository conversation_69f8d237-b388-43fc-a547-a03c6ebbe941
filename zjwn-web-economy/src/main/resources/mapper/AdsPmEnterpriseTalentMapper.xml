<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseTalentMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, enterprise_id, employee_num, dr_num, master_num, bachelor_num, junior_number, polytechnic_num, other_num, create_user, create_time, update_time
    </sql>
    <select id="getTalentDetail" resultType="com.zjhh.economy.vo.TalentDetailVo">
        select id,enterprise_id,employee_num,dr_num,master_num,bachelor_num,junior_number,polytechnic_num,other_num from ads_pm_enterprise_talent
        where enterprise_id = #{enterpriseId}
    </select>

</mapper>
