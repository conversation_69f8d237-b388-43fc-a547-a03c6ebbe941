<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.WarningRuleMapper">
    <select id="pageWarningRule" resultType="com.zjhh.economy.vo.WarningRuleVo">
        select row_number() over (order by create_time DESC) as xh, id,
               rule_name,
               monitor_rule_period,
               monitor_rule_compare,
               monitor_rule_change,
               monitor_rule_zf,
               enabled,
               case when
          (select count(1)
           from ads_pm_building
           where id in (select building_id from ads_rule_range arr where rule_id = wr.id)) > 0
                   then (select string_agg(building_name,',')
                         from ads_pm_building
                         where id in (select building_id from ads_rule_range arr where rule_id = wr.id))
                   else '全部楼宇' end as monitorRange,
            create_time,
            monitor_rule_type
        from ads_warning_rule wr
        where monitor_rule_type = #{req.type}
    </select>
    <select id="getWarningRuleDetail" resultType="com.zjhh.economy.vo.MonitorRuleDetailVo">
        select id,
               rule_name,
               monitor_rule_period,
               monitor_rule_compare,
               monitor_rule_change,
               monitor_rule_zf,
               (select string_agg(building_id,',') from ads_rule_range where rule_id = wr.id) as monitorRange
        from ads_warning_rule wr
        where id = #{id}
    </select>
</mapper>