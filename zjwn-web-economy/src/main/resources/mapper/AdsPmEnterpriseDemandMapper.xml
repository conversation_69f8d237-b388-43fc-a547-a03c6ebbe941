<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseDemandMapper">
    <select id="pageEnterpriseDemand" resultType="com.zjhh.economy.vo.EnterpriseDemandVo">
        select row_number() over(order by coalesce(handle_type,0) ASC, submit_date DESC) as xh,
        aped.id,
        aped.submit_date,
        aped.demand_type,
        aped.demand_desc,
        aped.submit_source,
        aped.phone,
        ape.id as enterpriseId,
        ape.enterprise_name,
        (select name from dm_pm where code = aped.demand_type and type = 'DemandType') as demandTypeStr,
        (select name from dm_pm where code = aped.submit_source and type = 'SubmitSource') as submitSourceStr,
        case when handle_type = 1 then '处理中'
        when handle_type = 2 then '已处理'
        when handle_type = 3 then '已忽略'
        else '待处理' end as handleTypeStr,
        aped.handle_type
        from ads_pm_enterprise_demand aped
        inner join v_p_building t1 on aped.building_id = t1.id
        left join ads_pm_enterprise ape on aped.enterprise_id = ape.id
        where 1=1
        <if test="req.enterpriseName != null and req.enterpriseName != ''">
            and ape.enterprise_name like concat('%',#{req.enterpriseName},'%')
        </if>
        <if test="req.submitSource != null and req.submitSource != ''">
            and aped.submit_source = #{req.submitSource}
        </if>
        <if test="req.startDate != null and req.startDate != ''">
            and aped.submit_date::date between #{req.startDate}::date and #{req.endDate}::date
        </if>

        <if test="req.demandType != null ">
            and aped.demand_type = #{req.demandType}
        </if>

        <choose>
            <when test="req.demandStatus != null and req.demandStatus != 0">
                and aped.handle_type = #{req.demandStatus}
            </when>
        <when test="req.demandStatus != null and req.demandStatus == 0">
            and aped.handle_type is null
        </when>
        </choose>
        <if test="req.code != null and req.code != ''">
            and aped.building_id in (
            select id from ads_pm_building where project_id in (select id from ads_pm_project where community_code = #{req.code})
            union all
            select id from ads_pm_building where project_id = #{req.code}
            union all
            select id from ads_pm_building where id = #{req.code}
            )
        </if>
    </select>
    <select id="getEnterpriseDemandDetail" resultType="com.zjhh.economy.vo.EnterpriseDemandDetailVo">
        select aped.id,
               aped.submit_date,
               aped.demand_type,
               (select name from dm_pm where code = aped.demand_type and type = 'DemandType') as demandTypeStr,
               (select name from dm_pm where code = aped.submit_source and type = 'SubmitSource') as submitSourceStr,
               aped.demand_desc,
               aped.submit_source,
               aped.phone,
               ape.id as enterpriseId,
               ape.enterprise_name,
               aped.handle_type,
               aped.contact_person,
               (select building_name from ads_pm_building where id = aped.building_id) as buildingName,
               aped.create_user
        from ads_pm_enterprise_demand aped
                 left join ads_pm_enterprise ape on aped.enterprise_id = ape.id
        where aped.id = #{id}
    </select>
    <select id="getWorkPlatformDemand" resultType="com.zjhh.economy.vo.WorkPlatformDemandVo">
        select sum(totalDemandCount)                                                        as totalDemandCount,
               sum(pendingDemandCount)                                                      as pendingDemandCount,
               sum(finishedDemandCount)                                                     as finishedDemandCount,
               sum(handlingCount) as handlingCount,
               case
                   when sum(totalDemandCount) = 0 then 0
                   else
                       round(sum(finishedDemandCount) / sum(totalDemandCount) * 100, 2) end as finishedRate
        from (select count(1) as totalDemandCount, 0 as pendingDemandCount, 0 as finishedDemandCount,0 as handlingCount
              from ads_pm_enterprise_demand
              where 1=1
        <if test="req.datekey != null and req.datekey != ''">
            and TO_CHAR(submit_date, 'YYYYMM') = #{req.datekey}
        </if>
              union all
              select 0 as totalDemandCount, count(1) as pendingDemandCount, 0 as finishedDemandCount, 0 as handlingCount
              from ads_pm_enterprise_demand
              where handle_type is null
        <if test="req.datekey != null and req.datekey != ''">
            and TO_CHAR(submit_date, 'YYYYMM') = #{req.datekey}
        </if>
              union all
              select 0 as totalDemandCount, 0 as pendingDemandCount, count(1) as finishedDemandCount, 0 as handlingCount
              from ads_pm_enterprise_demand
              where (handle_type = 2 or handle_type = 3)
              <if test="req.datekey != null and req.datekey != ''">
                  and TO_CHAR(submit_date, 'YYYYMM') = #{req.datekey}
              </if>
              union all
              select 0 as totalDemandCount, 0 as pendingDemandCount, 0 as finishedDemandCount, count(1) as handlingCount
              from ads_pm_enterprise_demand
              where handle_type = 1
        <if test="req.datekey != null and req.datekey != ''">
            and TO_CHAR(submit_date, 'YYYYMM') = #{req.datekey}
        </if>
              ) t1
    </select>
    <select id="getDemandBrief" resultType="java.util.Map">
        select sum(t1.totalCount) as totalCount, sum(t1.pendingCount) as pendingCount
        from (select count(1) as totalCount,
                     0        as pendingCount
              from ads_pm_enterprise_demand
              where submit_date::date between #{startDate}::date
                and #{endDate}::date
              union all
              select 0 as totalCount, count (1) as pendingCount
              from ads_pm_enterprise_demand
              where submit_date::date between #{startDate}::date
                and #{endDate}::date
                and (handle_type is null
                 or handle_type = 1)) t1
    </select>
    <select id="getWorkPlatformDemandByAuth" resultType="com.zjhh.economy.vo.WorkPlatformDemandVo">
        select sum(totalDemandCount)                                                        as totalDemandCount,
        sum(pendingDemandCount)                                                      as pendingDemandCount,
        sum(finishedDemandCount)                                                     as finishedDemandCount,
        sum(handlingCount) as handlingCount,
        case
        when sum(totalDemandCount) = 0 then 0
        else
        round(sum(finishedDemandCount) / sum(totalDemandCount) * 100, 2) end as finishedRate
        from (select count(1) as totalDemandCount, 0 as pendingDemandCount, 0 as finishedDemandCount,0 as handlingCount
        from ads_pm_enterprise_demand
        where 1=1
          and building_id in (select id from v_p_building where (id = #{req.id} or project_id = #{req.id}))

        union all
        select 0 as totalDemandCount, count(1) as pendingDemandCount, 0 as finishedDemandCount, 0 as handlingCount
        from ads_pm_enterprise_demand
        where handle_type is null
        and building_id in (select id from v_p_building where (id = #{req.id} or project_id = #{req.id}))

        union all
        select 0 as totalDemandCount, 0 as pendingDemandCount, count(1) as finishedDemandCount, 0 as handlingCount
        from ads_pm_enterprise_demand
        where (handle_type = 2 or handle_type = 3)
        and building_id in (select id from v_p_building where (id = #{req.id} or project_id = #{req.id}))

        union all
        select 0 as totalDemandCount, 0 as pendingDemandCount, 0 as finishedDemandCount, count(1) as handlingCount
        from ads_pm_enterprise_demand
        where handle_type = 1
        and building_id in (select id from v_p_building where (id = #{req.id} or project_id = #{req.id}))

        ) t1
    </select>
    <select id="listEnterpriseDemandByAuth" resultType="com.zjhh.economy.vo.EnterpriseDemandVo">
        select row_number() over(order by coalesce(handle_type,0) ASC, submit_date DESC) as xh,
        aped.id,
        aped.submit_date,
        aped.demand_type,
        aped.demand_desc,
        aped.submit_source,
        aped.phone,
        ape.id as enterpriseId,
        ape.enterprise_name,
        (select name from dm_pm where code = aped.demand_type and type = 'DemandType') as demandTypeStr,
        (select name from dm_pm where code = aped.submit_source and type = 'SubmitSource') as submitSourceStr,
        case when handle_type = 1 then '处理中'
        when handle_type = 2 then '已处理'
        when handle_type = 3 then '已忽略'
        else '待处理' end as handleTypeStr,
        aped.handle_type
        from ads_pm_enterprise_demand aped
        inner join v_p_building t1 on aped.building_id = t1.id
        left join ads_pm_enterprise ape on aped.enterprise_id = ape.id
        where 1=1
        and  (t1.id = #{req.id} or t1.project_id = #{req.id})
        <choose>
            <when test="req.demandStatus != null and req.demandStatus != 0">
                and aped.handle_type = #{req.demandStatus}
            </when>
            <when test="req.demandStatus != null and req.demandStatus == 0">
                and aped.handle_type is null
            </when>
        </choose>
        limit 5

    </select>
</mapper>