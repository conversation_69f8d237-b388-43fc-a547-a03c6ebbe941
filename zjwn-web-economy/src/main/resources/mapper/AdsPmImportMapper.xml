<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmImportMapper">
    <select id="pageImportExternalData" resultType="com.zjhh.economy.vo.ImportExternalDataVo">
        select id, row_number() over (order by upload_date DESC) as xh, upload_date, upload_type, upload_status from ads_pm_import where 1=1
        <if test="req.uploadType != null and req.uploadType != '' ">
            and upload_type = #{req.uploadType}
        </if>
        <if test="req.uploadStatus != null ">
            and upload_status = #{req.uploadStatus}
        </if>
        <if test="req.uploadStartDate != null and req.uploadStartDate != ''">
            and upload_date::date >= #{req.uploadStartDate}::date
        </if>
        <if test="req.uploadEndDate != null and req.uploadEndDate != ''">
            and upload_date::date &lt;= #{req.uploadEndDate}::date
        </if>
    </select>
</mapper>