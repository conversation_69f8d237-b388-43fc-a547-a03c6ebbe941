<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEconomicScoreMapper">
    <select id="getEconomicIndicatorScore" resultType="com.zjhh.economy.vo.cockpit.EconomicIndicatorScoreVo">
        select score,
               tax_rate_score,
               ent_cultivate_score,
               industry_scale_score,
               datekey,
               tax_income_score,
               settled_rate_score,
               unit_output_score
        from ads_pm_economic_score
        where building_id = #{buildingId}
        and datekey = #{queryDate}
    </select>
</mapper>