<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseChangeMapper">
    <select id="listEnterpriseChange" resultType="com.zjhh.economy.vo.EnterpriseChangeListVo">
        select id, change_date, change_type, remark from ads_pm_enterprise_change t1
        where t1.enterprise_id = #{enterpriseId}
        order by t1.create_time
    </select>
</mapper>