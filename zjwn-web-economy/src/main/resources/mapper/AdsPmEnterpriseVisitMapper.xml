<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseVisitMapper">
    <select id="pageEnterpriseVisit" resultType="com.zjhh.economy.vo.EnterpriseVisitVo">
        select row_number() over(order by visit_date DESC) as xh,apev.id,ape.id as
        enterpriseId,ape.enterprise_name,apev.visit_purpose,apev.visitor,apev.visit_date,apev.receptionist,apev.remark
        from
        ads_pm_enterprise_visit apev
        left join ads_pm_enterprise ape on apev.enterprise_id = ape.id
        where 1=1
        <if test="req.enterpriseName != null and req.enterpriseName != ''">
            and ape.enterprise_name like concat('%',#{req.enterpriseName},'%')
        </if>
        <if test="req.startDate != null and req.startDate != ''">
            and apev.visit_date::date >= #{req.startDate}::date
        and apev.visit_date::date &lt;= #{req.endDate}::date
        </if>
        order by apev.visit_date desc
    </select>
    <select id="getEnterpriseVisitDetail" resultType="com.zjhh.economy.vo.EnterpriseVisitDetailVo">
        select apev.id
             , ape.id                                                     as enterpriseId
             ,ape.enterprise_name
             , apev.visit_purpose
             , apev.visitor
             , apev.visit_date
             , apev.receptionist
             , apev.remark
             , apev.document_id
             , (select title from ads_document where id = apev.document_id) as documentName
            ,(select size from ads_document where id = apev.document_id) as size
        from ads_pm_enterprise_visit apev
                 left join ads_pm_enterprise ape on apev.enterprise_id = ape.id
        where apev.id = #{id}
    </select>
</mapper>