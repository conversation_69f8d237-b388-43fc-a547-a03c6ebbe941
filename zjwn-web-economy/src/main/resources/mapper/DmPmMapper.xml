<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.DmPmMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , code, name, type, sort
    </sql>
    <select id="getTitleName" resultType="java.lang.String">
        select name
        from dm_gy_hy
        where code = #{code}
    </select>
    <select id="getDate" resultType="com.zjhh.economy.vo.DocDateVo">

    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">
        select coalesce(max(sort), 0)
        from dm_pm
        where type = #{type}
    </select>
    <select id="listLabel" resultType="com.zjhh.economy.request.LabelReq">
        select id, name as labelName
        from dm_pm
        where type = #{type}
    </select>
    <select id="listCommunity" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, name as title, code as value
        from dm_pm
        where type = 'Community'
        order by code
    </select>
    <select id="listMoveRegisterLabel" resultType="com.zjhh.economy.vo.MoveRegisterLabelVo">
        select name as labelName, code as labelCode
        from dm_pm
        where type = 'MoveOut'
        and code in (
        select label_code
        from ads_pm_move_register_label
        where move_register_id = #{moveRegisterId}
        )
        order by code
    </select>
    <select id="listMoveRegisterLabelByName" resultType="java.lang.String">
        select code
        from dm_pm
        where type = 'MoveOut'
        and name in
        <foreach collection="names" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getRecursiveTitleName" resultType="java.lang.String">
        WITH RECURSIVE industry_hierarchy AS (
            -- 基础查询：获取第三级作为起始节点
            SELECT
                code,
                name,
                parent_code,
                name::character varying(500) as industry_path,
            3 as level  -- 从第三级开始
        FROM dm_gy_hy
        WHERE parent_code IN (
            SELECT code FROM dm_gy_hy
            WHERE parent_code IN (
            SELECT code FROM dm_gy_hy
            WHERE parent_code IS NULL OR parent_code = '' OR parent_code = code
           OR parent_code NOT IN (SELECT code FROM dm_gy_hy WHERE code IS NOT NULL)
            )
            )

        UNION ALL

        -- 递归查询：构建从第三级开始的完整路径
        SELECT
            child.code,
            child.name,
            child.parent_code,
            (parent.industry_path || '/' || child.name)::character varying(500) as industry_path,
        parent.level + 1 as level
        FROM dm_gy_hy child
            INNER JOIN industry_hierarchy parent ON child.parent_code = parent.code
        WHERE child.parent_code != child.code AND parent.level &lt; 10  -- 防止无限递归，最多10层
            )
        select industry_path from industry_hierarchy where code = #{code}
    </select>

</mapper>
