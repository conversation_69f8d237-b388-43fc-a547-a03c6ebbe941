{"imageActionName": "uploadimage", "imageFieldName": "upfile", "imageMaxSize": 2048000, "imageAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "imageCompressEnable": true, "imageCompressBorder": 1600, "imageInsertAlign": "none", "imageUrlPrefix": "/building/file/", "localSavePathPrefix": "images/inform", "imagePathFormat": "/ueditor/image/{yyyy}{mm}{dd}/{time}{rand:6}", "scrawlActionName": "uploadscrawl", "scrawlFieldName": "upfile", "scrawlPathFormat": "/ueditor/image/{yyyy}{mm}{dd}/{time}{rand:6}", "scrawlMaxSize": 2048000, "scrawlUrlPrefix": "", "scrawlInsertAlign": "none", "snapscreenActionName": "uploadimage", "snapscreenPathFormat": "/ueditor/image/{yyyy}{mm}{dd}/{time}{rand:6}", "snapscreenUrlPrefix": "", "snapscreenInsertAlign": "none", "catcherLocalDomain": ["127.0.0.1", "localhost", "img.baidu.com"], "catcherActionName": "catchimage", "catcherFieldName": "source", "catcherPathFormat": "/ueditor/image/{yyyy}{mm}{dd}/{time}{rand:6}", "catcherUrlPrefix": "", "catcherMaxSize": 2048000, "catcherAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "catchRemoteImageEnable": false, "videoActionName": "uploadvideo", "videoFieldName": "upfile", "videoPathFormat": "/ueditor/video/{yyyy}{mm}{dd}/{time}{rand:6}", "videoUrlPrefix": "/pc/file/", "videoMaxSize": 102400000, "videoAllowFiles": [".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid"], "fileActionName": "uploadfile", "fileFieldName": "upfile", "filePathFormat": "/ueditor/file/{yyyy}{mm}{dd}/{time}{rand:6}", "fileUrlPrefix": "/pc/file/", "fileMaxSize": 51200000, "fileAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"], "imageManagerActionName": "listimage", "imageManagerListPath": "/ueditor/image/", "imageManagerListSize": 20, "imageManagerUrlPrefix": "", "imageManagerInsertAlign": "none", "imageManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "fileManagerActionName": "listfile", "fileManagerListPath": "/ueditor/file/", "fileManagerUrlPrefix": "", "fileManagerListSize": 20, "fileManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"]}